# =============================================================================
# CRM ODONTO - UNIFIED DOCKER COMPOSE
# =============================================================================
# Este arquivo unificado funciona tanto para desenvolvimento quanto produção
# O comportamento é controlado pela variável NODE_ENV no arquivo .env:
# - NODE_ENV=development: Hot reload, volumes de código, logs detalhados
# - NODE_ENV=production: Otimizado, sem volumes de código, logs mínimos
# =============================================================================

services:
  # =============================================================================
  # DATABASE SERVICE - MySQL 8.4
  # =============================================================================
  mysql:
    image: mysql:8.4
    container_name: crm_odonto_mysql
    restart: unless-stopped
    ports:
      - "${DB_EXTERNAL_PORT:-3306}:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_0900_ai_ci
      --innodb_strict_mode=0
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - crm-network

  # =============================================================================
  # API INITIALIZATION SERVICE - Database Setup
  # =============================================================================
  api-init:
    build:
      context: ./api
      dockerfile: Dockerfile
      target: ${NODE_ENV}
    container_name: crm_odonto_api_init
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - NODE_ENV=${NODE_ENV}
      - JWT_SECRET=${JWT_SECRET}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PORT=${MINIO_PORT}
      - MINIO_USE_SSL=${MINIO_USE_SSL}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - MINIO_PUBLIC_URL=${MINIO_PUBLIC_URL}
    volumes:
      # Desenvolvimento: bind mount para hot reload
      - ./api:/app
      # Volume anônimo para preservar node_modules
      - /app/node_modules
    command: >
      bash -c "
        if [ '${NODE_ENV}' = 'development' ]; then
          echo '🔧 Configurando ambiente de desenvolvimento...' &&
          echo '📦 Instalando dependências...' &&
          npm install &&
          echo '🗄️ Executando migrations...' &&
          npm run typeorm:run &&
          echo '🌱 Executando seeds (apenas desenvolvimento)...' &&
          npm run seed:run &&
          echo '✅ Inicialização de desenvolvimento concluída!'
        else
          echo '🏭 Configurando ambiente de produção...' &&
          echo '🗄️ Executando migrations...' &&
          npm run typeorm:run &&
          echo '⚠️  Seeds não executados em produção (dados reais)' &&
          echo '✅ Inicialização de produção concluída!'
        fi
      "
    depends_on:
      mysql:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - crm-network

  # =============================================================================
  # API SERVICE - NestJS Backend
  # =============================================================================
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
      target: ${NODE_ENV}
    container_name: crm_odonto_api
    restart: unless-stopped
    ports:
      - "${API_EXTERNAL_PORT:-3000}:3000"
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - NODE_ENV=${NODE_ENV}
      - JWT_SECRET=${JWT_SECRET}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PORT=${MINIO_PORT}
      - MINIO_USE_SSL=${MINIO_USE_SSL}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - MINIO_PUBLIC_URL=${MINIO_PUBLIC_URL}
    volumes:
      # Desenvolvimento: bind mount para hot reload
      - ./api:/app
      # Volume anônimo para preservar node_modules
      - /app/node_modules
    depends_on:
      api-init:
        condition: service_completed_successfully
    networks:
      - crm-network

  # =============================================================================
  # WEB SERVICE - Angular Frontend
  # =============================================================================
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
      target: ${NODE_ENV}
    container_name: crm_odonto_web
    restart: unless-stopped
    ports:
      # Desenvolvimento: 4200:4200 (Angular dev server)
      # Produção: 80:80 (Nginx)
      - "${FRONTEND_EXTERNAL_PORT:-4200}:${FRONTEND_PORT:-4200}"
    environment:
      - NODE_ENV=${NODE_ENV}
    volumes:
      # Desenvolvimento: bind mount para hot reload
      - ./web:/app
      # Volume anônimo para preservar node_modules
      - /app/node_modules
    depends_on:
      - api
    networks:
      - crm-network

  # =============================================================================
  # AUTOMATION SERVICE - Python Backend
  # =============================================================================
  automacao:
    build:
      context: ./automacao
      dockerfile: Dockerfile
      target: ${NODE_ENV}
    container_name: crm_odonto_automacao
    restart: unless-stopped
    ports:
      - "${AUTOMATION_EXTERNAL_PORT:-8000}:8000"
    environment:
      - API_URL=${API_URL:-http://api:3000}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - AUTOMACAO_PORT=${AUTOMATION_PORT:-8000}
    volumes:
      # Desenvolvimento: bind mount para hot reload
      - ./automacao:/app
      # Volume anônimo para preservar venv
      - /app/venv
    depends_on:
      - api
    networks:
      - crm-network

  # =============================================================================
  # MINIO SERVICE - Object Storage
  # =============================================================================
  minio:
    image: minio/minio
    container_name: crm_odonto_minio
    restart: unless-stopped
    ports:
      - "${MINIO_EXTERNAL_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_EXTERNAL_PORT:-9001}:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_ADDRESS: ":9000"
      MINIO_CONSOLE_ADDRESS: ":9001"
    volumes:
      - minio_data:/data
    command: server /data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - crm-network

  # =============================================================================
  # MINIO SETUP SERVICE - Bucket Configuration
  # =============================================================================
  minio-setup:
    image: minio/mc
    container_name: crm_odonto_minio_setup
    depends_on:
      minio:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Aguardando o MinIO iniciar...' &&
        sleep 10 &&
        echo 'Configurando o cliente MinIO...' &&
        mc config host add myminio http://minio:9000 ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD} &&
        echo 'Verificando se o bucket existe...' &&
        if ! mc ls myminio | grep -q '${MINIO_BUCKET_NAME}'; then
          echo 'Criando bucket ${MINIO_BUCKET_NAME}...' &&
          mc mb myminio/${MINIO_BUCKET_NAME}
        fi &&
        echo 'Configurando política de acesso para o bucket...' &&
        mc anonymous set download myminio/${MINIO_BUCKET_NAME} &&
        echo 'Configuração do MinIO concluída com sucesso!'
      "
    restart: "no"
    networks:
      - crm-network

# =============================================================================
# VOLUMES CONFIGURATION
# =============================================================================
volumes:
  # Volume para dados do MySQL - persiste dados do banco entre restarts
  mysql_data:
    name: crm-odonto_mysql_data
    driver: local
    labels:
      - "com.crm-odonto.service=mysql"
      - "com.crm-odonto.description=MySQL database data persistence"

  # Volume para dados do MinIO - persiste arquivos e configurações
  minio_data:
    name: crm-odonto_minio_data
    driver: local
    labels:
      - "com.crm-odonto.service=minio"
      - "com.crm-odonto.description=MinIO object storage data persistence"

# =============================================================================
# NETWORKS CONFIGURATION
# =============================================================================
networks:
  crm-network:
    name: crm-odonto_network
    driver: bridge
