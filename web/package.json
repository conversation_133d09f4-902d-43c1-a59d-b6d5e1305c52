{"name": "crm-odonto-web", "version": "0.0.0", "scripts": {"ng": "ng", "dev": "ng serve --configuration=development --host 0.0.0.0 --disable-host-check", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:CRM-Odonto-web": "node dist/crm-odonto-web/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.2.5", "@angular/cdk": "^19.2.8", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.6", "@swimlane/ngx-charts": "^22.0.0", "@swimlane/ngx-graph": "^10.0.0", "cytoscape": "^3.32.0", "cytoscape-dagre": "^2.5.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "express": "^4.18.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "ngx-file-drop": "^16.0.0", "ngx-mask": "^14.2.4", "ngx-webcam": "^0.4.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.6", "@angular/cli": "^19.2.6", "@angular/compiler-cli": "^19.2.0", "@types/cytoscape": "^3.21.9", "@types/cytoscape-dagre": "^2.3.3", "@types/d3-shape": "^3.1.7", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "autoprefixer": "^10.4.21", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "~5.7.2"}}