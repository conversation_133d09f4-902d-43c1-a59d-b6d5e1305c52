import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { DivergentLeadsBoxComponent } from '../divergent-leads-box/divergent-leads-box.component';
import { AiSuggestionsBoxComponent } from '../ai-suggestions-box/ai-suggestions-box.component';

@Component({
  selector: 'app-dashboard-pro',
  standalone: true,
  imports: [CommonModule, RouterLink, DivergentLeadsBoxComponent, AiSuggestionsBoxComponent],
  template: `
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Estatísticas Rápidas -->
      <div class="pro-card col-span-1 md:col-span-2 lg:col-span-3">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Visão Geral</h2>
          <a routerLink="/treatment-board" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
            Ver Board de Tratamentos
          </a>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Card de Estatística 1 -->
          <div class="bg-white rounded-lg shadow-sm p-5 border-l-4 border-blue-500">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-sm text-gray-500 mb-1">Pacientes Ativos</p>
                <h3 class="text-2xl font-bold">248</h3>
                <p class="text-xs text-green-600 mt-2">
                  <span class="font-medium">↑ 12%</span> desde o mês passado
                </p>
              </div>
              <div class="p-2 bg-blue-100 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Card de Estatística 2 -->
          <div class="bg-white rounded-lg shadow-sm p-5 border-l-4 border-purple-500">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-sm text-gray-500 mb-1">Agendamentos Hoje</p>
                <h3 class="text-2xl font-bold">12</h3>
                <p class="text-xs text-purple-600 mt-2">
                  <span class="font-medium">3 confirmados</span> | 9 pendentes
                </p>
              </div>
              <div class="p-2 bg-purple-100 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Card de Estatística 3 -->
          <a routerLink="/treatment-board" class="block">
            <div class="bg-white rounded-lg shadow-sm p-5 border-l-4 border-green-500 hover:shadow-md transition-shadow">
              <div class="flex justify-between items-start">
                <div>
                  <p class="text-sm text-gray-500 mb-1">Tratamentos Ativos</p>
                  <h3 class="text-2xl font-bold">86</h3>
                  <p class="text-xs text-green-600 mt-2">
                    <span class="font-medium">↑ 8%</span> desde o mês passado
                  </p>
                </div>
                <div class="p-2 bg-green-100 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
            </div>
          </a>

          <!-- Card de Estatística 4 -->
          <div class="bg-white rounded-lg shadow-sm p-5 border-l-4 border-amber-500">
            <div class="flex justify-between items-start">
              <div>
                <p class="text-sm text-gray-500 mb-1">Faturamento Mensal</p>
                <h3 class="text-2xl font-bold">R$ 42.580</h3>
                <p class="text-xs text-amber-600 mt-2">
                  <span class="font-medium">↑ 15%</span> desde o mês passado
                </p>
              </div>
              <div class="p-2 bg-amber-100 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      
      <!-- Box de Leads Divergentes -->
      <div class="h-full">
        <app-divergent-leads-box></app-divergent-leads-box>
      </div>

      <!-- Box de Sugestões da IA Pendentes -->
      <div class="h-full ">
        <app-ai-suggestions-box></app-ai-suggestions-box>
      </div>

      <!-- Demandas Recentes -->
      <div class="pro-card col-span-1 lg:col-span-1">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Demandas Recentes</h2>
          <a routerLink="/tasks" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
            Ver todas
          </a>
        </div>

        <div class="space-y-3">
          <!-- Demanda 1 -->
          <div class="p-3 bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm">
            <div class="flex justify-between items-start">
              <h4 class="font-medium text-gray-900">Confirmar agendamentos</h4>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Em Progresso
              </span>
            </div>
            <p class="text-sm text-gray-500 mt-1">Ligar para pacientes da próxima semana</p>
            <div class="flex items-center mt-2">
              <span class="text-xs text-gray-500">Atribuído a: Ana</span>
              <span class="mx-2 text-gray-300">•</span>
              <span class="text-xs text-gray-500">Vence em: 2 dias</span>
            </div>
          </div>

          <!-- Demanda 2 -->
          <div class="p-3 bg-white rounded-lg border-l-4 border-blue-500 shadow-sm">
            <div class="flex justify-between items-start">
              <h4 class="font-medium text-gray-900">Atualizar estoque</h4>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                A Fazer
              </span>
            </div>
            <p class="text-sm text-gray-500 mt-1">Verificar materiais em falta</p>
            <div class="flex items-center mt-2">
              <span class="text-xs text-gray-500">Atribuído a: Carlos</span>
              <span class="mx-2 text-gray-300">•</span>
              <span class="text-xs text-gray-500">Vence em: 5 dias</span>
            </div>
          </div>

          <!-- Demanda 3 -->
          <div class="p-3 bg-white rounded-lg border-l-4 border-green-500 shadow-sm">
            <div class="flex justify-between items-start">
              <h4 class="font-medium text-gray-900">Relatório mensal</h4>
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Concluído
              </span>
            </div>
            <p class="text-sm text-gray-500 mt-1">Preparar relatório financeiro</p>
            <div class="flex items-center mt-2">
              <span class="text-xs text-gray-500">Atribuído a: Mariana</span>
              <span class="mx-2 text-gray-300">•</span>
              <span class="text-xs text-gray-500">Concluído: Ontem</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Próximos Agendamentos -->
      <div class="pro-card col-span-1 lg:col-span-3">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Próximos Agendamentos</h2>
          <a routerLink="/schedulings" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
            Ver todos
          </a>
        </div>

        <div class="space-y-3">
          <!-- Agendamento 1 -->
          <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <span class="text-blue-600 font-medium">09:30</span>
            </div>
            <div class="flex-grow">
              <h4 class="font-medium text-gray-900">Maria Silva</h4>
              <p class="text-sm text-gray-500">Consulta de Avaliação • Dr. Carlos</p>
            </div>
            <div class="flex-shrink-0">
              <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Confirmado
              </span>
            </div>
          </div>

          <!-- Agendamento 2 -->
          <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <span class="text-blue-600 font-medium">10:45</span>
            </div>
            <div class="flex-grow">
              <h4 class="font-medium text-gray-900">João Pereira</h4>
              <p class="text-sm text-gray-500">Limpeza • Dra. Ana</p>
            </div>
            <div class="flex-shrink-0">
              <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pendente
              </span>
            </div>
          </div>

          <!-- Agendamento 3 -->
          <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <span class="text-blue-600 font-medium">13:15</span>
            </div>
            <div class="flex-grow">
              <h4 class="font-medium text-gray-900">Carla Mendes</h4>
              <p class="text-sm text-gray-500">Tratamento de Canal • Dr. Paulo</p>
            </div>
            <div class="flex-shrink-0">
              <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Confirmado
              </span>
            </div>
          </div>

          <!-- Agendamento 4 -->
          <div class="flex items-center p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors">
            <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <span class="text-blue-600 font-medium">15:30</span>
            </div>
            <div class="flex-grow">
              <h4 class="font-medium text-gray-900">Roberto Alves</h4>
              <p class="text-sm text-gray-500">Extração • Dra. Ana</p>
            </div>
            <div class="flex-shrink-0">
              <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pendente
              </span>
            </div>
          </div>
        </div>
      </div>

    </div>

  `,
})
export class DashboardProComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}
}
