<div class="pro-card h-full">
  <div class="flex justify-between items-center mb-4">
    <div class="flex items-center">
      <h2 class="text-xl font-semibold">Leads com Campos Divergentes</h2>
      <span *ngIf="totalDivergentLeads > 0" class="ml-2 px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        {{ totalDivergentLeads }}
      </span>
    </div>
    <a [routerLink]="['/leads']" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
      Ver todos
    </a>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-32">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <!-- Lista de leads -->
  <div *ngIf="!isLoading && !error" class="space-y-3 flex-grow overflow-y-auto">
    <div *ngFor="let lead of divergentLeads"
         class="p-3 bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
         (click)="openLeadDetail(lead)">
      <div class="flex justify-between items-start">
        <div>
          <h4 class="font-medium text-gray-900">{{ lead.fullName }}</h4>
          <p class="text-sm text-gray-500">{{ lead.email }}</p>
          <p class="text-sm text-gray-500">{{ lead.phone }}</p>
        </div>
        <div>
          <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Campos Divergentes
          </span>
        </div>
      </div>
    </div>

    <!-- Mensagem quando não há leads -->
    <div *ngIf="divergentLeads.length === 0 && !isLoading && totalDivergentLeads === 0" class="text-center py-8 text-gray-500">
      <p>Nenhum lead com campos divergentes</p>
      <p class="text-sm mt-2">Todos os leads estão com cadastros atualizados</p>
    </div>

    <!-- Espaço vazio para manter a altura quando há poucos itens -->
    <div *ngIf="divergentLeads.length > 0 && divergentLeads.length < 3" class="py-8"></div>
  </div>
</div>



<!-- Modal de Detalhes do Lead -->
<app-modal *ngIf="showModal" [title]="'Detalhes do Lead'" [isOpen]="showModal" (close)="closeModal()" [showDefaultFooter]="true">
  <div *ngIf="selectedLead" class="p-4 max-h-[70vh] overflow-y-auto">
    <!-- Cabeçalho com informações principais -->
    <div class="mb-6 pb-4 border-b border-gray-200">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center">
        <div>
          <h2 class="text-xl font-bold">{{ selectedLead.fullName }}</h2>
          <p class="text-gray-600">{{ selectedLead.email }} | {{ selectedLead.phone }}</p>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full"
                [ngClass]="selectedLead.isExistingPatient ? (selectedLead.hasUpdatesAvailable ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') : 'bg-blue-100 text-blue-800'">
            {{ selectedLead.isExistingPatient ? (selectedLead.hasUpdatesAvailable ? 'Campos Divergentes' : 'Cadastro Atualizado') : 'Novo Lead' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Informações detalhadas -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Dados Pessoais -->
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Dados Pessoais</h3>
          <a *ngIf="selectedLead.isExistingPatient && selectedLead.patientId"
             [routerLink]="['/patients', selectedLead.patientId]"
             class="text-blue-600 hover:text-blue-800 transition-colors"
             (click)="closeModal()"
             title="Ver perfil do paciente">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </a>
        </div>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">Nome Completo:</span>
            <p class="font-medium">{{ selectedLead.fullName }}</p>
            <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('name')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.name }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(selectedLead.id, 'name')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(selectedLead.id, 'name')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>

          <div>
            <span class="text-sm text-gray-500">CPF:</span>
            <p class="font-medium">{{ selectedLead.cpf }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Telefone:</span>
            <p class="font-medium">{{ selectedLead.phone }}</p>
            <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('phone')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.phone }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(selectedLead.id, 'phone')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(selectedLead.id, 'phone')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>

          <div>
            <span class="text-sm text-gray-500">E-mail:</span>
            <p class="font-medium">{{ selectedLead.email }}</p>
            <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('email')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.email }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(selectedLead.id, 'email')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(selectedLead.id, 'email')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>

          <!-- Verificação de WhatsApp -->
          <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('whatsapp')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">Divergente do WhatsApp do paciente</p>
            <p class="text-sm text-gray-600">WhatsApp no cadastro: {{ selectedLead.patient?.whatsapp || 'Não informado' }}</p>
            <div class="flex space-x-2 mt-1">
              <button
                (click)="updatePatientField(selectedLead.id, 'whatsapp')"
                class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
              >
                Atualizar WhatsApp
              </button>
              <button
                (click)="skipPatientField(selectedLead.id, 'whatsapp')"
                class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
              >
                Não atualizar
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Endereço -->
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h3 class="text-lg font-semibold mb-4">Endereço</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">CEP:</span>
            <p class="font-medium">{{ selectedLead.cep }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Rua:</span>
            <p class="font-medium">{{ selectedLead.street }}, {{ selectedLead.number }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Bairro:</span>
            <p class="font-medium">{{ selectedLead.neighborhood }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Cidade/Estado:</span>
            <p class="font-medium">{{ selectedLead.city }}/{{ selectedLead.state }}</p>
          </div>

          <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('address')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">Endereço divergente do cadastro do paciente</p>
            <p class="text-sm text-gray-600">Endereço no cadastro: {{ selectedLead.patient?.addressStreet }}, {{ selectedLead.patient?.addressNumber }} - {{ selectedLead.patient?.addressNeighborhood }}, {{ selectedLead.patient?.addressCity }}/{{ selectedLead.patient?.addressState }}</p>
            <div class="flex space-x-2 mt-1">
              <button
                (click)="updatePatientField(selectedLead.id, 'address')"
                class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
              >
                Atualizar cadastro
              </button>
              <button
                (click)="skipPatientField(selectedLead.id, 'address')"
                class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
              >
                Não atualizar
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>

  <!-- Footer com botão de fechar -->
  <div footer>
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Fechar
    </button>
  </div>
</app-modal>
