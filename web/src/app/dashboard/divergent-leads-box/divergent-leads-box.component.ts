import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { LeadService } from '../../core/services/lead.service';
import { LeadForm } from '../../core/models/lead-form.model';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-divergent-leads-box',
  standalone: true,
  imports: [CommonModule, RouterLink, ModalComponent],
  templateUrl: './divergent-leads-box.component.html',
  styleUrls: ['./divergent-leads-box.component.scss']
})
export class DivergentLeadsBoxComponent implements OnInit {
  divergentLeads: LeadForm[] = [];
  isLoading = true;
  error = '';
  totalDivergentLeads = 0;

  // Modal properties
  showModal = false;
  selectedLead: LeadForm | null = null;

  constructor(
    private leadService: LeadService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDivergentLeads();
  }

  loadDivergentLeads(): void {
    this.isLoading = true;
    this.leadService.getLeads().subscribe({
      next: (leads) => {
        // Filtrar leads que são pacientes e possuem campos divergentes
        const filteredLeads = leads.filter(lead =>
          lead.isExistingPatient && lead.hasUpdatesAvailable
        );

        this.totalDivergentLeads = filteredLeads.length;

        // Limitar a 4 leads
        this.divergentLeads = filteredLeads.slice(0, 4);

        this.isLoading = false;

        if (this.totalDivergentLeads === 0) {
          console.log('Não há mais leads com campos divergentes');
        }
      },
      error: (error) => {
        console.error('Erro ao carregar leads divergentes:', error);
        this.error = 'Não foi possível carregar os leads divergentes.';
        this.isLoading = false;
      }
    });
  }

  openLeadDetail(lead: LeadForm): void {
    this.selectedLead = lead;
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedLead = null;
    // Recarregar os leads para atualizar a lista
    this.loadDivergentLeads();
  }

  updatePatientField(leadId: string, fieldName: string): void {
    this.leadService.updatePatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        this.selectedLead = { ...updatedLead }; // Criar nova referência para forçar detecção de mudanças
        this.cdr.detectChanges(); // Forçar detecção de mudanças

        // Se não houver mais campos para atualizar, recarregar a lista
        if (!updatedLead.hasUpdatesAvailable) {
          this.loadDivergentLeads();
        }
      },
      error: (error) => {
        console.error('Erro ao atualizar campo do paciente:', error);
        this.notificationService.error('Erro ao atualizar campo do paciente');
      },
    });
  }

  skipPatientField(leadId: string, fieldName: string): void {
    this.leadService.skipPatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        this.selectedLead = { ...updatedLead }; // Criar nova referência para forçar detecção de mudanças
        this.cdr.detectChanges(); // Forçar detecção de mudanças

        // Se não houver mais campos para atualizar, recarregar a lista
        if (!updatedLead.hasUpdatesAvailable) {
          this.loadDivergentLeads();
        }
      },
      error: (error) => {
        console.error('Erro ao ignorar campo do paciente:', error);
        this.notificationService.error('Erro ao ignorar campo do paciente');
      },
    });
  }
}
