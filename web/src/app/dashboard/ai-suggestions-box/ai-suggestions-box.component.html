<div class="pro-card h-full">
  <div class="flex justify-between items-center mb-4">
    <div class="flex items-center">
      <h2 class="text-xl font-semibold">Sugestões da IA Pendentes</h2>
      <span *ngIf="totalPendingSuggestions > 0" class="ml-2 px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        {{ totalPendingSuggestions }}
      </span>
    </div>
    <a [routerLink]="['/ai-suggestions']" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
      Ver todas
    </a>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-32">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="error && !isLoading" class="bg-red-50 text-red-700 p-4 rounded-md mb-4">
    <p>{{ error }}</p>
    <button
      (click)="loadPendingSuggestions()"
      class="mt-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
    >
      Tentar novamente
    </button>
  </div>

  <!-- Lista de sugestões -->
  <div *ngIf="!isLoading && !error" class="space-y-3 flex-grow overflow-y-auto">
    <div *ngFor="let suggestion of pendingSuggestions"
         class="p-3 bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
         (click)="openSuggestionDetail(suggestion)">
      <div class="flex justify-between items-start">
        <div>
          <h4 class="font-medium text-gray-900">{{ suggestion.patientName }}</h4>
          <p class="text-sm text-gray-500">{{ suggestion.treatmentName }}</p>
        </div>
        <div>
          <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Pendente
          </span>
        </div>
      </div>
      <div class="flex justify-end mt-2 space-x-2">
        <button
          (click)="quickApproveSuggestion($event, suggestion.id)"
          class="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded-md text-xs transition-colors"
        >
          Aprovar
        </button>
        <button
          class="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-md text-xs transition-colors"
        >
          Reprovar
        </button>
      </div>
    </div>

    <!-- Mensagem quando não há sugestões -->
    <div *ngIf="pendingSuggestions.length === 0 && !isLoading && totalPendingSuggestions === 0" class="text-center py-8 text-gray-500">
      <p>Nenhuma sugestão pendente</p>
      <p class="text-sm mt-2">Todas as sugestões foram analisadas</p>
    </div>

    <!-- Espaço vazio para manter a altura quando há poucos itens -->
    <div *ngIf="pendingSuggestions.length > 0 && pendingSuggestions.length < 3" class="py-8"></div>
  </div>
</div>

<!-- Modal de Detalhes da Sugestão -->
<app-modal *ngIf="showModal" [title]="'Detalhes da Sugestão da IA'" [isOpen]="showModal" (close)="closeModal()" [showDefaultFooter]="false">
  <div *ngIf="selectedSuggestion" class="p-6 max-h-[70vh] max-w-[800px] overflow-y-auto">
    <!-- Cabeçalho com informações principais -->
    <div class="mb-6 pb-4 border-b border-gray-200">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center">
        <div>
          <div class="flex items-center gap-2">
            <h2 class="text-xl font-bold">{{ selectedSuggestion.patientName }}</h2>
            <a
              [href]="'/patients/' + selectedSuggestion.patientId"
              target="_blank"
              class="text-blue-600 hover:text-blue-800 transition-colors"
              title="Ver detalhes do paciente"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
            </a>
          </div>
          <p class="text-gray-600">Tratamento: {{ selectedSuggestion.treatmentName }}</p>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
            Em Aprovação/Reprovação
          </span>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Coluna da esquerda -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-3">Mensagem da IA</h3>
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
          <p class="text-gray-700">{{ selectedSuggestion.message }}</p>
        </div>
      </div>

      <!-- Coluna da direita -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-3">Ações</h3>
        <button
          class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md mb-3"
          (click)="approveSuggestion(selectedSuggestion.id)"
        >
          Aprovar Sugestão
        </button>

        <div class="mt-4">
          <label class="block text-gray-700 text-sm font-medium mb-2">
            Motivo da Recusa
          </label>
          <textarea
            [(ngModel)]="rejectionReason"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows="3"
            placeholder="Informe o motivo da recusa..."
          ></textarea>
          <button
            [disabled]="!rejectionReason.trim()"
            [ngClass]="{'opacity-50 cursor-not-allowed': !rejectionReason.trim()}"
            class="w-full mt-2 bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md"
            (click)="rejectSuggestion(selectedSuggestion.id)"
          >
            Recusar Sugestão
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer com botões de ação -->
  <div footer class="flex justify-end space-x-3">
    <button
      class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
      (click)="closeModal()"
    >
      Fechar
    </button>
  </div>
</app-modal>
