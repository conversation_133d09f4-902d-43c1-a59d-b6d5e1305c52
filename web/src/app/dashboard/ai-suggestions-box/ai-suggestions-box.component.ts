import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AiSuggestionService } from '../../ai-suggestions/services/ai-suggestion.service';
import { AiSuggestion, AiSuggestionStatus } from '../../ai-suggestions/models/ai-suggestion.model';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-ai-suggestions-box',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule, ModalComponent],
  templateUrl: './ai-suggestions-box.component.html',
  styleUrls: ['./ai-suggestions-box.component.scss']
})
export class AiSuggestionsBoxComponent implements OnInit {
  pendingSuggestions: AiSuggestion[] = [];
  isLoading = true;
  error = '';
  totalPendingSuggestions = 0;

  // Modal properties
  showModal = false;
  selectedSuggestion: AiSuggestion | null = null;
  rejectionReason = '';

  // Enum para uso no template
  AiSuggestionStatus = AiSuggestionStatus;

  constructor(
    private aiSuggestionService: AiSuggestionService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadPendingSuggestions();
  }

  loadPendingSuggestions(): void {
    this.isLoading = true;
    this.aiSuggestionService.getAllSuggestions().subscribe({
      next: (suggestions) => {
        // Filtrar sugestões que estão em aprovação/reprovação
        const filteredSuggestions = suggestions.filter(suggestion =>
          suggestion.status === AiSuggestionStatus.IN_APPROVAL
        );

        this.totalPendingSuggestions = filteredSuggestions.length;

        // Limitar a 5 sugestões
        this.pendingSuggestions = filteredSuggestions.slice(0, 5);

        this.isLoading = false;

        if (this.totalPendingSuggestions === 0) {
          console.log('Não há sugestões pendentes de aprovação');
        }
      },
      error: (error) => {
        console.error('Erro ao carregar sugestões pendentes:', error);
        this.error = 'Não foi possível carregar as sugestões pendentes.';
        this.isLoading = false;
      }
    });
  }

  openSuggestionDetail(suggestion: AiSuggestion): void {
    this.selectedSuggestion = suggestion;
    this.rejectionReason = '';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedSuggestion = null;
    this.rejectionReason = '';
    // Recarregar as sugestões para atualizar a lista
    this.loadPendingSuggestions();
  }

  approveSuggestion(suggestionId: string): void {
    this.aiSuggestionService.updateSuggestionStatus(
      suggestionId,
      AiSuggestionStatus.ACCEPTED
    ).subscribe({
      next: () => {
        this.notificationService.success('Sugestão aprovada com sucesso!');
        this.closeModal();
      },
      error: (error) => {
        console.error('Erro ao aprovar sugestão:', error);
        this.notificationService.error('Erro ao aprovar sugestão');
      }
    });
  }

  rejectSuggestion(suggestionId: string): void {
    if (!this.rejectionReason.trim()) {
      this.notificationService.error('Informe o motivo da recusa');
      return;
    }

    this.aiSuggestionService.updateSuggestionStatus(
      suggestionId,
      AiSuggestionStatus.REJECTED,
      { rejectionReason: this.rejectionReason }
    ).subscribe({
      next: () => {
        this.notificationService.success('Sugestão recusada com sucesso!');
        this.closeModal();
      },
      error: (error) => {
        console.error('Erro ao recusar sugestão:', error);
        this.notificationService.error('Erro ao recusar sugestão');
      }
    });
  }

  // Aprovar diretamente do card (sem abrir o modal)
  quickApproveSuggestion(event: Event, suggestionId: string): void {
    event.stopPropagation(); // Evitar que o card seja clicado

    this.aiSuggestionService.updateSuggestionStatus(
      suggestionId,
      AiSuggestionStatus.ACCEPTED
    ).subscribe({
      next: () => {
        this.notificationService.success('Sugestão aprovada com sucesso!');
        this.loadPendingSuggestions();
      },
      error: (error) => {
        console.error('Erro ao aprovar sugestão:', error);
        this.notificationService.error('Erro ao aprovar sugestão');
      }
    });
  }

  // Formatar data para exibição
  formatDate(date: Date | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('pt-BR');
  }
}
