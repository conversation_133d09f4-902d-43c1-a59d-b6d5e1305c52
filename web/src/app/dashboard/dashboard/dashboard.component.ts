import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { PatientService } from '../../core/services/patient.service';
import { SchedulingService } from '../../core/services/scheduling.service';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { NotificationService } from '../../core/services/notification.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { DentistService } from '../../core/services/dentist.service';

// Interface para o modelo Treatment
interface Treatment {
  id: number;
  name: string;
  patientId: number;
  patientName?: string;
  dentistId: number;
  dentistName?: string;
  status: string;
  createdAt?: Date;
  cost?: number;
  paid?: boolean;
  notes?: string;
  [key: string]: any; // Para propriedades adicionais
}

interface TreatmentCard {
  id: number;
  name: string;
  patientId: number;
  patientName: string;
  patientCategory: string; // Alterado para aceitar qualquer string
  dentistId: number;
  dentistName: string;
  treatmentDate: Date;
  returnDate?: Date;
  status:
    | 'confirmed'
    | 'unconfirmed'
    | 'late'
    | 'no-show'
    | 'cancelled'
    | 'rescheduled'
    | 'in-progress'
    | 'completed'
    | 'scheduled-unconfirmed'
    | 'scheduled-confirmed'
    | 'unscheduled'
    | 'scheduled';
  priority: 'low' | 'medium' | 'high';
  lastUpdate: Date;
  createdAt?: Date; // Adicionado para compatibilidade
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
  standalone: true,
  imports: [CommonModule, RouterLink, DatePipe, DragDropModule, ModalComponent],
})
export class DashboardComponent implements OnInit {
  scheduledUnconfirmedTreatments: TreatmentCard[] = []; // Agendado não confirmado (amarelo)
  unscheduledTreatments: TreatmentCard[] = []; // Desmarcado (roxo)
  scheduledConfirmedTreatments: TreatmentCard[] = []; // Agendado confirmado (rosa)
  inProgressTreatments: TreatmentCard[] = []; // Em andamento (laranja)
  completedTreatments: TreatmentCard[] = []; // Concluído (verde)
  cancelledTreatments: TreatmentCard[] = []; // Cancelado (vermelho)

  isLoading = true;
  error = '';

  // Modal properties
  modalTitle: string = '';
  modalContent: any = null;
  isModalOpen: boolean = false;
  selectedTreatment: any = null;

  // Propriedade para controlar o arraste
  isDragging: boolean = false;

  constructor(
    private patientService: PatientService,
    private schedulingService: SchedulingService,
    private treatmentPlanService: TreatmentPlanService,
    private notificationService: NotificationService,
    private dentistService: DentistService
  ) {}

  ngOnInit(): void {
    this.loadTreatments();
  }

  loadTreatments(): void {
    this.isLoading = true;
    this.scheduledUnconfirmedTreatments = [];
    this.unscheduledTreatments = [];
    this.scheduledConfirmedTreatments = [];
    this.inProgressTreatments = [];
    this.completedTreatments = [];
    this.cancelledTreatments = [];

    // Carregar todos os planos de tratamento com procedimentos
    this.treatmentPlanService.getAllTreatmentPlans().subscribe({
      next: (plans: any[]) => {
        // Extrair procedimentos de todos os planos
        const procedures = plans.flatMap(plan =>
          (plan.procedures || []).map((proc: any) => ({
            id: proc.id,
            name: proc.name,
            patientId: plan.patientId,
            patientName: plan.patient?.name,
            dentistId: proc.professionalId,
            dentistName: proc.professional?.name,
            status: proc.status,
            createdAt: proc.createdAt,
            cost: proc.value,
            paid: proc.status === 'completed',
            notes: proc.notes
          }))
        );

        console.log(
          'Procedimentos carregados para o dashboard:',
          procedures.length
        );

        // Processar cada procedimento como se fosse um tratamento
        const processedTreatments = procedures.map((treatment: Treatment) => {
          // Determinar prioridade (não mais baseada na data do tratamento, pois o campo foi removido)
          let priority: 'low' | 'medium' | 'high' = 'low';

          // Usar a categoria do paciente para determinar a prioridade
          if (treatment.status === 'in-progress') {
            priority = 'high';
          } else if (treatment.status === 'scheduled-confirmed') {
            priority = 'medium';
          }

          // Criar o card de tratamento com um valor padrão para a categoria do paciente
          return {
            treatment,
            treatmentCard: {
              id: treatment.id,
              name: treatment.name,
              patientId: treatment.patientId,
              patientName: treatment.patientName || 'Paciente não identificado',
              patientCategory: 'Rotina', // Valor padrão que será substituído depois
              dentistId: treatment.dentistId,
              dentistName: treatment.dentistName || 'Dentista não identificado',
              treatmentDate: treatment.createdAt
                ? new Date(treatment.createdAt)
                : new Date(), // Usando createdAt em vez de treatmentDate
              status: treatment.status as
                | 'confirmed'
                | 'unconfirmed'
                | 'late'
                | 'no-show'
                | 'cancelled'
                | 'rescheduled'
                | 'in-progress'
                | 'completed'
                | 'scheduled-unconfirmed'
                | 'scheduled-confirmed'
                | 'unscheduled'
                | 'scheduled',
              priority: priority,
              lastUpdate: treatment.createdAt
                ? new Date(treatment.createdAt)
                : new Date(),
            },
          };
        });

        // Buscar as categorias dos pacientes
        const patientIds = [
          ...new Set(
            processedTreatments.map((item) => item.treatment.patientId)
          ),
        ];

        // Criar um mapa para armazenar as categorias dos pacientes
        const patientCategories = new Map<number, string>();

        // Buscar as informações de cada paciente
        const patientPromises = patientIds.map((patientId) => {
          return new Promise<void>((resolve) => {
            this.patientService.getPatient(patientId).subscribe({
              next: (patient) => {
                if (patient && patient.category) {
                  patientCategories.set(patient.id, patient.category);
                }
                resolve();
              },
              error: (error) => {
                console.error(
                  `Erro ao carregar categoria do paciente ${patientId}:`,
                  error
                );
                resolve();
              },
            });
          });
        });

        // Quando todas as promessas forem resolvidas
        Promise.all(patientPromises)
          .then(() => {
            // Adicionar a categoria do paciente a cada card de tratamento
            processedTreatments.forEach((item) => {
              const treatmentCard = item.treatmentCard;
              treatmentCard.patientCategory =
                patientCategories.get(item.treatment.patientId) || 'Rotina';

              // Adicionar o tratamento à categoria correta
              this.addTreatmentToCorrectList(treatmentCard);
            });

            // Ordenar os tratamentos por prioridade e data
            this.sortTreatments();
            this.isLoading = false;
          })
          .catch((error) => {
            console.error('Erro ao carregar categorias dos pacientes:', error);
            this.isLoading = false;
          });
      },
      error: (error: any) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.error =
          'Erro ao carregar procedimentos. Tente novamente mais tarde.';
        this.isLoading = false;
      },
    });
  }

  // Método auxiliar para adicionar o tratamento à lista correta
  addTreatmentToCorrectList(treatmentCard: TreatmentCard): void {
    // Adicionar o tratamento à categoria correta
    if (treatmentCard.status === 'scheduled-unconfirmed') {
      this.scheduledUnconfirmedTreatments.push(treatmentCard);
    } else if (treatmentCard.status === 'unscheduled') {
      this.unscheduledTreatments.push(treatmentCard);
    } else if (treatmentCard.status === 'scheduled-confirmed') {
      this.scheduledConfirmedTreatments.push(treatmentCard);
    } else if (treatmentCard.status === 'in-progress') {
      this.inProgressTreatments.push(treatmentCard);
    } else if (treatmentCard.status === 'completed') {
      this.completedTreatments.push(treatmentCard);
    } else if (treatmentCard.status === 'cancelled') {
      this.cancelledTreatments.push(treatmentCard);
    } else {
      // Para compatibilidade com registros antigos
      const statusStr = treatmentCard.status as string;
      if (statusStr === 'scheduled' || statusStr === 'scheduled-unconfirmed') {
        treatmentCard.status = 'unconfirmed' as any;
        this.scheduledUnconfirmedTreatments.push(treatmentCard);
      } else if (statusStr === 'confirmed' || statusStr === 'scheduled-confirmed') {
        treatmentCard.status = 'confirmed' as any;
        this.scheduledConfirmedTreatments.push(treatmentCard);
      }
    }
  }

  sortTreatments(): void {
    // Função para ordenar tratamentos por prioridade (alta -> média -> baixa) e depois por data
    const sortByPriorityAndDate = (a: TreatmentCard, b: TreatmentCard) => {
      // Primeiro por prioridade
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      const priorityDiff =
        priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Depois por data de tratamento
      return a.treatmentDate.getTime() - b.treatmentDate.getTime();
    };

    this.scheduledUnconfirmedTreatments.sort(sortByPriorityAndDate);
    this.unscheduledTreatments.sort(sortByPriorityAndDate);
    this.scheduledConfirmedTreatments.sort(sortByPriorityAndDate);
    this.inProgressTreatments.sort(sortByPriorityAndDate);
    this.completedTreatments.sort(sortByPriorityAndDate);
    this.cancelledTreatments.sort(sortByPriorityAndDate);
  }

  getCategoryClass(category: string): string {
    switch (category) {
      case 'Urgente':
        return 'bg-red-100 text-red-800';
      case 'Follow-up':
        return 'bg-blue-100 text-blue-800';
      case 'Rotina':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  openTreatmentModal(treatmentId: number): void {
    // Se estiver arrastando, não abrir o modal
    if (this.isDragging) {
      // Resetar a flag após um curto período
      setTimeout(() => {
        this.isDragging = false;
      }, 300);
      return;
    }

    this.treatmentPlanService.getProcedure(treatmentId).subscribe({
      next: (procedure: any) => {
        // Converter o procedimento para o formato de tratamento
        const treatment: Treatment = {
          id: procedure.id,
          name: procedure.name,
          patientId: procedure.treatmentPlan?.patientId,
          patientName: procedure.treatmentPlan?.patient?.name,
          dentistId: procedure.professionalId,
          dentistName: procedure.professional?.name,
          status: procedure.status,
          createdAt: procedure.createdAt,
          cost: procedure.value,
          paid: procedure.status === 'completed',
          notes: procedure.notes
        };

        this.selectedTreatment = treatment;
        this.loadTreatmentDetails(treatment);
      },
      error: (error: any) => {
        console.error('Erro ao carregar procedimento:', error);
        this.notificationService.error(
          'Erro ao carregar detalhes do procedimento'
        );
      },
    });
  }

  loadTreatmentDetails(treatment: any): void {
    // Carregar dados do paciente
    this.patientService.getPatient(treatment.patientId).subscribe({
      next: (patient) => {
        // Carregar dados do dentista
        this.dentistService.getDentist(treatment.dentistId).subscribe({
          next: (dentist) => {
            // Carregar agendamentos relacionados ao tratamento
            this.schedulingService
              .getSchedulingsByTreatmentId(treatment.id)
              .subscribe({
                next: (schedulings) => {
                  this.modalTitle = `Tratamento: ${treatment.name}`;
                  this.modalContent = {
                    treatment: treatment,
                    patient: patient,
                    dentist: dentist,
                    schedulings: schedulings,
                    statusMap: {
                      'scheduled-unconfirmed': 'Agendado não confirmado',
                      'scheduled-confirmed': 'Agendado confirmado',
                      unscheduled: 'Desmarcado',
                      'in-progress': 'Em andamento',
                      completed: 'Concluído',
                      cancelled: 'Cancelado',
                      scheduled: 'Agendado não confirmado',
                      confirmed: 'Agendado confirmado',
                    },
                  };
                  this.isModalOpen = true;
                },
                error: (error) => {
                  console.error('Erro ao carregar agendamentos:', error);
                  // Continuar mesmo se não conseguir carregar os agendamentos
                  this.modalTitle = `Tratamento: ${treatment.name}`;
                  this.modalContent = {
                    treatment: treatment,
                    patient: patient,
                    dentist: dentist,
                    schedulings: [],
                    statusMap: {
                      'scheduled-unconfirmed': 'Agendado não confirmado',
                      'scheduled-confirmed': 'Agendado confirmado',
                      unscheduled: 'Desmarcado',
                      'in-progress': 'Em andamento',
                      completed: 'Concluído',
                      cancelled: 'Cancelado',
                      scheduled: 'Agendado não confirmado',
                      confirmed: 'Agendado confirmado',
                    },
                  };
                  this.isModalOpen = true;
                },
              });
          },
          error: (error) => {
            console.error('Erro ao carregar dentista:', error);
            this.notificationService.error(
              'Erro ao carregar detalhes do dentista'
            );
          },
        });
      },
      error: (error) => {
        console.error('Erro ao carregar paciente:', error);
        this.notificationService.error('Erro ao carregar detalhes do paciente');
      },
    });
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedTreatment = null;
    this.modalContent = null;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-pink-100 text-pink-800'; // Rosa para Agendado não confirmado
      case 'scheduled-confirmed':
        return 'bg-yellow-100 text-yellow-800'; // Amarelo para Agendado confirmado
      case 'unscheduled':
        return 'bg-purple-100 text-purple-800'; // Roxo para Desmarcado
      case 'in-progress':
        return 'bg-orange-100 text-orange-800'; // Laranja para Em andamento
      case 'completed':
        return 'bg-green-100 text-green-800'; // Verde para Concluído
      case 'cancelled':
        return 'bg-red-100 text-red-800'; // Vermelho para Cancelado
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  moveTreatment(
    treatment: TreatmentCard,
    newStatus:
      | 'confirmed'
      | 'unconfirmed'
      | 'late'
      | 'no-show'
      | 'cancelled'
      | 'rescheduled'
      | 'in-progress'
      | 'completed'
      | 'scheduled-unconfirmed'
      | 'scheduled-confirmed'
      | 'unscheduled'
  ): void {
    // Remover o tratamento da lista atual
    if (treatment.status === 'scheduled-unconfirmed') {
      this.scheduledUnconfirmedTreatments =
        this.scheduledUnconfirmedTreatments.filter(
          (t) => t.id !== treatment.id
        );
    } else if (treatment.status === 'unscheduled') {
      this.unscheduledTreatments = this.unscheduledTreatments.filter(
        (t) => t.id !== treatment.id
      );
    } else if (treatment.status === 'scheduled-confirmed') {
      this.scheduledConfirmedTreatments =
        this.scheduledConfirmedTreatments.filter((t) => t.id !== treatment.id);
    } else if (treatment.status === 'in-progress') {
      this.inProgressTreatments = this.inProgressTreatments.filter(
        (t) => t.id !== treatment.id
      );
    } else if (treatment.status === 'completed') {
      this.completedTreatments = this.completedTreatments.filter(
        (t) => t.id !== treatment.id
      );
    } else if (treatment.status === 'cancelled') {
      this.cancelledTreatments = this.cancelledTreatments.filter(
        (t) => t.id !== treatment.id
      );
    } else {
      // Para compatibilidade com registros antigos
      if (treatment.status === 'scheduled') {
        this.scheduledUnconfirmedTreatments =
          this.scheduledUnconfirmedTreatments.filter(
            (t) => t.id !== treatment.id
          );
      } else if (treatment.status === 'confirmed') {
        this.scheduledConfirmedTreatments =
          this.scheduledConfirmedTreatments.filter(
            (t) => t.id !== treatment.id
          );
      }
    }

    // Atualizar o status do tratamento
    const oldStatus = treatment.status;
    treatment.status = newStatus;
    treatment.lastUpdate = new Date();

    // Adicionar o tratamento à nova lista
    if (newStatus === 'scheduled-unconfirmed') {
      this.scheduledUnconfirmedTreatments.push(treatment);
    } else if (newStatus === 'unscheduled') {
      this.unscheduledTreatments.push(treatment);
    } else if (newStatus === 'scheduled-confirmed') {
      this.scheduledConfirmedTreatments.push(treatment);
    } else if (newStatus === 'in-progress') {
      this.inProgressTreatments.push(treatment);
    } else if (newStatus === 'completed') {
      this.completedTreatments.push(treatment);
    } else if (newStatus === 'cancelled') {
      this.cancelledTreatments.push(treatment);
    }

    // Atualizar o procedimento na API
    this.treatmentPlanService
      .updateProcedure(treatment.id, { status: newStatus })
      .subscribe(
        () => {
          // Notificar o usuário
          this.notificationService.success(
            `Procedimento ${treatment.name} movido de ${this.getStatusLabel(
              oldStatus
            )} para ${this.getStatusLabel(newStatus)}`
          );
        },
        (error: any) => {
          console.error('Erro ao atualizar status do procedimento:', error);
          this.notificationService.error(
            'Erro ao atualizar status do procedimento. Tente novamente mais tarde.'
          );
          // Reverter a mudança em caso de erro
          this.loadTreatments();
        }
      );
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'in-progress':
        return 'Em Atendimento';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'Agendado';
      case 'confirmed':
        return 'Confirmado';
      default:
        return status;
    }
  }

  // Método chamado quando o usuário começa a arrastar um item
  onDragStarted(): void {
    this.isDragging = true;
  }

  onDrop(event: CdkDragDrop<TreatmentCard[]>) {
    // Marcar que estamos em um processo de arraste
    this.isDragging = true;

    if (event.previousContainer === event.container) {
      // Reordenar na mesma lista
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      // Mover para outra lista
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // Atualizar o status do tratamento
      const treatment = event.container.data[event.currentIndex];
      // Definir o novo status com base no ID do container
      let newStatus:
        | 'scheduled-unconfirmed'
        | 'scheduled-confirmed'
        | 'unscheduled'
        | 'in-progress'
        | 'completed'
        | 'cancelled' = 'scheduled-unconfirmed'; // Valor padrão

      if (event.container.id === 'scheduledUnconfirmedTreatments') {
        newStatus = 'scheduled-unconfirmed';
      } else if (event.container.id === 'unscheduledTreatments') {
        newStatus = 'unscheduled';
      } else if (event.container.id === 'scheduledConfirmedTreatments') {
        newStatus = 'scheduled-confirmed';
      } else if (event.container.id === 'inProgressTreatments') {
        newStatus = 'in-progress';
      } else if (event.container.id === 'completedTreatments') {
        newStatus = 'completed';
      } else if (event.container.id === 'cancelledTreatments') {
        newStatus = 'cancelled';
      }

      const oldStatus = treatment.status;
      treatment.status = newStatus;
      treatment.lastUpdate = new Date();

      // Atualizar o procedimento na API
      this.treatmentPlanService
        .updateProcedure(treatment.id, { status: newStatus })
        .subscribe(
          () => {
            // Notificar o usuário
            this.notificationService.success(
              `Procedimento ${treatment.name} movido de ${this.getStatusLabel(
                oldStatus
              )} para ${this.getStatusLabel(newStatus)}`
            );
            // Resetar a flag de arraste após a operação ser concluída
            setTimeout(() => {
              this.isDragging = false;
            }, 300);
          },
          (error: any) => {
            console.error('Erro ao atualizar status do procedimento:', error);
            this.notificationService.error(
              'Erro ao atualizar status do procedimento. Tente novamente mais tarde.'
            );
            // Reverter a mudança em caso de erro
            this.loadTreatments();
            // Resetar a flag de arraste após a operação ser concluída
            setTimeout(() => {
              this.isDragging = false;
            }, 300);
          }
        );
    }
  }
}
