<div class="py-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Dashboard de Tratamentos</h1>
    <p class="text-gray-600">Visão geral dos tratamentos por status</p>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Mensagem de erro -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
  </div>

  <!-- Kanban Board -->
  <div class="overflow-x-auto">
    <div
      *ngIf="!isLoading && !error"
      class="grid grid-cols-6 gap-4"
      style="min-width: 1200px"
    >
      <!-- Coluna: Agendados não confirmados (rosa) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-pink-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Agendado nao confirmado
          </h2>
          <span
            class="bg-white text-pink-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ scheduledUnconfirmedTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="scheduledUnconfirmedTreatments"
          [cdkDropListData]="scheduledUnconfirmedTreatments"
          [cdkDropListConnectedTo]="[
            'unscheduledTreatments',
            'scheduledConfirmedTreatments',
            'inProgressTreatments',
            'completedTreatments',
            'cancelledTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of scheduledUnconfirmedTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-pink-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="scheduledUnconfirmedTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento agendado não confirmado</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Desmarcado (roxo) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-purple-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">Desmarcado</h2>
          <span
            class="bg-white text-purple-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ unscheduledTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="unscheduledTreatments"
          [cdkDropListData]="unscheduledTreatments"
          [cdkDropListConnectedTo]="[
            'scheduledUnconfirmedTreatments',
            'scheduledConfirmedTreatments',
            'inProgressTreatments',
            'completedTreatments',
            'cancelledTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of unscheduledTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-purple-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="unscheduledTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento desmarcado</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Agendado confirmado (amarelo) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-yellow-500 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Agendado confirmado
          </h2>
          <span
            class="bg-white text-yellow-500 rounded-full px-2 py-1 text-xs font-bold"
            >{{ scheduledConfirmedTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="scheduledConfirmedTreatments"
          [cdkDropListData]="scheduledConfirmedTreatments"
          [cdkDropListConnectedTo]="[
            'scheduledUnconfirmedTreatments',
            'unscheduledTreatments',
            'inProgressTreatments',
            'completedTreatments',
            'cancelledTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of scheduledConfirmedTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-yellow-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="scheduledConfirmedTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento agendado confirmado</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Em Andamento (laranja) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-orange-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">Em Andamento</h2>
          <span
            class="bg-white text-orange-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ inProgressTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="inProgressTreatments"
          [cdkDropListData]="inProgressTreatments"
          [cdkDropListConnectedTo]="[
            'scheduledUnconfirmedTreatments',
            'unscheduledTreatments',
            'scheduledConfirmedTreatments',
            'completedTreatments',
            'cancelledTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of inProgressTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-orange-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="inProgressTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento em andamento</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Concluídos (verde) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-green-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">Concluídos</h2>
          <span
            class="bg-white text-green-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ completedTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="completedTreatments"
          [cdkDropListData]="completedTreatments"
          [cdkDropListConnectedTo]="[
            'scheduledUnconfirmedTreatments',
            'unscheduledTreatments',
            'scheduledConfirmedTreatments',
            'inProgressTreatments',
            'cancelledTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of completedTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-green-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="completedTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento concluído</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Cancelado (vermelho) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-red-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">Cancelado</h2>
          <span
            class="bg-white text-red-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ cancelledTreatments.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="cancelledTreatments"
          [cdkDropListData]="cancelledTreatments"
          [cdkDropListConnectedTo]="[
            'scheduledUnconfirmedTreatments',
            'unscheduledTreatments',
            'scheduledConfirmedTreatments',
            'inProgressTreatments',
            'completedTreatments'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-250px)] overflow-y-auto space-y-3"
        >
          <!-- Cartões de Tratamentos -->
          <div
            *ngFor="let treatment of cancelledTreatments"
            cdkDrag
            (cdkDragStarted)="onDragStarted()"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-red-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openTreatmentModal(treatment.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ treatment.name }}</h3>
              <span
                [class]="
                  'text-xs px-2 py-1 rounded-full ' +
                  getCategoryClass(treatment.patientCategory || 'Rotina')
                "
              >
                {{ treatment.patientCategory || "Rotina" }}
              </span>
            </div>

            <div class="text-sm text-gray-600 mb-2">
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  ></path>
                </svg>
                <span>{{ treatment.patientName }}</span>
              </div>
              <div class="flex items-center mb-1">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.createdAt | date : "dd/MM/yyyy" }}</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{{ treatment.dentistName }}</span>
              </div>
            </div>

            <div
              class="flex justify-between items-center text-xs text-gray-500"
            >
              <span
                >Atualizado:
                {{ treatment.lastUpdate | date : "dd/MM/yyyy" }}</span
              >

              <div class="flex space-x-1">
                <button
                  (click)="openTreatmentModal(treatment.id)"
                  class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-100"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há tratamentos -->
          <div
            *ngIf="cancelledTreatments.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum tratamento cancelado</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Botão para adicionar novo tratamento -->
  <div class="fixed bottom-6 right-6">
    <a
      routerLink="/treatments/new"
      class="bg-blue-600 text-white rounded-full p-3 shadow-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
        />
      </svg>
    </a>
  </div>
</div>

<!-- Modal de Detalhes do Tratamento -->
<app-modal [title]="modalTitle" [isOpen]="isModalOpen" (close)="closeModal()">
  <div *ngIf="modalContent" class="max-h-[70vh] overflow-y-auto overflow-x-hidden scrollbar-thin">
    <div class="p-4 space-y-6 max-w-4xl mx-auto">
      <!-- Informações do Tratamento -->
      <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">
          Informações do Tratamento
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="flex flex-wrap items-center">
            <span class="font-medium mr-2">Status:</span>
            <span
              class="px-2 py-1 rounded-full text-xs inline-flex items-center"
              [ngClass]="{
                'bg-pink-100 text-pink-800':
                  modalContent.treatment.status === 'scheduled-unconfirmed',
                'bg-yellow-100 text-yellow-800':
                  modalContent.treatment.status === 'scheduled-confirmed',
                'bg-purple-100 text-purple-800':
                  modalContent.treatment.status === 'unscheduled',
                'bg-blue-100 text-blue-800':
                  modalContent.treatment.status === 'in-progress',
                'bg-green-100 text-green-800':
                  modalContent.treatment.status === 'completed',
                'bg-red-100 text-red-800':
                  modalContent.treatment.status === 'cancelled'
              }"
            >
              {{ modalContent.statusMap[modalContent.treatment.status] }}
            </span>
          </div>
          <div class="flex flex-wrap items-center">
            <span class="font-medium mr-2">Data de Criação:</span>
            <span>{{ modalContent.treatment.createdAt | date : "dd/MM/yyyy" }}</span>
          </div>
          <div *ngIf="modalContent.treatment.cost" class="flex flex-wrap items-center">
            <span class="font-medium mr-2">Valor:</span>
            <span>{{ modalContent.treatment.cost | currency : "BRL" }}</span>
          </div>
          <div *ngIf="modalContent.treatment.paid !== undefined" class="flex flex-wrap items-center">
            <span class="font-medium mr-2">Pago:</span>
            <span>{{ modalContent.treatment.paid ? "Sim" : "Não" }}</span>
          </div>
        </div>
      </div>

      <!-- Informações do Paciente -->
      <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">Paciente</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Nome:</span>
            <span class="text-gray-900 break-words">{{ modalContent.patient.name }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">CPF:</span>
            <span class="text-gray-900">{{ modalContent.patient.cpf }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Telefone:</span>
            <span class="text-gray-900">{{ modalContent.patient.phone }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Email:</span>
            <span class="text-gray-900 break-words">{{ modalContent.patient.email }}</span>
          </div>
        </div>
      </div>

      <!-- Informações do Dentista -->
      <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">Dentista</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Nome:</span>
            <span class="text-gray-900 break-words">{{ modalContent.dentist.name }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">CRO:</span>
            <span class="text-gray-900">{{ modalContent.dentist.cro }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Especialidade:</span>
            <span class="text-gray-900">{{ modalContent.dentist.specialty }}</span>
          </div>
        </div>
      </div>

      <!-- Observações -->
      <div *ngIf="modalContent.treatment.notes" class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">Observações</h3>
        <p class="text-gray-700 whitespace-pre-line break-words">{{ modalContent.treatment.notes }}</p>
      </div>

      <!-- Agendamentos Relacionados -->
      <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">
          Agendamentos Relacionados
        </h3>

        <!-- Mensagem quando não há agendamentos -->
        <div
          *ngIf="modalContent.schedulings.length === 0"
          class="text-gray-500 text-center py-4 bg-gray-100 rounded-md"
        >
          Nenhum agendamento relacionado a este tratamento.
        </div>

        <!-- Tabela de agendamentos -->
        <div
          *ngIf="modalContent.schedulings.length > 0"
          class="overflow-x-auto table-container rounded-md border border-gray-200"
        >
          <table
            class="w-full divide-y divide-gray-200"
          >
            <thead class="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
                >
                  Data
                </th>
                <th
                  scope="col"
                  class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
                >
                  Hora
                </th>
                <th
                  scope="col"
                  class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
                >
                  Dentista
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                *ngFor="let scheduling of modalContent.schedulings"
                class="hover:bg-gray-50 transition-colors duration-150"
              >
                <td
                  class="px-4 sm:px-6 py-3 text-sm font-medium text-gray-900"
                >
                  {{ scheduling.date | date : "dd/MM/yyyy" }}
                </td>
                <td class="px-4 sm:px-6 py-3 text-sm text-gray-700">
                  {{ scheduling.time }}
                </td>
                <td class="px-4 sm:px-6 py-3 text-sm text-gray-700 break-words">
                  {{ scheduling.dentistName }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Campos adicionais -->
      <div
        *ngIf="
          modalContent.treatment.clinicalExamination ||
          modalContent.treatment.diagnosis ||
          modalContent.treatment.treatmentPlan ||
          modalContent.treatment.complementaryExams
        "
        class="bg-gray-50 p-4 rounded-lg shadow-sm"
      >
        <h3 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">
          Informações Clínicas
        </h3>

        <div *ngIf="modalContent.treatment.clinicalExamination" class="mb-4">
          <h4 class="font-medium text-gray-800 mb-2">Exame Clínico</h4>
          <p class="text-gray-700 bg-white p-3 rounded border border-gray-200 whitespace-pre-line break-words">
            {{ modalContent.treatment.clinicalExamination }}
          </p>
        </div>

        <div *ngIf="modalContent.treatment.diagnosis" class="mb-4">
          <h4 class="font-medium text-gray-800 mb-2">Diagnóstico</h4>
          <p class="text-gray-700 bg-white p-3 rounded border border-gray-200 whitespace-pre-line break-words">
            {{ modalContent.treatment.diagnosis }}
          </p>
        </div>

        <div *ngIf="modalContent.treatment.treatmentPlan" class="mb-4">
          <h4 class="font-medium text-gray-800 mb-2">Plano de Tratamento</h4>
          <p class="text-gray-700 bg-white p-3 rounded border border-gray-200 whitespace-pre-line break-words">
            {{ modalContent.treatment.treatmentPlan }}
          </p>
        </div>

        <div *ngIf="modalContent.treatment.complementaryExams" class="mb-4">
          <h4 class="font-medium text-gray-800 mb-2">Exames Complementares</h4>
          <p class="text-gray-700 bg-white p-3 rounded border border-gray-200 whitespace-pre-line break-words">
            {{ modalContent.treatment.complementaryExams }}
          </p>
        </div>
      </div>
    </div>
  </div>
</app-modal>
