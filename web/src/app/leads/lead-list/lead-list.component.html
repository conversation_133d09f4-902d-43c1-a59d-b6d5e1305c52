<div class="bg-white shadow rounded-lg p-6">
  <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-800"><PERSON><PERSON><PERSON> de Leads</h1>
      <p class="text-gray-600">
        Visualize e gerencie os leads capturados pelo formulário
      </p>
    </div>
    <div>
      <button
        (click)="openFormInNewTab()"
        class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
        Visualizar Formulário
      </button>
    </div>
  </div>

  <!-- Filtros -->
  <div class="bg-white rounded-lg mb-6">
    <div class="flex flex-col md:flex-row gap-4">
      <div class="flex-1">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            id="search"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Buscar por nome, email ou CPF"
            class="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div>
        <label for="patientFilter" class="block text-sm font-medium text-gray-700 mb-1">Paciente</label>
        <select
          id="patientFilter"
          [(ngModel)]="patientFilter"
          (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Todos</option>
          <option value="true">Paciente Existente</option>
          <option value="false">Novo Lead</option>
        </select>
      </div>

      <div>
        <label for="divergenceFilter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select
          id="divergenceFilter"
          [(ngModel)]="divergenceFilter"
          (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option [ngValue]="null">Todos</option>
          <option [ngValue]="true">Campos Divergentes</option>
          <option [ngValue]="false">Cadastro Atualizado</option>
        </select>
      </div>

      <div>
        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">Data de Envio</label>
        <input
          type="date"
          id="dateFilter"
          [(ngModel)]="dateFilter"
          (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>

    <div class="mt-4 flex justify-end">
      <button
        (click)="resetFilters()"
        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
      >
        Limpar Filtros
      </button>
    </div>
  </div>

  <!-- Lista de Leads -->
  <div *ngIf="!error && (filteredLeads.length > 0 || isLoading)" class="bg-white rounded-lg shadow-sm overflow-hidden mb-6 relative">
    <!-- Overlay de carregamento moderno -->
    <div
      *ngIf="isLoading"
      class="absolute inset-0 bg-white bg-opacity-70 backdrop-blur-[1px] z-10 flex justify-center items-center transition-all duration-300 ease-in-out"
    >
      <div class="bg-white/90 p-4 rounded-xl shadow-lg flex items-center space-x-3 border border-gray-100 animate-fadeIn">
        <div class="relative">
          <div class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
          <div class="absolute inset-0 rounded-full border-2 border-blue-100 animate-pulse"></div>
        </div>
        <span class="text-gray-700 font-medium">Carregando leads...</span>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nome
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CPF
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Data de Envio
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Paciente
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ações
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let lead of filteredLeads" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 font-medium text-lg">{{ lead.fullName.charAt(0) }}</span>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">{{ lead.fullName }}</div>
                  <div class="text-sm text-gray-500">{{ lead.phone }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ lead.cpf }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ formatDate(lead.createdAt) }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="lead.isExistingPatient ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'">
                {{ lead.isExistingPatient ? 'Paciente' : 'Novo Lead' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="getStatusClass(lead)">
                {{ getStatusLabel(lead) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  (click)="openLeadDetail(lead)"
                  class="text-blue-600 hover:text-blue-900"
                  title="Visualizar"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <a
                  *ngIf="lead.isExistingPatient && lead.patientId"
                  [routerLink]="['/patients', lead.patientId]"
                  class="text-indigo-600 hover:text-indigo-900"
                  title="Ver Paciente"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="error" class="bg-white rounded-lg shadow-sm p-6 text-center mb-6">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">Erro ao carregar leads</h3>
    <p class="text-gray-600 mb-4">{{ error }}</p>
    <button
      (click)="loadLeads()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Tentar Novamente
    </button>
  </div>

  <!-- Mensagem de nenhum lead encontrado -->
  <div *ngIf="!isLoading && !error && filteredLeads.length === 0" class="bg-white rounded-lg shadow-sm p-6 text-center mb-6">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum lead encontrado</h3>
    <p class="text-gray-600 mb-4">Tente ajustar os filtros para encontrar o que está procurando.</p>
    <button
      (click)="resetFilters()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Limpar Filtros
    </button>
  </div>

  <!-- Paginação Moderna -->
  <div
    *ngIf="!isLoading && !error && filteredLeads.length > 0"
    class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner mt-4"
  >
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
    >
      <!-- Informações de paginação e seletor de itens por página -->
      <div class="flex flex-wrap items-center text-sm text-gray-600">
        <div
          class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md shadow-sm"
        >
          <span>Mostrando</span>
          <span class="font-medium mx-1 text-blue-600">{{
            (currentPage - 1) * itemsPerPage + 1
          }}</span>
          <span>-</span>
          <span class="font-medium mx-1 text-blue-600">{{
            currentPage * itemsPerPage > totalItems
              ? totalItems
              : currentPage * itemsPerPage
          }}</span>
          <span>de</span>
          <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
        </div>

        <!-- Seletor de itens por página -->
        <div class="ml-3 flex items-center">
          <label for="itemsPerPage" class="mr-2 text-sm text-gray-500"
            >Itens por página:</label
          >
          <div class="relative">
            <select
              id="itemsPerPage"
              [(ngModel)]="itemsPerPage"
              (change)="changeItemsPerPage()"
              class="appearance-none bg-white border border-gray-200 rounded-md pl-3 pr-8 py-1.5 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            >
              <option [value]="6">6</option>
              <option [value]="12">12</option>
              <option [value]="24">24</option>
              <option [value]="48">48</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Controles de paginação -->
      <div class="flex items-center pagination-controls">
        <div class="flex rounded-lg shadow-sm overflow-hidden">
          <!-- Primeira página -->
          <button
            (click)="firstPage()"
            [disabled]="currentPage === 1 || isLoading"
            class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            [ngClass]="
              currentPage === 1 || isLoading
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            "
            aria-label="Primeira página"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
              />
            </svg>
          </button>

          <!-- Página anterior -->
          <button
            (click)="previousPage()"
            [disabled]="currentPage === 1 || isLoading"
            class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            [ngClass]="
              currentPage === 1 || isLoading
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            "
            aria-label="Página anterior"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <!-- Números de página -->
          <ng-container *ngFor="let page of getPageNumbers()">
            <ng-container *ngIf="page !== '...'">
              <button
                (click)="goToPage(page)"
                [disabled]="isLoading"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                [ngClass]="
                  currentPage === page
                    ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                    : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                "
              >
                {{ page }}
              </button>
            </ng-container>
            <div
              *ngIf="page === '...'"
              class="relative inline-flex items-center justify-center h-9 px-2 text-sm text-gray-500 border-r border-gray-200 bg-white"
            >
              ...
            </div>
          </ng-container>

          <!-- Próxima página -->
          <button
            (click)="nextPage()"
            [disabled]="currentPage === totalPages || isLoading"
            class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            [ngClass]="
              currentPage === totalPages || isLoading
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            "
            aria-label="Próxima página"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- Última página -->
          <button
            (click)="lastPage()"
            [disabled]="currentPage === totalPages || isLoading"
            class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            [ngClass]="
              currentPage === totalPages || isLoading
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            "
            aria-label="Última página"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 5l7 7-7 7M5 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de Detalhes -->
<div *ngIf="showModal && selectedLead" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto">
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-bold">Detalhes do Lead</h2>
        <button (click)="closeModal()" class="text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <div class="p-6">
      <!-- Informações do Lead -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Dados Pessoais -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Dados Pessoais</h3>
          <div class="space-y-2">
            <div>
              <span class="text-sm text-gray-500">Nome Completo:</span>
              <p class="font-medium">{{ selectedLead.fullName }}</p>

              <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('name')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
                <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.name }}</p>
                <div class="flex space-x-2 mt-1">
                  <button
                    (click)="updatePatientField(selectedLead.id, 'name')"
                    class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                  >
                    Atualizar cadastro
                  </button>
                  <button
                    (click)="skipPatientField(selectedLead.id, 'name')"
                    class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                  >
                    Não atualizar
                  </button>
                </div>
              </div>
            </div>

            <div>
              <span class="text-sm text-gray-500">CPF:</span>
              <p class="font-medium">{{ selectedLead.cpf }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Telefone:</span>
              <p class="font-medium">{{ selectedLead.phone }}</p>

              <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('phone')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
                <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.phone }}</p>
                <div class="flex space-x-2 mt-1">
                  <button
                    (click)="updatePatientField(selectedLead.id, 'phone')"
                    class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                  >
                    Atualizar cadastro
                  </button>
                  <button
                    (click)="skipPatientField(selectedLead.id, 'phone')"
                    class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                  >
                    Não atualizar
                  </button>
                </div>
              </div>

              <!-- Verificação de WhatsApp -->
              <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('whatsapp')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">Divergente do WhatsApp do paciente</p>
                <p class="text-sm text-gray-600">WhatsApp no cadastro: {{ selectedLead.patient?.whatsapp || 'Não informado' }}</p>
                <div class="flex space-x-2 mt-1">
                  <button
                    (click)="updatePatientField(selectedLead.id, 'whatsapp')"
                    class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                  >
                    Atualizar WhatsApp
                  </button>
                  <button
                    (click)="skipPatientField(selectedLead.id, 'whatsapp')"
                    class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                  >
                    Não atualizar
                  </button>
                </div>
              </div>
            </div>

            <div>
              <span class="text-sm text-gray-500">E-mail:</span>
              <p class="font-medium">{{ selectedLead.email }}</p>

              <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('email')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
                <p class="text-sm text-gray-600">Valor no cadastro: {{ selectedLead.patient?.email }}</p>
                <div class="flex space-x-2 mt-1">
                  <button
                    (click)="updatePatientField(selectedLead.id, 'email')"
                    class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                  >
                    Atualizar cadastro
                  </button>
                  <button
                    (click)="skipPatientField(selectedLead.id, 'email')"
                    class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                  >
                    Não atualizar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Endereço -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Endereço</h3>
          <div class="space-y-2">
            <div>
              <span class="text-sm text-gray-500">CEP:</span>
              <p class="font-medium">{{ selectedLead.cep }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Rua:</span>
              <p class="font-medium">{{ selectedLead.street }}, {{ selectedLead.number }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Bairro:</span>
              <p class="font-medium">{{ selectedLead.neighborhood }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Cidade/Estado:</span>
              <p class="font-medium">{{ selectedLead.city }}/{{ selectedLead.state }}</p>
            </div>

            <div *ngIf="selectedLead.isExistingPatient && selectedLead.hasUpdatesAvailable && selectedLead.fieldsToUpdate?.includes('address')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Endereço divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Endereço no cadastro: {{ selectedLead.patient?.addressStreet }}, {{ selectedLead.patient?.addressNumber }} - {{ selectedLead.patient?.addressNeighborhood }}, {{ selectedLead.patient?.addressCity }}/{{ selectedLead.patient?.addressState }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(selectedLead.id, 'address')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(selectedLead.id, 'address')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Histórico Odontológico -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Histórico Odontológico</h3>
          <div class="space-y-2">
            <div>
              <span class="text-sm text-gray-500">Tratamentos Anteriores:</span>
              <p class="font-medium">{{ selectedLead.pastTreatments || '' }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Último Procedimento:</span>
              <p class="font-medium">
                {{ selectedLead.lastProcedureTime === '<6meses' ? 'Menos de 6 meses' :
                   selectedLead.lastProcedureTime === '1ano' ? 'Aproximadamente 1 ano' :
                   'Mais de 2 anos' }}
              </p>
            </div>


          </div>
        </div>

        <!-- Interesse Atual -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Interesse Atual</h3>
          <div class="space-y-2">
            <div>
              <span class="text-sm text-gray-500">Tratamentos de Interesse:</span>
              <p class="font-medium">{{ selectedLead.interestedTreatment.join(', ') || 'Nenhum' }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Deseja Avaliação Gratuita:</span>
              <p class="font-medium">{{ selectedLead.wantsFreeEvaluation ? 'Sim' : 'Não' }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Deseja Receber Promoções:</span>
              <p class="font-medium">{{ selectedLead.wantsPromotions ? 'Sim' : 'Não' }}</p>
            </div>
          </div>
        </div>

        <!-- Personalização -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Personalização</h3>
          <div class="space-y-2">


            <div>
              <span class="text-sm text-gray-500">Melhor Horário para Contato:</span>
              <p class="font-medium">{{ selectedLead.bestContactTime }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Como nos Conheceu:</span>
              <p class="font-medium">{{ selectedLead.referralSource }}</p>
            </div>
          </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-lg font-semibold mb-3">Informações do Sistema</h3>
          <div class="space-y-2">
            <div>
              <span class="text-sm text-gray-500">Data de Criação:</span>
              <p class="font-medium">{{ formatDate(selectedLead.createdAt) }}</p>
            </div>

            <div>
              <span class="text-sm text-gray-500">Paciente Existente:</span>
              <p class="font-medium">{{ selectedLead.isExistingPatient ? 'Sim' : 'Não' }}</p>
            </div>

            <div *ngIf="selectedLead.isExistingPatient && selectedLead.patient">
              <span class="text-sm text-gray-500">ID do Paciente:</span>
              <p class="font-medium">{{ selectedLead.patientId }}</p>
              <a
                [routerLink]="['/patients', selectedLead.patientId]"
                class="text-blue-600 hover:underline text-sm"
                (click)="closeModal()"
              >
                Ver perfil do paciente
              </a>
            </div>

            <div *ngIf="selectedLead.isExistingPatient">
              <span class="text-sm text-gray-500">Status:</span>
              <p class="font-medium">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="selectedLead.hasUpdatesAvailable ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'">
                  {{ selectedLead.hasUpdatesAvailable ? 'Campos Divergentes' : 'Cadastro Atualizado' }}
                </span>
              </p>
              <div *ngIf="!selectedLead.hasUpdatesAvailable" class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                <p class="text-sm text-green-800">Todos os dados estão atualizados.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6 border-t border-gray-200 flex justify-end">
      <button
        (click)="closeModal()"
        class="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
      >
        Fechar
      </button>
    </div>
  </div>
</div>
