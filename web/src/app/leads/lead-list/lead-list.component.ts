import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { LeadService, LeadPaginationParams } from '../../core/services/lead.service';
import { LeadForm } from '../../core/models/lead-form.model';

@Component({
  selector: 'app-lead-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './lead-list.component.html',
  styleUrls: ['./lead-list.component.scss'],
})
export class LeadListComponent implements OnInit {
  leads: LeadForm[] = [];
  filteredLeads: LeadForm[] = [];
  isLoading = true;
  error: string | null = null;

  // Filtros
  dateFilter: string = '';
  patientFilter: string = '';
  divergenceFilter: boolean | null = null;
  searchTerm: string = '';

  // Modal
  selectedLead: LeadForm | null = null;
  showModal = false;

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;

  // Utilitários
  Math = Math;

  constructor(
    private leadService: LeadService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadLeads();
  }

  loadLeads(): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.error = null;

    // Preparar os parâmetros de paginação
    const params: LeadPaginationParams = {
      page: this.currentPage,
      limit: this.itemsPerPage,
      search: this.searchTerm || undefined
    };

    // Adicionar filtros se estiverem definidos
    if (this.patientFilter) {
      params.isExistingPatient = this.patientFilter === 'true';
    }

    if (this.divergenceFilter !== null) {
      params.hasUpdatesAvailable = this.divergenceFilter;
    }

    if (this.dateFilter) {
      params.createdAt = this.dateFilter;
    }

    this.leadService.getLeadsPaginated(params)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response) => {
          this.filteredLeads = response.data;
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.totalPages = response.totalPages;
        },
        error: (error) => {
          console.error('Erro ao carregar leads:', error);
          this.error = 'Não foi possível carregar os leads. Por favor, tente novamente mais tarde.';
        },
      });
  }

  applyFilters(): void {
    // Resetar para a primeira página
    this.currentPage = 1;

    // Recarregar os leads com os novos filtros
    this.loadLeads();
  }

  resetFilters(): void {
    this.dateFilter = '';
    this.patientFilter = '';
    this.divergenceFilter = null;
    this.searchTerm = '';
    this.applyFilters();
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadLeads();
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadLeads();
  }

  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  // Não precisamos mais deste getter, pois a paginação é feita no backend
  // get paginatedLeads(): LeadForm[] {
  //   const startIndex = (this.currentPage - 1) * this.itemsPerPage;
  //   return this.filteredLeads.slice(startIndex, startIndex + this.itemsPerPage);
  // }

  openLeadDetail(lead: LeadForm): void {
    this.selectedLead = lead;
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedLead = null;
  }

  updatePatientField(leadId: string, fieldName: string): void {
    this.leadService.updatePatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        // Atualizar o lead na lista
        const index = this.filteredLeads.findIndex((lead) => lead.id === updatedLead.id);
        if (index !== -1) {
          this.filteredLeads[index] = { ...updatedLead }; // Criar nova referência
          this.selectedLead = { ...updatedLead }; // Criar nova referência
        }

        this.cdr.detectChanges(); // Forçar detecção de mudanças
      },
      error: (error) => {
        console.error('Erro ao atualizar campo do paciente:', error);
      },
    });
  }

  skipPatientField(leadId: string, fieldName: string): void {
    this.leadService.skipPatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        // Atualizar o lead na lista
        const index = this.filteredLeads.findIndex((lead) => lead.id === updatedLead.id);
        if (index !== -1) {
          this.filteredLeads[index] = { ...updatedLead }; // Criar nova referência
          this.selectedLead = { ...updatedLead }; // Criar nova referência
        }

        this.cdr.detectChanges(); // Forçar detecção de mudanças
      },
      error: (error) => {
        console.error('Erro ao ignorar campo do paciente:', error);
      },
    });
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('pt-BR');
  }

  getStatusLabel(lead: LeadForm): string {
    if (!lead.isExistingPatient) {
      return 'Novo Lead';
    }

    if (lead.hasUpdatesAvailable) {
      return 'Campos Divergentes';
    }

    return 'Cadastro Atualizado';
  }

  getStatusClass(lead: LeadForm): string {
    if (!lead.isExistingPatient) {
      return 'bg-blue-100 text-blue-800';
    }

    if (lead.hasUpdatesAvailable) {
      return 'bg-yellow-100 text-yellow-800';
    }

    return 'bg-green-100 text-green-800';
  }

  /**
   * Abre o formulário público de leads em uma nova aba
   */
  openFormInNewTab(): void {
    // Obter a URL base da aplicação
    const baseUrl = window.location.origin;
    // Abrir o formulário em uma nova aba
    window.open(`${baseUrl}/formulario`, '_blank');
  }
}
