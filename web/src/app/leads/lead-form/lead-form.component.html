<div class="container mx-auto px-4 py-8 max-w-4xl">
  <div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h1 class="text-2xl font-bold text-center mb-6">Formulário de Interesse</h1>
    <p class="text-center text-gray-600 mb-8">Preencha o formulário abaixo para receber mais informações sobre nossos tratamentos odontológicos.</p>

    <!-- Modal de Sucesso -->
    <div *ngIf="showSuccessModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50" (click)="closeSuccessModal()"></div>
      <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full relative z-10">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <svg class="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-xl font-medium text-gray-900 mb-2">Formulário enviado com sucesso!</h3>
          <p class="text-gray-600 mb-6">Agradecemos pelo seu interesse. Entraremos em contato em breve.</p>
          <button
            type="button"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            (click)="closeSuccessModal()"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>

    <form [formGroup]="leadForm" (ngSubmit)="onSubmit()" class="space-y-8">
      <!-- Dados Pessoais -->
      <div class="bg-gray-50 p-4 rounded-md">
        <h2 class="text-xl font-semibold mb-4">Dados Pessoais</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="col-span-2 md:col-span-1">
            <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Nome Completo *</label>
            <input
              type="text"
              id="fullName"
              formControlName="fullName"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': fullNameControl?.invalid && fullNameControl?.touched}"
            >
            <div *ngIf="fullNameControl?.invalid && fullNameControl?.touched" class="text-red-500 text-sm mt-1">
              Nome completo é obrigatório
            </div>
          </div>

          <div>
            <label for="cpf" class="block text-sm font-medium text-gray-700 mb-1">CPF *</label>
            <input
              type="text"
              id="cpf"
              formControlName="cpf"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': cpfControl?.invalid && cpfControl?.touched}"
              placeholder="000.000.000-00"
              (input)="onCpfInput($event)"
              maxlength="14"
            >
            <div *ngIf="cpfControl?.invalid && cpfControl?.touched" class="text-red-500 text-sm mt-1">
              CPF é obrigatório
            </div>
          </div>

          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Telefone *</label>
            <input
              type="text"
              id="phone"
              formControlName="phone"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': phoneControl?.invalid && phoneControl?.touched}"
              placeholder="(00) 00000-0000"
              (input)="onPhoneInput($event)"
              maxlength="15"
            >
            <div *ngIf="phoneControl?.invalid && phoneControl?.touched" class="text-red-500 text-sm mt-1">
              Telefone é obrigatório
            </div>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">E-mail *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': emailControl?.invalid && emailControl?.touched}"
            >
            <div *ngIf="emailControl?.invalid && emailControl?.touched" class="text-red-500 text-sm mt-1">
              E-mail válido é obrigatório
            </div>
          </div>
        </div>
      </div>

      <!-- Endereço -->
      <div class="bg-gray-50 p-4 rounded-md">
        <h2 class="text-xl font-semibold mb-4">Endereço</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="cep" class="block text-sm font-medium text-gray-700 mb-1">CEP *</label>
            <div class="relative">
              <input
                type="text"
                id="cep"
                formControlName="cep"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': cepControl?.invalid && cepControl?.touched}"
                placeholder="00000-000"
                (input)="onCepInput($event)"
                maxlength="9"
              >
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            <div *ngIf="cepControl?.invalid && cepControl?.touched" class="text-red-500 text-sm mt-1">
              CEP é obrigatório
            </div>
          </div>

          <div class="col-span-2 md:col-span-1">
            <label for="street" class="block text-sm font-medium text-gray-700 mb-1">Rua *</label>
            <div class="relative">
              <input
                type="text"
                id="street"
                formControlName="street"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': streetControl?.invalid && streetControl?.touched, 'bg-gray-100': isLoadingCep}"
                [disabled]="isLoadingCep"
              >
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div class="h-4 w-4 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div *ngIf="streetControl?.invalid && streetControl?.touched" class="text-red-500 text-sm mt-1">
              Rua é obrigatória
            </div>
          </div>

          <div>
            <label for="number" class="block text-sm font-medium text-gray-700 mb-1">Número *</label>
            <div class="relative">
              <input
                type="text"
                id="number"
                formControlName="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': numberControl?.invalid && numberControl?.touched, 'bg-gray-100': isLoadingCep}"
                [disabled]="isLoadingCep"
              >
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div class="h-4 w-4 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div *ngIf="numberControl?.invalid && numberControl?.touched" class="text-red-500 text-sm mt-1">
              Número é obrigatório
            </div>
          </div>

          <div>
            <label for="neighborhood" class="block text-sm font-medium text-gray-700 mb-1">Bairro *</label>
            <div class="relative">
              <input
                type="text"
                id="neighborhood"
                formControlName="neighborhood"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': neighborhoodControl?.invalid && neighborhoodControl?.touched, 'bg-gray-100': isLoadingCep}"
                [disabled]="isLoadingCep"
              >
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div class="h-4 w-4 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div *ngIf="neighborhoodControl?.invalid && neighborhoodControl?.touched" class="text-red-500 text-sm mt-1">
              Bairro é obrigatório
            </div>
          </div>

          <div>
            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">Cidade *</label>
            <div class="relative">
              <input
                type="text"
                id="city"
                formControlName="city"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': cityControl?.invalid && cityControl?.touched, 'bg-gray-100': isLoadingCep}"
                [disabled]="isLoadingCep"
              >
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div class="h-4 w-4 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div *ngIf="cityControl?.invalid && cityControl?.touched" class="text-red-500 text-sm mt-1">
              Cidade é obrigatória
            </div>
          </div>

          <div>
            <label for="state" class="block text-sm font-medium text-gray-700 mb-1">Estado *</label>
            <div class="relative">
              <select
                id="state"
                formControlName="state"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{'border-red-500': stateControl?.invalid && stateControl?.touched, 'bg-gray-100': isLoadingCep}"
                [disabled]="isLoadingCep"
              >
                <option value="" disabled selected>Selecione um estado</option>
                <option *ngFor="let estado of estados" [value]="estado.sigla">{{ estado.nome }}</option>
              </select>
              <div *ngIf="isLoadingCep" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div class="h-4 w-4 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div *ngIf="stateControl?.invalid && stateControl?.touched" class="text-red-500 text-sm mt-1">
              Estado é obrigatório
            </div>
          </div>
        </div>
      </div>

      <!-- Histórico Odontológico -->
      <div class="bg-gray-50 p-4 rounded-md">
        <h2 class="text-xl font-semibold mb-4">Histórico Odontológico</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Quais tratamentos já realizou? *</label>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
              <div *ngFor="let option of pastTreatmentOptions" class="flex items-center">
                <input
                  type="checkbox"
                  [id]="'pastTreatment_' + option"
                  [value]="option"
                  [checked]="isPastTreatmentSelected(option)"
                  (change)="onPastTreatmentChange(option, $event)"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label [for]="'pastTreatment_' + option" class="ml-2 text-sm text-gray-700">{{ option }}</label>
              </div>
            </div>
            <div *ngIf="pastTreatmentsControl?.invalid && pastTreatmentsControl?.touched" class="text-red-500 text-sm mt-1">
              Selecione pelo menos uma opção
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">Quando foi o último procedimento? *</label>
            <div class="space-y-2" [ngClass]="{'border border-red-500 rounded-md p-3': lastProcedureTimeControl?.invalid && lastProcedureTimeControl?.touched}">
              <div *ngFor="let option of lastProcedureTimes" class="flex items-center">
                <input
                  type="radio"
                  [id]="'lastProcedureTime_' + option.value"
                  [value]="option.value"
                  formControlName="lastProcedureTime"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                >
                <label [for]="'lastProcedureTime_' + option.value" class="ml-2 text-sm font-medium text-gray-700 cursor-pointer">
                  {{ option.label }}
                </label>
              </div>
            </div>
            <div *ngIf="lastProcedureTimeControl?.invalid && lastProcedureTimeControl?.touched" class="text-red-500 text-sm mt-1">
              Este campo é obrigatório
            </div>
          </div>


        </div>
      </div>

      <!-- Interesse Atual -->
      <div class="bg-gray-50 p-4 rounded-md">
        <h2 class="text-xl font-semibold mb-4">Interesse Atual</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">Em qual tratamento tem interesse hoje? *</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2" [ngClass]="{'border border-red-500 rounded-md p-3': interestedTreatmentControl?.invalid && interestedTreatmentControl?.touched}">
              <div *ngFor="let option of treatmentOptions" class="flex items-center">
                <input
                  type="checkbox"
                  [id]="'interestedTreatment_' + option"
                  [checked]="isInterestedTreatmentSelected(option)"
                  (change)="onInterestedTreatmentChange(option, $event)"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label [for]="'interestedTreatment_' + option" class="ml-2 text-sm font-medium text-gray-700 cursor-pointer">
                  {{ option }}
                </label>
              </div>
            </div>
            <div *ngIf="interestedTreatmentControl?.invalid && interestedTreatmentControl?.touched" class="text-red-500 text-sm mt-1">
              Selecione pelo menos uma opção
            </div>
          </div>

          <div class="flex items-center">
            <input
              type="checkbox"
              id="wantsFreeEvaluation"
              formControlName="wantsFreeEvaluation"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <label for="wantsFreeEvaluation" class="ml-2 text-sm font-medium text-gray-700">Gostaria de uma avaliação gratuita?</label>
          </div>

          <div class="flex items-center">
            <input
              type="checkbox"
              id="wantsPromotions"
              formControlName="wantsPromotions"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <label for="wantsPromotions" class="ml-2 text-sm font-medium text-gray-700">Deseja receber promoções e sorteios?</label>
          </div>
        </div>
      </div>

      <!-- Personalização -->
      <div class="bg-gray-50 p-4 rounded-md">
        <h2 class="text-xl font-semibold mb-4">Personalização</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="bestContactTime" class="block text-sm font-medium text-gray-700 mb-1">Melhor horário de contato *</label>
            <select
              id="bestContactTime"
              formControlName="bestContactTime"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': bestContactTimeControl?.invalid && bestContactTimeControl?.touched}"
            >
              <option value="" disabled selected>Selecione uma opção</option>
              <option *ngFor="let option of bestContactTimes" [value]="option">{{ option }}</option>
            </select>
            <div *ngIf="bestContactTimeControl?.invalid && bestContactTimeControl?.touched" class="text-red-500 text-sm mt-1">
              Este campo é obrigatório
            </div>
          </div>

          <div>
            <label for="referralSource" class="block text-sm font-medium text-gray-700 mb-1">Como nos conheceu? *</label>
            <select
              id="referralSource"
              formControlName="referralSource"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': referralSourceControl?.invalid && referralSourceControl?.touched}"
            >
              <option value="" disabled selected>Selecione uma opção</option>
              <option *ngFor="let option of referralSources" [value]="option">{{ option }}</option>
            </select>
            <div *ngIf="referralSourceControl?.invalid && referralSourceControl?.touched" class="text-red-500 text-sm mt-1">
              Este campo é obrigatório
            </div>
          </div>
        </div>
      </div>

      <!-- Botão de Envio -->
      <div class="flex justify-center">
        <button
          type="submit"
          class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors text-lg font-medium"
          [disabled]="isSubmitting"
        >
          <span *ngIf="!isSubmitting">Enviar Formulário</span>
          <span *ngIf="isSubmitting">Enviando...</span>
        </button>
      </div>
    </form>
  </div>
</div>
