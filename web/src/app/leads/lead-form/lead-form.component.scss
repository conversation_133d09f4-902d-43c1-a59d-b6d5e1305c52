// Estilos específicos para o formulário de lead
:host {
  display: block;
  background-color: #f9fafb;
  height: auto;
  min-height: 100vh;
  overflow-y: auto;
}

// Sobrescrever os estilos globais para permitir rolagem na página do formulário
:host ::ng-deep {
  html, body, app-root {
    overflow: auto !important;
    height: auto !important;
  }
}

// Estilo para o checkbox quando selecionado
input[type="checkbox"]:checked {
  background-color: #2563eb;
  border-color: #2563eb;
}

// Estilo para o container do formulário em dispositivos móveis
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
}
