import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { LeadService } from '../../core/services/lead.service';
import { CepService } from '../../core/services/cep.service';
import { LastProcedureTime } from '../../core/models/lead-form.model';
import { ESTADOS_BRASILEIROS, Estado } from '../../core/models/estados';
import { NotificationService } from '../../core/services/notification.service';
import { maskCPF, maskPhone, maskCEP } from '../../core/utils/input-masks';

@Component({
  selector: 'app-lead-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './lead-form.component.html',
  styleUrls: ['./lead-form.component.scss'],
})
export class LeadFormComponent implements OnInit {
  leadForm: FormGroup;
  isSubmitting = false;
  isLoadingCep = false;
  showSuccessModal = false;
  estados: Estado[] = ESTADOS_BRASILEIROS;

  lastProcedureTimes = [
    { value: LastProcedureTime.LESS_THAN_6_MONTHS, label: 'Menos de 6 meses' },
    { value: LastProcedureTime.ONE_YEAR, label: 'Aproximadamente 1 ano' },
    { value: LastProcedureTime.MORE_THAN_2_YEARS, label: 'Mais de 2 anos' },
  ];

  bestContactTimes = [
    'Manhã',
    'Tarde',
    'Noite',
    'Qualquer horário',
  ];

  referralSources = [
    'Google',
    'Instagram',
    'Facebook',
    'Indicação de amigo',
    'Passando na frente',
    'Outro',
  ];

  treatmentOptions = [
    'Limpeza',
    'Clareamento',
    'Restauração',
    'Canal',
    'Extração',
    'Implante',
    'Prótese',
    'Aparelho ortodôntico',
    'Tratamento de gengiva',
    'Avaliação geral',
    'Outro',
  ];

  pastTreatmentOptions = [
    'Limpeza',
    'Clareamento',
    'Restauração',
    'Canal',
    'Extração',
    'Implante',
    'Prótese',
    'Aparelho ortodôntico',
    'Tratamento de gengiva',
    'Nenhum',
  ];

  constructor(
    private fb: FormBuilder,
    private leadService: LeadService,
    private cepService: CepService,
    private notificationService: NotificationService
  ) {
    this.leadForm = this.fb.group({
      // Dados pessoais
      fullName: ['', [Validators.required, Validators.minLength(3)]],
      cpf: ['', [Validators.required]],
      phone: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],

      // Endereço
      cep: ['', [Validators.required]],
      street: ['', [Validators.required]],
      number: ['', [Validators.required]],
      neighborhood: ['', [Validators.required]],
      city: ['', [Validators.required]],
      state: ['', [Validators.required]],

      // Histórico odontológico
      pastTreatments: [[], [Validators.required]],
      lastProcedureTime: ['', [Validators.required]],

      // Interesse atual
      interestedTreatment: [[], [Validators.required]],
      wantsFreeEvaluation: [true],
      wantsPromotions: [true],

      // Personalização
      bestContactTime: ['', [Validators.required]],
      referralSource: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    // Inicializar o formulário
  }

  onSubmit(): void {
    if (this.leadForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.leadForm.controls).forEach(key => {
        const control = this.leadForm.get(key);
        control?.markAsTouched();
      });

      this.notificationService.error('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    this.isSubmitting = true;

    // Obter os valores do formulário
    const formValues = this.leadForm.value;

    // Enviar o formulário
    this.leadService.createLead(formValues).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.leadForm.reset();
        // Mostrar modal de sucesso
        this.showSuccessModal = true;
      },
      error: (error) => {
        console.error('Erro ao enviar formulário:', error);
        this.isSubmitting = false;
        this.notificationService.error('Erro ao enviar formulário. Por favor, tente novamente.');
      }
    });
  }

  // Método para aplicar máscara de CPF
  onCpfInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCPF(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.leadForm.get('cpf')?.setValue(maskedValue, { emitEvent: false });
  }

  // Método para aplicar máscara de telefone
  onPhoneInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.leadForm.get('phone')?.setValue(maskedValue, { emitEvent: false });
  }

  // Método para aplicar máscara de CEP
  onCepInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCEP(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.leadForm.get('cep')?.setValue(maskedValue, { emitEvent: false });

    // Buscar o CEP quando tiver 9 caracteres (incluindo o hífen)
    if (maskedValue.length === 9) {
      this.buscarCep(maskedValue);
    }
  }

  // Método para buscar o CEP
  buscarCep(cep?: string): void {
    const cepValue = cep || this.leadForm.get('cep')?.value;
    if (cepValue && cepValue.length === 9) { // Considerando o formato 12345-678
      this.isLoadingCep = true;

      // Desabilitar os campos de endereço durante a requisição
      this.disableAddressFields();

      this.cepService.consultarCep(cepValue).subscribe({
        next: (data) => {
          if (data) {
            this.leadForm.patchValue({
              street: data.logradouro,
              neighborhood: data.bairro,
              city: data.localidade,
              state: data.uf,
            });
          } else {
            this.notificationService.error('CEP não encontrado.');
          }
          this.isLoadingCep = false;

          // Habilitar os campos de endereço após a requisição
          this.enableAddressFields();
        },
        error: () => {
          this.notificationService.error('Erro ao buscar CEP.');
          this.isLoadingCep = false;

          // Habilitar os campos de endereço após a requisição
          this.enableAddressFields();
        }
      });
    }
  }

  // Método para desabilitar os campos de endereço
  private disableAddressFields(): void {
    this.streetControl?.disable();
    this.numberControl?.disable();
    this.neighborhoodControl?.disable();
    this.cityControl?.disable();
    this.stateControl?.disable();
  }

  // Método para habilitar os campos de endereço
  private enableAddressFields(): void {
    this.streetControl?.enable();
    this.numberControl?.enable();
    this.neighborhoodControl?.enable();
    this.cityControl?.enable();
    this.stateControl?.enable();
  }

  // Método para lidar com a seleção de tratamentos anteriores
  onPastTreatmentChange(option: string, event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    const pastTreatments = this.pastTreatmentsControl?.value || [];

    if (isChecked) {
      // Adicionar o tratamento se não estiver na lista
      if (!pastTreatments.includes(option)) {
        this.pastTreatmentsControl?.setValue([...pastTreatments, option]);
      }
    } else {
      // Remover o tratamento da lista
      this.pastTreatmentsControl?.setValue(
        pastTreatments.filter((item: string) => item !== option)
      );
    }
  }

  // Método para verificar se um tratamento está selecionado
  isPastTreatmentSelected(option: string): boolean {
    const pastTreatments = this.pastTreatmentsControl?.value || [];
    return pastTreatments.includes(option);
  }

  // Método para lidar com a seleção de tratamentos de interesse
  onInterestedTreatmentChange(option: string, event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    const interestedTreatments = this.interestedTreatmentControl?.value || [];

    if (isChecked) {
      // Adicionar o tratamento se não estiver na lista
      if (!interestedTreatments.includes(option)) {
        this.interestedTreatmentControl?.setValue([...interestedTreatments, option]);
      }
    } else {
      // Remover o tratamento da lista
      this.interestedTreatmentControl?.setValue(
        interestedTreatments.filter((item: string) => item !== option)
      );
    }
  }

  // Método para verificar se um tratamento de interesse está selecionado
  isInterestedTreatmentSelected(option: string): boolean {
    const interestedTreatments = this.interestedTreatmentControl?.value || [];
    return interestedTreatments.includes(option);
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get fullNameControl() { return this.leadForm.get('fullName'); }
  get cpfControl() { return this.leadForm.get('cpf'); }
  get phoneControl() { return this.leadForm.get('phone'); }
  get emailControl() { return this.leadForm.get('email'); }
  get cepControl() { return this.leadForm.get('cep'); }
  get streetControl() { return this.leadForm.get('street'); }
  get numberControl() { return this.leadForm.get('number'); }
  get neighborhoodControl() { return this.leadForm.get('neighborhood'); }
  get cityControl() { return this.leadForm.get('city'); }
  get stateControl() { return this.leadForm.get('state'); }
  get pastTreatmentsControl() { return this.leadForm.get('pastTreatments'); }
  get lastProcedureTimeControl() { return this.leadForm.get('lastProcedureTime'); }
  get interestedTreatmentControl() { return this.leadForm.get('interestedTreatment'); }
  get wantsFreeEvaluationControl() { return this.leadForm.get('wantsFreeEvaluation'); }
  get wantsPromotionsControl() { return this.leadForm.get('wantsPromotions'); }
  get bestContactTimeControl() { return this.leadForm.get('bestContactTime'); }
  get referralSourceControl() { return this.leadForm.get('referralSource'); }

  // Método para fechar o modal de sucesso
  closeSuccessModal(): void {
    this.showSuccessModal = false;
  }
}
