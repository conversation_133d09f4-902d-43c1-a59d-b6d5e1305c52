import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LeadService } from '../../core/services/lead.service';
import { LeadForm } from '../../core/models/lead-form.model';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-lead-detail',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './lead-detail.component.html',
  styleUrls: ['./lead-detail.component.scss'],
})
export class LeadDetailComponent implements OnInit {
  lead: LeadForm | null = null;
  isLoading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private leadService: LeadService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadLead(id);
      } else {
        this.error = 'ID do lead não fornecido';
        this.isLoading = false;
      }
    });
  }

  loadLead(id: string): void {
    this.isLoading = true;
    this.leadService.getLead(id).subscribe({
      next: (lead) => {
        this.lead = lead;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar lead:', error);
        this.error = 'Não foi possível carregar os detalhes do lead. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      },
    });
  }

  updatePatientField(leadId: string, fieldName: string): void {
    this.leadService.updatePatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        this.lead = { ...updatedLead }; // Criar nova referência para forçar detecção de mudanças
        this.cdr.detectChanges(); // Forçar detecção de mudanças
      },
      error: (error) => {
        console.error('Erro ao atualizar campo do paciente:', error);
        this.notificationService.error('Erro ao atualizar campo do paciente');
      },
    });
  }

  skipPatientField(leadId: string, fieldName: string): void {
    this.leadService.skipPatientField(leadId, fieldName).subscribe({
      next: (updatedLead) => {
        this.lead = { ...updatedLead }; // Criar nova referência para forçar detecção de mudanças
        this.cdr.detectChanges(); // Forçar detecção de mudanças
      },
      error: (error) => {
        console.error('Erro ao ignorar campo do paciente:', error);
        this.notificationService.error('Erro ao ignorar campo do paciente');
      },
    });
  }

  formatDate(date: Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  }
}
