<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Detalhes do Lead</h1>
    <a routerLink="/leads" class="text-blue-600 hover:text-blue-800">
      Voltar para a lista
    </a>
  </div>

  <div *ngIf="isLoading" class="bg-white rounded-lg shadow-md p-6 text-center">
    <p class="text-gray-600">Carregando detalhes do lead...</p>
  </div>

  <div *ngIf="error" class="bg-white rounded-lg shadow-md p-6 text-center">
    <p class="text-red-600">{{ error }}</p>
    <button
      *ngIf="lead && lead.id"
      (click)="loadLead(lead.id)"
      class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
    >
      Tentar Novamente
    </button>
  </div>

  <div *ngIf="!isLoading && !error && lead" class="space-y-6">
    <!-- Cabeçalho com informações principais -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center">
        <div>
          <h2 class="text-xl font-bold">{{ lead.fullName }}</h2>
          <p class="text-gray-600">{{ lead.email }} | {{ lead.phone }}</p>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full"
                [ngClass]="lead.isExistingPatient ? (lead.hasUpdatesAvailable ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') : 'bg-blue-100 text-blue-800'">
            {{ lead.isExistingPatient ? (lead.hasUpdatesAvailable ? 'Campos Divergentes' : 'Cadastro Atualizado') : 'Novo Lead' }}
          </span>
          <p class="text-gray-600 mt-1">Enviado em {{ formatDate(lead.createdAt) }}</p>
        </div>
      </div>
    </div>

    <!-- Informações detalhadas -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Dados Pessoais -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Dados Pessoais</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">Nome Completo:</span>
            <p class="font-medium">{{ lead.fullName }}</p>

            <div *ngIf="lead.isExistingPatient && lead.hasUpdatesAvailable && lead.fieldsToUpdate?.includes('name')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ lead.patient?.name }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(lead.id, 'name')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(lead.id, 'name')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>

          <div>
            <span class="text-sm text-gray-500">CPF:</span>
            <p class="font-medium">{{ lead.cpf }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Telefone:</span>
            <p class="font-medium">{{ lead.phone }}</p>

            <div *ngIf="lead.isExistingPatient && lead.hasUpdatesAvailable && lead.fieldsToUpdate?.includes('phone')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ lead.patient?.phone }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(lead.id, 'phone')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(lead.id, 'phone')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>

          <div>
            <span class="text-sm text-gray-500">E-mail:</span>
            <p class="font-medium">{{ lead.email }}</p>

            <div *ngIf="lead.isExistingPatient && lead.hasUpdatesAvailable && lead.fieldsToUpdate?.includes('email')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="text-sm text-yellow-800">Divergente do cadastro do paciente</p>
              <p class="text-sm text-gray-600">Valor no cadastro: {{ lead.patient?.email }}</p>
              <div class="flex space-x-2 mt-1">
                <button
                  (click)="updatePatientField(lead.id, 'email')"
                  class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  Atualizar cadastro
                </button>
                <button
                  (click)="skipPatientField(lead.id, 'email')"
                  class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                >
                  Não atualizar
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Endereço -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Endereço</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">CEP:</span>
            <p class="font-medium">{{ lead.cep }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Rua:</span>
            <p class="font-medium">{{ lead.street }}, {{ lead.number }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Bairro:</span>
            <p class="font-medium">{{ lead.neighborhood }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Cidade/Estado:</span>
            <p class="font-medium">{{ lead.city }}/{{ lead.state }}</p>
          </div>

          <div *ngIf="lead.isExistingPatient && lead.hasUpdatesAvailable && lead.fieldsToUpdate?.includes('address')" class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-800">Endereço divergente do cadastro do paciente</p>
            <p class="text-sm text-gray-600">Endereço no cadastro: {{ lead.patient?.addressStreet }}, {{ lead.patient?.addressNumber }} - {{ lead.patient?.addressNeighborhood }}, {{ lead.patient?.addressCity }}/{{ lead.patient?.addressState }}</p>
            <div class="flex space-x-2 mt-1">
              <button
                (click)="updatePatientField(lead.id, 'address')"
                class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
              >
                Atualizar cadastro
              </button>
              <button
                (click)="skipPatientField(lead.id, 'address')"
                class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
              >
                Não atualizar
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Histórico Odontológico -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Histórico Odontológico</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">Tratamentos Anteriores:</span>
            <p class="font-medium">{{ lead.pastTreatments || '' }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Último Procedimento:</span>
            <p class="font-medium">
              {{ lead.lastProcedureTime === '<6meses' ? 'Menos de 6 meses' :
                 lead.lastProcedureTime === '1ano' ? 'Aproximadamente 1 ano' :
                 'Mais de 2 anos' }}
            </p>
          </div>


        </div>
      </div>

      <!-- Interesse Atual -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Interesse Atual</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">Tratamentos de Interesse:</span>
            <p class="font-medium">{{ lead.interestedTreatment.join(', ') || 'Nenhum' }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Deseja Avaliação Gratuita:</span>
            <p class="font-medium">{{ lead.wantsFreeEvaluation ? 'Sim' : 'Não' }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Deseja Receber Promoções:</span>
            <p class="font-medium">{{ lead.wantsPromotions ? 'Sim' : 'Não' }}</p>
          </div>
        </div>
      </div>

      <!-- Personalização -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Personalização</h3>
        <div class="space-y-3">


          <div>
            <span class="text-sm text-gray-500">Melhor Horário para Contato:</span>
            <p class="font-medium">{{ lead.bestContactTime }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Como nos Conheceu:</span>
            <p class="font-medium">{{ lead.referralSource }}</p>
          </div>
        </div>
      </div>

      <!-- Informações do Sistema -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">Informações do Sistema</h3>
        <div class="space-y-3">
          <div>
            <span class="text-sm text-gray-500">Data de Criação:</span>
            <p class="font-medium">{{ formatDate(lead.createdAt) }}</p>
          </div>

          <div>
            <span class="text-sm text-gray-500">Paciente Existente:</span>
            <p class="font-medium">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="lead.isExistingPatient ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'">
                {{ lead.isExistingPatient ? 'Paciente' : 'Novo Lead' }}
              </span>
            </p>
          </div>

          <div *ngIf="lead.isExistingPatient && lead.patient">
            <span class="text-sm text-gray-500">ID do Paciente:</span>
            <p class="font-medium">{{ lead.patientId }}</p>
            <a
              [routerLink]="['/patients', lead.patientId]"
              class="text-blue-600 hover:underline text-sm"
            >
              Ver perfil do paciente
            </a>
          </div>

          <div *ngIf="lead.isExistingPatient">
            <span class="text-sm text-gray-500">Status:</span>
            <p class="font-medium">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="lead.hasUpdatesAvailable ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'">
                {{ lead.hasUpdatesAvailable ? 'Campos Divergentes' : 'Cadastro Atualizado' }}
              </span>
            </p>
            <div *ngIf="!lead.hasUpdatesAvailable" class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
              <p class="text-sm text-green-800">Todos os dados estão atualizados.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
