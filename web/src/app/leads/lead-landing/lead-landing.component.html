<div class="landing-page">
  <!-- Header -->
  <header class="landing-header">
    <div class="container mx-auto px-4 py-6 flex justify-between items-center">
      <div class="logo">
        <img src="assets/images/logo.svg" alt="Logo Clínica Odontológica" class="h-12">
      </div>
      <div class="contact-info">
        <a href="tel:+551199999999" class="flex items-center text-white">
          <i class="material-icons mr-2">phone</i>
          (11) 9999-9999
        </a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="container mx-auto px-4 py-16 flex flex-col md:flex-row items-center">
      <div class="md:w-1/2 mb-8 md:mb-0">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">Sorria com Confiança</h1>
        <p class="text-xl text-white mb-8">Agende sua avaliação gratuita e descubra como podemos transformar seu sorriso.</p>
        <a href="#form-section" class="bg-white text-blue-600 px-6 py-3 rounded-full font-bold text-lg hover:bg-blue-100 transition-colors">
          Agendar Avaliação
        </a>
      </div>
      <div class="md:w-1/2">
        <img src="assets/images/hero-image.jpg" alt="Sorriso saudável" class="rounded-lg shadow-xl">
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section class="services-section py-16 bg-white">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Nossos Serviços</h2>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="service-card">
          <div class="icon-container mb-4">
            <i class="material-icons text-4xl text-blue-600">cleaning_services</i>
          </div>
          <h3 class="text-xl font-bold mb-2">Limpeza e Prevenção</h3>
          <p class="text-gray-600">Mantenha sua saúde bucal em dia com nossos serviços de limpeza profissional e prevenção.</p>
        </div>

        <div class="service-card">
          <div class="icon-container mb-4">
            <i class="material-icons text-4xl text-blue-600">auto_fix_high</i>
          </div>
          <h3 class="text-xl font-bold mb-2">Clareamento Dental</h3>
          <p class="text-gray-600">Tenha um sorriso mais branco e brilhante com nossos tratamentos de clareamento dental.</p>
        </div>

        <div class="service-card">
          <div class="icon-container mb-4">
            <i class="material-icons text-4xl text-blue-600">medical_services</i>
          </div>
          <h3 class="text-xl font-bold mb-2">Implantes Dentários</h3>
          <p class="text-gray-600">Recupere sua confiança com implantes dentários de alta qualidade e tecnologia avançada.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Form Section -->
  <section id="form-section" class="form-section py-16 bg-gray-100">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl overflow-hidden">
        <div class="p-6 bg-blue-600 text-white">
          <h2 class="text-2xl font-bold">Agende sua Avaliação Gratuita</h2>
          <p class="mt-2">Preencha o formulário abaixo e entraremos em contato em breve.</p>
        </div>

        <form [formGroup]="leadForm" (ngSubmit)="onSubmit()" class="p-6 space-y-8">
          <!-- Dados Pessoais -->
          <div>
            <h3 class="text-xl font-semibold mb-4 text-blue-600">Dados Pessoais</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="col-span-2 md:col-span-1">
                <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Nome Completo *</label>
                <input
                  type="text"
                  id="fullName"
                  formControlName="fullName"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': fullNameControl?.invalid && fullNameControl?.touched}"
                >
                <div *ngIf="fullNameControl?.invalid && fullNameControl?.touched" class="text-red-500 text-sm mt-1">
                  Nome completo é obrigatório
                </div>
              </div>

              <div>
                <label for="cpf" class="block text-sm font-medium text-gray-700 mb-1">CPF *</label>
                <input
                  type="text"
                  id="cpf"
                  formControlName="cpf"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': cpfControl?.invalid && cpfControl?.touched}"
                  placeholder="000.000.000-00"
                >
                <div *ngIf="cpfControl?.invalid && cpfControl?.touched" class="text-red-500 text-sm mt-1">
                  CPF é obrigatório
                </div>
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Telefone *</label>
                <input
                  type="text"
                  id="phone"
                  formControlName="phone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': phoneControl?.invalid && phoneControl?.touched}"
                  placeholder="(00) 00000-0000"
                >
                <div *ngIf="phoneControl?.invalid && phoneControl?.touched" class="text-red-500 text-sm mt-1">
                  Telefone é obrigatório
                </div>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">E-mail *</label>
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': emailControl?.invalid && emailControl?.touched}"
                >
                <div *ngIf="emailControl?.invalid && emailControl?.touched" class="text-red-500 text-sm mt-1">
                  E-mail válido é obrigatório
                </div>
              </div>
            </div>
          </div>

          <!-- Endereço -->
          <div>
            <h3 class="text-xl font-semibold mb-4 text-blue-600">Endereço</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="cep" class="block text-sm font-medium text-gray-700 mb-1">CEP *</label>
                <div class="flex">
                  <input
                    type="text"
                    id="cep"
                    formControlName="cep"
                    class="w-full px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    [ngClass]="{'border-red-500': cepControl?.invalid && cepControl?.touched}"
                    placeholder="00000-000"
                    (blur)="buscarCep()"
                  >
                  <button
                    type="button"
                    class="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 transition-colors"
                    (click)="buscarCep()"
                    [disabled]="isLoadingCep"
                  >
                    <span *ngIf="!isLoadingCep">Buscar</span>
                    <span *ngIf="isLoadingCep">...</span>
                  </button>
                </div>
                <div *ngIf="cepControl?.invalid && cepControl?.touched" class="text-red-500 text-sm mt-1">
                  CEP é obrigatório
                </div>
              </div>

              <div class="col-span-2 md:col-span-1">
                <label for="street" class="block text-sm font-medium text-gray-700 mb-1">Rua *</label>
                <input
                  type="text"
                  id="street"
                  formControlName="street"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': streetControl?.invalid && streetControl?.touched}"
                >
                <div *ngIf="streetControl?.invalid && streetControl?.touched" class="text-red-500 text-sm mt-1">
                  Rua é obrigatória
                </div>
              </div>

              <div>
                <label for="number" class="block text-sm font-medium text-gray-700 mb-1">Número *</label>
                <input
                  type="text"
                  id="number"
                  formControlName="number"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': numberControl?.invalid && numberControl?.touched}"
                >
                <div *ngIf="numberControl?.invalid && numberControl?.touched" class="text-red-500 text-sm mt-1">
                  Número é obrigatório
                </div>
              </div>

              <div>
                <label for="neighborhood" class="block text-sm font-medium text-gray-700 mb-1">Bairro *</label>
                <input
                  type="text"
                  id="neighborhood"
                  formControlName="neighborhood"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': neighborhoodControl?.invalid && neighborhoodControl?.touched}"
                >
                <div *ngIf="neighborhoodControl?.invalid && neighborhoodControl?.touched" class="text-red-500 text-sm mt-1">
                  Bairro é obrigatório
                </div>
              </div>

              <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">Cidade *</label>
                <input
                  type="text"
                  id="city"
                  formControlName="city"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': cityControl?.invalid && cityControl?.touched}"
                >
                <div *ngIf="cityControl?.invalid && cityControl?.touched" class="text-red-500 text-sm mt-1">
                  Cidade é obrigatória
                </div>
              </div>

              <div>
                <label for="state" class="block text-sm font-medium text-gray-700 mb-1">Estado *</label>
                <select
                  id="state"
                  formControlName="state"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': stateControl?.invalid && stateControl?.touched}"
                >
                  <option value="" disabled selected>Selecione um estado</option>
                  <option *ngFor="let estado of estados" [value]="estado.sigla">{{ estado.nome }}</option>
                </select>
                <div *ngIf="stateControl?.invalid && stateControl?.touched" class="text-red-500 text-sm mt-1">
                  Estado é obrigatório
                </div>
              </div>
            </div>
          </div>

          <!-- Histórico Odontológico -->
          <div>
            <h3 class="text-xl font-semibold mb-4 text-blue-600">Histórico Odontológico</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Quais tratamentos já realizou? *</label>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div *ngFor="let option of pastTreatmentOptions" class="flex items-center">
                    <input
                      type="checkbox"
                      [id]="'pastTreatment_' + option"
                      [value]="option"
                      (change)="onPastTreatmentChange(option, $event)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label [for]="'pastTreatment_' + option" class="ml-2 text-sm text-gray-700">{{ option }}</label>
                  </div>
                </div>
                <div *ngIf="pastTreatmentsControl?.invalid && pastTreatmentsControl?.touched" class="text-red-500 text-sm mt-1">
                  Selecione pelo menos uma opção
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Quando foi o último procedimento? *</label>
                <div class="space-y-2" [ngClass]="{'border border-red-500 rounded-md p-3': lastProcedureTimeControl?.invalid && lastProcedureTimeControl?.touched}">
                  <div *ngFor="let option of lastProcedureTimes" class="flex items-center">
                    <input
                      type="radio"
                      [id]="'lastProcedureTime_' + option.value"
                      [value]="option.value"
                      formControlName="lastProcedureTime"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    >
                    <label [for]="'lastProcedureTime_' + option.value" class="ml-2 text-sm font-medium text-gray-700 cursor-pointer">
                      {{ option.label }}
                    </label>
                  </div>
                </div>
                <div *ngIf="lastProcedureTimeControl?.invalid && lastProcedureTimeControl?.touched" class="text-red-500 text-sm mt-1">
                  Este campo é obrigatório
                </div>
              </div>


            </div>
          </div>

          <!-- Interesse Atual -->
          <div>
            <h3 class="text-xl font-semibold mb-4 text-blue-600">Interesse Atual</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Em qual tratamento tem interesse hoje? *</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2" [ngClass]="{'border border-red-500 rounded-md p-3': interestedTreatmentControl?.invalid && interestedTreatmentControl?.touched}">
                  <div *ngFor="let option of treatmentOptions" class="flex items-center">
                    <input
                      type="checkbox"
                      [id]="'interestedTreatment_' + option"
                      [checked]="isInterestedTreatmentSelected(option)"
                      (change)="onInterestedTreatmentChange(option, $event)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label [for]="'interestedTreatment_' + option" class="ml-2 text-sm font-medium text-gray-700 cursor-pointer">
                      {{ option }}
                    </label>
                  </div>
                </div>
                <div *ngIf="interestedTreatmentControl?.invalid && interestedTreatmentControl?.touched" class="text-red-500 text-sm mt-1">
                  Selecione pelo menos uma opção
                </div>
              </div>

              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="wantsFreeEvaluation"
                  formControlName="wantsFreeEvaluation"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label for="wantsFreeEvaluation" class="ml-2 text-sm font-medium text-gray-700">Gostaria de uma avaliação gratuita?</label>
              </div>

              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="wantsPromotions"
                  formControlName="wantsPromotions"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label for="wantsPromotions" class="ml-2 text-sm font-medium text-gray-700">Deseja receber promoções e sorteios?</label>
              </div>
            </div>
          </div>

          <!-- Personalização -->
          <div>
            <h3 class="text-xl font-semibold mb-4 text-blue-600">Personalização</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="bestContactTime" class="block text-sm font-medium text-gray-700 mb-1">Melhor horário de contato *</label>
                <select
                  id="bestContactTime"
                  formControlName="bestContactTime"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': bestContactTimeControl?.invalid && bestContactTimeControl?.touched}"
                >
                  <option value="" disabled selected>Selecione uma opção</option>
                  <option *ngFor="let option of bestContactTimes" [value]="option">{{ option }}</option>
                </select>
                <div *ngIf="bestContactTimeControl?.invalid && bestContactTimeControl?.touched" class="text-red-500 text-sm mt-1">
                  Este campo é obrigatório
                </div>
              </div>

              <div>
                <label for="referralSource" class="block text-sm font-medium text-gray-700 mb-1">Como nos conheceu? *</label>
                <select
                  id="referralSource"
                  formControlName="referralSource"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{'border-red-500': referralSourceControl?.invalid && referralSourceControl?.touched}"
                >
                  <option value="" disabled selected>Selecione uma opção</option>
                  <option *ngFor="let option of referralSources" [value]="option">{{ option }}</option>
                </select>
                <div *ngIf="referralSourceControl?.invalid && referralSourceControl?.touched" class="text-red-500 text-sm mt-1">
                  Este campo é obrigatório
                </div>
              </div>
            </div>
          </div>

          <!-- Botão de Envio -->
          <div class="flex justify-center">
            <button
              type="submit"
              class="bg-blue-600 text-white px-8 py-4 rounded-full hover:bg-blue-700 transition-colors text-lg font-bold"
              [disabled]="isSubmitting"
            >
              <span *ngIf="!isSubmitting">Agendar Minha Avaliação</span>
              <span *ngIf="isSubmitting">Enviando...</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials-section py-16 bg-white">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">O Que Nossos Pacientes Dizem</h2>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="testimonial-card bg-gray-50 p-6 rounded-lg shadow">
          <div class="flex items-center mb-4">
            <div class="text-yellow-400 flex">
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
            </div>
          </div>
          <p class="text-gray-600 mb-4">"Excelente atendimento! Os profissionais são muito atenciosos e o resultado do meu tratamento superou minhas expectativas."</p>
          <div class="font-bold">Maria Silva</div>
        </div>

        <div class="testimonial-card bg-gray-50 p-6 rounded-lg shadow">
          <div class="flex items-center mb-4">
            <div class="text-yellow-400 flex">
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
            </div>
          </div>
          <p class="text-gray-600 mb-4">"Fiz um implante e fiquei muito satisfeito com o resultado. Equipe muito profissional e ambiente super agradável."</p>
          <div class="font-bold">João Santos</div>
        </div>

        <div class="testimonial-card bg-gray-50 p-6 rounded-lg shadow">
          <div class="flex items-center mb-4">
            <div class="text-yellow-400 flex">
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
              <i class="material-icons">star</i>
            </div>
          </div>
          <p class="text-gray-600 mb-4">"Meu filho tinha muito medo de dentista, mas a equipe foi incrível e agora ele adora ir às consultas. Recomendo muito!"</p>
          <div class="font-bold">Ana Oliveira</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="landing-footer py-8 text-white">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4">Clínica Odontológica</h3>
          <p>Oferecendo os melhores tratamentos odontológicos com profissionais qualificados e tecnologia de ponta.</p>
        </div>

        <div>
          <h3 class="text-xl font-bold mb-4">Contato</h3>
          <p class="mb-2">Av. Paulista, 1000 - São Paulo/SP</p>
          <p class="mb-2">contato&#64;clinicaodonto.com.br</p>
          <p>(11) 9999-9999</p>
        </div>

        <div>
          <h3 class="text-xl font-bold mb-4">Horário de Funcionamento</h3>
          <p class="mb-2">Segunda a Sexta: 8h às 20h</p>
          <p>Sábado: 8h às 12h</p>
        </div>
      </div>

      <div class="mt-8 pt-8 border-t border-blue-500 text-center">
        <p>&copy; 2023 Clínica Odontológica. Todos os direitos reservados.</p>
      </div>
    </div>
  </footer>
</div>
