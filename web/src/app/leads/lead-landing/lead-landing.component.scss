.landing-page {
  font-family: '<PERSON>o', sans-serif;
  color: #333;
  
  // Header
  .landing-header {
    background-color: #1e40af; // blue-700
    position: sticky;
    top: 0;
    z-index: 100;
  }
  
  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    padding: 80px 0;
    
    img {
      max-width: 100%;
      height: auto;
    }
  }
  
  // Services Section
  .services-section {
    .service-card {
      text-align: center;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }
      
      .icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
      }
    }
  }
  
  // Form Section
  .form-section {
    form {
      .checkbox-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        
        @media (min-width: 768px) {
          grid-template-columns: repeat(3, 1fr);
        }
      }
    }
  }
  
  // Testimonials Section
  .testimonials-section {
    .testimonial-card {
      transition: transform 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
      }
    }
  }
  
  // Footer
  .landing-footer {
    background-color: #1e40af; // blue-700
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .landing-page {
    .hero-section {
      text-align: center;
    }
  }
}
