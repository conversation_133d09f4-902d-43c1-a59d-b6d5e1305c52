.form-page-container {
  width: 100%;
  min-height: 100vh;
  height: auto;
  overflow-y: auto;
  background-color: #f9fafb;
  padding: 1rem 0;
}

:host {
  display: block;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

// Sobrescrever os estilos globais para permitir rolagem
:host ::ng-deep {
  html, body, app-root {
    overflow: auto !important;
    height: auto !important;
    min-height: 100vh !important;
  }
}
