import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '../header/header.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { SidebarService } from '../../core/services/sidebar.service';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
  standalone: true,
  imports: [CommonModule, HeaderComponent, SidebarComponent],
})
export class LayoutComponent implements OnInit {
  isSidebarOpen = true;
  isSidebarCollapsed = false;
  isLargeScreen = false;
  isExtraLargeScreen = false;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit(): void {
    this.sidebarService.isOpen$.subscribe((isOpen) => {
      this.isSidebarOpen = isOpen;
    });

    this.sidebarService.isCollapsed$.subscribe((isCollapsed) => {
      this.isSidebarCollapsed = isCollapsed;
    });

    // Verificar o tamanho da tela
    this.checkScreenSize();
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  private checkScreenSize(): void {
    this.isLargeScreen = window.innerWidth >= 1024; // lg breakpoint do Tailwind
    this.isExtraLargeScreen = window.innerWidth >= 1920; // Full HD e acima
  }

  closeSidebar(): void {
    this.sidebarService.close();
  }
}
