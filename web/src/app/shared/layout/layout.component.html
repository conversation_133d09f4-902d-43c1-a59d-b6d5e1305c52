<div class="flex h-screen bg-gray-50 overflow-hidden">
  <!-- Overlay para fechar o sidebar em dispositivos móveis -->
  <div
    *ngIf="isSidebarOpen && !isLargeScreen"
    class="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden transition-opacity duration-300 ease-in-out"
    (click)="closeSidebar()"
  ></div>

  <!-- Sidebar -->
  <app-sidebar></app-sidebar>

  <!-- <PERSON>rea principal -->
  <div class="flex flex-col flex-1 w-full">
    <!-- Header -->
    <app-header class="flex items-center shadow-sm z-10 h-full"></app-header>

    <!-- Con<PERSON><PERSON><PERSON> principal -->
    <main class="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
      <div class="mx-auto"
           [ngClass]="{
             'max-w-7xl': !isSidebarCollapsed || !isLargeScreen,
             'max-w-none px-4': isSidebarCollapsed && isLargeScreen
           }">
        <ng-content></ng-content>
      </div>
    </main>
  </div>
</div>
