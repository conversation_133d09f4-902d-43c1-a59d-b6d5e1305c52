import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalOpenSubject = new BehaviorSubject<boolean>(false);
  private modalTitleSubject = new BehaviorSubject<string>('');
  private modalContentSubject = new BehaviorSubject<any>(null);

  modalOpen$ = this.modalOpenSubject.asObservable();
  modalTitle$ = this.modalTitleSubject.asObservable();
  modalContent$ = this.modalContentSubject.asObservable();

  openModal(title: string, content: any) {
    this.modalTitleSubject.next(title);
    this.modalContentSubject.next(content);
    this.modalOpenSubject.next(true);
  }

  closeModal() {
    this.modalOpenSubject.next(false);
  }
}
