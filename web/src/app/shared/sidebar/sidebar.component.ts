import { Component, OnInit, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { SidebarService } from '../../core/services/sidebar.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  standalone: true,
  imports: [CommonModule, RouterLink, RouterLinkActive],
})
export class SidebarComponent implements OnInit, AfterViewInit {
  clinicalManagementExpanded = false;
  settingsExpanded = false;
  isOpen = true;
  isCollapsed = false;

  // Estados para submenus no modo colapsado
  collapsedSubmenuOpen: string | null = null;

  @ViewChild('clinicalDropdown') clinicalDropdown!: ElementRef;
  @ViewChild('settingsDropdown') settingsDropdown!: ElementRef;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit(): void {
    this.sidebarService.isOpen$.subscribe((isOpen) => {
      this.isOpen = isOpen;
    });

    this.sidebarService.isCollapsed$.subscribe((isCollapsed) => {
      this.isCollapsed = isCollapsed;
      // Fechar submenus quando colapsado/expandido
      if (isCollapsed) {
        this.clinicalManagementExpanded = false;
        this.settingsExpanded = false;
      } else {
        this.collapsedSubmenuOpen = null;
      }
    });

    // Fechar submenu colapsado quando clicar fora
    document.addEventListener('click', (event) => {
      if (this.isCollapsed && this.collapsedSubmenuOpen) {
        const target = event.target as HTMLElement;
        if (!target.closest('.collapsed-submenu-container')) {
          this.collapsedSubmenuOpen = null;
        }
      }
    });
  }

  ngAfterViewInit(): void {
    // Implementação após a view ser inicializada
  }

  toggleClinicalManagement(event?: Event) {
    if (this.isCollapsed) {
      // No modo colapsado, alternar o submenu dropdown
      const wasOpen = this.collapsedSubmenuOpen === 'clinical';
      this.collapsedSubmenuOpen = wasOpen ? null : 'clinical';

      if (!wasOpen && event) {
        this.positionDropdown(event, 'clinical');
      }
    } else {
      // No modo expandido, comportamento normal
      this.clinicalManagementExpanded = !this.clinicalManagementExpanded;
    }
  }

  toggleSettings(event?: Event) {
    if (this.isCollapsed) {
      // No modo colapsado, alternar o submenu dropdown
      const wasOpen = this.collapsedSubmenuOpen === 'settings';
      this.collapsedSubmenuOpen = wasOpen ? null : 'settings';

      if (!wasOpen && event) {
        this.positionDropdown(event, 'settings');
      }
    } else {
      // No modo expandido, comportamento normal
      this.settingsExpanded = !this.settingsExpanded;
    }
  }

  private positionDropdown(event: Event, menuType: string) {
    setTimeout(() => {
      const target = event.target as HTMLElement;
      const menuItem = target.closest('li');

      if (menuItem) {
        const rect = menuItem.getBoundingClientRect();
        const dropdown = menuType === 'clinical' ? this.clinicalDropdown : this.settingsDropdown;

        if (dropdown) {
          const dropdownEl = dropdown.nativeElement;
          const viewportHeight = window.innerHeight;
          const dropdownHeight = dropdownEl.offsetHeight || 300; // Altura estimada se não calculada ainda

          // Calcular posição ideal
          let topPosition = rect.top;

          // Se o dropdown sair da tela por baixo, posicionar acima do item
          if (topPosition + dropdownHeight > viewportHeight - 20) {
            topPosition = rect.bottom - dropdownHeight;
          }

          // Se ainda sair da tela por cima, posicionar no topo da viewport
          if (topPosition < 20) {
            topPosition = 20;
          }

          dropdownEl.style.top = `${topPosition}px`;
          dropdownEl.style.bottom = 'auto'; // Reset bottom positioning
        }
      }
    }, 0);
  }

  isCollapsedSubmenuOpen(menuName: string): boolean {
    return this.isCollapsed && this.collapsedSubmenuOpen === menuName;
  }
}
