/* Estilos para o sidebar */
:host {
  display: block;
  height: 100%;
}

.rotate-180 {
  transform: rotate(180deg);
}

.transition-transform {
  transition: transform 0.3s ease-in-out;
}

/* Animação para o submenu */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Aplicar animação ao submenu */
.ml-6 {
  animation: slideDown 0.3s ease-out;
}

/* Efeito de transição para todos os elementos interativos */
:host ::ng-deep a,
:host ::ng-deep button,
:host ::ng-deep div[role="button"] {
  transition: all 0.2s ease-in-out;
}

/* Estilos para o modo colapsado */
.collapsed-sidebar {
  width: 5rem; /* 80px */
}

.expanded-sidebar {
  width: 16rem; /* 256px */
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  left: 100%;
  margin-left: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  white-space: nowrap;
  z-index: 50;
}

.group:hover .tooltip {
  opacity: 1;
}

/* Animações suaves para o logo */
.logo-transition {
  transition: all 0.3s ease-in-out;
}

/* Estilos para o logo colapsado */
.collapsed-logo {
  width: 3.5rem; /* 56px */
  height: 3.5rem; /* 56px */
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 0.75rem; /* 12px */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1rem; /* 16px */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-in-out;
}

.collapsed-logo:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}

/* Estilos para submenus dropdown no modo colapsado */
.collapsed-submenu-container {
  position: relative;
}

/* Animações para o submenu dropdown */
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
    filter: blur(1px);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-10px) scale(0.95);
    filter: blur(0.5px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

@keyframes slideOutToLeft {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
    filter: blur(1px);
  }
}

.animate-in {
  animation: slideInFromLeft 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  animation-fill-mode: both;
}

.animate-out {
  animation: slideOutToLeft 0.25s ease-in;
  animation-fill-mode: both;
}

/* Estilos para o submenu dropdown */
.collapsed-submenu-dropdown {
  position: fixed;
  left: 5rem; /* 80px - largura do sidebar colapsado */
  background: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  min-width: 200px;
  max-width: 280px;
  width: auto;
  height: auto;
  max-height: calc(100vh - 100px); /* Altura máxima para não sair da tela */
  overflow-y: auto;
  z-index: 9999 !important;
  transform-origin: left center;
}

/* Estilos específicos para dropdowns com animação */
.dropdown-menu {
  position: fixed;
  background: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 9999 !important;
  transform-origin: left center;
  opacity: 0;
  transform: translateX(-20px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.dropdown-menu.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* Overlay para garantir que o dropdown fique por cima */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

/* Header do submenu */
.submenu-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 0.25rem;
}

/* Itens do submenu */
.submenu-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
  white-space: nowrap;
  min-height: 2.5rem; /* Altura mínima consistente */
}

.submenu-item:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.submenu-item.active {
  background-color: #eff6ff;
  color: #2563eb;
}

.submenu-item svg {
  width: 1rem;
  height: 1rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Estilos específicos para dropdowns dinâmicos */
.dynamic-dropdown {
  position: fixed;
  left: 5rem; /* 80px - largura do sidebar colapsado */
  background: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  min-width: 200px;
  max-width: 280px;
  width: auto;
  height: auto;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  z-index: 9999 !important;
  transform-origin: left center;
}

/* Scrollbar personalizada para o dropdown */
.dynamic-dropdown::-webkit-scrollbar {
  width: 4px;
}

.dynamic-dropdown::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.dynamic-dropdown::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.dynamic-dropdown::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Indicador visual para itens com submenu no modo colapsado */
.has-submenu-indicator {
  position: relative;
}

.has-submenu-indicator::after {
  content: '';
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid currentColor;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  opacity: 0.6;
}

