/* Estilos para o header */
:host {
  display: block;
  width: 100%;
}

/* Animação para o menu de perfil e notificações */
.profile-menu,
.notification-menu {
  transition: all 0.3s ease-in-out;
}

.profile-menu-enter,
.notification-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.profile-menu-enter-active,
.notification-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.profile-menu-exit,
.notification-menu-exit {
  opacity: 1;
  transform: translateY(0);
}

.profile-menu-exit-active,
.notification-menu-exit-active {
  opacity: 0;
  transform: translateY(-10px);
}

/* Efeito de transição para todos os elementos interativos */
:host ::ng-deep button,
:host ::ng-deep a {
  transition: all 0.2s ease-in-out;
}

/* Estilos para o indicador de notificações */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(220, 38, 38, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

/* Estilo para notificações não lidas */
.notification-unread {
  position: relative;
}

.notification-unread::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #3b82f6;
}

/* Estilos para os itens de notificação */
.notification-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;

  &:hover {
    background-color: #f9fafb;
  }

  &.unread {
    background-color: rgba(59, 130, 246, 0.05);

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: #3b82f6;
    }
  }
}

.notification-icon {
  flex-shrink: 0;
  margin-right: 12px;
  color: #6b7280;

  .unread & {
    color: #3b82f6;
  }
}

.notification-content {
  flex-grow: 1;
  min-width: 0; /* Para permitir que o texto seja truncado */
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;

  .unread & {
    font-weight: 600;
    color: #1f2937;
  }
}

.notification-message {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  word-break: break-word;
  white-space: normal;
}

.notification-time {
  font-size: 11px;
  color: #9ca3af;
}

.notification-status {
  flex-shrink: 0;
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3b82f6;
}

/* Estilos para o dropdown de notificações */
.notification-menu-container {
  position: relative;

  .absolute {
    max-height: 90vh;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .max-h-80 {
    max-height: 60vh; /* Aumenta a altura máxima para evitar overflow em telas menores */
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.5);
      border-radius: 3px;
    }
  }
}

/* Animação para o dropdown de notificações */
.notification-menu-container .absolute {
  animation: slideDown 0.2s ease-out;
  z-index: 50;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
