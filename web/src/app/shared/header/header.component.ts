import { Component, HostListener, OnInit, OnDestroy } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { SidebarService } from '../../core/services/sidebar.service';
import { NotificationService } from '../../core/services/notification.service';
import {
  UserNotificationService,
  UserNotification,
} from '../../core/services/user-notification.service';
import { Subscription, interval } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  standalone: true,
  imports: [CommonModule, RouterLink, DatePipe],
})
export class HeaderComponent implements OnInit, OnDestroy {
  isProfileMenuOpen = false;
  isNotificationMenuOpen = false;
  isSidebarOpen = true;

  // Notificações
  notifications: UserNotification[] = [];
  unreadNotificationsCount = 0;
  isLoadingNotifications = false;
  private notificationSubscription: Subscription | null = null;
  private pollingSubscription: Subscription | null = null;

  constructor(
    private router: Router,
    private sidebarService: SidebarService,
    private notificationService: NotificationService,
    private userNotificationService: UserNotificationService
  ) {
    this.sidebarService.isOpen$.subscribe((isOpen) => {
      this.isSidebarOpen = isOpen;
    });
  }

  ngOnInit(): void {
    // Inscrever-se no observable de notificações
    this.userNotificationService.notifications$.subscribe((notifications) => {
      this.notifications = notifications;
      this.updateUnreadCount();
    });

    // Carregar notificações iniciais
    this.loadNotifications();

    // Configurar polling para atualizar notificações periodicamente
    this.pollingSubscription = interval(30000)
      .pipe(switchMap(() => this.userNotificationService.getNotifications()))
      .subscribe();

    // Inscrever-se para novas notificações de UI
    this.subscribeToUINotifications();
  }

  ngOnDestroy(): void {
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }

    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
  }

  // Método para carregar notificações do usuário
  loadNotifications(): void {
    this.isLoadingNotifications = true;

    this.userNotificationService.getNotifications().subscribe({
      next: () => {
        this.isLoadingNotifications = false;
      },
      error: (error) => {
        console.error('Erro ao carregar notificações:', error);
        this.isLoadingNotifications = false;
        this.notificationService.error(
          'Não foi possível carregar as notificações'
        );
      },
    });
  }

  // Método para se inscrever em novas notificações de UI
  subscribeToUINotifications(): void {
    // Este método mantém a inscrição para notificações de UI (toast messages)
    this.notificationSubscription =
      this.notificationService.notifications$.subscribe();
  }

  // Atualizar contagem de notificações não lidas
  updateUnreadCount(): void {
    this.unreadNotificationsCount = this.notifications.filter(
      (n) => !n.read
    ).length;
  }

  // Marcar uma notificação como lida
  markAsRead(notification: UserNotification): void {
    if (!notification.read) {
      // Otimismo UI: atualizar localmente primeiro
      notification.read = true;
      this.updateUnreadCount();

      // Enviar atualização para a API
      this.userNotificationService.markAsRead(notification.id).subscribe(
        () => {
          // Sucesso - já atualizamos a UI
        },
        (error) => {
          console.error(
            `Erro ao marcar notificação ${notification.id} como lida:`,
            error
          );
          // Reverter mudança em caso de erro
          notification.read = false;
          this.updateUnreadCount();
          this.notificationService.error(
            'Não foi possível marcar a notificação como lida'
          );
        }
      );
    }
  }

  // Marcar todas as notificações como lidas
  markAllAsRead(): void {
    // Otimismo UI: atualizar localmente primeiro
    const previousState = this.notifications.map((n) => ({ ...n }));

    this.notifications.forEach((notification) => {
      notification.read = true;
    });
    this.updateUnreadCount();

    // Enviar atualização para a API
    this.userNotificationService.markAllAsRead().subscribe(
      (result) => {
        console.log(`${result.count} notificações marcadas como lidas`);
      },
      (error) => {
        console.error(
          'Erro ao marcar todas as notificações como lidas:',
          error
        );
        // Reverter mudanças em caso de erro
        this.notifications = previousState;
        this.updateUnreadCount();
        this.notificationService.error(
          'Não foi possível marcar todas as notificações como lidas'
        );
      }
    );
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Verificar se o clique foi fora do menu de perfil
    const target = event.target as HTMLElement;
    if (!target.closest('.profile-menu-container') && this.isProfileMenuOpen) {
      this.isProfileMenuOpen = false;
    }

    // Verificar se o clique foi fora do menu de notificações
    if (
      !target.closest('.notification-menu-container') &&
      this.isNotificationMenuOpen
    ) {
      this.isNotificationMenuOpen = false;
    }
  }

  toggleSidebar(): void {
    this.sidebarService.toggle();
  }

  toggleSidebarCollapse(): void {
    this.sidebarService.toggleCollapse();
  }

  toggleProfileMenu(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation(); // Impedir que o clique se propague para o documento
    }
    this.isProfileMenuOpen = !this.isProfileMenuOpen;

    // Fechar o menu de notificações se estiver aberto
    if (this.isNotificationMenuOpen) {
      this.isNotificationMenuOpen = false;
    }
  }

  toggleNotificationMenu(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation(); // Impedir que o clique se propague para o documento
    }
    this.isNotificationMenuOpen = !this.isNotificationMenuOpen;

    // Se estiver abrindo o menu, recarregar as notificações
    if (this.isNotificationMenuOpen) {
      this.loadNotifications();
    }

    // Fechar o menu de perfil se estiver aberto
    if (this.isProfileMenuOpen) {
      this.isProfileMenuOpen = false;
    }
  }

  // Método para obter o ícone apropriado com base no tipo de notificação
  getNotificationIcon(type: string): string {
    switch (type) {
      case 'TASK_ASSIGNED':
      case 'TAREFA_ATRIBUÍDA':
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                </svg>`;
      case 'TASK_REMINDER':
      case 'LEMBRETE_DE_TAREFA':
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>`;
      case 'TASK_UPDATED':
      case 'TAREFA_ATUALIZADA':
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>`;
      case 'TASK_COMPLETED':
      case 'TAREFA_CONCLUÍDA':
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>`;
      case 'APPOINTMENT':
      case 'AGENDAMENTO':
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>`;
      default:
        return `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>`;
    }
  }

  logout(): void {
    // Aqui você implementaria a lógica real de logout
    // Por exemplo, limpar tokens de autenticação, etc.
    console.log('Logout realizado');

    // Fechar o menu de perfil
    this.isProfileMenuOpen = false;

    // Redirecionar para a página de login
    this.router.navigate(['/auth/login']);
  }
}
