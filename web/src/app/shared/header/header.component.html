<div
  class="flex justify-between items-center bg-white h-16 px-4 md:px-6 w-full"
>
  <div class="flex items-center">
    <!-- <PERSON><PERSON><PERSON> de toggle do sidebar para desktop -->
    <button
      (click)="toggleSidebarCollapse()"
      class="hidden lg:flex p-2 rounded-lg text-gray-600 hover:bg-gray-100 focus:outline-none transition-colors mr-2"
      aria-label="Toggle sidebar collapse"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h16"
        />
      </svg>
    </button>

    <!-- T<PERSON><PERSON><PERSON> da página atual (opcional) -->
    <h1 class="ml-4 text-lg font-medium text-gray-800">CRM Odonto</h1>
  </div>

  <div class="flex items-center">
    <!-- Dropdown de notificações -->
    <div class="relative notification-menu-container mr-4">
      <button
        (click)="toggleNotificationMenu($event)"
        class="flex items-center text-gray-700 focus:outline-none hover:text-primary-600 transition-colors rounded-full p-1.5 hover:bg-gray-100 relative"
        aria-label="Notificações"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
          />
        </svg>
        <!-- Indicador de notificações não lidas -->
        <span
          *ngIf="unreadNotificationsCount > 0"
          class="absolute top-1 right-1 bg-red-500 text-white text-xs font-bold rounded-full h-4 w-4 flex items-center justify-center"
        >
          {{ unreadNotificationsCount > 9 ? "9+" : unreadNotificationsCount }}
        </span>
      </button>

      <!-- Menu dropdown de notificações -->
      <div
        *ngIf="isNotificationMenuOpen"
        class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-10"
      >
        <div
          class="px-4 py-2 border-b border-gray-100 flex justify-between items-center"
        >
          <h3 class="text-sm font-medium text-gray-700">Notificações</h3>
          <span
            *ngIf="unreadNotificationsCount > 0"
            class="text-xs bg-red-500 text-white px-2 py-0.5 rounded-full"
          >
            {{ unreadNotificationsCount }} não lida{{
              unreadNotificationsCount !== 1 ? "s" : ""
            }}
          </span>
        </div>

        <!-- Indicador de carregamento -->
        <div *ngIf="isLoadingNotifications" class="py-4 text-center">
          <div
            class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"
          ></div>
          <p class="text-sm text-gray-500 mt-2">Carregando notificações...</p>
        </div>

        <div *ngIf="!isLoadingNotifications" class="max-h-80 overflow-y-auto">
          <!-- Lista de notificações -->
          <div *ngIf="notifications.length > 0">
            <div
              *ngFor="let notification of notifications"
              class="notification-item"
              [class.unread]="!notification.read"
              (click)="markAsRead(notification)"
            >
              <div
                class="notification-icon"
                [innerHTML]="getNotificationIcon(notification.type)"
              ></div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">
                  {{ notification.message }}
                </div>
                <div class="notification-time">
                  {{ notification.createdAt | date : "dd/MM/yyyy HH:mm" }}
                </div>
              </div>
              <div class="notification-status">
                <span
                  class="unread-indicator"
                  *ngIf="!notification.read"
                ></span>
              </div>
            </div>
          </div>

          <!-- Mensagem quando não há notificações -->
          <div
            *ngIf="notifications.length === 0"
            class="px-4 py-3 text-sm text-gray-500 text-center"
          >
            Nenhuma notificação no momento
          </div>
        </div>

        <div class="border-t border-gray-100 px-4 py-2">
          <button
            *ngIf="unreadNotificationsCount > 0"
            (click)="markAllAsRead()"
            class="text-xs text-blue-600 hover:text-blue-800 w-full text-center"
          >
            Marcar todas como lidas
          </button>
          <button
            class="text-xs text-blue-600 hover:text-blue-800 w-full text-center mt-1"
          >
            Ver todas
          </button>
        </div>
      </div>
    </div>

    <div class="relative profile-menu-container">
      <button
        (click)="toggleProfileMenu($event)"
        class="flex items-center text-gray-700 focus:outline-none hover:text-primary-600 transition-colors rounded-full p-1.5 hover:bg-gray-100"
      >
        <span class="mr-2">Admin</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
            clip-rule="evenodd"
          />
        </svg>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Menu dropdown -->
      <div
        *ngIf="isProfileMenuOpen"
        class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10"
      >
        <a
          routerLink="/profile"
          class="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Meu Perfil</span>
          </div>
        </a>
        <a
          routerLink="/settings"
          class="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372-.836 2.942.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Configurações</span>
          </div>
        </a>
        <div class="border-t border-gray-100 my-1"></div>
        <button
          (click)="logout()"
          class="block w-full text-left px-4 py-2.5 text-sm text-red-600 hover:bg-gray-100 transition-colors"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L12 12.586V8z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Sair</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>
