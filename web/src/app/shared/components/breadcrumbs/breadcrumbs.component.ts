import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd, ActivatedRoute, RouterLink } from '@angular/router';
import { filter } from 'rxjs/operators';

interface Breadcrumb {
  label: string;
  url: string;
  isLast: boolean;
}

@Component({
  selector: 'app-breadcrumbs',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <nav aria-label="Breadcrumb">
      <ol class="flex flex-wrap items-center space-x-2 text-sm">
        <li>
          <a routerLink="/" class="text-gray-500 hover:text-blue-600 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </a>
        </li>
        <li *ngFor="let breadcrumb of breadcrumbs">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <a
              *ngIf="!breadcrumb.isLast"
              [routerLink]="breadcrumb.url"
              class="ml-2 text-gray-500 hover:text-blue-600 transition-colors"
            >
              {{ breadcrumb.label }}
            </a>
            <span
              *ngIf="breadcrumb.isLast"
              class="ml-2 text-gray-800 font-medium"
            >
              {{ breadcrumb.label }}
            </span>
          </div>
        </li>
      </ol>
    </nav>
  `,
})
export class BreadcrumbsComponent implements OnInit {
  breadcrumbs: Breadcrumb[] = [];

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {}

  ngOnInit() {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
      });

    // Initialize breadcrumbs on component load
    this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
  }

  private createBreadcrumbs(route: ActivatedRoute, url: string = '', breadcrumbs: Breadcrumb[] = []): Breadcrumb[] {
    const children: ActivatedRoute[] = route.children;

    if (children.length === 0) {
      return breadcrumbs;
    }

    for (const child of children) {
      const routeURL: string = child.snapshot.url.map(segment => segment.path).join('/');
      if (routeURL !== '') {
        url += `/${routeURL}`;
      }

      // Get the route data
      const label = child.snapshot.data['breadcrumb'] || this.getDefaultLabel(routeURL);

      if (label) {
        // Mark the last breadcrumb
        const isLast = Object.is(child, children[children.length - 1]) && child.children.length === 0;

        breadcrumbs.push({
          label,
          url,
          isLast
        });
      }

      return this.createBreadcrumbs(child, url, breadcrumbs);
    }

    return breadcrumbs;
  }

  private getDefaultLabel(path: string): string {
    if (!path) return '';

    // Map routes to human-readable labels
    const routeLabels: { [key: string]: string } = {
      'patients': 'Pacientes',
      'schedulings': 'Agendamentos',
      'treatments': 'Tratamentos',
      'reports': 'Relatórios',
      'medical-records': 'Prontuários',
      'dentists': 'Dentistas',
      'schedule': 'Agenda',
      'crc': 'CRC',
      'employees': 'Funcionários',
      'tasks': 'Demandas',
      'settings': 'Configurações',
      'patient-types': 'Tipos de Paciente',
      'treatment-board': 'Board de Atendimentos',
      'treatment-flow': 'Fluxo de Tratamento',
      'new': 'Novo',
      'edit': 'Editar',
      'view': 'Visualizar',
      'detail': 'Detalhes'
    };

    // Check if we have a predefined label for this path
    if (routeLabels[path]) {
      return routeLabels[path];
    }

    // If not, format the path as a label (capitalize first letter)
    return path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' ');
  }
}
