import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClickOutsideDirective } from '../../directives/click-outside.directive';

export interface SelectOption {
  value: any;
  label: string;
  description?: string;
}

@Component({
  selector: 'app-searchable-select',
  standalone: true,
  imports: [CommonModule, FormsModule, ClickOutsideDirective],
  templateUrl: './searchable-select.component.html',
  styleUrl: './searchable-select.component.scss'
})
export class SearchableSelectComponent implements OnInit {
  @Input() options: SelectOption[] = [];
  @Input() placeholder: string = 'Selecione uma opção';
  @Input() value: any = null;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() invalid: boolean = false;
  @Input() touched: boolean = false;
  @Input() searchPlaceholder: string = 'Pesquisar...';
  @Input() noResultsText: string = 'Nenhum resultado encontrado';
  @Input() labelKey: string = 'label';
  @Input() valueKey: string = 'value';

  @Output() valueChange = new EventEmitter<any>();
  @Output() blur = new EventEmitter<void>();

  @ViewChild('searchInput') searchInput!: ElementRef;

  isOpen: boolean = false;
  searchText: string = '';
  filteredOptions: SelectOption[] = [];
  selectedOption: SelectOption | null = null;

  ngOnInit(): void {
    this.filteredOptions = [...this.options];
    this.updateSelectedOption();
  }

  ngOnChanges(): void {
    this.updateSelectedOption();
  }

  updateSelectedOption(): void {
    if (this.value !== null && this.value !== undefined) {
      this.selectedOption = this.options.find(option => option.value === this.value) || null;
    } else {
      this.selectedOption = null;
    }
  }

  toggleDropdown(): void {
    if (this.disabled) return;

    this.isOpen = !this.isOpen;
    this.searchText = '';
    this.filteredOptions = [...this.options];

    if (this.isOpen) {
      setTimeout(() => {
        this.searchInput?.nativeElement.focus();
      }, 0);
    }
  }

  closeDropdown(): void {
    this.isOpen = false;
    this.blur.emit();
  }

  onSearch(): void {
    if (!this.searchText.trim()) {
      this.filteredOptions = [...this.options];
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredOptions = this.options.filter(option =>
      option.label.toLowerCase().includes(searchLower) ||
      (option.description && option.description.toLowerCase().includes(searchLower))
    );
  }

  selectOption(option: SelectOption): void {
    this.selectedOption = option;
    this.value = option.value;
    this.searchText = ''; // Clear search text after selection
    this.valueChange.emit(option.value);

    // Ensure dropdown is closed after selection
    setTimeout(() => {
      this.closeDropdown();
    }, 0);

    console.log('Option selected:', option);
  }

  clearSelection(event: Event): void {
    event.stopPropagation();
    this.selectedOption = null;
    this.value = null;
    this.searchText = '';
    this.valueChange.emit(null);
    this.filteredOptions = [...this.options];

    console.log('Selection cleared');
  }

  getDisplayValue(): string {
    return this.selectedOption ? this.selectedOption.label : this.placeholder;
  }

  highlightMatch(text: string): string {
    if (!this.searchText.trim() || !text) {
      return text;
    }

    const searchRegex = new RegExp(`(${this.escapeRegExp(this.searchText)})`, 'gi');
    return text.replace(searchRegex, '<span class="bg-yellow-200">$1</span>');
  }

  escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  onClickOutside(): void {
    if (this.isOpen) {
      this.closeDropdown();
    }
  }
}
