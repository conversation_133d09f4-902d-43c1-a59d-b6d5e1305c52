<div class="relative w-full bg-white" appClickOutside (clickOutside)="onClickOutside()">
  <!-- Search input or Selected Value Display -->
  <div class="relative">
    <input
      *ngIf="isOpen"
      #searchInput
      type="text"
      [(ngModel)]="searchText"
      (ngModelChange)="onSearch()"
      [placeholder]="searchPlaceholder"
      [disabled]="disabled"
      [class.cursor-not-allowed]="disabled"
      [class.bg-gray-100]="disabled"
      [class.border-red-500]="invalid && touched"
      class="bg-white w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
    />

    <!-- Selected value display when dropdown is closed -->
    <div
      *ngIf="!isOpen"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 cursor-pointer flex items-center justify-between relative"
      [class.bg-gray-100]="disabled"
      [class.cursor-not-allowed]="disabled"
      [class.border-red-500]="invalid && touched"
    >
      <!-- Value text (clickable area) -->
      <div
        class="flex-grow truncate pr-8"
        (click)="toggleDropdown()"
      >
        <span [class.text-gray-400]="!selectedOption">
          {{ selectedOption ? selectedOption.label : placeholder }}
        </span>
      </div>

      <!-- Clear button (when value is selected) -->
      <button
        *ngIf="selectedOption && !disabled"
        type="button"
        class="absolute right-8 text-gray-400 hover:text-gray-600"
        (click)="clearSelection($event)"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>

      <!-- Dropdown arrow -->
      <div
        class="absolute right-2"
        (click)="toggleDropdown()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </div>
    </div>

    <!-- Clear button (only when dropdown is open) -->
    <button
      *ngIf="isOpen && selectedOption && searchText"
      type="button"
      class="absolute right-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
      (click)="clearSelection($event)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>

    <!-- Close button (only when dropdown is open) -->
    <button
      *ngIf="isOpen"
      type="button"
      class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
      (click)="closeDropdown()"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <!-- Dropdown -->
  <div
    *ngIf="isOpen"
    class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
  >
    <!-- Options list -->
    <div class="py-1">
      <div
        *ngFor="let option of filteredOptions"
        (click)="selectOption(option)"
        class="px-3 py-2 cursor-pointer hover:bg-gray-100"
        [class.bg-blue-50]="selectedOption?.value === option.value"
      >
        <div [innerHTML]="highlightMatch(option.label)"></div>
        <div *ngIf="option.description" class="text-xs text-gray-500 mt-0.5" [innerHTML]="highlightMatch(option.description)"></div>
      </div>

      <!-- No results message -->
      <div *ngIf="filteredOptions.length === 0" class="px-3 py-2 text-gray-500 text-center">
        {{ noResultsText }}
      </div>
    </div>
  </div>
</div>
