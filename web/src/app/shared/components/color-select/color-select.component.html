<div *ngIf="isOpen" class="fixed inset-0 z-40" (click)="closeDropdown()"></div>
<div class="color-select-container w-full min-w-[220px]">
  <!-- Label -->
  <label *ngIf="label" class="block text-sm font-medium text-gray-700 mb-2">
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Select Button -->
  <div class="relative w-full overflow-visible">
    <button
      type="button"
      [disabled]="disabled"
      (click)="toggleDropdown()"
      class="relative w-full h-11 cursor-pointer rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500"
      [class.border-red-300]="required && !selectedColor"
    >
      <div class="flex items-center h-full">
        <!-- Color Preview -->
        <div
          class="h-5 w-8 rounded border border-gray-200 mr-3 flex-shrink-0"
          [style.background-color]="selectedColorHex"
        ></div>

        <!-- Color Name -->
        <span
          class="block truncate flex-1 min-w-0"
          [class.text-gray-500]="!selectedColor"
        >
          {{ selectedColorName }}
        </span>
      </div>

      <!-- Arrow Icon -->
      <span
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
      >
        <svg
          class="h-5 w-5 text-gray-400 transition-transform duration-200"
          [class.rotate-180]="isOpen"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </span>
    </button>

    <!-- Dropdown -->
    <div
      *ngIf="isOpen"
      class="absolute z-[999] mt-1 w-full rounded-md bg-white shadow-xl border border-gray-200 max-h-60"
      style="overflow-y: auto; -webkit-overflow-scrolling: touch"
    >
      <div class="py-1">
        <div
          *ngFor="let color of colors; trackBy: trackByColorId"
          (click)="selectColor(color)"
          class="color-option cursor-pointer relative py-2 px-3 hover:bg-gray-50 transition-colors duration-150"
          [style.background-color]="color.hex"
          role="option"
          [attr.aria-selected]="selectedColor?.id === color.id"
        >
          <!-- Color overlay for text readability -->
          <div
            class="absolute inset-0 bg-black bg-opacity-20 pointer-events-none"
          ></div>

          <!-- Color name with white text and shadow for readability -->
          <div
            class="relative z-10 flex items-center justify-between pointer-events-none"
          >
            <span class="text-white font-medium drop-shadow-lg">
              {{ color.name }}
            </span>

            <!-- Selected indicator -->
            <svg
              *ngIf="selectedColor?.id === color.id"
              class="h-5 w-5 text-white drop-shadow-lg"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Validation Error -->
  <p *ngIf="required && !selectedColor" class="mt-1 text-sm text-red-600">
    Este campo é obrigatório
  </p>
</div>
