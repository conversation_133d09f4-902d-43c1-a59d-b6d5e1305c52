/* Estilos para o color select */

.color-select-container {
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Garantir largura consistente do botão */
button[type="button"] {
  min-height: 44px;
  width: 100% !important;

  &:focus {
    width: 100% !important;
  }

  &:hover {
    width: 100% !important;
  }
}

/* Animações para o dropdown */
.absolute.z-50 {
  animation: slideDown 0.2s ease-out;
  width: 100% !important;
  min-width: 100%;
  max-width: 100%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  left: 0 !important;
  right: 0 !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilo para as opções de cor */
.color-option {
  position: relative;
  min-height: 40px;
  display: flex;
  align-items: center;

  &:hover {
    .absolute {
      background-color: rgba(0, 0, 0, 0.3) !important;
    }
  }

  &:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
  }

  &:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }
}

/* Scrollbar personalizada para o dropdown */
.overflow-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;

    &:hover {
      background-color: rgba(156, 163, 175, 0.7);
    }
  }
}

/* Transições suaves */
button {
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/* Estilo para o ícone de seta */
svg {
  transition: transform 0.2s ease-in-out;
}

/* Responsividade */
@media (max-width: 640px) {
  .color-option {
    min-height: 48px; /* Maior área de toque em mobile */
    padding: 1rem;
    font-size: 1.1rem;
  }
  .color-select-container {
    font-size: 1.1rem;
  }
}
