import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface ColorOption {
  id: number;
  name: string;
  hex: string;
}

@Component({
  selector: 'app-color-select',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './color-select.component.html',
  styleUrl: './color-select.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ColorSelectComponent),
      multi: true,
    },
  ],
})
export class ColorSelectComponent implements ControlValueAccessor {
  @Input() colors: ColorOption[] = [];
  @Input() placeholder = 'Selecione uma cor';
  @Input() disabled = false;
  @Input() required = false;
  @Input() label = '';

  @Output() colorChange = new EventEmitter<ColorOption | null>();

  selectedColor: ColorOption | null = null;
  isOpen = false;

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  writeValue(value: any): void {
    if (value) {
      this.selectedColor =
        this.colors.find((color) => color.hex === value) || null;
    } else {
      this.selectedColor = null;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
      }
    }
  }

  selectColor(color: ColorOption): void {
    this.selectedColor = color;
    this.isOpen = false;
    this.onChange(color.hex);
    this.colorChange.emit(color);
  }

  closeDropdown(): void {
    this.isOpen = false;
  }

  get selectedColorName(): string {
    return this.selectedColor?.name || this.placeholder;
  }

  get selectedColorHex(): string {
    return this.selectedColor?.hex || '#f3f4f6';
  }

  trackByColorId(index: number, color: ColorOption): number {
    return color.id;
  }
}
