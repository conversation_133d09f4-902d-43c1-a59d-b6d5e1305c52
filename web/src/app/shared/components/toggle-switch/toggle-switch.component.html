<div class="flex items-center space-x-3">
  <!-- Switch -->
  <button
    type="button"
    [class]="switchClasses"
    [disabled]="disabled"
    (click)="onToggle()"
    [attr.aria-checked]="checked"
    [attr.aria-labelledby]="label ? 'switch-label' : null"
    role="switch"
  >
    <span [class]="thumbClasses"></span>
  </button>

  <!-- Label -->
  <span
    *ngIf="showLabel && label"
    id="switch-label"
    [class]="labelClasses"
    (click)="onToggle()"
    [style.cursor]="disabled ? 'not-allowed' : 'pointer'"
  >
    {{ label }}
  </span>
</div>
