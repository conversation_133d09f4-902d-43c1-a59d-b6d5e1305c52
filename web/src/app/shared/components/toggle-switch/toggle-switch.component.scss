/* Estilos específicos para o toggle switch */

/* Animações suaves para o switch */
button[role="switch"] {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
}

/* Animação do thumb */
span {
  transition: transform 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Estados de hover para o label */
span[id="switch-label"] {
  transition: color 0.2s ease-in-out;
  user-select: none;

  &:hover {
    color: #374151;
  }
}

/* Animação de loading (se necessário no futuro) */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsividade */
@media (max-width: 640px) {
  .flex {
    flex-direction: column;
    align-items: flex-start;
    space-x: 0;
    gap: 0.5rem;
  }
}
