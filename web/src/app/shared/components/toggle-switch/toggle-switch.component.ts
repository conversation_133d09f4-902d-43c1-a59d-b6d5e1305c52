import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Componente de switch toggle reutilizável
 * 
 * @example
 * ```html
 * <app-toggle-switch
 *   [checked]="isActive"
 *   [disabled]="isLoading"
 *   label="Ativo"
 *   size="md"
 *   (change)="onToggle($event)"
 * ></app-toggle-switch>
 * ```
 */
@Component({
  selector: 'app-toggle-switch',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toggle-switch.component.html',
  styleUrl: './toggle-switch.component.scss',
})
export class ToggleSwitchComponent {
  @Input() checked = false;
  @Input() disabled = false;
  @Input() label = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() color: 'blue' | 'green' | 'red' | 'orange' = 'green';
  @Input() showLabel = true;
  
  @Output() change = new EventEmitter<boolean>();

  onToggle(): void {
    if (!this.disabled) {
      this.change.emit(!this.checked);
    }
  }

  get switchClasses(): string {
    const baseClasses = 'relative inline-flex items-center cursor-pointer transition-colors duration-200 ease-in-out rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    // Size classes
    const sizeClasses = {
      sm: 'h-5 w-9',
      md: 'h-6 w-11',
      lg: 'h-7 w-14'
    };

    // Color classes
    const colorClasses = {
      blue: this.checked ? 'bg-blue-600 focus:ring-blue-500' : 'bg-gray-200',
      green: this.checked ? 'bg-green-600 focus:ring-green-500' : 'bg-gray-200',
      red: this.checked ? 'bg-red-600 focus:ring-red-500' : 'bg-gray-200',
      orange: this.checked ? 'bg-orange-600 focus:ring-orange-500' : 'bg-gray-200'
    };

    // Disabled classes
    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : '';

    return `${baseClasses} ${sizeClasses[this.size]} ${colorClasses[this.color]} ${disabledClasses}`;
  }

  get thumbClasses(): string {
    const baseClasses = 'pointer-events-none inline-block rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200';
    
    // Size classes for thumb
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    // Position classes
    const positionClasses = {
      sm: this.checked ? 'translate-x-4' : 'translate-x-0',
      md: this.checked ? 'translate-x-5' : 'translate-x-0',
      lg: this.checked ? 'translate-x-7' : 'translate-x-0'
    };

    return `${baseClasses} ${sizeClasses[this.size]} ${positionClasses[this.size]}`;
  }

  get labelClasses(): string {
    const baseClasses = 'text-sm font-medium';
    const colorClasses = this.disabled ? 'text-gray-400' : 'text-gray-700';
    return `${baseClasses} ${colorClasses}`;
  }
}
