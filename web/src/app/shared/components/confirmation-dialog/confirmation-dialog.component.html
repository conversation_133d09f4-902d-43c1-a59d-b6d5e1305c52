<!-- Overlay -->
<div *ngIf="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
  <!-- Backdrop -->
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>

  <!-- Dialog -->
  <div class="flex items-center justify-center min-h-screen p-4">
    <div
      class="bg-white rounded-lg shadow-xl max-w-md w-full mx-auto z-10 relative"
    >
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center">
        <div [class]="getIconClass()" [innerHTML]="getIcon()"></div>
        <h3 class="text-lg font-medium text-gray-900 ml-2">{{ title }}</h3>
      </div>

      <!-- Body -->
      <div class="px-6 py-4">
        <p class="text-gray-700">{{ message }}</p>
      </div>

      <!-- Footer -->
      <div
        class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3"
      >
        <button
          type="button"
          (click)="onCancel()"
          [disabled]="isLoading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ cancelButtonText }}
        </button>
        <button
          type="button"
          (click)="onConfirm()"
          [disabled]="isLoading"
          [class]="
            'px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ' +
            confirmButtonClass
          "
        >
          <span *ngIf="isLoading" class="flex items-center">
            <svg
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ loadingText }}
          </span>
          <span *ngIf="!isLoading">
            {{ confirmButtonText }}
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
