import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Componente de diálogo de confirmação reutilizável
 *
 * @example
 * ```html
 * <app-confirmation-dialog
 *   [isOpen]="showConfirmation"
 *   title="Excluir Item"
 *   message="Tem certeza que deseja excluir este item?"
 *   confirmButtonText="Excluir"
 *   cancelButtonText="Cancelar"
 *   confirmButtonClass="bg-red-600 hover:bg-red-700"
 *   type="danger"
 *   [isLoading]="isDeleting"
 *   loadingText="Excluindo..."
 *   (confirm)="confirmDelete()"
 *   (cancel)="closeConfirmation()"
 * ></app-confirmation-dialog>
 * ```
 */
@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './confirmation-dialog.component.html',
  styleUrl: './confirmation-dialog.component.scss',
})
export class ConfirmationDialogComponent {
  @Input() isOpen = false;
  @Input() title = 'Confirmação';
  @Input() message = 'Tem certeza que deseja realizar esta ação?';
  @Input() confirmButtonText = 'Confirmar';
  @Input() cancelButtonText = 'Cancelar';
  @Input() confirmButtonClass = 'bg-blue-600 hover:bg-blue-700';
  @Input() type: 'info' | 'warning' | 'danger' = 'info';
  @Input() isLoading = false;
  @Input() loadingText = 'Processando...';

  @Output() confirm = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  onConfirm(): void {
    if (!this.isLoading) {
      this.confirm.emit();
    }
  }

  onCancel(): void {
    if (!this.isLoading) {
      this.cancel.emit();
    }
  }

  getIconClass(): string {
    switch (this.type) {
      case 'warning':
        return 'text-yellow-500';
      case 'danger':
        return 'text-red-500';
      case 'info':
      default:
        return 'text-blue-500';
    }
  }

  getIcon(): string {
    switch (this.type) {
      case 'warning':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        `;
      case 'danger':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `;
      case 'info':
      default:
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `;
    }
  }
}
