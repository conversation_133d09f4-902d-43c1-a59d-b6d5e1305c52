/* Estilos para o componente de seleção de procedimentos */

/* Animação suave para o dropdown */
.dropdown-enter {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Estilo para truncar texto em múltiplas linhas */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effect para os itens do dropdown */
.dropdown-item {
  transition: all 0.15s ease-in-out;
}

.dropdown-item:hover {
  background-color: #eff6ff;
  transform: translateX(2px);
}

/* Estilo para as tags dos procedimentos selecionados */
.procedure-tag {
  transition: all 0.15s ease-in-out;
}

.procedure-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Animação para remoção de tags */
.tag-remove-btn {
  transition: all 0.15s ease-in-out;
}

.tag-remove-btn:hover {
  transform: scale(1.1);
}

/* Scrollbar customizada para o dropdown */
.dropdown-scroll::-webkit-scrollbar {
  width: 6px;
}

.dropdown-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.dropdown-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dropdown-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Estilo para o campo de input quando focado */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsividade para mobile */
@media (max-width: 640px) {
  .procedure-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .dropdown-item {
    padding: 0.75rem;
  }
}
