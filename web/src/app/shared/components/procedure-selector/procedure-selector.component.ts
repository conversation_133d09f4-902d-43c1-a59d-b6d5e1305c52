import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  forwardRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { ProcedureService } from '../../../core/services/procedure.service';
import { Procedure } from '../../../core/models/procedure.model';

@Component({
  selector: 'app-procedure-selector',
  templateUrl: './procedure-selector.component.html',
  styleUrls: ['./procedure-selector.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ProcedureSelectorComponent),
      multi: true,
    },
  ],
})
export class ProcedureSelectorComponent
  implements OnInit, ControlValueAccessor
{
  @Input() placeholder: string = 'Pesquisar procedimentos...';
  @Input() disabled: boolean = false;
  @Output() proceduresChange = new EventEmitter<Procedure[]>();

  searchTerm: string = '';
  allProcedures: Procedure[] = [];
  filteredProcedures: Procedure[] = [];
  selectedProcedures: Procedure[] = [];
  showDropdown: boolean = false;
  isLoading: boolean = false;

  // ControlValueAccessor
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor(private procedureService: ProcedureService) {}

  ngOnInit(): void {
    this.loadProcedures();
  }

  loadProcedures(): void {
    this.isLoading = true;
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.allProcedures = procedures.filter((p) => p.status === 'ACTIVE');
        this.isLoading = false;

        // Atualizar procedimentos selecionados se já houver IDs definidos
        const currentValue = this.selectedProcedures.map((p) => p.id);
        if (currentValue.length > 0) {
          this.writeValue(currentValue);
        }
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoading = false;
      },
    });
  }

  onSearchInput(): void {
    if (!this.searchTerm.trim()) {
      this.filteredProcedures = [];
      this.showDropdown = false;
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.filteredProcedures = this.allProcedures
      .filter(
        (procedure) =>
          !this.isProcedureSelected(procedure) &&
          (procedure.name.toLowerCase().includes(searchLower) ||
            procedure.description?.toLowerCase().includes(searchLower) ||
            procedure.type.toLowerCase().includes(searchLower))
      )
      .slice(0, 10); // Limitar a 10 resultados

    this.showDropdown = this.filteredProcedures.length > 0;
  }

  onSearchFocus(): void {
    if (this.searchTerm.trim()) {
      this.onSearchInput();
    }
  }

  onSearchBlur(): void {
    // Delay para permitir clique nos itens do dropdown
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

  selectProcedure(procedure: Procedure): void {
    if (!this.isProcedureSelected(procedure)) {
      this.selectedProcedures.push(procedure);
      this.searchTerm = '';
      this.showDropdown = false;
      this.filteredProcedures = [];
      this.emitChange();
    }
  }

  removeProcedure(procedure: Procedure): void {
    const index = this.selectedProcedures.findIndex(
      (p) => p.id === procedure.id
    );
    if (index > -1) {
      this.selectedProcedures.splice(index, 1);
      this.emitChange();
    }
  }

  isProcedureSelected(procedure: Procedure): boolean {
    return this.selectedProcedures.some((p) => p.id === procedure.id);
  }

  getTotalDuration(): number {
    return this.selectedProcedures.reduce(
      (total, p) => total + p.estimatedDuration,
      0
    );
  }

  getTotalPrice(): number {
    return this.selectedProcedures.reduce(
      (total, p) => total + p.defaultPrice,
      0
    );
  }

  private emitChange(): void {
    this.proceduresChange.emit([...this.selectedProcedures]);
    this.onChange(this.selectedProcedures.map((p) => p.id));
    this.onTouched();
  }

  // ControlValueAccessor implementation
  writeValue(value: number[]): void {
    if (value && Array.isArray(value) && this.allProcedures.length > 0) {
      this.selectedProcedures = this.allProcedures.filter((p) =>
        value.includes(p.id)
      );
    } else {
      this.selectedProcedures = [];
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  getTypeLabel(type: string): string {
    const typeLabels: { [key: string]: string } = {
      CLINICAL: 'Clínico',
      SURGICAL: 'Cirúrgico',
      AESTHETIC: 'Estético',
      ORTHODONTIC: 'Ortodôntico',
      ENDODONTIC: 'Endodôntico',
      PERIODONTIC: 'Periodontal',
      PEDIATRIC: 'Pediátrico',
      RADIOLOGY: 'Radiologia',
      PROSTHODONTIC: 'Protético',
    };
    return typeLabels[type] || type;
  }

  getTypeColor(type: string): string {
    const typeColors: { [key: string]: string } = {
      CLINICAL: 'bg-blue-100 text-blue-800',
      SURGICAL: 'bg-red-100 text-red-800',
      AESTHETIC: 'bg-pink-100 text-pink-800',
      ORTHODONTIC: 'bg-purple-100 text-purple-800',
      ENDODONTIC: 'bg-orange-100 text-orange-800',
      PERIODONTIC: 'bg-green-100 text-green-800',
      PEDIATRIC: 'bg-yellow-100 text-yellow-800',
      RADIOLOGY: 'bg-gray-100 text-gray-800',
      PROSTHODONTIC: 'bg-indigo-100 text-indigo-800',
    };
    return typeColors[type] || 'bg-gray-100 text-gray-800';
  }
}
