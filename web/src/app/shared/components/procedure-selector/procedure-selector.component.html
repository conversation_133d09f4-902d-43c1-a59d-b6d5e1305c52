<div class="space-y-3">
  <!-- Campo de pesquisa -->
  <div class="relative">
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (input)="onSearchInput()"
        (focus)="onSearchFocus()"
        (blur)="onSearchBlur()"
        [placeholder]="placeholder"
        [disabled]="disabled"
        class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
        [class.bg-gray-100]="disabled"
        [class.cursor-not-allowed]="disabled"
      />

      <!-- Ícone de pesquisa -->
      <div
        class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
      >
        <svg
          *ngIf="!isLoading"
          class="h-4 w-4 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>

        <!-- Loading spinner -->
        <svg
          *ngIf="isLoading"
          class="animate-spin h-4 w-4 text-blue-500"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
    </div>

    <!-- Dropdown de resultados -->
    <div
      *ngIf="showDropdown && filteredProcedures.length > 0"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
    >
      <ul class="py-1">
        <li
          *ngFor="let procedure of filteredProcedures"
          (mousedown)="selectProcedure(procedure)"
          class="px-3 py-3 cursor-pointer hover:bg-blue-50 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ procedure.name }}
              </p>
              <p
                *ngIf="procedure.description"
                class="text-xs text-gray-500 mt-1 line-clamp-2"
              >
                {{ procedure.description }}
              </p>
              <div class="flex items-center mt-2 space-x-2">
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  [class]="getTypeColor(procedure.type)"
                >
                  {{ getTypeLabel(procedure.type) }}
                </span>
                <span class="text-xs text-gray-500">
                  {{ procedure.estimatedDuration }} min
                </span>
              </div>
            </div>
            <div class="ml-3 text-right flex-shrink-0">
              <p class="text-sm font-medium text-gray-900">
                R$ {{ procedure.defaultPrice | number : "1.2-2" }}
              </p>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <!-- Mensagem quando não há resultados -->
    <div
      *ngIf="
        showDropdown && filteredProcedures.length === 0 && searchTerm.trim()
      "
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
    >
      <div class="px-3 py-3 text-sm text-gray-500 text-center">
        Nenhum procedimento encontrado
      </div>
    </div>
  </div>

  <!-- Tags dos procedimentos selecionados -->
  <div *ngIf="selectedProcedures.length > 0" class="space-y-2">
    <div class="flex flex-wrap gap-2">
      <div
        *ngFor="let procedure of selectedProcedures"
        class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200"
      >
        <span class="mr-2">{{ procedure.name }}</span>
        <button
          type="button"
          (click)="removeProcedure(procedure)"
          class="flex-shrink-0 ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none focus:bg-blue-200 focus:text-blue-600 transition-colors duration-150"
          [disabled]="disabled"
        >
          <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Resumo dos procedimentos -->
    <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
      <div class="flex items-center justify-between text-sm">
        <div class="flex items-center space-x-4">
          <span class="text-gray-600">
            <span class="font-medium">Total:</span>
            {{ selectedProcedures.length }} procedimento(s)
          </span>
          <span class="text-gray-600">
            <span class="font-medium">Duração:</span>
            {{ getTotalDuration() }} min
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
