import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreatmentProcedure, TreatmentProcedureStatus } from '../../../core/models/treatment-procedure.model';

export interface ToothData {
  number: string;
  status: 'pending' | 'completed' | 'none';
  procedures: TreatmentProcedure[];
}

export interface ToothPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

@Component({
  selector: 'app-tooth-diagram',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tooth-diagram.component.html',
  styleUrls: ['./tooth-diagram.component.scss']
})
export class ToothDiagramComponent implements OnInit, OnChanges {
  @Input() procedures: TreatmentProcedure[] = [];
  @Output() toothSelected = new EventEmitter<string>();

  // Mapa de dentes com seus procedimentos e status
  teethMap: Map<string, ToothData> = new Map();

  // Arrays para cada quadrante
  quadrant1: ToothData[] = []; // 18-11
  quadrant2: ToothData[] = []; // 21-28
  quadrant3: ToothData[] = []; // 38-31
  quadrant4: ToothData[] = []; // 41-48

  // Posições dos dentes no SVG
  toothPositions: { [key: string]: ToothPosition } = {
    // Quadrante 1 (18-11)
    '18': { x: 95, y: 0, width: 83, height: 110 },
    '17': { x: 159.6, y: 0, width: 83, height: 110 },
    '16': { x: 224.2, y: 0, width: 83, height: 110 },
    '15': { x: 288.8, y: 0, width: 83, height: 110 },
    '14': { x: 353.4, y: 0, width: 83, height: 110 },
    '13': { x: 402.8, y: 0, width: 83, height: 110 },
    '12': { x: 452.2, y: 0, width: 83, height: 110 },
    '11': { x: 501.6, y: 0, width: 83, height: 110 },

    // Quadrante 2 (21-28)
    '21': { x: 581.4, y: 0, width: 83, height: 110 },
    '22': { x: 630.8, y: 0, width: 83, height: 110 },
    '23': { x: 680.2, y: 0, width: 83, height: 110 },
    '24': { x: 744.8, y: 0, width: 83, height: 110 },
    '25': { x: 809.4, y: 0, width: 83, height: 110 },
    '26': { x: 874, y: 0, width: 83, height: 110 },
    '27': { x: 938.6, y: 0, width: 83, height: 110 },
    '28': { x: 1003.2, y: 0, width: 83, height: 110 },

    // Quadrante 3 (38-31)
    '38': { x: 1003.2, y: 443.3333333333333, width: 83, height: 110 },
    '37': { x: 938.6, y: 443.3333333333333, width: 83, height: 110 },
    '36': { x: 874, y: 443.3333333333333, width: 83, height: 110 },
    '35': { x: 809.4, y: 443.3333333333333, width: 83, height: 110 },
    '34': { x: 744.8, y: 443.3333333333333, width: 83, height: 110 },
    '33': { x: 680.2, y: 443.3333333333333, width: 83, height: 110 },
    '32': { x: 630.8, y: 443.3333333333333, width: 83, height: 110 },
    '31': { x: 581.4, y: 443.3333333333333, width: 83, height: 110 },

    // Quadrante 4 (41-48)
    '41': { x: 501.6, y: 443.3333333333333, width: 83, height: 110 },
    '42': { x: 452.2, y: 443.3333333333333, width: 83, height: 110 },
    '43': { x: 402.8, y: 443.3333333333333, width: 83, height: 110 },
    '44': { x: 353.4, y: 443.3333333333333, width: 83, height: 110 },
    '45': { x: 288.8, y: 443.3333333333333, width: 83, height: 110 },
    '46': { x: 224.2, y: 443.3333333333333, width: 83, height: 110 },
    '47': { x: 159.6, y: 443.3333333333333, width: 83, height: 110 },
    '48': { x: 95, y: 443.3333333333333, width: 83, height: 110 }
  };

  // Posições das visualizações oclusal
  occlusalPositions: { [key: string]: ToothPosition } = {
    // Quadrante 1 (18-11)
    '18': { x: 115, y: 102, width: 55, height: 65 },
    '17': { x: 179.6, y: 102, width: 55, height: 65 },
    '16': { x: 244.2, y: 102, width: 55, height: 65 },
    '15': { x: 308.8, y: 107, width: 41, height: 65 },
    '14': { x: 373.4, y: 107, width: 41, height: 65 },
    '13': { x: 422.8, y: 107, width: 41, height: 65 },
    '12': { x: 472.2, y: 107, width: 41, height: 65 },
    '11': { x: 521.6, y: 107, width: 41, height: 65 },

    // Quadrante 2 (21-28)
    '21': { x: 601.4, y: 107, width: 41, height: 65 },
    '22': { x: 650.8, y: 107, width: 41, height: 65 },
    '23': { x: 700.2, y: 107, width: 41, height: 65 },
    '24': { x: 764.8, y: 107, width: 41, height: 65 },
    '25': { x: 829.4, y: 107, width: 41, height: 65 },
    '26': { x: 894, y: 102, width: 55, height: 65 },
    '27': { x: 958.6, y: 102, width: 55, height: 65 },
    '28': { x: 1023.2, y: 102, width: 55, height: 65 },

    // Quadrante 4 (41-48) - Oclusal inferior
    '48': { x: 115, y: 379.3333333333333, width: 55, height: 65 },
    '47': { x: 179.6, y: 379.3333333333333, width: 55, height: 65 },
    '46': { x: 244.2, y: 379.3333333333333, width: 55, height: 65 },
    '45': { x: 308.8, y: 387.3333333333333, width: 41, height: 65 },
    '44': { x: 373.4, y: 387.3333333333333, width: 41, height: 65 },
    '43': { x: 422.8, y: 387.3333333333333, width: 41, height: 65 },
    '42': { x: 472.2, y: 387.3333333333333, width: 41, height: 65 },
    '41': { x: 521.6, y: 387.3333333333333, width: 41, height: 65 },

    // Quadrante 3 (38-31) - Oclusal inferior
    '31': { x: 601.4, y: 387.3333333333333, width: 41, height: 65 },
    '32': { x: 650.8, y: 387.3333333333333, width: 41, height: 65 },
    '33': { x: 700.2, y: 387.3333333333333, width: 41, height: 65 },
    '34': { x: 764.8, y: 387.3333333333333, width: 41, height: 65 },
    '35': { x: 829.4, y: 387.3333333333333, width: 41, height: 65 },
    '36': { x: 894, y: 379.3333333333333, width: 55, height: 65 },
    '37': { x: 958.6, y: 379.3333333333333, width: 55, height: 65 },
    '38': { x: 1023.2, y: 379.3333333333333, width: 55, height: 65 }
  };

  // Posições das visualizações palatina/lingual
  palatinaLingualPositions: { [key: string]: ToothPosition } = {
    // Quadrante 1 (18-11) - Palatina
    '18': { x: 105, y: 167, width: 65, height: 100 },
    '17': { x: 169.6, y: 167, width: 65, height: 100 },
    '16': { x: 234.2, y: 167, width: 65, height: 100 },
    '15': { x: 298.8, y: 167, width: 65, height: 100 },
    '14': { x: 363.4, y: 167, width: 65, height: 100 },
    '13': { x: 412.8, y: 167, width: 65, height: 100 },
    '12': { x: 462.2, y: 167, width: 65, height: 100 },
    '11': { x: 511.6, y: 167, width: 65, height: 100 },

    // Quadrante 2 (21-28) - Palatina
    '21': { x: 591.4, y: 167, width: 65, height: 100 },
    '22': { x: 640.8, y: 167, width: 65, height: 100 },
    '23': { x: 690.2, y: 167, width: 65, height: 100 },
    '24': { x: 754.8, y: 167, width: 65, height: 100 },
    '25': { x: 819.4, y: 167, width: 65, height: 100 },
    '26': { x: 884, y: 167, width: 65, height: 100 },
    '27': { x: 948.6, y: 167, width: 65, height: 100 },
    '28': { x: 1013.2, y: 167, width: 65, height: 100 },

    // Quadrante 4 (41-48) - Lingual
    '48': { x: 105, y: 298.3333333333333, width: 65, height: 100 },
    '47': { x: 169.6, y: 298.3333333333333, width: 65, height: 100 },
    '46': { x: 234.2, y: 298.3333333333333, width: 65, height: 100 },
    '45': { x: 298.8, y: 298.3333333333333, width: 65, height: 100 },
    '44': { x: 363.4, y: 298.3333333333333, width: 65, height: 100 },
    '43': { x: 412.8, y: 298.3333333333333, width: 65, height: 100 },
    '42': { x: 462.2, y: 298.3333333333333, width: 65, height: 100 },
    '41': { x: 511.6, y: 298.3333333333333, width: 65, height: 100 },

    // Quadrante 3 (38-31) - Lingual
    '31': { x: 591.4, y: 298.3333333333333, width: 65, height: 100 },
    '32': { x: 640.8, y: 298.3333333333333, width: 65, height: 100 },
    '33': { x: 690.2, y: 298.3333333333333, width: 65, height: 100 },
    '34': { x: 754.8, y: 298.3333333333333, width: 65, height: 100 },
    '35': { x: 819.4, y: 298.3333333333333, width: 65, height: 100 },
    '36': { x: 884, y: 298.3333333333333, width: 65, height: 100 },
    '37': { x: 948.6, y: 298.3333333333333, width: 65, height: 100 },
    '38': { x: 1013.2, y: 298.3333333333333, width: 65, height: 100 }
  };

  // Dente selecionado atualmente
  selectedTooth: string | null = null;

  // Dimensões do SVG
  svgWidth = 1080;
  svgHeight = 550;

  constructor() { }

  ngOnInit(): void {
    this.initializeTeeth();
    this.updateTeethStatus();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['procedures']) {
      this.updateTeethStatus();
    }
  }

  private initializeTeeth(): void {
    // Inicializar todos os dentes com status 'none'
    // Quadrante 1 (18-11)
    for (let i = 18; i >= 11; i--) {
      this.quadrant1.push({ number: i.toString(), status: 'none', procedures: [] });
      this.teethMap.set(i.toString(), { number: i.toString(), status: 'none', procedures: [] });
    }

    // Quadrante 2 (21-28)
    for (let i = 21; i <= 28; i++) {
      this.quadrant2.push({ number: i.toString(), status: 'none', procedures: [] });
      this.teethMap.set(i.toString(), { number: i.toString(), status: 'none', procedures: [] });
    }

    // Quadrante 3 (38-31)
    for (let i = 38; i >= 31; i--) {
      this.quadrant3.push({ number: i.toString(), status: 'none', procedures: [] });
      this.teethMap.set(i.toString(), { number: i.toString(), status: 'none', procedures: [] });
    }

    // Quadrante 4 (41-48)
    for (let i = 41; i <= 48; i++) {
      this.quadrant4.push({ number: i.toString(), status: 'none', procedures: [] });
      this.teethMap.set(i.toString(), { number: i.toString(), status: 'none', procedures: [] });
    }
  }

  private updateTeethStatus(): void {
    // Resetar o status de todos os dentes para 'none'
    this.teethMap.forEach(tooth => {
      tooth.status = 'none';
      tooth.procedures = [];
    });

    // Atualizar com base nos procedimentos
    for (const procedure of this.procedures) {
      if (procedure.tooth) {
        const tooth = this.teethMap.get(procedure.tooth);
        if (tooth) {
          // Adicionar o procedimento à lista de procedimentos do dente
          tooth.procedures.push(procedure);

          // Atualizar o status do dente com base no status do procedimento
          if (procedure.status === TreatmentProcedureStatus.COMPLETED) {
            tooth.status = 'completed';
          } else if (tooth.status !== 'completed' &&
                    (procedure.status === TreatmentProcedureStatus.PENDING ||
                     procedure.status === TreatmentProcedureStatus.IN_PROGRESS)) {
            tooth.status = 'pending';
          }
        }
      }
    }

    // Atualizar os quadrantes
    this.updateQuadrants();
  }

  private updateQuadrants(): void {
    // Atualizar quadrante 1
    for (let i = 0; i < this.quadrant1.length; i++) {
      const toothNumber = this.quadrant1[i].number;
      const tooth = this.teethMap.get(toothNumber);
      if (tooth) {
        this.quadrant1[i] = { ...tooth };
      }
    }

    // Atualizar quadrante 2
    for (let i = 0; i < this.quadrant2.length; i++) {
      const toothNumber = this.quadrant2[i].number;
      const tooth = this.teethMap.get(toothNumber);
      if (tooth) {
        this.quadrant2[i] = { ...tooth };
      }
    }

    // Atualizar quadrante 3
    for (let i = 0; i < this.quadrant3.length; i++) {
      const toothNumber = this.quadrant3[i].number;
      const tooth = this.teethMap.get(toothNumber);
      if (tooth) {
        this.quadrant3[i] = { ...tooth };
      }
    }

    // Atualizar quadrante 4
    for (let i = 0; i < this.quadrant4.length; i++) {
      const toothNumber = this.quadrant4[i].number;
      const tooth = this.teethMap.get(toothNumber);
      if (tooth) {
        this.quadrant4[i] = { ...tooth };
      }
    }
  }

  onToothClick(toothNumber: string): void {
    this.selectedTooth = toothNumber;
    this.toothSelected.emit(toothNumber);
  }

  getToothStatusClass(status: string): string {
    switch (status) {
      case 'pending':
        return 'tooth-pending';
      case 'completed':
        return 'tooth-completed';
      default:
        return '';
    }
  }

  getToothFillColor(toothNumber: string): string {
    const tooth = this.teethMap.get(toothNumber);
    if (!tooth) return 'rgba(255, 255, 255, 0)';

    switch (tooth.status) {
      case 'pending':
        return 'rgba(239, 68, 68, 0.6)'; // Vermelho com transparência
      case 'completed':
        return 'rgba(16, 185, 129, 0.6)'; // Verde com transparência
      default:
        return 'rgba(255, 255, 255, 0)'; // Transparente
    }
  }

  getAllTeeth(): string[] {
    return Array.from(this.teethMap.keys());
  }

  getToothNumberAsInt(toothNumber: string): number {
    return parseInt(toothNumber, 10);
  }
}
