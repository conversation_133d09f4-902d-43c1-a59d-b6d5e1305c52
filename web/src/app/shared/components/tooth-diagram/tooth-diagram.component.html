<div class="tooth-diagram">
  <!-- Legenda -->
  <div class="legend flex justify-center mb-4 space-x-6">
    <div class="flex items-center">
      <div class="w-4 h-4 bg-red-500 rounded-sm mr-2"></div>
      <span class="text-sm">A Realizar</span>
    </div>
    <div class="flex items-center">
      <div class="w-4 h-4 bg-green-500 rounded-sm mr-2"></div>
      <span class="text-sm">Executado</span>
    </div>
  </div>

  <!-- Odontograma SVG -->
  <div class="odontogram-container">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      [attr.viewBox]="'0 0 ' + svgWidth + ' ' + svgHeight"
      style="width: 100%; height: 100%"
    >
      <!-- <PERSON><PERSON><PERSON><PERSON> das vistas -->
      <svg>
        <text fill="#9e9e9e" x="0" y="65" style="font-size: 20px">
          Vestibular
        </text>
        <text fill="#9e9e9e" x="0" y="140" style="font-size: 20px">
          Oclusal
        </text>
        <text fill="#9e9e9e" x="0" y="215" style="font-size: 18px">
          Palatina
        </text>
        <text fill="#9e9e9e" x="0" y="347" style="font-size: 18px">
          Lingual
        </text>
        <text fill="#9e9e9e" x="0" y="418" style="font-size: 20px">
          Oclusal
        </text>
        <text fill="#9e9e9e" x="0" y="497" style="font-size: 20px">
          Vestibular
        </text>
      </svg>

      <!-- Dentes -->
      <g *ngFor="let toothNumber of getAllTeeth()">
        <!-- Vestibular superior ou inferior -->
        <foreignObject
          [attr.height]="toothPositions[toothNumber].height"
          [attr.width]="toothPositions[toothNumber].width"
          [attr.x]="toothPositions[toothNumber].x"
          [attr.y]="toothPositions[toothNumber].y"
          style="pointer-events: none"
        >
          <div>
            <svg viewBox="0 0 76 93">
              <!-- Imagem do dente -->
              <image
                *ngIf="
                  getToothNumberAsInt(toothNumber) >= 11 &&
                  getToothNumberAsInt(toothNumber) <= 28
                "
                [attr.xlink:href]="
                  'assets/images/Dentes/OdontogramaPermanente/Dente' +
                  toothNumber +
                  '/denteCompletoVestibular.png'
                "
                width="76"
                height="93"
                [attr.alt]="toothNumber"
                [attr.name]="toothNumber"
              >
              </image>
              <image
                *ngIf="
                  getToothNumberAsInt(toothNumber) >= 31 &&
                  getToothNumberAsInt(toothNumber) <= 48
                "
                [attr.xlink:href]="
                  'assets/images/Dentes/OdontogramaPermanente/Dente' +
                  toothNumber +
                  '/denteCompletoVestibular.png'
                "
                width="76"
                height="93"
                [attr.alt]="toothNumber"
                [attr.name]="toothNumber"
              >
              </image>

              <!-- Áreas clicáveis com cores de status -->
              <path
                id="Path"
                d="M18.348,91.5l5.206.889,5.762-1.7L33.5,87.581l2.3-1.361,4.956,2.525,6.035,1.943,6.021,1.7,5.039-.889V67.621h-39.5Z"
                [attr.fill]="getToothFillColor(toothNumber)"
                fill-opacity="0.6"
                [attr.stroke]="
                  selectedTooth === toothNumber ? '#3b82f6' : '#707070'
                "
                [attr.stroke-opacity]="
                  selectedTooth === toothNumber ? '1' : '0.4'
                "
                [attr.stroke-width]="selectedTooth === toothNumber ? '2' : '0'"
                [attr.style]="
                  selectedTooth === toothNumber
                    ? 'cursor: pointer; pointer-events: all; animation: pulse 2s infinite;'
                    : 'cursor: pointer; pointer-events: all;'
                "
                (click)="onToothClick(toothNumber)"
              >
              </path>
            </svg>
          </div>
        </foreignObject>

        <!-- Oclusal superior ou inferior -->
        <foreignObject
          [attr.height]="occlusalPositions[toothNumber].height"
          [attr.width]="occlusalPositions[toothNumber].width"
          [attr.x]="occlusalPositions[toothNumber].x"
          [attr.y]="occlusalPositions[toothNumber].y"
          style="pointer-events: none"
        >
          <svg viewBox="0 0 76 93">
            <!-- Imagem do dente oclusal -->
            <image
              [attr.xlink:href]="
                'assets/images/Dentes/OdontogramaPermanente/Dente' +
                toothNumber +
                '/oclusal.png'
              "
              width="76"
              height="93"
              [attr.alt]="toothNumber"
              [attr.name]="toothNumber"
            >
            </image>

            <!-- Áreas clicáveis com cores de status -->
            <path
              id="Path"
              d="M12.983,23.163l7.381-4.583L30.5,13.637l9.9-2.81L52.5,9.07l6.543,1.016L65.211,77.7,60.947,80.52l-7.813,1.644L45,83.21H37.45L23.021,84.864Z"
              [attr.fill]="getToothFillColor(toothNumber)"
              fill-opacity="0.6"
              [attr.stroke]="
                selectedTooth === toothNumber ? '#3b82f6' : '#707070'
              "
              [attr.stroke-opacity]="
                selectedTooth === toothNumber ? '1' : '0.4'
              "
              [attr.stroke-width]="selectedTooth === toothNumber ? '2' : '0'"
              [attr.style]="
                selectedTooth === toothNumber
                  ? 'cursor: pointer; pointer-events: all; animation: pulse 2s infinite;'
                  : 'cursor: pointer; pointer-events: all;'
              "
              (click)="onToothClick(toothNumber)"
            >
            </path>
          </svg>
        </foreignObject>

        <!-- Palatina/Lingual -->
        <foreignObject
          [attr.height]="palatinaLingualPositions[toothNumber].height"
          [attr.width]="palatinaLingualPositions[toothNumber].width"
          [attr.x]="palatinaLingualPositions[toothNumber].x"
          [attr.y]="palatinaLingualPositions[toothNumber].y"
          style="pointer-events: none"
        >
          <svg viewBox="0 0 76 93">
            <!-- Imagem do dente palatina/lingual -->
            <image
              *ngIf="
                getToothNumberAsInt(toothNumber) >= 11 &&
                getToothNumberAsInt(toothNumber) <= 28
              "
              [attr.xlink:href]="
                'assets/images/Dentes/OdontogramaPermanente/Dente' +
                toothNumber +
                '/denteCompletoLingual.png'
              "
              width="76"
              height="93"
              [attr.alt]="toothNumber"
              [attr.name]="toothNumber"
            >
            </image>
            <image
              *ngIf="
                getToothNumberAsInt(toothNumber) >= 31 &&
                getToothNumberAsInt(toothNumber) <= 48
              "
              [attr.xlink:href]="
                'assets/images/Dentes/OdontogramaPermanente/Dente' +
                toothNumber +
                '/denteCompletoLingual.png'
              "
              width="76"
              height="93"
              [attr.alt]="toothNumber"
              [attr.name]="toothNumber"
            >
            </image>

            <!-- Áreas clicáveis com cores de status -->
            <path
              id="Path"
              d="M17.991.957h6.448l4.25.8L32.68,4.5l3.455,1.542,7.958-2.816L50.081.957H55.16l3.958.8-.5,22.394H17.991Z"
              [attr.fill]="getToothFillColor(toothNumber)"
              fill-opacity="0.6"
              [attr.stroke]="
                selectedTooth === toothNumber ? '#3b82f6' : '#707070'
              "
              [attr.stroke-opacity]="
                selectedTooth === toothNumber ? '1' : '0.4'
              "
              [attr.stroke-width]="selectedTooth === toothNumber ? '2' : '0'"
              [attr.style]="
                selectedTooth === toothNumber
                  ? 'cursor: pointer; pointer-events: all; animation: pulse 2s infinite;'
                  : 'cursor: pointer; pointer-events: all;'
              "
              (click)="onToothClick(toothNumber)"
            >
            </path>
          </svg>
        </foreignObject>
      </g>

      <!-- Números dos dentes -->
      <rect
        fill="white"
        x="123"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="125" y="268" style="font-size: 20px">18</text>
      <rect
        fill="white"
        x="187.6"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="189.6" y="268" style="font-size: 20px">17</text>
      <rect
        fill="white"
        x="252.2"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="254.2" y="268" style="font-size: 20px">16</text>
      <rect
        fill="white"
        x="316.8"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="318.8" y="268" style="font-size: 20px">15</text>
      <rect
        fill="white"
        x="381.4"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="383.4" y="268" style="font-size: 20px">14</text>
      <rect
        fill="white"
        x="430.8"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="432.8" y="268" style="font-size: 20px">13</text>
      <rect
        fill="white"
        x="480.2"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="482.2" y="268" style="font-size: 20px">12</text>
      <rect
        fill="white"
        x="529.6"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="531.6" y="268" style="font-size: 20px">11</text>
      <rect
        fill="white"
        x="609.4"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="611.4" y="268" style="font-size: 20px">21</text>
      <rect
        fill="white"
        x="658.8"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="660.8" y="268" style="font-size: 20px">22</text>
      <rect
        fill="white"
        x="708.2"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="710.2" y="268" style="font-size: 20px">23</text>
      <rect
        fill="white"
        x="772.8"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="774.8" y="268" style="font-size: 20px">24</text>
      <rect
        fill="white"
        x="837.4"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="839.4" y="268" style="font-size: 20px">25</text>
      <rect
        fill="white"
        x="902"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="904" y="268" style="font-size: 20px">26</text>
      <rect
        fill="white"
        x="966.6"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="968.6" y="268" style="font-size: 20px">27</text>
      <rect
        fill="white"
        x="1031.2"
        y="248"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="1033.2" y="268" style="font-size: 20px">28</text>

      <rect
        fill="white"
        x="123"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="125" y="295" style="font-size: 20px">48</text>
      <rect
        fill="white"
        x="187.6"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="189.6" y="295" style="font-size: 20px">47</text>
      <rect
        fill="white"
        x="252.2"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="254.2" y="295" style="font-size: 20px">46</text>
      <rect
        fill="white"
        x="316.8"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="318.8" y="295" style="font-size: 20px">45</text>
      <rect
        fill="white"
        x="381.4"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="383.4" y="295" style="font-size: 20px">44</text>
      <rect
        fill="white"
        x="430.8"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="432.8" y="295" style="font-size: 20px">43</text>
      <rect
        fill="white"
        x="480.2"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="482.2" y="295" style="font-size: 20px">42</text>
      <rect
        fill="white"
        x="529.6"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="531.6" y="295" style="font-size: 20px">41</text>
      <rect
        fill="white"
        x="609.4"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="611.4" y="295" style="font-size: 20px">31</text>
      <rect
        fill="white"
        x="658.8"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="660.8" y="295" style="font-size: 20px">32</text>
      <rect
        fill="white"
        x="708.2"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="710.2" y="295" style="font-size: 20px">33</text>
      <rect
        fill="white"
        x="772.8"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="774.8" y="295" style="font-size: 20px">34</text>
      <rect
        fill="white"
        x="837.4"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="839.4" y="295" style="font-size: 20px">35</text>
      <rect
        fill="white"
        x="902"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="904" y="295" style="font-size: 20px">36</text>
      <rect
        fill="white"
        x="966.6"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="968.6" y="295" style="font-size: 20px">37</text>
      <rect
        fill="white"
        x="1031.2"
        y="275"
        height="25"
        width="25"
        rx="12"
        ry="12"
      ></rect>
      <text fill="#9e9e9e" x="1033.2" y="295" style="font-size: 20px">38</text>

      <!-- Linhas divisórias -->
      <svg height="50%" width="100%" x="90" y="49.4%">
        <line
          stroke-dasharray="5,5"
          x1="100%"
          y1="0"
          x2="0"
          y2="0"
          style="stroke: rgb(158, 158, 158); stroke-width: 2"
        ></line>
      </svg>
      <svg height="100%" width="50%" x="54.1%">
        <line
          stroke-dasharray="5,5"
          x1="0"
          y1="100%"
          x2="0"
          y2="0"
          style="stroke: rgb(158, 158, 158); stroke-width: 2"
        ></line>
      </svg>
    </svg>
  </div>
</div>
