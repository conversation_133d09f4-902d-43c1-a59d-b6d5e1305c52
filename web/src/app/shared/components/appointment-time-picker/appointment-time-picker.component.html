<div class="appointment-time-picker">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-2 text-gray-600">Carregando horários...</span>
  </div>

  <!-- Time grid -->
  <div *ngIf="!isLoading" class="time-grid">

    <!-- Morning slots (08:30 - 12:55) -->
    <div class="mb-2">
      <div class="grid gap-0 md:overflow-visible overflow-x-auto" [style.grid-template-columns]="'repeat(' + morningSlots.length + ', minmax(0, 1fr))'">
        <div
          *ngFor="let slot of morningSlots; trackBy: trackByTime"
          [class]="getSlotClasses(slot)"
          [title]="slot.time"
          (click)="onSlotClick(slot)">
          <span [class]="getLabelClasses(slot)">{{ getSlotLabel(slot) }}</span>
        </div>
      </div>
    </div>

    <!-- Afternoon slots (13:00 - 18:55) -->
    <div>
      <div class="grid gap-0 md:overflow-visible overflow-x-auto" [style.grid-template-columns]="'repeat(' + afternoonSlots.length + ', minmax(0, 1fr))'">
        <div
          *ngFor="let slot of afternoonSlots; trackBy: trackByTime"
          [class]="getSlotClasses(slot)"
          [title]="slot.time"
          (click)="onSlotClick(slot)">
          <span [class]="getLabelClasses(slot)">{{ getSlotLabel(slot) }}</span>
        </div>
      </div>
    </div>

    <!-- Selection info -->
    <div *ngIf="selection.selectedSlots.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
      <div class="text-sm">
        <span class="font-medium">Seleção atual:</span>
        <span class="ml-2">{{ getSelectionSummary() }}</span>
      </div>
      <div class="text-xs text-gray-600 mt-1">
        Clique em células adjacentes para estender a seleção ou nas extremidades para reduzir.
      </div>
      <button
        (click)="resetSelection()"
        class="mt-2 text-xs text-blue-600 hover:text-blue-800 underline">
        Limpar seleção
      </button>
    </div>

    <!-- No dentist selected message -->
    <div *ngIf="!dentistId" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
      <p class="text-sm text-yellow-800">
        <i class="fas fa-info-circle mr-2"></i>
        Selecione um dentista para visualizar os horários disponíveis.
      </p>
    </div>

    <!-- No date selected message -->
    <div *ngIf="dentistId && !date" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
      <p class="text-sm text-yellow-800">
        <i class="fas fa-info-circle mr-2"></i>
        Selecione uma data para visualizar os horários disponíveis.
      </p>
    </div>
  </div>
</div>
