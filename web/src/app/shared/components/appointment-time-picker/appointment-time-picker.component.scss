.appointment-time-picker {
  .time-grid {
    user-select: none;

    .grid {
      &.grid-cols-auto {
        display: grid;
        gap: 1px;
      }

      // <PERSON><PERSON><PERSON><PERSON> que todas as c<PERSON><PERSON><PERSON> tenham a mesma largura
      > div {
        min-width: 0; // Permite que o flex-shrink funcione
        width: 100%; // Força largura uniforme
        box-sizing: border-box; // Inclui border no cálculo da largura
      }

      // Evitar quebra de linha
      display: grid;
      grid-auto-flow: column;
      grid-auto-columns: 1fr;
    }

    // Slot styling
    .slot {
      position: relative;
      transition: all 0.2s ease;
      font-size: 10px;
      line-height: 1;
      overflow: visible; // Permite que labels se estendam

      &:hover {
        transform: scale(1.05);
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      // Status colors
      &.unavailable {
        background-color: #374151; // gray-700
        color: white;
        cursor: not-allowed;
        
        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
      
      &.available {
        background-color: white;
        border-color: #d1d5db; // gray-300
        
        &:hover {
          background-color: #f3f4f6; // gray-100
        }
      }
      
      &.selected {
        background-color: #06b6d4; // cyan-500
        color: white;
        border-color: #0891b2; // cyan-600
      }
      
      &.conflict {
        background-color: #1d4ed8; // blue-700
        color: white;
        border-color: #1e40af; // blue-800
      }
      
      &.occupied {
        background-color: #fda4af; // rose-300
        color: #881337; // rose-900
        cursor: pointer;
        
        &:hover {
          background-color: #fb7185; // rose-400
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .time-grid {
      .grid {
        overflow-x: auto;
        padding-bottom: 8px;
        grid-template-columns: repeat(auto-fit, minmax(32px, 1fr)) !important;

        &::-webkit-scrollbar {
          height: 4px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 2px;

          &:hover {
            background: #94a3b8;
          }
        }
      }

      .slot {
        min-width: 32px;
        font-size: 8px; // Ainda menor em mobile para labels
        height: 2rem; // h-8
        width: 100%; // Largura uniforme

        &:not(.has-label) {
          font-size: 9px; // Texto normal para slots sem label
        }

        // Labels em mobile também podem se estender
        span {
          &.absolute {
            font-size: 8px;
          }
        }
      }
    }
  }

  // Desktop adjustments
  @media (min-width: 769px) {
    .time-grid {
      .grid {
        overflow-x: visible;

        // Garantir largura uniforme em desktop
        > div {
          flex: 1;
          min-width: 0;
        }
      }

      .slot {
        min-width: auto;
        height: 2rem; // h-8
        width: 100%; // Largura uniforme

        // Labels em desktop
        span {
          &.absolute {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  // Loading state
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
  }
  
  // Selection info styling
  .selection-info {
    border-left: 4px solid #06b6d4;
    
    .selection-text {
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }
  }
  
  // Legend styling
  .legend {
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-color {
        width: 16px;
        height: 16px;
        border: 1px dashed #d1d5db;
        flex-shrink: 0;
      }
    }
  }
  
  // Animation for slot selection
  @keyframes slot-select {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
  
  .slot.selecting {
    animation: slot-select 0.3s ease;
  }
  
  // Tooltip styling (if using custom tooltips)
  .slot-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 30;
    margin-bottom: 4px;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: #1f2937;
    }
  }
}
