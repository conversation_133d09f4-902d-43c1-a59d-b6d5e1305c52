<!-- Loading indicator -->
<div *ngIf="isLoading" class="flex justify-center items-center py-8">
  <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
</div>

<div *ngIf="!isLoading">
  <form [formGroup]="schedulingForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Primeira linha: Paciente, Email, Celular -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
      <!-- Paciente -->
      <div>
        <div class="space-y-2">
          <label for="patientId" class="flex items-center gap-2 text-sm font-medium text-gray-700">
            Paciente *
            <span *ngIf="isNewPatient" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Novo
            </span>
          </label>
          <div class="relative">
            <input
              type="text"
              [(ngModel)]="patientSearchTerm"
              [ngModelOptions]="{ standalone: true }"
              (input)="filterPatients()"
              (focus)="onPatientInputFocus()"
              (blur)="onPatientInputBlur()"
              placeholder="Buscar paciente por nome ou CPF"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-10"
              [class.border-red-500]="schedulingForm.get('patientId')?.invalid && schedulingForm.get('patientId')?.touched"
              [disabled]="isEditMode"
              [class.bg-gray-100]="isEditMode"
            />
            <div
              *ngIf="showPatientDropdown && !isEditMode"
              class="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto border border-gray-200"
            >
              <ul>
                <!-- Pacientes encontrados -->
                <li
                  *ngFor="let patient of filteredPatients"
                  (mousedown)="selectPatient(patient)"
                  class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                >
                  {{ patient.name }}
                  <span class="text-gray-500 text-sm ml-2">{{ patient.cpf }}</span>
                </li>

                <!-- Opção "Novo Paciente" sempre visível quando há busca -->
                <li
                  *ngIf="patientSearchTerm.length >= 3"
                  (mousedown)="selectNewPatient()"
                  class="px-4 py-2 hover:bg-green-50 cursor-pointer text-green-700 font-medium border-t border-gray-200"
                >
                  <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Novo Paciente: "{{ patientSearchTerm }}"
                </li>
              </ul>
            </div>
          </div>
        </div>
        <!-- Área reservada para mensagens -->
        <div class="min-h-[0.5rem]">
          <div
            *ngIf="!isPatientValid() && (schedulingForm.get('patientId')?.touched || patientSearchTerm.length > 0)"
            class="text-red-500 text-sm"
          >
            {{ isNewPatient ? "Nome do paciente deve ter pelo menos 3 caracteres" : "Paciente é obrigatório" }}
          </div>
          <div *ngIf="isEditMode" class="text-blue-600 text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Não é possível alterar o paciente
          </div>
        </div>
        <input type="hidden" formControlName="patientId" />
      </div>

      <!-- Email -->
      <div class="space-y-2">
        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="<EMAIL>"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-10"
        />
        <div class="min-h-[0.5rem]"></div>
      </div>

      <!-- Celular -->
      <div class="space-y-2">
        <label for="phone" class="block text-sm font-medium text-gray-700">Celular</label>
        <input
          type="tel"
          id="phone"
          formControlName="phone"
          placeholder="(11) 99999-9999"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-10"
        />
        <div class="min-h-[0.5rem]"></div>
      </div>
    </div>

    <!-- Segunda linha: Primeira consulta -->
    <div class="space-y-2">
      <div class="flex items-center pb-6">
        <input
          type="checkbox"
          id="isFirstAppointment"
          formControlName="isFirstAppointment"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="isFirstAppointment" class="ml-2 block text-sm text-gray-700 h-4">
          Primeira Consulta
          <p class="text-xs text-gray-500">
            Marque se esta é a primeira consulta do paciente
          </p>
        </label>
      </div>
    </div>

    <!-- Terceira linha: Seleção de procedimentos -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">Procedimentos</label>
      <app-procedure-selector
        formControlName="procedureIds"
        placeholder="Pesquisar e selecionar procedimentos..."
        [disabled]="isSubmitting"
      ></app-procedure-selector>
    </div>

    <!-- Quarta linha: Data do agendamento, Profissional, Agendado por -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Data do Agendamento -->
      <div class="space-y-2">
        <label for="date" class="block text-sm font-medium text-gray-700">Data do Agendamento *</label>
        <input
          type="date"
          id="date"
          formControlName="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [class.border-red-500]="schedulingForm.get('date')?.invalid && schedulingForm.get('date')?.touched"
        />
        <div
          *ngIf="schedulingForm.get('date')?.invalid && schedulingForm.get('date')?.touched"
          class="text-red-500 text-sm mt-1"
        >
          Data é obrigatória
        </div>
      </div>

      <!-- Profissional (Dentista) -->
      <div class="space-y-2">
        <label for="dentistId" class="block text-sm font-medium text-gray-700">Profissional (Dentista) *</label>
        <select
          id="dentistId"
          formControlName="dentistId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [class.border-red-500]="schedulingForm.get('dentistId')?.invalid && schedulingForm.get('dentistId')?.touched"
        >
          <option value="">Selecione</option>
          <option *ngFor="let dentist of dentists" [value]="dentist.id">
            {{ dentist.name }}
          </option>
        </select>
        <div
          *ngIf="schedulingForm.get('dentistId')?.invalid && schedulingForm.get('dentistId')?.touched"
          class="text-red-500 text-sm mt-1"
        >
          Dentista é obrigatório
        </div>
      </div>

      <!-- Agendado por -->
      <div class="space-y-2">
        <label for="scheduledBy" class="block text-sm font-medium text-gray-700">Agendado por</label>
        <select
          id="scheduledBy"
          formControlName="scheduledBy"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Selecione</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ employee.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- Quinta linha: Observações e Categoria de Agendamento -->
    <div class="grid grid-cols-7 gap-4">
      <!-- Observações -->
      <div class="col-span-5 space-y-2">
        <label for="notes" class="block text-sm font-medium text-gray-700">Observações</label>
        <input
          type="text"
          id="notes"
          formControlName="notes"
          placeholder="Observações adicionais sobre o agendamento"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- Categoria de Agendamento -->
      <div class="col-span-2 space-y-2">
        <label for="appointmentCategoryId" class="block text-sm font-medium text-gray-700">Categoria</label>
        <select
          id="appointmentCategoryId"
          formControlName="appointmentCategoryId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Selecione</option>
          <option *ngFor="let category of appointmentCategories" [value]="category.id">
            {{ category.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- Sexta linha: Seletor de horário -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">Horário *</label>
      <app-appointment-time-picker
        [dentistId]="schedulingForm.get('dentistId')?.value"
        [date]="schedulingForm.get('date')?.value"
        [excludeAppointmentId]="isEditMode && appointment ? appointment.id : undefined"
        (timeSelected)="onTimeSelected($event)"
      ></app-appointment-time-picker>
      <div
        *ngIf="schedulingForm.get('time')?.invalid && schedulingForm.get('time')?.touched"
        class="text-red-500 text-sm mt-1"
      >
        Horário é obrigatório
      </div>
    </div>

    <!-- Botões de ação (opcionais) -->
    <div *ngIf="showSubmitButtons" class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        (click)="cancel()"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
      >
        Cancelar
      </button>
      <button
        type="submit"
        [disabled]="isSubmitting"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        [class.opacity-50]="isSubmitting"
      >
        <svg
          *ngIf="isSubmitting"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isEditMode ? "Atualizar" : "Salvar" }} Agendamento
      </button>
    </div>
  </form>
</div>
