.photo-upload-container {
  @apply bg-white rounded-lg p-6;
  max-height: 90vh;
  overflow-y: auto;
}

// Drag and drop styles
.drag-over {
  @apply border-blue-500 bg-blue-50;
}

.dragging {
  @apply opacity-50 transform rotate-2 shadow-lg;
  transition: all 0.2s ease-in-out;
}

// Photo grid animations
.photo-item {
  transition: all 0.3s ease-in-out;
}

.photo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// Camera modal styles
.camera-modal {
  backdrop-filter: blur(4px);
}

// Loading spinner
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .photo-upload-container {
    @apply p-4;
    max-height: 95vh;
  }
  
  .grid {
    @apply grid-cols-2 gap-2;
  }
}

// Custom scrollbar
.photo-upload-container::-webkit-scrollbar {
  width: 6px;
}

.photo-upload-container::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.photo-upload-container::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded;
}

.photo-upload-container::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

// Button hover effects
.btn-hover {
  transition: all 0.2s ease-in-out;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

// Photo preview styles
.photo-preview {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease-in-out;
  }
  
  &:hover::before {
    transform: translateX(100%);
  }
}

// Drag handle cursor
.drag-handle {
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

// Error message animation
.error-message {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Success message animation
.success-message {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Photo order badge
.order-badge {
  @apply bg-blue-600 text-white text-xs font-bold;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Camera badge
.camera-badge {
  @apply bg-green-600 text-white text-xs;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
}

// Caption input focus styles
.caption-input {
  transition: all 0.2s ease-in-out;
  
  &:focus {
    @apply ring-2 ring-blue-500 border-blue-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

// Grid item hover effects
.grid-item {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: scale(1.02);
    z-index: 10;
  }
}

// Modal backdrop
.modal-backdrop {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Modal content
.modal-content {
  animation: slideInScale 0.3s ease-out;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Video element styles
video {
  border-radius: 8px;
  background-color: #000;
}

// Canvas hidden styles
canvas.hidden {
  display: none !important;
}

// Disabled state styles
.disabled {
  @apply opacity-50 cursor-not-allowed;
  pointer-events: none;
}

// Loading state
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Utility classes
.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

// Focus styles
.focus-ring {
  &:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }
}

// Smooth transitions
.transition-all {
  transition: all 0.2s ease-in-out;
}
