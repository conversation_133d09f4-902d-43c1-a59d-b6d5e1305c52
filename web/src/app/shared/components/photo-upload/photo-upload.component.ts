import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PatientPhoto } from '../../../core/models/photo.model';
import { PhotoService } from '../../../core/services/photo.service';
import { NotificationService } from '../../../core/services/notification.service';

export interface PhotoUploadItem {
  id: string;
  file?: File;
  imageBase64?: string;
  preview: string;
  caption: string;
  order: number;
  isFromCamera: boolean;
}

export interface PhotoUploadConfig {
  mode: 'upload' | 'camera' | 'both';
  allowMultiple: boolean;
  maxFiles?: number;
  showCaptions: boolean;
  showReorder: boolean;
  globalCaption?: boolean;
}

@Component({
  selector: 'app-photo-upload',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './photo-upload.component.html',
  styleUrls: ['./photo-upload.component.scss'],
})
export class PhotoUploadComponent implements OnInit, OnDestroy {
  @Input() patientId: number | null = null;
  @Input() folderId: string | null = null;
  @Input() config: PhotoUploadConfig = {
    mode: 'both',
    allowMultiple: true,
    maxFiles: 10,
    showCaptions: true,
    showReorder: true,
    globalCaption: false,
  };

  @Output() photosUploaded = new EventEmitter<PatientPhoto[]>();
  @Output() close = new EventEmitter<void>();

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvasElement') canvasElement!: ElementRef<HTMLCanvasElement>;

  // Estados
  isUploading = false;
  isCameraOpen = false;
  isCapturing = false;
  error: string | null = null;

  // Dados
  photos: PhotoUploadItem[] = [];
  globalCaption = '';
  photoCounter = 0;

  // Câmera
  stream: MediaStream | null = null;
  availableCameras: MediaDeviceInfo[] = [];
  currentCameraIndex = 0;
  multipleWebcamsAvailable = false;

  // Drag & Drop
  draggedPhotoIndex: number | null = null;

  constructor(
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.stopCamera();
  }

  private initializeComponent(): void {
    // Configurações iniciais se necessário
  }

  // Métodos de Upload
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    const files = Array.from(input.files);
    this.addFilesToPhotos(files);
    
    // Limpar o input para permitir selecionar os mesmos arquivos novamente
    input.value = '';
  }

  private addFilesToPhotos(files: File[]): void {
    const maxFiles = this.config.maxFiles || 10;
    const remainingSlots = maxFiles - this.photos.length;
    const filesToAdd = files.slice(0, remainingSlots);

    filesToAdd.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const photoItem: PhotoUploadItem = {
          id: this.generateUniqueId(),
          file,
          preview: e.target?.result as string,
          caption: '',
          order: this.photos.length + index + 1,
          isFromCamera: false,
        };
        this.photos.push(photoItem);
      };
      reader.readAsDataURL(file);
    });

    if (files.length > remainingSlots) {
      this.notificationService.warning(
        `Apenas ${remainingSlots} fotos foram adicionadas. Limite máximo: ${maxFiles}`
      );
    }
  }

  // Métodos de Câmera
  async openCamera(): Promise<void> {
    this.isCameraOpen = true;
    this.error = null;

    try {
      // Enumerar dispositivos de câmera disponíveis
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableCameras = devices.filter(
        (device) => device.kind === 'videoinput'
      );
      this.multipleWebcamsAvailable = this.availableCameras.length > 1;

      // Iniciar a câmera
      await this.startCameraWithIndex(this.currentCameraIndex);
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  private async startCameraWithIndex(index: number): Promise<void> {
    try {
      // Parar qualquer stream existente
      this.stopCameraStream();

      // Configurações da câmera
      const videoConstraints: any = {
        width: { ideal: 1280 },
        height: { ideal: 720 },
      };

      // Adicionar deviceId apenas se disponível e válido
      if (this.availableCameras.length > 0 &&
          this.availableCameras[index] &&
          this.availableCameras[index].deviceId) {
        videoConstraints.deviceId = { exact: this.availableCameras[index].deviceId };
      }

      const constraints: MediaStreamConstraints = {
        video: videoConstraints,
        audio: false,
      };

      // Solicitar acesso à câmera
      try {
        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (error: any) {
        // Se falhar com deviceId específico, tentar sem deviceId
        if (error.name === 'OverconstrainedError' && videoConstraints.deviceId) {
          console.log('Tentando novamente sem deviceId específico...');
          delete videoConstraints.deviceId;
          const fallbackConstraints: MediaStreamConstraints = {
            video: videoConstraints,
            audio: false,
          };
          this.stream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        } else {
          throw error;
        }
      }

      // Configurar o elemento de vídeo
      if (this.videoElement && this.videoElement.nativeElement) {
        const video = this.videoElement.nativeElement;
        video.srcObject = this.stream;

        video.onloadedmetadata = () => {
          video
            .play()
            .then(() => {
              this.isCapturing = false;
            })
            .catch((err) => {
              console.error('Erro ao iniciar reprodução de vídeo:', err);
              this.error = 'Erro ao iniciar a câmera. Por favor, tente novamente.';
            });
        };
      }
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  private handleCameraError(error: unknown): void {
    console.error('Erro ao acessar a câmera:', error);

    let errorMessage = 'Não foi possível acessar a câmera. ';

    if (error instanceof Error) {
      if (
        error.name === 'NotAllowedError' ||
        error.name === 'PermissionDeniedError'
      ) {
        errorMessage +=
          'Permissão negada. Por favor, permita o acesso à câmera nas configurações do navegador.';
      } else if (error.name === 'NotFoundError') {
        errorMessage +=
          'Nenhuma câmera encontrada. Verifique se sua câmera está conectada.';
      } else if (
        error.name === 'NotReadableError' ||
        error.name === 'TrackStartError'
      ) {
        errorMessage +=
          'Sua câmera pode estar sendo usada por outro aplicativo.';
      } else {
        errorMessage += error.message || 'Verifique as permissões do navegador.';
      }
    } else {
      errorMessage += 'Verifique as permissões do navegador.';
    }

    this.error = errorMessage;
    this.isCapturing = false;
  }

  switchCamera(): void {
    if (!this.multipleWebcamsAvailable) return;

    this.currentCameraIndex =
      (this.currentCameraIndex + 1) % this.availableCameras.length;
    this.startCameraWithIndex(this.currentCameraIndex);
  }

  capturePhoto(): void {
    if (!this.videoElement || !this.canvasElement) {
      this.error = 'Não foi possível capturar a foto. Elementos não encontrados.';
      return;
    }

    const video = this.videoElement.nativeElement;
    const canvas = this.canvasElement.nativeElement;
    const context = canvas.getContext('2d');

    if (!context) {
      this.error = 'Não foi possível capturar a foto. Erro no contexto do canvas.';
      return;
    }

    try {
      // Verificar se o vídeo está pronto
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        this.error = 'Não foi possível capturar a foto. Câmera não está pronta.';
        return;
      }

      // Definir as dimensões do canvas para corresponder ao vídeo
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Desenhar o quadro atual do vídeo no canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Obter a imagem como base64
      const imageBase64 = canvas.toDataURL('image/jpeg', 0.9);

      // Adicionar à lista de fotos
      const photoItem: PhotoUploadItem = {
        id: this.generateUniqueId(),
        imageBase64,
        preview: imageBase64,
        caption: '',
        order: this.photos.length + 1,
        isFromCamera: true,
      };

      this.photos.push(photoItem);
      this.photoCounter++;

      this.notificationService.success('Foto capturada com sucesso!');
    } catch (error: unknown) {
      console.error('Erro ao capturar foto:', error);
      this.error = 'Erro ao capturar foto. Tente novamente.';
    }
  }

  closeCamera(): void {
    this.isCameraOpen = false;
    this.stopCamera();
  }

  private stopCamera(): void {
    this.stopCameraStream();
  }

  private stopCameraStream(): void {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;
    }
  }

  // Métodos de Gerenciamento de Fotos
  removePhoto(index: number): void {
    this.photos.splice(index, 1);
    this.reorderPhotos();
  }

  private reorderPhotos(): void {
    this.photos.forEach((photo, index) => {
      photo.order = index + 1;
    });
  }

  private generateUniqueId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Métodos de Drag & Drop
  onDragStart(event: DragEvent, index: number): void {
    this.draggedPhotoIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  onDrop(event: DragEvent, targetIndex: number): void {
    event.preventDefault();

    if (this.draggedPhotoIndex === null || this.draggedPhotoIndex === targetIndex) {
      return;
    }

    // Mover o item
    const draggedItem = this.photos[this.draggedPhotoIndex];
    this.photos.splice(this.draggedPhotoIndex, 1);
    this.photos.splice(targetIndex, 0, draggedItem);

    // Reordenar
    this.reorderPhotos();
    this.draggedPhotoIndex = null;
  }

  movePhotoUp(index: number): void {
    if (index > 0) {
      const temp = this.photos[index];
      this.photos[index] = this.photos[index - 1];
      this.photos[index - 1] = temp;
      this.reorderPhotos();
    }
  }

  movePhotoDown(index: number): void {
    if (index < this.photos.length - 1) {
      const temp = this.photos[index];
      this.photos[index] = this.photos[index + 1];
      this.photos[index + 1] = temp;
      this.reorderPhotos();
    }
  }

  // Métodos de Salvamento
  async savePhotos(): Promise<void> {
    if (!this.patientId || this.photos.length === 0) {
      this.error = 'Nenhuma foto para salvar ou paciente não selecionado.';
      return;
    }

    this.isUploading = true;
    this.error = null;

    try {
      // Aplicar legenda global se configurado
      if (this.config.globalCaption && this.globalCaption.trim()) {
        this.photos.forEach(photo => {
          if (!photo.caption.trim()) {
            photo.caption = this.globalCaption.trim();
          }
        });
      }

      // Separar fotos de upload e de câmera
      const uploadPhotos = this.photos.filter(p => p.file);
      const cameraPhotos = this.photos.filter(p => p.imageBase64);

      const savedPhotos: PatientPhoto[] = [];

      // Upload de arquivos
      if (uploadPhotos.length > 0) {
        const files = uploadPhotos.map(p => p.file!);
        const photosInfo = uploadPhotos.map(p => ({
          caption: p.caption || undefined,
          order: p.order,
        }));

        const uploadedPhotos = await this.photoService
          .uploadMultiplePhotos(this.patientId, files, this.folderId || undefined, photosInfo)
          .toPromise();

        if (uploadedPhotos) {
          savedPhotos.push(...uploadedPhotos);
        }
      }

      // Upload de fotos da câmera
      if (cameraPhotos.length > 0) {
        const cameraData = cameraPhotos.map(p => ({
          imageBase64: p.imageBase64!,
          caption: p.caption || undefined,
          order: p.order,
        }));

        const capturedPhotos = await this.photoService
          .captureMultiplePhotos(this.patientId, cameraData, this.folderId || undefined)
          .toPromise();

        if (capturedPhotos) {
          savedPhotos.push(...capturedPhotos);
        }
      }

      this.photosUploaded.emit(savedPhotos);
      this.notificationService.success(
        `${savedPhotos.length} foto(s) salva(s) com sucesso!`
      );
      this.resetComponent();
    } catch (error: any) {
      console.error('Erro ao salvar fotos:', error);
      this.error = 'Erro ao salvar fotos. Tente novamente.';
      this.notificationService.error('Erro ao salvar fotos');
    } finally {
      this.isUploading = false;
    }
  }

  private resetComponent(): void {
    this.photos = [];
    this.globalCaption = '';
    this.photoCounter = 0;
    this.closeCamera();
  }

  // Métodos de Interface
  canAddMorePhotos(): boolean {
    const maxFiles = this.config.maxFiles || 10;
    return this.photos.length < maxFiles;
  }

  getPhotoCount(): string {
    const maxFiles = this.config.maxFiles || 10;
    return `${this.photos.length}/${maxFiles}`;
  }

  closeComponent(): void {
    this.resetComponent();
    this.close.emit();
  }
}
