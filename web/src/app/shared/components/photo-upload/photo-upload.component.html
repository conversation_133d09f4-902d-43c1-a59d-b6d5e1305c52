<div class="photo-upload-container">
  <!-- Header -->
  <div class="flex justify-between items-center mb-4">
    <h3 class="text-lg font-semibold text-gray-900">
      Upload de Fotos
      <span class="text-sm font-normal text-gray-500 ml-2">
        ({{ getPhotoCount() }})
      </span>
    </h3>
    <button
      (click)="closeComponent()"
      class="text-gray-400 hover:text-gray-500 focus:outline-none"
    >
      <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
    <p class="text-sm font-medium">{{ error }}</p>
  </div>

  <!-- Upload Actions -->
  <div class="mb-6 flex flex-wrap gap-3">
    <!-- Upload Button -->
    <button
      *ngIf="config.mode === 'upload' || config.mode === 'both'"
      (click)="fileInput.click()"
      [disabled]="!canAddMorePhotos() || isUploading"
      class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
      </svg>
      Selecionar Arquivos
    </button>

  </div>

  <!-- Hidden file input -->
  <input
    #fileInput
    type="file"
    accept="image/*"
    [multiple]="config.allowMultiple"
    (change)="onFileSelected($event)"
    class="hidden"
  />

  <!-- Global Caption -->
  <div *ngIf="config.globalCaption && config.showCaptions" class="mb-4">
    <label class="block text-sm font-medium text-gray-700 mb-2">
      Legenda para todas as fotos
    </label>
    <input
      type="text"
      [(ngModel)]="globalCaption"
      placeholder="Digite uma legenda que será aplicada a todas as fotos sem legenda individual"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
    />
  </div>

  <!-- Camera Modal -->
  <div
    *ngIf="isCameraOpen"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
      <!-- Camera Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Capturar Fotos</h3>
        <button
          (click)="closeCamera()"
          class="text-gray-400 hover:text-gray-500 focus:outline-none"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Camera Body -->
      <div class="px-6 py-4">
        <!-- Camera view -->
        <div class="relative bg-black rounded-lg overflow-hidden">
          <video
            #videoElement
            autoplay
            playsinline
            muted
            class="w-full h-auto max-h-[60vh]"
          ></video>

          <!-- Camera switch button -->
          <div *ngIf="multipleWebcamsAvailable" class="absolute top-2 right-2">
            <button
              class="p-2 bg-gray-800 bg-opacity-50 text-white rounded-full hover:bg-opacity-70 focus:outline-none"
              (click)="switchCamera()"
            >
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Hidden canvas for capturing -->
        <canvas #canvasElement class="hidden"></canvas>
      </div>

      <!-- Camera Footer -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button
          type="button"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
          (click)="closeCamera()"
        >
          Fechar
        </button>

        <button
          type="button"
          (click)="capturePhoto()"
          [disabled]="!canAddMorePhotos()"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
          </svg>
          Capturar
        </button>
      </div>
    </div>
  </div>

  <!-- Photos Grid -->
  <div *ngIf="photos.length > 0" class="mb-6">
    <h4 class="text-md font-medium text-gray-900 mb-3">
      Fotos Selecionadas ({{ photos.length }})
    </h4>
    
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <div
        *ngFor="let photo of photos; let i = index"
        class="relative group border border-gray-200 rounded-lg overflow-hidden"
        [class.opacity-50]="draggedPhotoIndex === i"
        draggable="true"
        (dragstart)="onDragStart($event, i)"
        (dragover)="onDragOver($event)"
        (drop)="onDrop($event, i)"
      >
        <!-- Photo Preview -->
        <div class="aspect-square">
          <img
            [src]="photo.preview"
            [alt]="photo.caption || 'Foto ' + (i + 1)"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Photo Controls -->
        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
          <!-- Remove Button -->
          <button
            (click)="removePhoto(i)"
            class="p-1 bg-red-600 text-white rounded-full mx-1 hover:bg-red-700"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>

          <!-- Reorder Buttons -->
          <div *ngIf="config.showReorder" class="flex flex-col">
            <button
              (click)="movePhotoUp(i)"
              [disabled]="i === 0"
              class="p-1 bg-blue-600 text-white rounded-full mb-1 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
              </svg>
            </button>
            <button
              (click)="movePhotoDown(i)"
              [disabled]="i === photos.length - 1"
              class="p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Order Badge -->
        <div class="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
          {{ photo.order }}
        </div>

        <!-- Camera Badge -->
        <div *ngIf="photo.isFromCamera" class="absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
          📷
        </div>

        <!-- Caption Input -->
        <div *ngIf="config.showCaptions" class="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-2">
          <input
            type="text"
            [(ngModel)]="photo.caption"
            placeholder="Legenda da foto"
            class="w-full text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="flex justify-between items-center">
    <div class="text-sm text-gray-500">
      <span *ngIf="photos.length === 0">Nenhuma foto selecionada</span>
      <span *ngIf="photos.length > 0">{{ photos.length }} foto(s) pronta(s) para upload</span>
    </div>

    <div class="flex space-x-3">
      <button
        type="button"
        (click)="closeComponent()"
        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
      >
        Cancelar
      </button>

      <button
        type="button"
        (click)="savePhotos()"
        [disabled]="photos.length === 0 || isUploading || !patientId"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <span *ngIf="isUploading" class="mr-2">
          <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        <span *ngIf="!isUploading">
          <svg class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </span>
        {{ isUploading ? 'Salvando...' : 'Salvar Fotos' }}
      </button>
    </div>
  </div>
</div>
