import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreatmentProcedure, TreatmentProcedureStatus } from '../../../core/models/treatment-procedure.model';

export interface ToothData {
  number: string;
  status: 'pending' | 'completed' | 'none';
  procedures: TreatmentProcedure[];
}

@Component({
  selector: 'app-odontogram',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './odontogram.component.html',
  styleUrls: ['./odontogram.component.scss']
})
export class OdontogramComponent implements OnInit {
  @Input() procedures: TreatmentProcedure[] = [];
  @Output() toothSelected = new EventEmitter<string>();

  // Dente selecionado atualmente
  selectedTooth: string | null = null;

  constructor() { }

  ngOnInit(): void {
  }

  /**
   * Manipula o clique em um dente
   * @param toothNumber Número do dente clicado
   */
  onToothClick(toothNumber: string): void {
    this.selectedTooth = toothNumber;
    this.toothSelected.emit(toothNumber);
  }

  /**
   * Obtém a cor de preenchimento para um dente com base em seus procedimentos
   * @param toothNumber Número do dente
   * @returns Cor CSS para o preenchimento
   */
  getToothFillColor(toothNumber: string): string {
    // Encontrar procedimentos associados a este dente
    const toothProcedures = this.procedures.filter(proc => proc.tooth === toothNumber);
    
    if (toothProcedures.length === 0) {
      return 'rgba(255, 255, 255, 0)'; // Transparente
    }
    
    // Verificar se há pelo menos um procedimento concluído
    const hasCompleted = toothProcedures.some(proc => proc.status === TreatmentProcedureStatus.COMPLETED);
    if (hasCompleted) {
      return 'rgba(16, 185, 129, 0.6)'; // Verde com transparência
    }
    
    // Verificar se há pelo menos um procedimento pendente ou em andamento
    const hasPending = toothProcedures.some(proc => 
      proc.status === TreatmentProcedureStatus.PENDING || 
      proc.status === TreatmentProcedureStatus.IN_PROGRESS
    );
    if (hasPending) {
      return 'rgba(239, 68, 68, 0.6)'; // Vermelho com transparência
    }
    
    return 'rgba(255, 255, 255, 0)'; // Transparente por padrão
  }
}
