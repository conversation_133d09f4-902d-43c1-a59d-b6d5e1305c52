<div class="odontogram-container">
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
       viewBox="0 0 1080 550"
       style="width: 100%; height: 100%;">

    <!-- R<PERSON><PERSON><PERSON> das vistas -->
    <svg>
      <text fill="#9e9e9e" x="0" y="65" style="font-size: 20px;">Vestibular</text>
      <text fill="#9e9e9e" x="0" y="140" style="font-size: 20px;">Oclusal</text>
      <text fill="#9e9e9e" x="0" y="215" style="font-size: 18px;">Palatina</text>
      <text fill="#9e9e9e" x="0" y="347" style="font-size: 18px;">Lingual</text>
      <text fill="#9e9e9e" x="0" y="418" style="font-size: 20px;">Oclusal</text>
      <text fill="#9e9e9e" x="0" y="497" style="font-size: 20px;">Vestibular</text>
    </svg>

    <!-- Dente 18 -->
    <g id="tooth-18">
      <!-- Vestibular superior -->
      <foreignObject
        height="110"
        width="83"
        x="95"
        y="0"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente vestibular -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente18/denteCompletoVestibular.png"
            width="76" height="93" alt="18" name="18">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M18.348,91.5l5.206.889,5.762-1.7L33.5,87.581l2.3-1.361,4.956,2.525,6.035,1.943,6.021,1.7,5.039-.889V67.621h-39.5Z"
                fill="rgba(255,255,255,0)"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                (click)="onToothClick('18')">
          </path>
        </svg>
      </foreignObject>

      <!-- Oclusal superior -->
      <foreignObject
        height="65"
        width="55"
        x="115"
        y="102"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente oclusal -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente18/oclusal.png"
            width="76" height="93" alt="18" name="18">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M12.983,23.163l7.381-4.583L30.5,13.637l9.9-2.81L52.5,9.07l6.543,1.016L65.211,77.7,60.947,80.52l-7.813,1.644L45,83.21H37.45L23.021,84.864Z"
                fill="rgba(255,255,255,0)"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                (click)="onToothClick('18')">
          </path>
        </svg>
      </foreignObject>

      <!-- Palatina -->
      <foreignObject
        height="100"
        width="65"
        x="105"
        y="167"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente palatina -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente18/denteCompletoLingual.png"
            width="76" height="93" alt="18" name="18">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M17.991.957h6.448l4.25.8L32.68,4.5l3.455,1.542,7.958-2.816L50.081.957H55.16l3.958.8-.5,22.394H17.991Z"
                [attr.fill]="getToothFillColor('18')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '18' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('18')">
          </path>
        </svg>
      </foreignObject>
    </g>

    <!-- Dente 17 -->
    <g id="tooth-17">
      <!-- Vestibular superior -->
      <foreignObject
        height="110"
        width="83"
        x="159.6"
        y="0"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente vestibular -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente17/denteCompletoVestibular.png"
            width="76" height="93" alt="17" name="17">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M18.348,91.5l5.206.889,5.762-1.7L33.5,87.581l2.3-1.361,4.956,2.525,6.035,1.943,6.021,1.7,5.039-.889V67.621h-39.5Z"
                [attr.fill]="getToothFillColor('17')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '17' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('17')">
          </path>
        </svg>
      </foreignObject>

      <!-- Oclusal superior -->
      <foreignObject
        height="65"
        width="55"
        x="179.6"
        y="102"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente oclusal -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente17/oclusal.png"
            width="76" height="93" alt="17" name="17">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M12.983,23.163l7.381-4.583L30.5,13.637l9.9-2.81L52.5,9.07l6.543,1.016L65.211,77.7,60.947,80.52l-7.813,1.644L45,83.21H37.45L23.021,84.864Z"
                [attr.fill]="getToothFillColor('17')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '17' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('17')">
          </path>
        </svg>
      </foreignObject>

      <!-- Palatina -->
      <foreignObject
        height="100"
        width="65"
        x="169.6"
        y="167"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente palatina -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente17/denteCompletoLingual.png"
            width="76" height="93" alt="17" name="17">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M17.991.957h6.448l4.25.8L32.68,4.5l3.455,1.542,7.958-2.816L50.081.957H55.16l3.958.8-.5,22.394H17.991Z"
                [attr.fill]="getToothFillColor('17')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '17' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('17')">
          </path>
        </svg>
      </foreignObject>
    </g>

    <!-- Dente 16 -->
    <g id="tooth-16">
      <!-- Vestibular superior -->
      <foreignObject
        height="110"
        width="83"
        x="224.2"
        y="0"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente vestibular -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente16/denteCompletoVestibular.png"
            width="76" height="93" alt="16" name="16">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M18.348,91.5l5.206.889,5.762-1.7L33.5,87.581l2.3-1.361,4.956,2.525,6.035,1.943,6.021,1.7,5.039-.889V67.621h-39.5Z"
                [attr.fill]="getToothFillColor('16')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '16' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('16')">
          </path>
        </svg>
      </foreignObject>

      <!-- Oclusal superior -->
      <foreignObject
        height="65"
        width="55"
        x="244.2"
        y="102"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente oclusal -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente16/oclusal.png"
            width="76" height="93" alt="16" name="16">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M12.983,23.163l7.381-4.583L30.5,13.637l9.9-2.81L52.5,9.07l6.543,1.016L65.211,77.7,60.947,80.52l-7.813,1.644L45,83.21H37.45L23.021,84.864Z"
                [attr.fill]="getToothFillColor('16')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '16' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('16')">
          </path>
        </svg>
      </foreignObject>

      <!-- Palatina -->
      <foreignObject
        height="100"
        width="65"
        x="234.2"
        y="167"
        style="pointer-events: none;">
        <svg viewBox="0 0 76 93">
          <!-- Imagem do dente palatina -->
          <image
            xlink:href="https://sistema.clinicorp.com/images/Dentes/OdontogramaPermanente/Dente16/denteCompletoLingual.png"
            width="76" height="93" alt="16" name="16">
          </image>

          <!-- Área clicável com cores de status -->
          <path d="M17.991.957h6.448l4.25.8L32.68,4.5l3.455,1.542,7.958-2.816L50.081.957H55.16l3.958.8-.5,22.394H17.991Z"
                [attr.fill]="getToothFillColor('16')"
                fill-opacity="0.6"
                stroke="#707070"
                stroke-opacity="0.4"
                style="cursor: pointer; pointer-events: all;"
                [attr.style]="selectedTooth === '16' ? 'cursor: pointer; pointer-events: all; animation: pulse 1.5s infinite;' : 'cursor: pointer; pointer-events: all;'"
                (click)="onToothClick('16')">
          </path>
        </svg>
      </foreignObject>
    </g>

    <!-- Números dos dentes -->
    <rect fill="white" x="123" y="248" height="25" width="25" rx="12" ry="12"></rect>
    <text fill="#9e9e9e" x="125" y="268" style="font-size: 20px;">18</text>
    <rect fill="white" x="187.6" y="248" height="25" width="25" rx="12" ry="12"></rect>
    <text fill="#9e9e9e" x="189.6" y="268" style="font-size: 20px;">17</text>
    <rect fill="white" x="252.2" y="248" height="25" width="25" rx="12" ry="12"></rect>
    <text fill="#9e9e9e" x="254.2" y="268" style="font-size: 20px;">16</text>
  </svg>
</div>
