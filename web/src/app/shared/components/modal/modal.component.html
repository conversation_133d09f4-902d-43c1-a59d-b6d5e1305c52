<div *ngIf="isOpen" class="fixed inset-0 z-50 flex items-center justify-center">
  <!-- Overlay -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50"
    (click)="closeModal()"
  ></div>

  <!-- Modal -->
  <div [ngClass]="fullscreen ? 'w-full h-full max-w-full max-h-full rounded-none' : 'w-auto max-w-full mx-2 rounded-lg max-h-[95vh]'" class="bg-white shadow-xl z-10 overflow-hidden flex flex-col">
    <!-- Header -->
    <div class="flex justify-between items-center p-5 border-b bg-gray-50">
      <h3 class="text-xl font-semibold text-gray-800">{{ title }}</h3>
      <button
        type="button"
        class="text-gray-500 hover:text-gray-700 transition-colors"
        (click)="closeModal()"
      >
        <svg
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="p-0 flex-1 overflow-auto">
      <ng-content></ng-content>
    </div>

    <!-- Footer -->
    <div *ngIf="showDefaultFooter" class="flex justify-end p-5 border-t bg-gray-50">
      <ng-content select="[footer]"></ng-content>
    </div>
  </div>
</div>
