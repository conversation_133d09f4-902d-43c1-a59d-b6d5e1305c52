import {
  Component,
  Input,
  Output,
  EventEmitter,
  ContentChild,
  ElementRef,
  AfterContentInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal.component.html',
  styles: [],
})
export class ModalComponent implements AfterContentInit {
  @Input() title: string = '';
  @Input() isOpen: boolean = false;
  @Input() showDefaultFooter: boolean = true;
  @Input() fullscreen: boolean = false;
  @Output() close = new EventEmitter<void>();
  @ContentChild('[footer]') footerContent: ElementRef | null = null;

  hasFooterContent = false;

  ngAfterContentInit() {
    this.hasFooterContent = !!this.footerContent;
  }

  closeModal() {
    this.close.emit();
  }
}
