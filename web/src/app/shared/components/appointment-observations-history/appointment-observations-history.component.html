<!-- Overlay -->
<div *ngIf="isOpen" class="fixed inset-0 bg-black bg-opacity-50 z-50" (click)="onClose()"></div>

<!-- Side Panel -->
<div 
  class="fixed top-0 left-0 h-full bg-white shadow-xl transform transition-transform duration-300 ease-in-out z-50"
  [class.translate-x-0]="isOpen"
  [class.-translate-x-full]="!isOpen"
  style="width: 500px;"
>
  <!-- Header -->
  <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gray-50">
    <div>
      <h3 class="text-lg font-semibold text-gray-900">Histórico de Observações</h3>
      <p class="text-sm text-gray-600">Observações anteriores dos agendamentos</p>
    </div>
    <button
      (click)="onClose()"
      class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 hover:bg-gray-200 rounded-full"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-y-auto p-6">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">Carregando observações...</span>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !isLoading" class="text-center py-8">
      <div class="text-red-600 mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <p class="text-red-600 font-medium">{{ error }}</p>
      <button 
        (click)="loadObservations()"
        class="mt-3 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
      >
        Tentar novamente
      </button>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && !error && observations.length === 0" class="text-center py-8">
      <div class="text-gray-400 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <h4 class="text-lg font-medium text-gray-900 mb-2">Nenhuma observação encontrada</h4>
      <p class="text-gray-600">Este paciente ainda não possui observações registradas em agendamentos.</p>
    </div>

    <!-- Observations List -->
    <div *ngIf="!isLoading && !error && observations.length > 0" class="space-y-4">
      <div 
        *ngFor="let observation of observations; trackBy: trackByObservationId"
        class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
      >
        <!-- Observation Header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-900">
                {{ formatDate(observation.createdAt) }}
              </span>
              <span
                *ngIf="observation.appointment"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="getStatusClass(observation.appointment.status)"
              >
                {{ getStatusText(observation.appointment.status) }}
              </span>
            </div>
            <div *ngIf="observation.appointment" class="text-xs text-gray-500">
              Agendamento: {{ formatAppointmentDate(observation.appointment.date) }} às {{ observation.appointment.time }}
            </div>
          </div>
          <!-- Link para o agendamento -->
          <div *ngIf="observation.appointment" class="flex-shrink-0">
            <button
              (click)="openAppointment(observation.appointment.id)"
              class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50 transition-colors duration-200"
              title="Abrir agendamento"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Observation Content -->
        <div class="bg-gray-50 rounded-md p-3">
          <p class="text-sm text-gray-800 leading-relaxed">{{ observation.note }}</p>
        </div>


      </div>
    </div>
  </div>
</div>
