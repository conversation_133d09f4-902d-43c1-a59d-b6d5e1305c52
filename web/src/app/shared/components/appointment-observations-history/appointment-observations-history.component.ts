import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppointmentObservationService, AppointmentObservation } from '../../../core/services/appointment-observation.service';

@Component({
  selector: 'app-appointment-observations-history',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './appointment-observations-history.component.html',
  styleUrl: './appointment-observations-history.component.scss'
})
export class AppointmentObservationsHistoryComponent implements OnInit {
  @Input() patientId!: number;
  @Input() isOpen = false;
  @Output() close = new EventEmitter<void>();

  observations: AppointmentObservation[] = [];
  isLoading = false;
  error: string | null = null;

  constructor(private appointmentObservationService: AppointmentObservationService) {}

  ngOnInit(): void {
    if (this.patientId && this.isOpen) {
      this.loadObservations();
    }
  }

  ngOnChanges(): void {
    if (this.patientId && this.isOpen) {
      this.loadObservations();
    }
  }

  loadObservations(): void {
    this.isLoading = true;
    this.error = null;

    this.appointmentObservationService.getObservationsByPatient(this.patientId).subscribe({
      next: (observations) => {
        this.observations = observations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar observações:', error);
        this.error = 'Erro ao carregar histórico de observações';
        this.isLoading = false;
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatAppointmentDate(date: Date): string {
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  getStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'confirmed': 'Confirmado',
      'unconfirmed': 'Não confirmado',
      'late': 'Atrasado',
      'no-show': 'Não compareceu',
      'cancelled': 'Desmarcado',
      'rescheduled': 'Remarcado',
      'in-progress': 'Em andamento',
      'completed': 'Concluído',
      // Para compatibilidade com registros antigos
      'scheduled-unconfirmed': 'Agendado (não confirmado)',
      'scheduled-confirmed': 'Agendado (confirmado)',
      'unscheduled': 'Não agendado'
    };
    return statusMap[status] || status;
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'scheduled-unconfirmed': 'bg-yellow-100 text-yellow-800',
      'scheduled-confirmed': 'bg-blue-100 text-blue-800',
      'unscheduled': 'bg-gray-100 text-gray-800',
      'in-progress': 'bg-orange-100 text-orange-800',
      'completed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  }

  trackByObservationId(_index: number, observation: AppointmentObservation): string {
    return observation.id;
  }

  openAppointment(appointmentId: number): void {
    const url = `http://localhost:4200/schedulings/${appointmentId}`;
    window.open(url, '_blank');
  }
}
