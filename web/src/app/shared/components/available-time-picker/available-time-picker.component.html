<div class="space-y-2">
  <label class="block text-sm font-medium text-gray-700">
    <PERSON><PERSON><PERSON><PERSON> *
  </label>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-4">
    <div class="flex items-center space-x-2 text-gray-500">
      <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="text-sm">Carregando horários...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="bg-red-50 border border-red-200 rounded-md p-3">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-red-800">{{ error }}</p>
      </div>
    </div>
  </div>

  <!-- No Times Available -->
  <div *ngIf="!isLoading && !error && availableTimes.length === 0 && dentistId && date" 
       class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-yellow-800">Nenhum horário disponível neste dia</p>
      </div>
    </div>
  </div>

  <!-- Instructions -->
  <div *ngIf="!isLoading && !error && !dentistId || !date" 
       class="bg-gray-50 border border-gray-200 rounded-md p-3">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-gray-600">Selecione um dentista e uma data para ver os horários disponíveis</p>
      </div>
    </div>
  </div>

  <!-- Available Times Grid -->
  <div *ngIf="!isLoading && !error && availableTimes.length > 0" 
       class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 max-h-48 overflow-y-auto">
    <button
      *ngFor="let time of availableTimes"
      type="button"
      (click)="onTimeSelect(time)"
      [disabled]="disabled"
      class="relative px-3 py-2 text-sm font-medium rounded-md border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      [class]="isTimeSelected(time) 
        ? 'bg-blue-600 text-white border-blue-600 shadow-md' 
        : 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-300'"
      [class.opacity-50]="disabled"
      [class.cursor-not-allowed]="disabled"
    >
      <div class="text-center">
        <div class="font-medium">{{ formatTime(time) }}</div>
        <div class="text-xs opacity-75">até {{ getEndTime(time) }}</div>
      </div>
    </button>
  </div>

  <!-- Selected Time Info -->
  <div *ngIf="selectedTime && availableTimes.includes(selectedTime)" 
       class="bg-green-50 border border-green-200 rounded-md p-3 mt-2">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-green-800">
          <span class="font-medium">Horário selecionado:</span> 
          {{ selectedTime }} às {{ getEndTime(selectedTime) }} ({{ duration }} min)
        </p>
      </div>
    </div>
  </div>
</div>
