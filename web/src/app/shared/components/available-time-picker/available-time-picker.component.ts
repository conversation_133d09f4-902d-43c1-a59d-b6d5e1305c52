import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { DentistScheduleService } from '../../../core/services/dentist-schedule.service';
import { AvailableTimes } from '../../../core/models/dentist-schedule.model';

@Component({
  selector: 'app-available-time-picker',
  templateUrl: './available-time-picker.component.html',
  styleUrls: ['./available-time-picker.component.scss'],
  standalone: false,
})
export class AvailableTimePickerComponent implements OnInit, OnChanges {
  @Input() dentistId: number | null = null;
  @Input() date: string | null = null;
  @Input() duration: number = 30;
  @Input() selectedTime: string | null = null;
  @Input() excludeAppointmentId?: number;
  @Input() disabled: boolean = false;
  @Output() timeSelected = new EventEmitter<string>();

  availableTimes: string[] = [];
  isLoading = false;
  error: string | null = null;

  constructor(private dentistScheduleService: DentistScheduleService) {}

  ngOnInit(): void {
    this.loadAvailableTimes();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Recarregar horários quando dentista, data ou duração mudarem
    if (changes['dentistId'] || changes['date'] || changes['duration'] || changes['excludeAppointmentId']) {
      this.loadAvailableTimes();
    }
  }

  private loadAvailableTimes(): void {
    // Limpar seleção anterior se não estiver mais disponível
    this.error = null;

    if (!this.dentistId || !this.date) {
      this.availableTimes = [];
      return;
    }

    this.isLoading = true;
    this.dentistScheduleService.getAvailableTimes(
      this.dentistId, 
      this.date, 
      this.duration,
      this.excludeAppointmentId
    ).subscribe({
      next: (response: AvailableTimes) => {
        this.availableTimes = response.availableTimes;
        this.isLoading = false;

        // Se o horário selecionado não está mais disponível, limpar seleção
        if (this.selectedTime && !this.availableTimes.includes(this.selectedTime)) {
          this.onTimeSelect('');
        }
      },
      error: (error) => {
        console.error('Erro ao carregar horários disponíveis:', error);
        this.error = 'Erro ao carregar horários disponíveis';
        this.availableTimes = [];
        this.isLoading = false;
      }
    });
  }

  onTimeSelect(time: string): void {
    if (this.disabled) return;
    
    this.timeSelected.emit(time);
  }

  isTimeSelected(time: string): boolean {
    return this.selectedTime === time;
  }

  formatTime(time: string): string {
    // Converter formato HH:MM para formato mais amigável se necessário
    return time;
  }

  getEndTime(startTime: string): string {
    if (!startTime) return '';
    
    const [hours, minutes] = startTime.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + this.duration;
    const endHours = Math.floor(totalMinutes / 60);
    const endMins = totalMinutes % 60;
    
    return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
  }
}
