<!-- <PERSON>t<PERSON> com ícone apenas (para uso fora do dropdown) -->
<button
  *ngIf="!text"
  (click)="onClick()"
  class="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
  [title]="tooltip"
  [ngClass]="{
    'hover:text-red-600': hoverColor === 'red',
    'hover:text-blue-600': hoverColor === 'blue',
    'hover:text-green-600': hoverColor === 'green',
    'hover:text-orange-600': hoverColor === 'orange',
    'hover:text-purple-600': hoverColor === 'purple'
  }"
>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    class="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      [attr.d]="svgPath"
    />
  </svg>
</button>

<!-- Botão com ícone e texto (para uso no dropdown) -->
<button
  *ngIf="text"
  (click)="onClick()"
  class="flex items-center px-4 py-2 text-sm hover:bg-gray-100 focus:outline-none transition-colors"
  [ngClass]="{ 'w-full text-left': fullWidth }"
  [class]="textColor"
>
  <svg
    *ngIf="svgPath"
    xmlns="http://www.w3.org/2000/svg"
    class="h-5 w-5 mr-2"
    [class]="iconColor"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      [attr.d]="svgPath"
    />
  </svg>
  <span>{{ text }}</span>
</button>
