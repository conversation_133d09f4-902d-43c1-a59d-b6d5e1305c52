import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-action-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './action-button.component.html',
  styleUrls: ['./action-button.component.scss'],
})
export class ActionButtonComponent {
  @Input() svgPath: string = '';
  @Input() tooltip: string = '';
  @Input() hoverColor: string = 'blue';
  @Input() text: string = '';
  @Input() fullWidth: boolean = false;
  @Input() textColor: string = 'text-gray-700';
  @Input() iconColor: string = 'text-gray-500';

  @Output() clicked = new EventEmitter<void>();

  onClick(): void {
    this.clicked.emit();
  }
}
