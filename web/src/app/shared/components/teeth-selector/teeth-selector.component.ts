import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-teeth-selector',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './teeth-selector.component.html',
  styleUrl: './teeth-selector.component.scss'
})
export class TeethSelectorComponent implements OnInit {
  @Input() selectedTeeth: string[] = [];
  @Output() selectedTeethChange = new EventEmitter<string[]>();
  @ViewChild('toothInput') toothInput!: ElementRef;

  currentTooth: string | number = '';
  errorMessage: string = '';

  ngOnInit(): void {
    if (!this.selectedTeeth) {
      this.selectedTeeth = [];
    }
  }

  addTooth(): void {
    this.errorMessage = '';

    // Converter para string se for número
    const toothValue = typeof this.currentTooth === 'number' ?
      this.currentTooth.toString() :
      (this.currentTooth || '');

    // Verificar se está vazio
    if (!toothValue || (typeof toothValue === 'string' && !toothValue.trim())) {
      this.errorMessage = 'Digite um número de dente válido';
      return;
    }

    // Verificar se é um número válido
    const toothNumber = parseInt(toothValue);
    if (isNaN(toothNumber)) {
      this.errorMessage = 'Digite apenas números';
      return;
    }

    if (toothNumber < 1 || toothNumber > 48) {
      this.errorMessage = 'O número do dente deve estar entre 1 e 48';
      return;
    }

    // Converter para string para garantir consistência
    const toothStr = toothNumber.toString();

    // Verificar se já existe
    if (this.selectedTeeth.includes(toothStr)) {
      this.errorMessage = 'Este dente já foi adicionado';
      return;
    }

    console.log('Adicionando dente:', toothStr);

    // Adicionar o dente
    this.selectedTeeth.push(toothStr);

    // Criar uma cópia do array para garantir que o Angular detecte a mudança
    const updatedTeeth = [...this.selectedTeeth];

    // Emitir o evento com a cópia do array
    this.selectedTeethChange.emit(updatedTeeth);

    console.log('Dentes após adição:', updatedTeeth);

    // Limpar o campo e focar nele novamente
    this.currentTooth = '';
    setTimeout(() => {
      if (this.toothInput) {
        this.toothInput.nativeElement.focus();
      }
    }, 0);
  }

  removeTooth(tooth: string): void {
    const index = this.selectedTeeth.indexOf(tooth);
    if (index !== -1) {
      this.selectedTeeth.splice(index, 1);

      // Criar uma cópia do array para garantir que o Angular detecte a mudança
      const updatedTeeth = [...this.selectedTeeth];

      console.log('Dentes após remoção:', updatedTeeth);

      // Emitir o evento com a cópia do array
      this.selectedTeethChange.emit(updatedTeeth);
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.addTooth();
    }
  }

  getTeethString(): string {
    return this.selectedTeeth.join(', ');
  }

  clearError(): void {
    this.errorMessage = '';
  }
}
