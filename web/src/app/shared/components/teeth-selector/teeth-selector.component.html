<div class="teeth-selector">
  <div class="flex items-center mb-2">
    <input
      #toothInput
      type="number"
      [(ngModel)]="currentTooth"
      (keydown)="onKeyDown($event)"
      (focus)="clearError()"
      (change)="clearError()"
      placeholder="1"
      class="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
      [class.border-red-500]="errorMessage"
      min="1"
      max="48"
    />
    <button
      type="button"
      (click)="addTooth()"
      class="px-3 py-2 h-[42px] bg-blue-600 -ml-1 text-white rounded-r-md hover:bg-blue-700 transition-colors"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="errorMessage" class="text-red-500 text-xs mb-2">
    {{ errorMessage }}
  </div>

  <!-- Tags de dentes selecionados -->
  <div *ngIf="selectedTeeth.length > 0" class="flex flex-wrap gap-2 mt-2">
    <div
      *ngFor="let tooth of selectedTeeth"
      class="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md"
    >
      <span class="mr-1">{{ tooth }}</span>
      <button
        type="button"
        (click)="removeTooth(tooth)"
        class="text-blue-600 hover:text-blue-800"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Mensagem de ajuda -->
  <p *ngIf="selectedTeeth.length === 0" class="text-xs text-gray-500 mt-1">
    Adicione os números dos dentes (1-48)
  </p>

  <!-- Contagem de dentes -->
  <p *ngIf="selectedTeeth.length > 0" class="text-xs text-blue-600 mt-1">
    {{ selectedTeeth.length }} dente(s) selecionado(s)
  </p>
</div>
