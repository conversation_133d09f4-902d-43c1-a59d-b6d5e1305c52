/* Estilos específicos para o seletor de tipo de paciente */

/* Estilo para o dropdown que evita que seja cortado por containers pais */
.dropdown-menu {
  position: fixed !important;
  width: auto !important;
  min-width: 200px;
  max-width: 300px;
  z-index: 9999 !important; /* Valor alto para garantir que fique acima de outros elementos */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Transição suave para o dropdown */
.dropdown-menu {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
