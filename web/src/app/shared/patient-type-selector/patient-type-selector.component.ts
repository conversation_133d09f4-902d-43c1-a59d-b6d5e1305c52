import {
  Component,
  forwardRef,
  Input,
  OnInit,
  ElementRef,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
  FormsModule,
} from '@angular/forms';
import { PatientType } from '../../core/models/patient-type.model';
import { PatientTypeService } from '../../core/services/patient-type.service';

@Component({
  selector: 'app-patient-type-selector',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './patient-type-selector.component.html',
  styleUrls: ['./patient-type-selector.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PatientTypeSelectorComponent),
      multi: true,
    },
  ],
})
export class PatientTypeSelectorComponent
  implements OnInit, ControlValueAccessor
{
  @Input() label: string = 'Tipo de Paciente';
  @Input() placeholder: string = 'Selecione um tipo de paciente';
  @Input() required: boolean = false;
  @Input() invalid: boolean = false;
  @Input() touched: boolean = false;
  @Input() disabled: boolean = false;

  patientTypes: PatientType[] = [];
  selectedTypeId: number | null = null;
  selectedType: PatientType | null = null;
  isLoading: boolean = false;
  isOpen: boolean = false;

  // ControlValueAccessor
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(
    private patientTypeService: PatientTypeService,
    private elementRef: ElementRef
  ) {}

  // Fechar o dropdown quando clicar fora do componente
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }

  // Reposicionar o dropdown quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize() {
    if (this.isOpen) {
      this.positionDropdown();
    }
  }

  // Reposicionar o dropdown quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll() {
    if (this.isOpen) {
      this.positionDropdown();
    }
  }

  ngOnInit(): void {
    console.log('PatientTypeSelectorComponent inicializado com selectedTypeId:', this.selectedTypeId);
    this.loadPatientTypes();
  }

  loadPatientTypes(): void {
    this.isLoading = true;
    // Carregar todos os tipos, não apenas os ativos, para garantir que encontremos o tipo do paciente
    this.patientTypeService.getAllPatientTypes(false).subscribe({
      next: (types) => {
        console.log('Tipos de paciente recebidos no selector:', types);

        if (types && types.length > 0) {
          this.patientTypes = types;
          console.log(
            'Tipos de paciente no selector:',
            this.patientTypes.map(t => ({ id: t.id, nome: t.nome, ativo: t.ativo }))
          );
        } else {
          this.patientTypes = [];
          console.warn('Nenhum tipo de paciente encontrado no selector');
        }

        // Atualizar o tipo selecionado se já tiver um valor
        if (this.selectedTypeId !== null && this.selectedTypeId !== undefined) {
          console.log('Atualizando tipo selecionado após carregar tipos. ID:', this.selectedTypeId);
          this.updateSelectedType();

          // Verificar se o tipo foi encontrado
          if (!this.selectedType && this.patientTypes.length > 0) {
            console.warn(`Tipo com ID ${this.selectedTypeId} não encontrado após carregar tipos. Tentando buscar tipo específico.`);

            // Tentar buscar o tipo específico
            this.patientTypeService.getPatientType(Number(this.selectedTypeId)).subscribe({
              next: (type) => {
                console.log('Tipo específico carregado:', type);
                // Verificar se o tipo já existe na lista
                if (!this.patientTypes.some(t => t.id === type.id)) {
                  // Adicionar o tipo à lista
                  this.patientTypes.push(type);
                  // Atualizar o tipo selecionado
                  this.updateSelectedType();
                }
              },
              error: (error) => {
                console.error('Erro ao carregar tipo específico:', error);
              }
            });
          }
        }
      },
      error: (error) => {
        console.error('Erro ao carregar tipos de paciente no selector:', error);
        this.patientTypes = [];
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  updateSelectedType(): void {
    console.log(
      'updateSelectedType chamado, selectedTypeId:',
      this.selectedTypeId
    );
    console.log('patientTypes disponíveis:', this.patientTypes);

    if (this.selectedTypeId !== null && this.selectedTypeId !== undefined && this.patientTypes.length > 0) {
      // Converter para número para garantir comparação correta
      const typeId = Number(this.selectedTypeId);

      this.selectedType =
        this.patientTypes.find((type) => type.id === typeId) ||
        null;

      console.log('Tipo encontrado:', this.selectedType);

      if (!this.selectedType) {
        console.warn(`Tipo de paciente com ID ${typeId} não encontrado na lista de tipos disponíveis.`);
        // Verificar se há algum tipo com ID semelhante (para depuração)
        this.patientTypes.forEach(type => {
          console.log(`Tipo disponível: ID=${type.id} (${typeof type.id}), Nome=${type.nome}`);
        });
      }
    } else {
      this.selectedType = null;
      console.log('Nenhum tipo selecionado ou tipos não carregados');
    }
  }

  // Métodos para o dropdown customizado
  toggleDropdown(): void {
    if (!this.disabled && !this.isLoading) {
      this.isOpen = !this.isOpen;

      if (this.isOpen) {
        // Posicionar o dropdown corretamente após abrir
        setTimeout(() => this.positionDropdown(), 0);
      }
    }
  }

  openDropdown(): void {
    if (!this.disabled && !this.isLoading) {
      this.isOpen = true;

      // Posicionar o dropdown corretamente após abrir
      setTimeout(() => this.positionDropdown(), 0);
    }
  }

  // Método para posicionar o dropdown corretamente
  positionDropdown(): void {
    const triggerElement = this.elementRef.nativeElement.querySelector('.cursor-pointer');
    const dropdownElement = this.elementRef.nativeElement.querySelector('.dropdown-menu');

    if (triggerElement && dropdownElement) {
      const rect = triggerElement.getBoundingClientRect();

      // Definir a largura do dropdown igual à largura do elemento trigger
      dropdownElement.style.width = `${rect.width}px`;

      // Posicionar o dropdown abaixo do elemento trigger
      dropdownElement.style.top = `${rect.bottom + window.scrollY}px`;
      dropdownElement.style.left = `${rect.left + window.scrollX}px`;
    }
  }

  onBlur(): void {
    // Não fechamos o dropdown aqui para permitir cliques nas opções
    // O fechamento é tratado pelo HostListener document:click
    this.onTouched();
  }

  selectType(type: PatientType | null): void {
    if (type) {
      this.selectedTypeId = type.id;
      this.selectedType = type;
      console.log('Tipo de paciente selecionado:', type);
      console.log('ID do tipo selecionado:', type.id);
    } else {
      this.selectedTypeId = null;
      this.selectedType = null;
      console.log('Tipo de paciente removido (null)');
    }

    // Notificar o formulário sobre a mudança
    this.onChange(this.selectedTypeId);
    this.onTouched();
    this.isOpen = false;
  }

  // Para o select nativo escondido (acessibilidade)
  onNativeSelectChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const value = select.value;

    if (value === '') {
      this.selectedTypeId = null;
      this.selectedType = null;
    } else {
      this.selectedTypeId = parseInt(value, 10);
      this.updateSelectedType();
    }

    this.onChange(this.selectedTypeId);
    this.onTouched();
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    console.log('writeValue chamado com valor:', value, 'tipo:', typeof value);

    // Garantir que o valor seja um número ou null
    if (value === null || value === undefined || value === '') {
      this.selectedTypeId = null;
    } else {
      this.selectedTypeId = Number(value);
    }

    console.log('selectedTypeId após conversão:', this.selectedTypeId);

    // Se os tipos já foram carregados, atualiza o tipo selecionado
    if (this.patientTypes.length > 0) {
      this.updateSelectedType();
    } else {
      // Se os tipos ainda não foram carregados, carrega-os e depois atualiza
      this.loadPatientTypes();
    }

    console.log('Após writeValue, selectedTypeId:', this.selectedTypeId);
    console.log('Após writeValue, selectedType:', this.selectedType);

    // Forçar uma atualização após um pequeno delay para garantir que o valor seja aplicado
    if (this.selectedTypeId !== null && this.selectedTypeId !== undefined) {
      setTimeout(() => {
        if (!this.selectedType && this.patientTypes.length > 0) {
          console.log('Tentando atualizar o tipo selecionado novamente após delay');
          this.updateSelectedType();
        }
      }, 500);
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
