<div>
  <label
    *ngIf="label"
    [for]="label"
    class="block text-sm font-medium text-gray-700 mb-1"
  >
    {{ label }}
    <span *ngIf="required" class="text-red-500">*</span>
  </label>

  <!-- Dropdown customizado -->
  <div class="relative">
    <!-- Campo de exibição (que parece um select) -->
    <div
      (click)="toggleDropdown()"
      class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 cursor-pointer flex items-center justify-between h-[42px]"
      [ngClass]="{
        'border-red-500': invalid && touched,
        'bg-gray-100': disabled,
        'border-blue-500 ring-2 ring-blue-500 ring-opacity-50': isOpen
      }"
      tabindex="0"
      (blur)="onBlur()"
      (keydown.enter)="toggleDropdown()"
      (keydown.space)="toggleDropdown()"
      (keydown.arrowdown)="openDropdown()"
    >
      <!-- Exibição do item selecionado ou placeholder -->
      <div class="flex items-center truncate">
        <div *ngIf="selectedType" class="flex items-center">
          <div
            class="w-4 h-4 rounded-full mr-2 flex-shrink-0"
            [style.backgroundColor]="selectedType.cor"
            [style.border]="'1px solid #e2e8f0'"
          ></div>
          <span>{{ selectedType.nome }}</span>
        </div>
        <span *ngIf="!selectedType" class="text-gray-500">{{
          placeholder
        }}</span>
      </div>

      <!-- Ícone de seta para baixo ou indicador de carregamento -->
      <div class="flex-shrink-0">
        <svg
          *ngIf="!isLoading"
          class="w-5 h-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 9l-7 7-7-7"
          ></path>
        </svg>

        <svg
          *ngIf="isLoading"
          class="animate-spin h-5 w-5 text-blue-500"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
    </div>

    <!-- Lista de opções (dropdown) -->
    <div
      *ngIf="isOpen && !disabled && !isLoading"
      class="fixed z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto dropdown-menu"
    >
      <!-- Opção "Nenhum" -->
      <div
        class="px-3 py-2 cursor-pointer hover:bg-gray-100"
        (click)="selectType(null)"
        (mousedown)="$event.preventDefault()"
      >
        <span class="text-gray-500">{{ placeholder }}</span>
      </div>

      <!-- Opções de tipos de paciente -->
      <div
        *ngFor="let type of patientTypes"
        class="px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center"
        [class.bg-blue-50]="selectedTypeId === type.id"
        (click)="selectType(type)"
        (mousedown)="$event.preventDefault()"
      >
        <!-- Círculo colorido -->
        <div
          class="w-4 h-4 rounded-full mr-2 flex-shrink-0"
          [style.backgroundColor]="type.cor"
          [style.border]="'1px solid #e2e8f0'"
        ></div>
        <span>{{ type.nome }}</span>
      </div>

      <!-- Mensagem quando não há tipos disponíveis -->
      <div
        *ngIf="patientTypes.length === 0"
        class="px-3 py-2 text-gray-500 text-center"
      >
        Nenhum tipo disponível
      </div>
    </div>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="invalid && touched" class="text-red-500 text-sm mt-1">
    Este campo é obrigatório
  </div>

  <!-- Select nativo escondido para dispositivos móveis e acessibilidade -->
  <select
    [id]="label"
    [disabled]="disabled || isLoading"
    (change)="onNativeSelectChange($event)"
    class="sr-only"
    aria-hidden="true"
    tabindex="-1"
  >
    <option value="">{{ placeholder }}</option>
    <option
      *ngFor="let type of patientTypes"
      [value]="type.id"
      [selected]="selectedTypeId === type.id"
    >
      {{ type.nome }}
    </option>
  </select>
</div>
