import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { AvailableTimePickerComponent } from './components/available-time-picker/available-time-picker.component';
import { ProcedureSelectorComponent } from './components/procedure-selector/procedure-selector.component';
import { AppointmentTimePickerComponent } from './components/appointment-time-picker/appointment-time-picker.component';

@NgModule({
  declarations: [AvailableTimePickerComponent],
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    ProcedureSelectorComponent,
    AppointmentTimePickerComponent,
  ],
  exports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    AvailableTimePickerComponent,
    ProcedureSelectorComponent,
    AppointmentTimePickerComponent,
  ],
})
export class SharedModule {}
