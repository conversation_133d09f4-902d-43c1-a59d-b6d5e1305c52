<div class="relative">
  <label [for]="label" class="block text-sm font-medium text-gray-700 mb-1">
    {{ label }}<span *ngIf="required" class="text-red-500">*</span>
  </label>

  <div class="relative">
    <input
      [id]="label"
      [formControl]="searchControl"
      [placeholder]="placeholder"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
      [ngClass]="{
        'border-red-500': invalid && touched,
        'bg-gray-100': isDisabled
      }"
      (focus)="onFocus()"
      (blur)="onBlur()"
      autocomplete="off"
    >

    <!-- Ícone de busca -->
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
  </div>

  <!-- Mensagem de erro -->
  <div *ngIf="invalid && touched" class="text-red-500 text-sm mt-1">
    {{ errorMessage }}
  </div>

  <!-- Dropdown de resultados -->
  <div *ngIf="showDropdown && filteredPatients.length > 0" class="fixed z-50 mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto patient-dropdown">
    <ul class="py-1">
      <li
        *ngFor="let patient of filteredPatients"
        class="px-4 py-2 hover:bg-blue-100 cursor-pointer flex justify-between items-center"
        (click)="selectPatient(patient)"
      >
        <div>
          <div class="font-medium">{{ patient.name }}</div>
          <div class="text-sm text-gray-500">CPF: {{ patient.cpf }}</div>
        </div>
        <div class="text-sm text-gray-500">{{ patient.phone }}</div>
      </li>
    </ul>
  </div>

  <!-- Mensagem de nenhum resultado -->
  <div *ngIf="showDropdown && filteredPatients.length === 0 && searchControl.value && searchControl.value.length >= 3" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md p-4 text-center text-gray-500">
    Nenhum paciente encontrado
  </div>

  <!-- Mensagem de orientação -->
  <div *ngIf="searchControl.value && searchControl.value.length > 0 && searchControl.value.length < 3" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md p-4 text-center text-gray-500">
    Digite pelo menos 3 caracteres para buscar
  </div>
</div>
