import { Component, OnInit, forwardRef, Input, OnDestroy, ElementRef, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  ReactiveFormsModule,
  NG_VALUE_ACCESSOR,
  ControlValueAccessor,
} from '@angular/forms';
import { PatientService } from '../../core/services/patient.service';
import { Patient } from '../../core/models/patient.model';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-patient-selector',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './patient-selector.component.html',
  styleUrl: './patient-selector.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PatientSelectorComponent),
      multi: true,
    },
  ],
})
export class PatientSelectorComponent
  implements OnInit, ControlValueAccessor, OnD<PERSON>roy
{
  @Input() label = 'Paciente';
  @Input() required = false;
  @Input() errorMessage = 'Paciente é obrigatório';
  @Input() placeholder = 'Digite o nome do paciente';
  @Input() invalid: boolean | undefined = false;
  @Input() touched: boolean | undefined = false;

  searchControl = new FormControl('');
  patients: Patient[] = [];
  filteredPatients: Patient[] = [];
  selectedPatient: Patient | null = null;
  showDropdown = false;
  isDisabled = false;
  private destroy$ = new Subject<void>();

  // ControlValueAccessor methods
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(
    private patientService: PatientService,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    // Carregar todos os pacientes
    this.loadPatients();

    // Configurar a busca com debounce
    this.searchControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(300), distinctUntilChanged())
      .subscribe((value) => {
        this.filterPatients(value || '');
        this.onTouched();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPatients(): void {
    this.patientService.getAllPatients().subscribe((patients) => {
      this.patients = patients;
      // Não preencher a lista de filtrados inicialmente
      this.filteredPatients = [];
    });
  }

  filterPatients(value: string): void {
    // Não filtrar se o componente estiver desabilitado
    if (this.isDisabled) {
      this.filteredPatients = [];
      this.showDropdown = false;
      return;
    }

    const filterValue = value.toLowerCase();

    // Mostrar resultados apenas se tiver pelo menos 3 caracteres
    if (filterValue.length < 3) {
      this.filteredPatients = [];
      this.showDropdown = false;
      return;
    }

    this.filteredPatients = this.patients.filter(
      (patient) =>
        patient.name.toLowerCase().includes(filterValue) ||
        patient.cpf?.toLowerCase().includes(filterValue) ||
        patient.phone?.toLowerCase().includes(filterValue)
    );

    this.showDropdown = true;

    // Posicionar o dropdown corretamente após filtrar
    setTimeout(() => this.positionDropdown(), 0);
  }

  selectPatient(patient: Patient): void {
    this.selectedPatient = patient;
    this.searchControl.setValue(patient.name, { emitEvent: false });
    this.onChange(patient.id);
    this.showDropdown = false;
  }

  // Reposicionar o dropdown quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize() {
    if (this.showDropdown) {
      this.positionDropdown();
    }
  }

  // Reposicionar o dropdown quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll() {
    if (this.showDropdown) {
      this.positionDropdown();
    }
  }

  onFocus(): void {
    // Não mostrar dropdown se o componente estiver desabilitado
    if (this.isDisabled) {
      return;
    }

    // Mostrar dropdown apenas se já tiver pelo menos 3 caracteres digitados
    const value = this.searchControl.value || '';
    if (value.length >= 3) {
      this.showDropdown = true;
      // Posicionar o dropdown corretamente após abrir
      setTimeout(() => this.positionDropdown(), 0);
    }
  }

  onBlur(): void {
    // Delay para permitir que o clique no item seja processado
    setTimeout(() => {
      this.showDropdown = false;
      this.onTouched();
    }, 200);
  }

  // Método para posicionar o dropdown corretamente
  positionDropdown(): void {
    const inputElement = this.elementRef.nativeElement.querySelector('input');
    const dropdownElement = this.elementRef.nativeElement.querySelector('.patient-dropdown');

    if (inputElement && dropdownElement) {
      const rect = inputElement.getBoundingClientRect();

      // Definir a largura do dropdown igual à largura do elemento input
      dropdownElement.style.width = `${rect.width}px`;

      // Posicionar o dropdown abaixo do elemento input
      dropdownElement.style.top = `${rect.bottom + window.scrollY}px`;
      dropdownElement.style.left = `${rect.left + window.scrollX}px`;
    }
  }

  // ControlValueAccessor interface
  writeValue(patientId: number): void {
    if (patientId) {
      this.patientService.getPatient(patientId).subscribe((patient) => {
        this.selectedPatient = patient;
        this.searchControl.setValue(patient.name, { emitEvent: false });
      });
    } else {
      this.selectedPatient = null;
      this.searchControl.setValue('', { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
    if (isDisabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }
}
