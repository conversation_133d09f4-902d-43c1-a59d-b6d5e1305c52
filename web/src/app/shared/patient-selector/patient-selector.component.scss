input {
  padding-left: 2.5rem; /* Espaço para o ícone de busca */
}

.dropdown-item {
  transition: background-color 0.2s ease;
}

.dropdown-container {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilo para o dropdown de pacientes */
.patient-dropdown {
  position: fixed !important;
  width: auto !important;
  min-width: 300px;
  max-width: 500px;
  z-index: 9999 !important; /* Valor alto para garantir que fique acima de outros elementos */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
