import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { PatientType } from '../../core/models/patient-type.model';
import { PatientTypeService } from '../../core/services/patient-type.service';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { PatientTypeFormComponent } from '../patient-type-form/patient-type-form.component';
import { ModalComponent } from '../../shared/components/modal/modal.component';

@Component({
  selector: 'app-patient-type-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PatientTypeFormComponent,
    ModalComponent,
  ],
  templateUrl: './patient-type-list.component.html',
  styleUrls: ['./patient-type-list.component.scss'],
})
export class PatientTypeListComponent implements OnInit {
  patientTypes: PatientType[] = [];
  isLoading = true;
  error: string | null = null;
  searchTerm: string = '';
  showOnlyActive: boolean = true;

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;

  // Modal
  isModalOpen = false;
  modalTitle = '';
  selectedPatientType: PatientType | null = null;

  // Modal de confirmação
  isConfirmModalOpen = false;
  patientTypeToDelete: PatientType | null = null;

  // Expose Math to the template
  Math = Math;

  constructor(private patientTypeService: PatientTypeService) {}

  ngOnInit(): void {
    this.loadPatientTypes();
  }

  loadPatientTypes(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.error = null;

    this.patientTypeService
      .getPatientTypes(
        page,
        this.itemsPerPage,
        this.searchTerm,
        this.showOnlyActive
      )
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response: PaginatedResponse<PatientType>) => {
          this.patientTypes = response.data;
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },
        error: (err) => {
          console.error('Erro ao carregar tipos de paciente:', err);
          this.error =
            'Não foi possível carregar a lista de tipos de paciente. Por favor, tente novamente mais tarde.';
        },
      });
  }

  searchPatientTypes(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.currentPage = 1; // Voltar para a primeira página ao pesquisar
    this.loadPatientTypes(this.currentPage);
  }

  toggleActiveFilter(): void {
    this.showOnlyActive = !this.showOnlyActive;
    this.currentPage = 1; // Voltar para a primeira página ao mudar o filtro
    this.loadPatientTypes(this.currentPage);
  }

  openNewPatientTypeModal(): void {
    this.selectedPatientType = null;
    this.modalTitle = 'Novo Tipo de Paciente';
    this.isModalOpen = true;
  }

  openEditPatientTypeModal(patientType: PatientType): void {
    this.selectedPatientType = patientType;
    this.modalTitle = 'Editar Tipo de Paciente';
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
  }

  onPatientTypeSaved(): void {
    this.closeModal();
    this.loadPatientTypes(this.currentPage);
  }

  openDeleteConfirmModal(patientType: PatientType): void {
    this.patientTypeToDelete = patientType;
    this.isConfirmModalOpen = true;
  }

  closeConfirmModal(): void {
    this.isConfirmModalOpen = false;
    this.patientTypeToDelete = null;
  }

  confirmDelete(): void {
    if (this.patientTypeToDelete) {
      this.patientTypeService
        .deletePatientType(this.patientTypeToDelete.id)
        .subscribe({
          next: () => {
            this.closeConfirmModal();
            this.loadPatientTypes(this.currentPage);
          },
          error: (err) => {
            console.error('Erro ao excluir tipo de paciente:', err);
            this.error =
              'Não foi possível excluir o tipo de paciente. Verifique se não há pacientes associados a ele.';
            this.closeConfirmModal();
          },
        });
    }
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxPagesToShow = 5;

    if (this.totalPages <= maxPagesToShow) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar
      let startPage = Math.max(2, this.currentPage - 1);
      let endPage = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Ajustar para mostrar sempre 3 páginas no meio
      if (startPage === 2) {
        endPage = Math.min(this.totalPages - 1, 4);
      } else if (endPage === this.totalPages - 1) {
        startPage = Math.max(2, this.totalPages - 3);
      }

      // Adicionar elipses se necessário
      if (startPage > 2) {
        pages.push('...');
      }

      // Adicionar páginas do meio
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Adicionar elipses se necessário
      if (endPage < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadPatientTypes(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadPatientTypes(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }
}
