<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Tipos de Paciente</h1>
    <button
      (click)="openNewPatientTypeModal()"
      class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Novo Tipo
    </button>
  </div>

  <div class="pb-6">
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4"
    >
      <!-- Busca -->
      <div class="relative flex-grow">
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <input
          type="text"
          (input)="searchPatientTypes($event)"
          placeholder="Buscar tipos de paciente por nome ou descrição"
          class="pl-10 pr-4 py-1.5 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- Filtro de status -->
      <div class="flex items-center">
        <label class="inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            [checked]="showOnlyActive"
            (change)="toggleActiveFilter()"
            class="sr-only peer"
          />
          <div
            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
          ></div>
          <span class="ms-3 text-sm font-medium text-gray-700">
            {{ showOnlyActive ? "Mostrar apenas ativos" : "Mostrar todos" }}
          </span>
        </label>
      </div>
    </div>

    <!-- Tabela de tipos de paciente -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Nome
            </th>
            <th
              class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Descrição
            </th>
            <th
              class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Cor
            </th>
            <th
              class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Ações
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <ng-container *ngIf="!isLoading && patientTypes.length > 0">
            <tr *ngFor="let patientType of patientTypes">
              <td class="py-3 px-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ patientType.nome }}
                </div>
              </td>
              <td class="py-3 px-4">
                <div class="text-sm text-gray-500">
                  {{ patientType.descricao || "Sem descrição" }}
                </div>
              </td>
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <div
                    class="w-6 h-6 rounded-full mr-2"
                    [style.background-color]="patientType.cor"
                  ></div>
                  <span class="text-sm text-gray-500">{{
                    patientType.cor
                  }}</span>
                </div>
              </td>
              <td class="py-3 px-4">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  [ngClass]="{
                    'bg-green-100 text-green-800': patientType.ativo,
                    'bg-red-100 text-red-800': !patientType.ativo
                  }"
                >
                  {{ patientType.ativo ? "Ativo" : "Inativo" }}
                </span>
              </td>
              <td class="py-3 px-4 text-right text-sm font-medium">
                <button
                  (click)="openEditPatientTypeModal(patientType)"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
                    />
                  </svg>
                </button>
                <button
                  (click)="openDeleteConfirmModal(patientType)"
                  class="text-red-600 hover:text-red-900"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </td>
            </tr>
          </ng-container>

          <!-- Estado de carregamento -->
          <tr *ngIf="isLoading">
            <td colspan="5" class="py-10 text-center">
              <div class="flex justify-center items-center">
                <svg
                  class="animate-spin h-8 w-8 text-blue-600"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span class="ml-2 text-gray-500">Carregando...</span>
              </div>
            </td>
          </tr>

          <!-- Estado vazio -->
          <tr *ngIf="!isLoading && patientTypes.length === 0">
            <td colspan="5" class="py-10 text-center">
              <div class="text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 mx-auto text-gray-400 mb-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p>Nenhum tipo de paciente encontrado.</p>
                <button
                  (click)="openNewPatientTypeModal()"
                  class="mt-2 text-blue-600 hover:text-blue-800 font-medium"
                >
                  Criar um novo tipo de paciente
                </button>
              </div>
            </td>
          </tr>

          <!-- Estado de erro -->
          <tr *ngIf="error">
            <td colspan="5" class="py-10 text-center">
              <div class="text-red-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 mx-auto text-red-400 mb-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p>{{ error }}</p>
                <button
                  (click)="loadPatientTypes()"
                  class="mt-2 text-blue-600 hover:text-blue-800 font-medium"
                >
                  Tentar novamente
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginação Moderna -->
    <div
      *ngIf="totalItems > 0"
      class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner"
    >
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <!-- Informações de paginação e seletor de itens por página -->
        <div class="flex flex-wrap items-center text-sm text-gray-600">
          <div
            class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md shadow-sm"
          >
            <span>Mostrando</span>
            <span class="font-medium mx-1 text-blue-600">{{
              (currentPage - 1) * itemsPerPage + 1
            }}</span>
            <span>-</span>
            <span class="font-medium mx-1 text-blue-600">{{
              currentPage * itemsPerPage > totalItems
                ? totalItems
                : currentPage * itemsPerPage
            }}</span>
            <span>de</span>
            <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
          </div>

          <!-- Seletor de itens por página -->
          <div class="ml-3 flex items-center">
            <label for="itemsPerPage" class="mr-2 text-sm text-gray-500"
              >Itens por página:</label
            >
            <div class="relative">
              <select
                id="itemsPerPage"
                [(ngModel)]="itemsPerPage"
                (change)="changeItemsPerPage()"
                class="appearance-none bg-white border border-gray-200 rounded-md pl-3 pr-8 py-1.5 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option [value]="6">6</option>
                <option [value]="10">10</option>
                <option [value]="20">20</option>
                <option [value]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Controles de paginação -->
        <div class="flex items-center pagination-controls">
          <div class="flex rounded-lg shadow-sm overflow-hidden">
            <!-- Primeira página -->
            <button
              (click)="firstPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Primeira página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Página anterior -->
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Página anterior"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Números de página -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page !== '...'">
                <button
                  (click)="goToPage(page)"
                  [disabled]="isLoading"
                  class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  [ngClass]="
                    currentPage === page
                      ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                      : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  "
                >
                  {{ page }}
                </button>
              </ng-container>
              <div
                *ngIf="page === '...'"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm text-gray-500 border-r border-gray-200 bg-white"
              >
                ...
              </div>
            </ng-container>

            <!-- Próxima página -->
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Próxima página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- Última página -->
            <button
              (click)="lastPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Última página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para criar/editar tipo de paciente -->
<app-modal [isOpen]="isModalOpen" [title]="modalTitle" (close)="closeModal()" [showDefaultFooter]="true">
  <app-patient-type-form
    [patientType]="selectedPatientType"
    (saved)="onPatientTypeSaved()"
    (cancelled)="closeModal()"
    #patientTypeForm
  ></app-patient-type-form>

  <!-- Footer com botões de ação -->
  <div footer class="flex justify-end space-x-3">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
    >
      Cancelar
    </button>
    <button
      type="button"
      [disabled]="patientTypeForm.isSubmitting"
      (click)="patientTypeForm.onSubmit()"
      class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
    >
      <svg
        *ngIf="patientTypeForm.isSubmitting"
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {{ patientTypeForm.isSubmitting ? "Salvando..." : "Salvar" }}
    </button>
  </div>
</app-modal>

<!-- Modal de confirmação de exclusão -->
<app-modal
  [isOpen]="isConfirmModalOpen"
  [title]="'Confirmar exclusão'"
  (close)="closeConfirmModal()"
  [showDefaultFooter]="true"
>
  <div class="p-6">
    <p class="text-gray-700">
      Tem certeza que deseja excluir o tipo de paciente
      <span class="font-semibold">{{ patientTypeToDelete?.nome }}</span
      >?
    </p>
    <p class="text-gray-500 mt-2">
      Esta ação não poderá ser desfeita. Se houver pacientes associados a este
      tipo, a exclusão não será permitida.
    </p>
  </div>

  <div footer class="flex justify-end space-x-3">
    <button
      (click)="closeConfirmModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
    >
      Cancelar
    </button>
    <button
      (click)="confirmDelete()"
      class="px-4 py-2 bg-red-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-red-700"
    >
      Excluir
    </button>
  </div>
</app-modal>
