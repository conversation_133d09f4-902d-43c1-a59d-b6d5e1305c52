import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PatientType } from '../../core/models/patient-type.model';
import { PatientTypeService } from '../../core/services/patient-type.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-patient-type-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './patient-type-form.component.html',
  styleUrls: ['./patient-type-form.component.scss'],
})
export class PatientTypeFormComponent implements OnInit, OnChanges {
  @Input() patientType: PatientType | null = null;
  @Output() saved = new EventEmitter<PatientType>();
  @Output() cancelled = new EventEmitter<void>();

  patientTypeForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  constructor(
    private fb: FormBuilder,
    private patientTypeService: PatientTypeService
  ) {
    this.patientTypeForm = this.fb.group({
      nome: ['', [Validators.required, Validators.minLength(2)]],
      descricao: [''],
      cor: [
        '#4299E1',
        [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)],
      ],
      ativo: [true],
    });
  }

  ngOnInit(): void {
    this.resetForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patientType'] && this.patientType) {
      this.patientTypeForm.patchValue({
        nome: this.patientType.nome,
        descricao: this.patientType.descricao || '',
        cor: this.patientType.cor,
        ativo: this.patientType.ativo,
      });
    } else {
      this.resetForm();
    }
  }

  resetForm(): void {
    this.patientTypeForm.reset({
      nome: '',
      descricao: '',
      cor: '#4299E1',
      ativo: true,
    });
    this.error = null;
  }

  async onSubmit(): Promise<void> {
    if (this.patientTypeForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.patientTypeForm.controls).forEach((key) => {
        const control = this.patientTypeForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      const formData = this.patientTypeForm.value;
      let savedPatientType: PatientType;

      if (this.patientType) {
        // Atualizar tipo existente
        savedPatientType = await firstValueFrom(
          this.patientTypeService.updatePatientType(
            this.patientType.id,
            formData
          )
        );
      } else {
        // Criar novo tipo
        savedPatientType = await firstValueFrom(
          this.patientTypeService.createPatientType(formData)
        );
      }

      this.saved.emit(savedPatientType);
      this.resetForm();
    } catch (err: any) {
      console.error('Erro ao salvar tipo de paciente:', err);
      this.error =
        err.error?.message ||
        'Ocorreu um erro ao salvar o tipo de paciente. Por favor, tente novamente.';
    } finally {
      this.isSubmitting = false;
    }
  }

  onCancel(): void {
    this.resetForm();
    this.cancelled.emit();
  }

  /**
   * Atualiza o valor do campo de texto quando o color picker é alterado
   */
  onColorPickerChange(event: Event): void {
    const colorValue = (event.target as HTMLInputElement).value;
    this.patientTypeForm.get('cor')?.setValue(colorValue);
    this.patientTypeForm.get('cor')?.markAsDirty();
  }

  /**
   * Valida o formato hexadecimal quando o usuário digita no campo de texto
   */
  onColorInputChange(event: Event): void {
    const inputValue = (event.target as HTMLInputElement).value;
    // Verifica se o valor digitado é um código hexadecimal válido
    if (/^#[0-9A-Fa-f]{6}$/.test(inputValue)) {
      // Se for válido, atualiza o color picker
      const colorPicker = document.getElementById('cor') as HTMLInputElement;
      if (colorPicker) {
        colorPicker.value = inputValue;
      }
    }
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get nomeControl() {
    return this.patientTypeForm.get('nome');
  }
  get descricaoControl() {
    return this.patientTypeForm.get('descricao');
  }
  get corControl() {
    return this.patientTypeForm.get('cor');
  }
  get ativoControl() {
    return this.patientTypeForm.get('ativo');
  }
}
