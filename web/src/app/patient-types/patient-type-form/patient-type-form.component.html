<div class="p-6">
  <form [formGroup]="patientTypeForm" (ngSubmit)="onSubmit()">
    <!-- Mensagem de erro -->
    <div *ngIf="error" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
      {{ error }}
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Coluna 1 -->
      <div class="space-y-6">
        <!-- Nome -->
        <div>
          <label
            for="nome"
            class="block text-sm font-medium text-gray-700 mb-1"
          >
            Nome*
          </label>
          <input
            type="text"
            id="nome"
            formControlName="nome"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': nomeControl?.invalid && nomeControl?.touched
            }"
          />
          <div
            *ngIf="nomeControl?.invalid && nomeControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="nomeControl?.errors?.['required']">
              Nome é obrigatório
            </span>
            <span *ngIf="nomeControl?.errors?.['minlength']">
              Nome deve ter pelo menos 2 caracteres
            </span>
          </div>
        </div>

        <!-- Descrição -->
        <div>
          <label
            for="descricao"
            class="block text-sm font-medium text-gray-700 mb-1"
          >
            Descrição
          </label>
          <textarea
            id="descricao"
            formControlName="descricao"
            rows="5"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>
      </div>

      <!-- Coluna 2 -->
      <div class="space-y-6">
        <!-- Cor -->
        <div>
          <label for="cor" class="block text-sm font-medium text-gray-700 mb-1">
            Cor*
          </label>
          <div class="flex items-center">
            <input
              type="color"
              id="cor"
              [value]="corControl?.value"
              (input)="onColorPickerChange($event)"
              class="h-10 w-10 border-0 p-0 mr-2"
            />
            <input
              type="text"
              formControlName="cor"
              (input)="onColorInputChange($event)"
              class="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{
                'border-red-500': corControl?.invalid && corControl?.touched
              }"
            />
          </div>
          <div
            *ngIf="corControl?.invalid && corControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="corControl?.errors?.['required']">
              Cor é obrigatória
            </span>
            <span *ngIf="corControl?.errors?.['pattern']">
              Cor deve estar no formato hexadecimal (ex: #FF0000)
            </span>
          </div>
          <!-- Visualização da cor -->
          <div class="mt-2 flex items-center">
            <div
              class="w-full h-8 rounded border border-gray-300"
              [style.background-color]="
                corControl?.valid ? corControl?.value : '#FFFFFF'
              "
            ></div>
          </div>
        </div>

        <!-- Status (Ativo/Inativo) -->
        <div class="mt-4">
          <div class="flex items-center">
            <input
              type="checkbox"
              id="ativo"
              formControlName="ativo"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="ativo" class="ml-2 block text-sm text-gray-700">
              Ativo
            </label>
          </div>
        </div>
      </div>
    </div>


  </form>
</div>
