import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MockDataService, Patient } from '../../core/services/mock-data.service';

@Component({
  selector: 'app-treatment-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './treatment-form.component.html',
  styleUrl: './treatment-form.component.scss'
})
export class TreatmentFormComponent implements OnInit {
  treatmentForm: FormGroup;
  patients: Patient[] = [];
  isEditMode = false;
  treatmentId: number | null = null;
  isSubmitting = false;
  
  // Lista de dentistas (mockada)
  dentists = [
    { id: 1, name: '<PERSON><PERSON><PERSON>' },
    { id: 2, name: 'Dr. <PERSON>' },
    { id: 3, name: '<PERSON><PERSON><PERSON>' },
    { id: 4, name: 'Dr. <PERSON>' }
  ];
  
  // Lista de tratamentos (mockada)
  treatmentTypes = [
    { id: 1, name: 'Consulta de Avaliação', cost: 100 },
    { id: 2, name: 'Limpeza', cost: 150 },
    { id: 3, name: 'Restauração', cost: 250 },
    { id: 4, name: 'Tratamento de Canal', cost: 800 },
    { id: 5, name: 'Extração', cost: 300 },
    { id: 6, name: 'Aplicação de Flúor', cost: 100 },
    { id: 7, name: 'Clareamento', cost: 500 },
    { id: 8, name: 'Prótese Dentária', cost: 1200 },
    { id: 9, name: 'Ortodontia', cost: 2500 },
    { id: 10, name: 'Implante', cost: 3000 }
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private mockDataService: MockDataService
  ) {
    this.treatmentForm = this.fb.group({
      patientId: ['', [Validators.required]],
      name: ['', [Validators.required]],
      description: [''],
      treatmentDate: ['', [Validators.required]],
      returnDate: [''],
      dentist: ['', [Validators.required]],
      notes: [''],
      status: ['scheduled'],
      cost: [0, [Validators.required, Validators.min(0)]],
      paid: [false]
    });
  }

  ngOnInit(): void {
    // Carrega a lista de pacientes
    this.mockDataService.getPatients().subscribe(patients => {
      this.patients = patients;
    });

    // Verifica se é modo de edição
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.treatmentId = +id;
      this.loadTreatmentData(this.treatmentId);
    }

    // Verifica se há um paciente pré-selecionado (vindo de outra tela)
    const patientId = this.route.snapshot.queryParamMap.get('patientId');
    if (patientId) {
      this.treatmentForm.patchValue({ patientId: +patientId });
    }

    // Define a data padrão como hoje
    const today = new Date();
    this.treatmentForm.patchValue({
      treatmentDate: this.formatDateForInput(today)
    });

    // Atualiza o custo quando o tipo de tratamento muda
    this.treatmentForm.get('name')?.valueChanges.subscribe(treatmentName => {
      if (treatmentName) {
        const selectedTreatment = this.treatmentTypes.find(t => t.name === treatmentName);
        if (selectedTreatment) {
          this.treatmentForm.patchValue({ cost: selectedTreatment.cost });
        }
      }
    });
  }

  loadTreatmentData(id: number): void {
    const treatment = this.mockDataService.getTreatmentById(id);
    if (treatment) {
      this.treatmentForm.patchValue({
        patientId: treatment.patientId,
        name: treatment.name,
        description: treatment.description,
        treatmentDate: this.formatDateForInput(new Date(treatment.treatmentDate)),
        returnDate: treatment.returnDate ? this.formatDateForInput(new Date(treatment.returnDate)) : '',
        dentist: treatment.dentist,
        notes: treatment.notes,
        status: treatment.status,
        cost: treatment.cost || 0,
        paid: treatment.paid || false
      });
    } else {
      this.router.navigate(['/treatments']);
    }
  }

  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onSubmit(): void {
    if (this.treatmentForm.invalid) {
      // Marca todos os campos como touched para mostrar os erros
      Object.keys(this.treatmentForm.controls).forEach(key => {
        const control = this.treatmentForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    const formValues = this.treatmentForm.value;
    const selectedPatient = this.patients.find(p => p.id === +formValues.patientId);

    if (!selectedPatient) {
      this.isSubmitting = false;
      alert('Paciente não encontrado');
      return;
    }

    const treatmentData = {
      patientId: +formValues.patientId,
      patientName: selectedPatient.name,
      name: formValues.name,
      description: formValues.description,
      treatmentDate: new Date(formValues.treatmentDate),
      returnDate: formValues.returnDate ? new Date(formValues.returnDate) : undefined,
      dentist: formValues.dentist,
      notes: formValues.notes,
      status: formValues.status,
      cost: +formValues.cost,
      paid: formValues.paid,
      createdAt: new Date()
    };

    if (this.isEditMode && this.treatmentId) {
      this.mockDataService.updateTreatment(this.treatmentId, treatmentData);
    } else {
      this.mockDataService.addTreatment(treatmentData);
    }

    setTimeout(() => {
      this.isSubmitting = false;
      this.router.navigate(['/treatments']);
    }, 500);
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get patientIdControl() { return this.treatmentForm.get('patientId'); }
  get nameControl() { return this.treatmentForm.get('name'); }
  get treatmentDateControl() { return this.treatmentForm.get('treatmentDate'); }
  get dentistControl() { return this.treatmentForm.get('dentist'); }
  get costControl() { return this.treatmentForm.get('cost'); }
}
