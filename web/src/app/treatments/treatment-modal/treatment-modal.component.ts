import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Treatment } from '../../core/models/treatment.model';
import { TreatmentService } from '../../core/services/treatment.service';
import { PatientService } from '../../core/services/patient.service';
import { DentistService } from '../../core/services/dentist.service';
import { Patient } from '../../core/models/patient.model';
import { Dentist } from '../../core/models/dentist.model';

@Component({
  selector: 'app-treatment-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './treatment-modal.component.html',
  styleUrl: './treatment-modal.component.scss'
})
export class TreatmentModalComponent implements OnInit {
  @Input() treatmentId: number = 0;
  @Input() isVisible: boolean = false;
  @Output() closeModal = new EventEmitter<void>();

  treatment: Treatment | null = null;
  patient: Patient | null = null;
  dentist: Dentist | null = null;
  loading = true;
  error = false;

  statusMap: { [key: string]: string } = {
    'scheduled-unconfirmed': 'Agendado não confirmado',
    'scheduled-confirmed': 'Agendado confirmado',
    'unscheduled': 'Desmarcado',
    'in-progress': 'Em andamento',
    'completed': 'Concluído',
    'cancelled': 'Cancelado',
    'scheduled': 'Agendado não confirmado', // Para compatibilidade
    'confirmed': 'Agendado confirmado' // Para compatibilidade
  };

  paymentStatusMap: { [key: string]: string } = {
    'pending': 'Pendente',
    'paid': 'Pago'
  };

  priorityMap: { [key: string]: string } = {
    'low': 'Baixa',
    'medium': 'Média',
    'high': 'Alta'
  };

  constructor(
    private treatmentService: TreatmentService,
    private patientService: PatientService,
    private dentistService: DentistService
  ) {}

  ngOnInit(): void {}

  ngOnChanges(): void {
    if (this.isVisible && this.treatmentId > 0) {
      this.loadTreatment();
    }
  }

  loadTreatment(): void {
    this.loading = true;
    this.error = false;

    this.treatmentService.getTreatment(this.treatmentId).subscribe({
      next: (treatment) => {
        this.treatment = treatment;
        this.loadPatient(treatment.patientId);
        this.loadDentist(treatment.dentistId);
      },
      error: (error) => {
        console.error('Erro ao carregar tratamento:', error);
        this.error = true;
        this.loading = false;
      }
    });
  }

  loadPatient(patientId: number): void {
    this.patientService.getPatient(patientId).subscribe({
      next: (patient) => {
        this.patient = patient;
        this.checkLoadingComplete();
      },
      error: (error) => {
        console.error('Erro ao carregar paciente:', error);
        this.checkLoadingComplete();
      }
    });
  }

  loadDentist(dentistId: number): void {
    this.dentistService.getDentist(dentistId).subscribe({
      next: (dentist) => {
        this.dentist = dentist;
        this.checkLoadingComplete();
      },
      error: (error) => {
        console.error('Erro ao carregar dentista:', error);
        this.checkLoadingComplete();
      }
    });
  }

  checkLoadingComplete(): void {
    if (this.patient !== null && this.dentist !== null) {
      this.loading = false;
    }
  }

  close(): void {
    this.closeModal.emit();
  }
}
