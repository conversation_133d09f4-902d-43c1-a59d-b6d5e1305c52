<div class="modal" [class.show]="isVisible" [style.display]="isVisible ? 'block' : 'none'" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Detalhes do Tratamento</h5>
        <button type="button" class="close" (click)="close()" aria-label="Fechar">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div *ngIf="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Carregando...</span>
          </div>
          <p class="mt-2">Carregando detalhes do tratamento...</p>
        </div>

        <div *ngIf="error" class="text-center py-4">
          <div class="alert alert-danger" role="alert">
            Ocorreu um erro ao carregar os detalhes do tratamento.
          </div>
          <button type="button" class="btn btn-primary" (click)="loadTreatment()">Tentar novamente</button>
        </div>

        <div *ngIf="!loading && !error && treatment" class="treatment-details">
          <div class="card mb-3">
            <div class="card-header bg-light">
              <h5 class="mb-0">Informações Gerais</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-2">
                  <strong>Nome do Tratamento:</strong>
                  <p>{{ treatment.name }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>Status:</strong>
                  <span class="badge" [ngClass]="{
                    'badge-danger': treatment.status === 'cancelled',
                    'badge-success': treatment.status === 'completed',
                    'badge-warning': treatment.status === 'scheduled-confirmed',
                    'badge-info': treatment.status === 'in-progress',
                    'badge-secondary': treatment.status === 'unscheduled',
                    'badge-primary': treatment.status === 'scheduled-unconfirmed'
                  }">
                    {{ statusMap[treatment.status] }}
                  </span>
                </div>
                <div class="col-md-6 mb-2" *ngIf="treatment.priority">
                  <strong>Prioridade:</strong>
                  <span class="badge" [ngClass]="{
                    'badge-danger': treatment.priority === 'high',
                    'badge-warning': treatment.priority === 'medium',
                    'badge-success': treatment.priority === 'low'
                  }">
                    {{ priorityMap[treatment.priority || 'medium'] }}
                  </span>
                </div>
                <div class="col-md-6 mb-2" *ngIf="treatment.paymentStatus">
                  <strong>Status de Pagamento:</strong>
                  <span class="badge" [ngClass]="{
                    'badge-danger': treatment.paymentStatus === 'pending',
                    'badge-success': treatment.paymentStatus === 'paid'
                  }">
                    {{ paymentStatusMap[treatment.paymentStatus || 'pending'] }}
                  </span>
                </div>
                <div class="col-md-6 mb-2" *ngIf="treatment.value">
                  <strong>Valor:</strong>
                  <p>{{ treatment.value | currency:'BRL' }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-3">
            <div class="card-header bg-light">
              <h5 class="mb-0">Datas</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Os campos de data e hora do tratamento e retorno foram removidos conforme solicitado -->
                <div class="col-md-6 mb-2">
                  <strong>Data de Criação:</strong>
                  <p>{{ treatment.createdAt | date:'dd/MM/yyyy HH:mm' }}</p>
                </div>
                <div class="col-md-6 mb-2" *ngIf="treatment.lastUpdate">
                  <strong>Última Atualização:</strong>
                  <p>{{ treatment.lastUpdate | date:'dd/MM/yyyy HH:mm' }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-3" *ngIf="patient">
            <div class="card-header bg-light">
              <h5 class="mb-0">Paciente</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-2">
                  <strong>Nome:</strong>
                  <p>{{ patient.name }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>CPF:</strong>
                  <p>{{ patient.cpf }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>Telefone:</strong>
                  <p>{{ patient.phone }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>Email:</strong>
                  <p>{{ patient.email }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-3" *ngIf="dentist">
            <div class="card-header bg-light">
              <h5 class="mb-0">Dentista</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-2">
                  <strong>Nome:</strong>
                  <p>{{ dentist.name }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>CRO:</strong>
                  <p>{{ dentist.cro }}</p>
                </div>
                <div class="col-md-6 mb-2">
                  <strong>Especialidade:</strong>
                  <p>{{ dentist.specialty }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-3" *ngIf="treatment.notes">
            <div class="card-header bg-light">
              <h5 class="mb-0">Observações</h5>
            </div>
            <div class="card-body">
              <p>{{ treatment.notes }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="close()">Fechar</button>
      </div>
    </div>
  </div>
</div>

<div class="modal-backdrop fade show" *ngIf="isVisible"></div>
