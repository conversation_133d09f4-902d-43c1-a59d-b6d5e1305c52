<!-- Modal backdrop -->
<div *ngIf="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <!-- Modal container -->
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <!-- Modal header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Adicionar Agendamento</h3>
        <button (click)="closeModal()" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Modal body -->
    <div class="px-6 py-4">
      <p class="text-sm text-gray-600 mb-4">
        Adicionar agendamento para o tratamento: <span class="font-medium">{{ treatmentName }}</span>
      </p>

      <form [formGroup]="schedulingForm" (ngSubmit)="onSubmit()">
        <div class="space-y-4">
          <!-- Data -->
          <div>
            <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Data *</label>
            <input
              type="date"
              id="date"
              formControlName="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': dateControl?.invalid && dateControl?.touched}"
            >
            <div *ngIf="dateControl?.invalid && dateControl?.touched" class="text-red-500 text-sm mt-1">
              Data é obrigatória
            </div>
          </div>

          <!-- Hora -->
          <div>
            <label for="time" class="block text-sm font-medium text-gray-700 mb-1">Hora *</label>
            <input
              type="time"
              id="time"
              formControlName="time"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{'border-red-500': timeControl?.invalid && timeControl?.touched}"
            >
            <div *ngIf="timeControl?.invalid && timeControl?.touched" class="text-red-500 text-sm mt-1">
              Hora é obrigatória
            </div>
          </div>
        </div>

        <!-- Modal footer -->
        <div class="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            (click)="closeModal()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cancelar
          </button>
          <button
            type="submit"
            [disabled]="isSubmitting"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
            [class.opacity-50]="isSubmitting"
          >
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Adicionar
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
