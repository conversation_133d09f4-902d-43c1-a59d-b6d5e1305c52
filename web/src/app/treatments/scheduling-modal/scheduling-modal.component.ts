import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

@Component({
  selector: 'app-scheduling-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './scheduling-modal.component.html',
  styleUrls: ['./scheduling-modal.component.scss']
})
export class SchedulingModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() treatmentName = '';
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<any>();

  schedulingForm: FormGroup;
  isSubmitting = false;

  constructor(private fb: FormBuilder) {
    this.schedulingForm = this.fb.group({
      date: ['', Validators.required],
      time: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // Definir a data padrão como hoje
    const today = new Date();
    const formattedDate = this.formatDateForInput(today);
    this.schedulingForm.patchValue({
      date: formattedDate,
      time: '09:00'
    });
  }

  formatDateForInput(date: Date): string {
    // Formatar a data como YYYY-MM-DD sem ajuste de fuso horário
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onSubmit(): void {
    if (this.schedulingForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.schedulingForm.controls).forEach(key => {
        const control = this.schedulingForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValues = this.schedulingForm.value;

    // Garantir que a data está no formato correto (YYYY-MM-DD)
    // O input type="date" já fornece a data no formato YYYY-MM-DD
    console.log('Data do formulário a ser enviada:', formValues.date);

    // Verificar se a data está no formato correto
    if (formValues.date && typeof formValues.date === 'string' && !formValues.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      console.error('Data não está no formato YYYY-MM-DD:', formValues.date);
    }

    // Emitir os valores do formulário
    this.save.emit(formValues);
    this.isSubmitting = false;
    this.closeModal();
  }

  closeModal(): void {
    this.schedulingForm.reset();
    this.close.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get dateControl() { return this.schedulingForm.get('date'); }
  get timeControl() { return this.schedulingForm.get('time'); }
}
