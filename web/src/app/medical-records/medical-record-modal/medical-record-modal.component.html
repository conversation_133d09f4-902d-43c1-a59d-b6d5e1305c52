<div
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-hidden"
>
  <div class="bg-white w-full h-full flex flex-col">
    <!-- Header -->
    <div
      class="bg-blue-300 text-white px-6 py-4 flex justify-between items-center shadow-md"
    >
      <div class="flex items-center">
        <h2 class="text-xl font-semibold">Prontuário do Paciente</h2>
        <span
          *ngIf="medicalRecord"
          class="ml-2 text-sm bg-blue-700 px-2 py-1 rounded-full"
        >
          {{ medicalRecord.patient.name }}
        </span>
      </div>
      <div class="flex items-center space-x-4">
        <button
          (click)="exportToPdf()"
          class="bg-white text-blue-600 px-4 py-2 rounded-md flex items-center text-sm font-medium hover:bg-blue-50 transition-colors"
          [disabled]="isGeneratingPdf || !medicalRecord"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <span *ngIf="!isGeneratingPdf">Baixar em PDF</span>
          <span *ngIf="isGeneratingPdf">Gerando...</span>
        </button>
        <button
          (click)="closeModal()"
          class="text-white hover:text-gray-200 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div *ngIf="isLoading" class="flex-1 flex items-center justify-center">
      <div class="flex flex-col items-center">
        <div
          class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"
        ></div>
        <p class="text-gray-600">Carregando prontuário...</p>
      </div>
    </div>

    <!-- Error state -->
    <div
      *ngIf="error && !isLoading"
      class="flex-1 flex items-center justify-center"
    >
      <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-lg">
        <div class="flex items-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 text-red-500 mr-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 class="text-lg font-medium text-red-800">
            Erro ao carregar prontuário
          </h3>
        </div>
        <p class="text-red-700 mb-4">{{ error }}</p>
        <button
          (click)="loadMedicalRecord()"
          class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
        >
          Tentar novamente
        </button>
      </div>
    </div>

    <!-- Content -->
    <div
      *ngIf="!isLoading && !error && medicalRecord"
      class="flex-1 flex overflow-hidden"
    >
      <!-- Sidebar menu -->
      <div
        #sectionMenu
        class="w-64 bg-gray-100 overflow-y-auto border-r border-gray-200 p-4"
      >
        <h3
          class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4"
        >
          Seções
        </h3>
        <ul class="space-y-2">
          <li *ngFor="let section of sections">
            <button
              (click)="scrollToSection(section.id)"
              class="w-full text-left px-3 py-2 rounded-md text-sm transition-colors"
              [class.bg-blue-100]="activeSection === section.id"
              [class.text-blue-700]="activeSection === section.id"
              [class.font-medium]="activeSection === section.id"
              [class.text-gray-700]="activeSection !== section.id"
              [class.hover:bg-gray-200]="activeSection !== section.id"
            >
              {{ section.label }}
            </button>
          </li>
        </ul>
      </div>

      <!-- Main content -->
      <div #contentContainer class="flex-1 overflow-y-auto p-6">
        <!-- Informações do Paciente -->
        <section id="patient-info" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Informações do Paciente
          </h2>
          <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 class="text-lg font-medium text-gray-800 mb-4">
                  Dados Pessoais
                </h3>
                <div class="space-y-3">
                  <div>
                    <span class="text-sm text-gray-500">Nome:</span>
                    <p class="font-medium">{{ medicalRecord.patient.name }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500"
                      >Data de Nascimento:</span
                    >
                    <p>{{ formatDate(medicalRecord.patient.birthDate) }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">Gênero:</span>
                    <p>{{ medicalRecord.patient.gender }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">CPF:</span>
                    <p>{{ medicalRecord.patient.cpf }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">Profissão:</span>
                    <p>
                      {{ medicalRecord.patient.profession || "Não informado" }}
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-800 mb-4">Contato</h3>
                <div class="space-y-3">
                  <div>
                    <span class="text-sm text-gray-500">Email:</span>
                    <p>{{ medicalRecord.patient.email }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">Telefone:</span>
                    <p>{{ medicalRecord.patient.phone }}</p>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">WhatsApp:</span>
                    <p>
                      {{ medicalRecord.patient.whatsapp || "Não informado" }}
                    </p>
                  </div>
                  <div *ngIf="medicalRecord.patient.address">
                    <span class="text-sm text-gray-500">Endereço:</span>
                    <p>
                      {{ medicalRecord.patient.address.street }},
                      {{ medicalRecord.patient.address.number }}
                      <br
                        *ngIf="
                          medicalRecord.patient.address.neighborhood ||
                          medicalRecord.patient.address.city
                        "
                      />
                      <span *ngIf="medicalRecord.patient.address.neighborhood"
                        >{{ medicalRecord.patient.address.neighborhood }},
                      </span>
                      <span *ngIf="medicalRecord.patient.address.city"
                        >{{ medicalRecord.patient.address.city }}/{{
                          medicalRecord.patient.address.state
                        }}</span
                      >
                      <br *ngIf="medicalRecord.patient.address.zipCode" />
                      <span *ngIf="medicalRecord.patient.address.zipCode"
                        >CEP: {{ medicalRecord.patient.address.zipCode }}</span
                      >
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              *ngIf="medicalRecord.patient.notes"
              class="mt-6 pt-4 border-t border-gray-100"
            >
              <h3 class="text-lg font-medium text-gray-800 mb-2">
                Observações
              </h3>
              <p class="text-gray-700 whitespace-pre-line">
                {{ medicalRecord.patient.notes }}
              </p>
            </div>
          </div>
        </section>

        <!-- Anamnese -->
        <section id="anamnesis" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Anamnese
          </h2>
          <div
            *ngIf="medicalRecord.anamneses.length === 0"
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhuma anamnese registrada para este paciente.
            </p>
          </div>

          <!-- Lista de anamneses em formato de accordion -->
          <div class="space-y-4">
            <div
              *ngFor="let anamnesis of medicalRecord.anamneses; let i = index"
              class="bg-white rounded-lg shadow-sm overflow-hidden"
            >
              <!-- Cabeçalho do accordion (sempre visível) -->
              <div
                (click)="toggleAnamnesis(i)"
                class="p-6 cursor-pointer hover:bg-gray-50 transition-colors flex justify-between items-center"
              >
                <div class="flex items-center">
                  <div>
                    <h3 class="text-lg font-medium text-gray-800">
                      Anamnese #{{ i + 1 }}
                      <span
                        *ngIf="isLatestAnamnesis(i)"
                        class="ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full"
                      >
                        Mais recente
                      </span>
                    </h3>
                    <p class="text-sm text-gray-500">
                      Realizada em {{ formatDate(anamnesis.createdAt) }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center">
                  <div
                    *ngIf="anamnesis.employeeName"
                    class="text-sm text-gray-500 mr-4"
                  >
                    Profissional: {{ anamnesis.employeeName }}
                  </div>
                  <!-- Ícone de expansão/contração -->
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 transition-transform duration-200"
                    [ngClass]="{
                      'transform rotate-180': isAnamnesisExpanded(i)
                    }"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>

              <!-- Conteúdo do accordion (visível apenas quando expandido) -->
              <div
                *ngIf="isAnamnesisExpanded(i)"
                class="border-t border-gray-100 p-6 bg-blue-50 transition-all duration-300 ease-in-out"
              >
                <div class="space-y-4">
                  <div
                    *ngFor="let answer of anamnesis.answers"
                    class="rounded-lg bg-white p-4 shadow-sm"
                  >
                    <p class="text-sm font-medium text-blue-700 mb-2">
                      {{ answer.questionText }}
                    </p>
                    <p
                      class="text-gray-800 bg-gray-50 p-3 rounded-md border-l-4 border-green-500"
                    >
                      {{ answer.answer }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Agendamentos -->
        <section id="appointments" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Agendamentos
          </h2>
          <div
            *ngIf="medicalRecord.schedulings.length === 0"
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum agendamento registrado para este paciente.
            </p>
          </div>
          <div class="space-y-4">
            <div
              *ngFor="let scheduling of medicalRecord.schedulings"
              class="bg-white rounded-lg shadow-sm p-6"
            >
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-800">
                    Consulta em {{ formatDate(scheduling.date) }} às
                    {{ scheduling.time }}
                  </h3>
                  <p class="text-sm text-gray-500">
                    Dentista: {{ scheduling.dentistName || "Não especificado" }}
                  </p>
                </div>
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  [ngClass]="{
                    'bg-green-100 text-green-800':
                      scheduling.status === 'COMPLETED',
                    'bg-yellow-100 text-yellow-800':
                      scheduling.status === 'SCHEDULED',
                    'bg-red-100 text-red-800':
                      scheduling.status === 'CANCELLED',
                    'bg-blue-100 text-blue-800':
                      scheduling.status === 'CONFIRMED'
                  }"
                >
                  {{
                    scheduling.status === "COMPLETED"
                      ? "Realizado"
                      : scheduling.status === "SCHEDULED"
                      ? "Agendado"
                      : scheduling.status === "CANCELLED"
                      ? "Cancelado"
                      : scheduling.status === "CONFIRMED"
                      ? "Confirmado"
                      : scheduling.status
                  }}
                </span>
              </div>
              <div
                *ngIf="
                  scheduling.procedures && scheduling.procedures.length > 0
                "
                class="mb-4"
              >
                <h4 class="text-sm font-medium text-gray-700 mb-2">
                  Procedimentos:
                </h4>
                <ul class="space-y-2">
                  <li
                    *ngFor="let proc of scheduling.procedures"
                    class="flex items-start"
                  >
                    <span class="text-gray-800">{{ proc.name }}</span>
                    <span *ngIf="proc.tooth" class="ml-2 text-sm text-gray-500"
                      >(Dente {{ proc.tooth }})</span
                    >
                  </li>
                </ul>
              </div>
              <div
                *ngIf="scheduling.notes"
                class="mt-3 pt-3 border-t border-gray-100"
              >
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Observações:
                </h4>
                <p class="text-gray-800 whitespace-pre-line">
                  {{ scheduling.notes }}
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Procedimentos Realizados -->
        <section id="procedures" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Procedimentos Realizados
          </h2>
          <div
            *ngIf="
              !medicalRecord?.completedProcedures ||
              medicalRecord?.completedProcedures?.length === 0
            "
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum procedimento realizado para este paciente.
            </p>
          </div>
          <div class="space-y-4">
            <div
              *ngFor="let procedure of medicalRecord?.completedProcedures || []"
              class="bg-white rounded-lg shadow-sm p-6"
            >
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-800">
                    {{ procedure.name }}
                  </h3>
                  <p *ngIf="procedure.tooth" class="text-sm text-gray-500">
                    Dente: {{ procedure.tooth }}
                  </p>
                  <p
                    *ngIf="procedure.executionDate"
                    class="text-sm text-gray-500"
                  >
                    Realizado em: {{ formatDate(procedure.executionDate) }}
                  </p>
                </div>
                <div class="text-right">
                  <span class="text-lg font-medium text-gray-800">{{
                    formatCurrency(procedure.value)
                  }}</span>
                  <p
                    *ngIf="procedure.professionalName"
                    class="text-sm text-gray-500"
                  >
                    Profissional: {{ procedure.professionalName }}
                  </p>
                </div>
              </div>
              <div
                *ngIf="procedure.notes"
                class="mt-3 pt-3 border-t border-gray-100"
              >
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Observações:
                </h4>
                <p class="text-gray-800 whitespace-pre-line">
                  {{ procedure.notes }}
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Planos de Tratamento -->
        <section id="treatment-plans" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Planos de Tratamento
          </h2>
          <div
            *ngIf="
              !medicalRecord.treatmentPlans ||
              medicalRecord.treatmentPlans.length === 0
            "
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum plano de tratamento registrado para este paciente.
            </p>
          </div>
          <!-- Lista de planos em formato de accordion -->
          <div class="space-y-4">
            <div
              *ngFor="let plan of medicalRecord.treatmentPlans; let i = index"
              class="bg-white rounded-lg shadow-sm overflow-hidden"
            >
              <!-- Cabeçalho do accordion (sempre visível) -->
              <div
                (click)="toggleTreatmentPlan(i)"
                class="p-6 cursor-pointer hover:bg-gray-50 transition-colors flex justify-between items-center"
              >
                <div class="flex items-center">
                  <div>
                    <h3 class="text-lg font-medium text-gray-800">
                      Plano de Tratamento #{{ i + 1 }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      Criado em {{ formatDate(plan.createdAt) }}
                      <span *ngIf="plan.dentistName">
                        • Dr(a). {{ plan.dentistName }}</span
                      >
                    </p>
                    <div class="flex items-center mt-2 gap-4">
                      <span class="text-lg font-semibold text-green-600">{{
                        formatCurrency(plan.totalValue)
                      }}</span>
                      <div class="flex items-center">
                        <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            [style.width.%]="plan.completionPercentage"
                          ></div>
                        </div>
                        <span class="text-sm text-gray-600"
                          >{{ plan.completionPercentage }}%</span
                        >
                      </div>
                      <span
                        class="px-2 py-1 text-xs font-medium rounded-full"
                        [ngClass]="{
                          'bg-green-100 text-green-800':
                            plan.status === 'completed',
                          'bg-yellow-100 text-yellow-800':
                            plan.status === 'open',
                          'bg-red-100 text-red-800': plan.status === 'cancelled'
                        }"
                      >
                        {{ getTreatmentPlanStatusLabel(plan.status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Ícone de expansão -->
                <div class="ml-4">
                  <svg
                    class="w-5 h-5 text-gray-400 transition-transform duration-200"
                    [class.rotate-180]="isTreatmentPlanExpanded(i)"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </div>

              <!-- Conteúdo do accordion (procedimentos) -->
              <div
                *ngIf="isTreatmentPlanExpanded(i)"
                class="border-t border-gray-100 bg-blue-50 transition-all duration-300 ease-in-out"
              >
                <div class="p-4 bg-blue-100 border-b border-blue-200">
                  <h4 class="font-medium text-blue-800">
                    Procedimentos do Plano ({{ plan.procedures.length || 0 }})
                  </h4>
                  <!-- Debug: mostrar se procedures existe -->
                  <p
                    *ngIf="!plan.procedures || plan.procedures.length === 0"
                    class="text-xs text-blue-600 mt-1"
                  >
                    Nenhum procedimento encontrado para este plano
                  </p>
                </div>
                <div class="divide-y divide-blue-200">
                  <div
                    *ngFor="let proc of plan.procedures"
                    class="p-4 hover:bg-blue-100 transition-colors"
                  >
                    <div class="flex justify-between items-start">
                      <div class="flex-1">
                        <p class="font-medium text-gray-800">{{ proc.name }}</p>
                        <div class="mt-1 space-y-1">
                          <p *ngIf="proc.tooth" class="text-sm text-gray-600">
                            <span class="font-medium">Dente:</span>
                            {{ proc.tooth }}
                          </p>
                          <p
                            *ngIf="proc.executionDate"
                            class="text-sm text-gray-600"
                          >
                            <span class="font-medium">Executado em:</span>
                            {{ formatDate(proc.executionDate) }}
                          </p>
                          <p
                            *ngIf="proc.professionalName"
                            class="text-sm text-gray-600"
                          >
                            <span class="font-medium">Profissional:</span>
                            {{ proc.professionalName }}
                          </p>
                        </div>
                        <div
                          *ngIf="proc.notes"
                          class="mt-2 p-2 bg-white rounded border-l-4 border-blue-500"
                        >
                          <p class="text-sm text-gray-700">
                            <span class="font-medium text-blue-700"
                              >Observações:</span
                            >
                            {{ proc.notes }}
                          </p>
                        </div>
                      </div>
                      <div class="text-right ml-4">
                        <p class="font-semibold text-lg text-gray-800">
                          {{ formatCurrency(proc.value) }}
                        </p>
                        <span
                          class="inline-block mt-1 px-2 py-0.5 text-xs font-medium rounded-full"
                          [ngClass]="{
                            'bg-green-100 text-green-800':
                              proc.status === 'completed',
                            'bg-yellow-100 text-yellow-800':
                              proc.status === 'scheduled' ||
                              proc.status === 'in_progress',
                            'bg-blue-100 text-blue-800':
                              proc.status === 'pending',
                            'bg-red-100 text-red-800':
                              proc.status === 'cancelled'
                          }"
                        >
                          {{ getProcedureStatusLabel(proc.status) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Orçamentos -->
        <section id="budgets" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Orçamentos
          </h2>
          <div
            *ngIf="!medicalRecord.budgets || medicalRecord.budgets.length === 0"
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum orçamento registrado para este paciente.
            </p>
          </div>
          <!-- Lista de orçamentos em formato de accordion -->
          <div class="space-y-4">
            <div
              *ngFor="let budget of medicalRecord.budgets; let i = index"
              class="bg-white rounded-lg shadow-sm overflow-hidden"
            >
              <!-- Cabeçalho do accordion (sempre visível) -->
              <div
                (click)="toggleBudget(i)"
                class="p-6 cursor-pointer hover:bg-gray-50 transition-colors flex justify-between items-center"
              >
                <div class="flex items-center">
                  <div>
                    <h3 class="text-lg font-medium text-gray-800">
                      Orçamento #{{ i + 1 }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      Criado em {{ formatDate(budget.createdAt) }}
                      <span *ngIf="budget.dentistName">
                        • Dr(a). {{ budget.dentistName }}</span
                      >
                    </p>
                    <div class="flex items-center mt-2 gap-4">
                      <span class="text-lg font-semibold text-green-600"
                        >R$ {{ formatCurrency(budget.amountPaid) }}</span
                      >
                      <span
                        class="px-2 py-1 text-xs font-medium rounded-full"
                        [ngClass]="{
                          'bg-green-100 text-green-800':
                            budget.status === 'approved',
                          'bg-yellow-100 text-yellow-800':
                            budget.status === 'pending',
                          'bg-red-100 text-red-800':
                            budget.status === 'rejected'
                        }"
                      >
                        {{ getBudgetStatusLabel(budget.status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Ícone de expansão -->
                <div class="ml-4">
                  <svg
                    class="w-5 h-5 text-gray-400 transition-transform duration-200"
                    [class.rotate-180]="isBudgetExpanded(i)"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </div>

              <!-- Conteúdo do accordion (itens do orçamento) -->
              <div
                *ngIf="isBudgetExpanded(i)"
                class="border-t border-gray-100 bg-green-50 transition-all duration-300 ease-in-out"
              >
                <div class="p-4 bg-green-100 border-b border-green-200">
                  <h4 class="font-medium text-green-800">
                    Itens do Orçamento ({{ budget.items.length || 0 }})
                  </h4>
                </div>
                <div class="divide-y divide-green-200">
                  <div
                    *ngFor="let item of budget.items"
                    class="p-4 hover:bg-green-100 transition-colors"
                  >
                    <div class="flex justify-between items-start">
                      <div class="flex-1">
                        <p class="font-medium text-gray-800">
                          {{ item.procedureName }}
                        </p>
                        <div class="mt-1 space-y-1">
                          <p *ngIf="item.tooth" class="text-sm text-gray-600">
                            <span class="font-medium">Dente:</span>
                            {{ item.tooth }}
                          </p>
                        </div>
                      </div>
                      <div class="text-right ml-4">
                        <p class="font-semibold text-lg text-gray-800">
                          R$ {{ formatCurrency(item.value) }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Desconto -->
                <div class="p-4 bg-green-100 border-b border-green-200">
                  <div class="flex justify-between items-center">
                    <span class="font-semibold text-green-800">Desconto:</span>
                    <span class="text-lg font-bold text-red-800">
                      -
                      {{
                        formatCurrency(budget.totalValue - budget.amountPaid)
                      }}
                    </span>
                  </div>
                </div>

                <!-- Total do orçamento -->
                <div class="p-4 bg-green-200 border-t border-green-300">
                  <div class="flex justify-between items-center">
                    <span class="font-semibold text-green-800"
                      >Total do Orçamento:</span
                    >
                    <span class="text-xl font-bold text-green-800">
                      R$ {{ formatCurrency(budget.amountPaid) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Sugestões da IA -->
        <section id="ai-suggestions" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Sugestões da IA
          </h2>
          <div
            *ngIf="
              !medicalRecord.suggestions ||
              medicalRecord.suggestions.length === 0
            "
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhuma sugestão da IA registrada para este paciente.
            </p>
          </div>
          <div class="space-y-6">
            <div
              *ngFor="
                let suggestion of medicalRecord.suggestions;
                let i = index
              "
              class="bg-white rounded-lg shadow-sm p-6"
            >
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-800">
                    Sugestão #{{ getSuggestionNumber(i) }}
                  </h3>
                  <p class="text-sm text-gray-500">
                    Gerada em {{ formatDate(suggestion.createdAt) }}
                  </p>
                </div>
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  [ngClass]="{
                    'bg-green-100 text-green-800':
                      suggestion.status === 'APPROVED',
                    'bg-yellow-100 text-yellow-800':
                      suggestion.status === 'IN_ANALYSIS' ||
                      suggestion.status === 'PENDING_REVIEW',
                    'bg-red-100 text-red-800': suggestion.status === 'REJECTED',
                    'bg-blue-100 text-blue-800':
                      suggestion.status === 'WAITING_CONTACT'
                  }"
                >
                  {{
                    suggestion.status === "APPROVED"
                      ? "Aprovada"
                      : suggestion.status === "IN_ANALYSIS"
                      ? "Em Análise"
                      : suggestion.status === "PENDING_REVIEW"
                      ? "Aguardando Revisão"
                      : suggestion.status === "REJECTED"
                      ? "Rejeitada"
                      : suggestion.status === "WAITING_CONTACT"
                      ? "Aguardando Contato"
                      : suggestion.status
                  }}
                </span>
              </div>

              <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">
                  Raciocínio da IA:
                </h4>
                <p class="text-blue-700 whitespace-pre-line">
                  {{ suggestion.iaReasoning }}
                </p>
              </div>

              <div
                *ngIf="
                  suggestion.procedures && suggestion.procedures.length > 0
                "
              >
                <h4 class="text-sm font-medium text-gray-700 mb-2">
                  Procedimentos Sugeridos:
                </h4>
                <ul class="space-y-2">
                  <li
                    *ngFor="let proc of suggestion.procedures"
                    class="flex justify-between items-start p-2 border-b border-gray-100"
                  >
                    <div>
                      <p class="font-medium text-gray-800">
                        {{ proc.procedureName }}
                      </p>
                      <p *ngIf="proc.notes" class="text-sm text-gray-600 mt-1">
                        {{ proc.notes }}
                      </p>
                    </div>
                    <div
                      *ngIf="proc.expectedDate"
                      class="text-sm text-gray-500"
                    >
                      Data Prevista: {{ formatDate(proc.expectedDate) }}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <!-- Exames -->
        <section id="exams" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Exames
          </h2>
          <div
            *ngIf="!medicalRecord.exams || medicalRecord.exams.length === 0"
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum exame registrado para este paciente.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              *ngFor="let exam of medicalRecord.exams"
              class="bg-white rounded-lg shadow-sm p-4 flex flex-col"
            >
              <div class="flex items-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-blue-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <h3 class="font-medium text-gray-800 truncate">
                  {{ exam.name }}
                </h3>
              </div>
              <p class="text-sm text-gray-500 mb-3">
                Enviado em {{ formatDate(exam.uploadedAt) }}
              </p>
              <div
                class="mt-auto pt-3 border-t border-gray-100 flex justify-between"
              >
                <a
                  [href]="exam.fileUrl"
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  Visualizar
                </a>
                <a
                  [href]="exam.fileUrl"
                  download
                  class="text-green-600 hover:text-green-800 text-sm font-medium flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                  Download
                </a>
              </div>
            </div>
          </div>
        </section>

        <!-- Documentos -->
        <section id="documents" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Documentos
          </h2>
          <div
            *ngIf="
              !medicalRecord.documents || medicalRecord.documents.length === 0
            "
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhum documento registrado para este paciente.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              *ngFor="let document of medicalRecord.documents"
              class="bg-white rounded-lg shadow-sm p-4 flex flex-col"
            >
              <div class="flex items-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-indigo-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
                <h3 class="font-medium text-gray-800 truncate">
                  {{ document.name }}
                </h3>
              </div>
              <p class="text-sm text-gray-500 mb-3">
                Enviado em {{ formatDate(document.uploadedAt) }}
              </p>
              <div
                class="mt-auto pt-3 border-t border-gray-100 flex justify-between"
              >
                <a
                  [href]="document.fileUrl"
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  Visualizar
                </a>
                <a
                  [href]="document.fileUrl"
                  download
                  class="text-green-600 hover:text-green-800 text-sm font-medium flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                  Download
                </a>
              </div>
            </div>
          </div>
        </section>

        <!-- Fotos -->
        <section id="photos" class="mb-10">
          <h2
            class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b border-gray-200"
          >
            Fotos
          </h2>
          <div
            *ngIf="!medicalRecord.photos || medicalRecord.photos.length === 0"
            class="bg-gray-50 rounded-lg p-6 text-center"
          >
            <p class="text-gray-500">
              Nenhuma foto registrada para este paciente.
            </p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div
              *ngFor="let photo of medicalRecord.photos"
              class="bg-white rounded-lg shadow-sm p-3"
            >
              <div class="aspect-w-1 aspect-h-1 mb-2">
                <img
                  [src]="photo.fileUrl"
                  [alt]="photo.name || 'Foto do paciente'"
                  class="object-cover rounded-md w-full h-full"
                />
              </div>
              <p class="text-sm text-gray-800 truncate">
                {{ photo.name || "Foto" }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatDate(photo.uploadedAt) }}
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
