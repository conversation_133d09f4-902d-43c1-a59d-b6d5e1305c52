import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CompleteMedicalRecord } from '../../core/models/complete-medical-record.model';
import { MedicalRecordService } from '../../core/services/medical-record.service';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-medical-record-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './medical-record-modal.component.html',
  styleUrls: ['./medical-record-modal.component.scss']
})
export class MedicalRecordModalComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() patientId!: number;
  @Output() close = new EventEmitter<void>();

  @ViewChild('sectionMenu') sectionMenu!: ElementRef;
  @ViewChild('contentContainer') contentContainer!: ElementRef;

  medicalRecord: CompleteMedicalRecord | null = null;
  isLoading = true;
  error: string | null = null;
  activeSection: string = 'patient-info';

  // Controle dos accordions de anamnese
  expandedAnamnesisIndex: number | null = null;

  // Controle dos accordions de planos de tratamento
  expandedTreatmentPlanIndex: number | null = null;

  // Controle dos accordions de orçamentos
  expandedBudgetIndex: number | null = null;

  // Controle de seções
  sections = [
    { id: 'patient-info', label: 'Informações do Paciente' },
    { id: 'anamnesis', label: 'Anamnese' },
    { id: 'appointments', label: 'Agendamentos' },
    { id: 'procedures', label: 'Procedimentos Realizados' },
    { id: 'treatment-plans', label: 'Planos de Tratamento' },
    { id: 'budgets', label: 'Orçamentos' },
    { id: 'ai-suggestions', label: 'Sugestões da IA' },
    { id: 'exams', label: 'Exames' },
    { id: 'documents', label: 'Documentos' },
    { id: 'photos', label: 'Fotos' }
  ];

  // Para gerenciar a inscrição
  private destroy$ = new Subject<void>();

  // Para exportação de PDF
  isGeneratingPdf = false;

  constructor(private medicalRecordService: MedicalRecordService) {}

  ngOnInit(): void {
    this.loadMedicalRecord();
  }

  ngAfterViewInit(): void {
    // Configurar o scroll spy após a renderização
    this.setupScrollSpy();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadMedicalRecord(): void {
    this.isLoading = true;
    this.error = null;

    this.medicalRecordService.getPatientMedicalRecord(this.patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          if (data) {
            this.medicalRecord = data;
            this.isLoading = false;
          } else {
            // Se não houver dados, criar um prontuário
            this.createNewMedicalRecord();
          }
        },
        error: (err) => {
          console.error('Erro ao carregar prontuário:', err);
          // Tentar criar um novo prontuário em vez de mostrar erro
          this.createNewMedicalRecord();
        }
      });
  }

  /**
   * Cria um novo prontuário quando não existe
   */
  createNewMedicalRecord(): void {
    this.isLoading = true;
    this.error = null;

    // Tentar criar o prontuário
    this.medicalRecordService.createPatientMedicalRecord(this.patientId)
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (medicalRecord) => {
          console.log('Prontuário criado ou recuperado com sucesso:', medicalRecord);

          // Após criar, carregar o prontuário completo
          setTimeout(() => {
            this.medicalRecordService.getPatientMedicalRecord(this.patientId)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: (data) => {
                  this.medicalRecord = data;
                  this.isLoading = false;
                },
                error: (err) => {
                  console.error('Erro ao carregar prontuário após criação:', err);

                  // Tentar uma abordagem alternativa - usar getCompleteMedicalRecord
                  this.medicalRecordService.getCompleteMedicalRecord(this.patientId)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe({
                      next: (data) => {
                        this.medicalRecord = data;
                        this.isLoading = false;
                      },
                      error: (finalErr) => {
                        console.error('Erro final ao carregar prontuário:', finalErr);
                        this.error = 'Não foi possível carregar o prontuário. Por favor, tente novamente.';
                        this.isLoading = false;
                      }
                    });
                }
              });
          }, 1000); // Aguardar 1 segundo para garantir que o backend processou a criação
        },
        error: (err) => {
          console.error('Erro ao criar prontuário após várias tentativas:', err);
          this.error = 'Não foi possível criar o prontuário. Por favor, tente novamente.';
          this.isLoading = false;
        }
      });
  }

  closeModal(): void {
    this.close.emit();
  }

  scrollToSection(sectionId: string): void {
    this.activeSection = sectionId;

    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  setupScrollSpy(): void {
    if (!this.contentContainer) return;

    const container = this.contentContainer.nativeElement;

    // Observar o scroll do container
    container.addEventListener('scroll', () => {
      // Encontrar qual seção está visível
      const sections = this.sections.map(s => document.getElementById(s.id));

      // Filtrar seções que existem no DOM
      const validSections = sections.filter(s => s !== null) as HTMLElement[];

      // Encontrar a seção mais próxima do topo
      let activeSection = this.activeSection;
      let minDistance = Infinity;

      validSections.forEach(section => {
        const rect = section.getBoundingClientRect();
        const distance = Math.abs(rect.top - 100); // 100px de offset

        if (distance < minDistance) {
          minDistance = distance;
          activeSection = section.id;
        }
      });

      // Atualizar a seção ativa
      if (activeSection !== this.activeSection) {
        this.activeSection = activeSection;
      }
    });
  }

  async exportToPdf(): Promise<void> {
    if (!this.medicalRecord) {
      console.error('Nenhum prontuário disponível para exportar');
      return;
    }

    this.isGeneratingPdf = true;

    try {
      console.log('Iniciando exportação de PDF para:', this.medicalRecord.patient.name);

      const pdfBlob = await this.medicalRecordService.generatePDF(this.medicalRecord);

      // Verificar se o blob foi gerado corretamente
      if (!pdfBlob || pdfBlob.size === 0) {
        throw new Error('PDF gerado está vazio ou corrompido');
      }

      console.log('PDF gerado com sucesso, tamanho:', pdfBlob.size, 'bytes');

      // Criar URL para download
      const url = URL.createObjectURL(pdfBlob);

      // Criar link para download
      const link = document.createElement('a');
      link.href = url;
      const fileName = `prontuario_${this.medicalRecord.patient.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      link.download = fileName;

      // Adicionar ao DOM temporariamente para garantir que funcione em todos os navegadores
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpar URL
      URL.revokeObjectURL(url);

      console.log('Download iniciado para:', fileName);
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      alert('Erro ao gerar PDF do prontuário. Por favor, tente novamente.');
    } finally {
      this.isGeneratingPdf = false;
    }
  }

  // Formatadores de data e valores
  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR');
  }

  formatCurrency(value: number | undefined): string {
    if (value === undefined) return 'N/A';
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  /**
   * Alterna a expansão/contração de uma anamnese específica
   * @param index Índice da anamnese na lista
   */
  toggleAnamnesis(index: number): void {
    // Se o índice já está expandido, feche-o
    if (this.expandedAnamnesisIndex === index) {
      this.expandedAnamnesisIndex = null;
    } else {
      // Caso contrário, expanda este índice
      this.expandedAnamnesisIndex = index;
    }
  }

  /**
   * Verifica se uma anamnese está expandida
   * @param index Índice da anamnese na lista
   * @returns true se a anamnese estiver expandida, false caso contrário
   */
  isAnamnesisExpanded(index: number): boolean {
    return this.expandedAnamnesisIndex === index;
  }

  /**
   * Verifica se uma anamnese é a mais recente
   * @param index Índice da anamnese na lista
   * @returns true se a anamnese for a mais recente, false caso contrário
   */
  isLatestAnamnesis(index: number): boolean {
    return index === 0; // Assumindo que a lista está ordenada com a mais recente primeiro
  }

  /**
   * Alterna a expansão/contração de um plano de tratamento específico
   * @param index Índice do plano de tratamento na lista
   */
  toggleTreatmentPlan(index: number): void {
    if (this.expandedTreatmentPlanIndex === index) {
      this.expandedTreatmentPlanIndex = null;
    } else {
      this.expandedTreatmentPlanIndex = index;
    }
  }

  /**
   * Verifica se um plano de tratamento está expandido
   * @param index Índice do plano de tratamento na lista
   * @returns true se o plano estiver expandido, false caso contrário
   */
  isTreatmentPlanExpanded(index: number): boolean {
    return this.expandedTreatmentPlanIndex === index;
  }

  /**
   * Alterna a expansão/contração de um orçamento específico
   * @param index Índice do orçamento na lista
   */
  toggleBudget(index: number): void {
    if (this.expandedBudgetIndex === index) {
      this.expandedBudgetIndex = null;
    } else {
      this.expandedBudgetIndex = index;
    }
  }

  /**
   * Verifica se um orçamento está expandido
   * @param index Índice do orçamento na lista
   * @returns true se o orçamento estiver expandido, false caso contrário
   */
  isBudgetExpanded(index: number): boolean {
    return this.expandedBudgetIndex === index;
  }

  /**
   * Gera um número sequencial para as sugestões da IA
   * @param index Índice da sugestão na lista
   * @returns Número sequencial da sugestão
   */
  getSuggestionNumber(index: number): number {
    return index + 1;
  }

  /**
   * Traduz o status do plano de tratamento para português
   * @param status Status em inglês
   * @returns Status traduzido
   */
  getTreatmentPlanStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      'open': 'Em Aberto',
      'completed': 'Concluído',
      'cancelled': 'Cancelado'
    };
    return statusLabels[status] || status;
  }

  /**
   * Traduz o status do procedimento para português
   * @param status Status em inglês
   * @returns Status traduzido
   */
  getProcedureStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      'pending': 'Pendente',
      'in_progress': 'Em Andamento',
      'completed': 'Concluído',
      'cancelled': 'Cancelado',
      'scheduled': 'Agendado'
    };
    return statusLabels[status] || status;
  }

  /**
   * Traduz o status do orçamento para português
   * @param status Status em inglês
   * @returns Status traduzido
   */
  getBudgetStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      'pending': 'Pendente',
      'approved': 'Aprovado',
      'rejected': 'Rejeitado'
    };
    return statusLabels[status] || status;
  }
}
