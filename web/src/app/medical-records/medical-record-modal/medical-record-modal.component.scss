:host {
  display: block;
}

/* Estilos para o menu lateral fixo */
.section-menu {
  position: sticky;
  top: 0;
  height: calc(100vh - 64px); /* Altura da tela menos o header */
  overflow-y: auto;
}

/* Estilos para o container de conteúdo */
.content-container {
  scroll-behavior: smooth;
}

/* Estilos para as seções */
section {
  scroll-margin-top: 1rem;
}

/* Animação de fade-in para o modal */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

:host {
  animation: fadeIn 0.3s ease-in-out;
}

/* Estilos para os cards */
.card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Estilos para os botões */
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Estilos para as imagens e documentos */
.thumbnail {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.thumbnail:hover {
  transform: scale(1.05);
}

/* Estilos para o preview de imagens */
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.preview-content {
  max-width: 90%;
  max-height: 90%;
}

.preview-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.preview-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.preview-close:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
