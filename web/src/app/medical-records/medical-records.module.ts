import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MedicalRecordListComponent } from './medical-record-list/medical-record-list.component';
import { MedicalRecordFormComponent } from './medical-record-form/medical-record-form.component';
import { MedicalRecordDetailComponent } from './medical-record-detail/medical-record-detail.component';

const routes: Routes = [
  {
    path: '',
    component: MedicalRecordListComponent
  },
  {
    path: 'new',
    component: MedicalRecordFormComponent
  },
  {
    path: ':id/edit',
    component: MedicalRecordFormComponent
  },
  {
    path: ':id',
    component: MedicalRecordDetailComponent
  }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
    // Componentes standalone não precisam ser importados aqui
    // MedicalRecordListComponent,
    // MedicalRecordFormComponent,
    // MedicalRecordDetailComponent
  ]
})
export class MedicalRecordsModule { }
