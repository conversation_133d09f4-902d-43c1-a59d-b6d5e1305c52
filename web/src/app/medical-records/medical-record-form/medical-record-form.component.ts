import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MedicalRecordService } from '../../core/services/medical-record.service';
import { PatientService } from '../../core/services/patient.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-medical-record-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './medical-record-form.component.html',
  styleUrls: ['./medical-record-form.component.scss'],
})
export class MedicalRecordFormComponent implements OnInit {
  medicalRecordForm: FormGroup;
  isEditMode = false;
  medicalRecordId: number | null = null;
  isSubmitting = false;
  patientId: number | null = null;
  patientName: string = '';
  returnUrl: string = '';
  @ViewChild('fileInput') fileInput!: ElementRef;
  selectedFile: File | null = null;
  fileError: string | null = null;
  readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB em bytes
  readonly ALLOWED_FILE_TYPES = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  constructor(
    private fb: FormBuilder,
    private medicalRecordService: MedicalRecordService,
    private patientService: PatientService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.medicalRecordForm = this.fb.group({
      // Anamnese
      preExistingDiseases: [''],
      allergies: [''],
      medications: [''],
      medicalHistory: [''],
      habits: [''],

      // Exame Clínico e Diagnóstico
      clinicalExamination: [''],
      diagnosis: [''],

      // Plano de Tratamento
      treatmentPlan: [''],
      risks: [''],
      patientConsent: [false],

      // Evolução do Caso
      evolution: [''],

      // Exames complementares
      complementaryExams: [''],
    });
  }

  ngOnInit(): void {
    // Obter parâmetros da rota
    this.route.params.subscribe((params) => {
      this.medicalRecordId = params['id'] ? +params['id'] : null;
      this.isEditMode = !!this.medicalRecordId;

      // Obter parâmetros de consulta
      this.route.queryParams.subscribe((queryParams) => {
        this.patientId = queryParams['patientId']
          ? +queryParams['patientId']
          : null;
        this.returnUrl = queryParams['returnUrl'] || '';

        // Carregar dados do paciente
        if (this.patientId) {
          this.patientService
            .getPatient(this.patientId)
            .subscribe((patient) => {
              this.patientName = patient.name;
            });
        }

        // Se estiver no modo de edição, carregar dados do prontuário
        if (this.isEditMode && this.medicalRecordId) {
          this.loadMedicalRecord();
        }
      });
    });
  }

  loadMedicalRecord(): void {
    if (this.medicalRecordId) {
      this.medicalRecordService
        .getMedicalRecord(this.medicalRecordId)
        .subscribe((medicalRecord) => {
          this.patientId = medicalRecord.patientId;

          // Carregar dados do paciente
          this.patientService
            .getPatient(medicalRecord.patientId)
            .subscribe((patient) => {
              this.patientName = patient.name;
            });

          // Converter strings em arrays para os campos que são multi-select
          const preExistingDiseases = medicalRecord.preExistingDiseases
            ? medicalRecord.preExistingDiseases.split(', ')
            : [];
          const allergies = medicalRecord.allergies
            ? medicalRecord.allergies.split(', ')
            : [];
          const habits = medicalRecord.habits
            ? medicalRecord.habits.split(', ')
            : [];
          const complementaryExams = medicalRecord.complementaryExams
            ? medicalRecord.complementaryExams.split(', ')
            : [];

          // Preencher o formulário
          this.medicalRecordForm.patchValue({
            preExistingDiseases: preExistingDiseases,
            allergies: allergies,
            medications: medicalRecord.medications,
            medicalHistory: medicalRecord.medicalHistory,
            habits: habits,
            clinicalExamination: medicalRecord.clinicalExamination,
            diagnosis: medicalRecord.diagnosis,
            treatmentPlan: medicalRecord.treatmentPlan,
            risks: medicalRecord.risks,
            patientConsent: medicalRecord.patientConsent,
            evolution: medicalRecord.evolution,
            complementaryExams: complementaryExams,
          });
        });
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file) {
      this.clearFile();
      return;
    }

    // Validar tamanho do arquivo
    if (file.size > this.MAX_FILE_SIZE) {
      this.fileError = 'O arquivo excede o tamanho máximo permitido de 5MB';
      this.clearFile();
      return;
    }

    // Validar tipo do arquivo
    if (!this.ALLOWED_FILE_TYPES.includes(file.type)) {
      this.fileError =
        'Tipo de arquivo não permitido. Use PDF, JPG, PNG ou DOC';
      this.clearFile();
      return;
    }

    this.selectedFile = file;
    this.fileError = null;
  }

  clearFile(): void {
    this.selectedFile = null;
    this.fileError = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  async onSubmit() {
    try {
      if (this.medicalRecordForm.invalid) {
        return;
      }

      this.isSubmitting = true;
      const formData = new FormData();

      // Adicionar arquivo primeiro
      if (this.selectedFile) {
        formData.append('examFile', this.selectedFile);
      }

      // Adicionar outros campos do formulário
      Object.keys(this.medicalRecordForm.value).forEach((key) => {
        const value = this.medicalRecordForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      // Adicionar patientId ao formData
      if (this.patientId) {
        formData.append('patientId', this.patientId.toString());
      }

      // Enviar para o serviço apropriado
      if (this.isEditMode && this.medicalRecordId) {
        await firstValueFrom(
          this.medicalRecordService.updateMedicalRecord(
            this.medicalRecordId,
            formData
          )
        );
      } else {
        await firstValueFrom(
          this.medicalRecordService.createMedicalRecord(formData)
        );
      }

      this.router.navigate(['/medical-records']);
    } catch (error) {
      console.error('Erro ao salvar prontuário:', error);
    } finally {
      this.isSubmitting = false;
    }
  }

  getTitle(): string {
    if (this.isEditMode) {
      return 'Editar Prontuário';
    }
    return 'Novo Prontuário';
  }

  getSubtitle(): string {
    return 'Prontuário do Paciente';
  }
}
