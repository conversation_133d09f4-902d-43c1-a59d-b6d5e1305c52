<div class="py-8">
  <div class="mb-6">
    <div class="flex items-center mb-2">
      <a
        [routerLink]="returnUrl ? returnUrl : '/medical-records'"
        class="text-blue-600 hover:text-blue-800 mr-2"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </a>
      <h1 class="text-2xl font-bold text-gray-800">{{ getTitle() }}</h1>
    </div>
    <p class="text-gray-600">
      {{ getSubtitle() }} - Paciente: {{ patientName }}
    </p>
  </div>

  <form
    [formGroup]="medicalRecordForm"
    class="bg-white rounded-lg shadow-md overflow-hidden"
  >
    <!-- Anamnese -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Anamnese</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Doenças pré-existentes -->
        <div>
          <label
            for="preExistingDiseases"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Doenças pré-existentes</label
          >
          <select
            id="preExistingDiseases"
            formControlName="preExistingDiseases"
            multiple
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Hipertensão">Hipertensão</option>
            <option value="Diabetes">Diabetes</option>
            <option value="Cardiopatia">Cardiopatia</option>
            <option value="Asma">Asma</option>
            <option value="Epilepsia">Epilepsia</option>
            <option value="Hepatite">Hepatite</option>
            <option value="HIV/AIDS">HIV/AIDS</option>
            <option value="Tuberculose">Tuberculose</option>
            <option value="Câncer">Câncer</option>
            <option value="Outro">Outro</option>
          </select>
        </div>

        <!-- Alergias -->
        <div>
          <label
            for="allergies"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Alergias</label
          >
          <select
            id="allergies"
            formControlName="allergies"
            multiple
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Penicilina">Penicilina</option>
            <option value="Anestésicos locais">Anestésicos locais</option>
            <option value="Látex">Látex</option>
            <option value="Anti-inflamatórios">Anti-inflamatórios</option>
            <option value="Analgésicos">Analgésicos</option>
            <option value="Outro">Outro</option>
          </select>
        </div>

        <!-- Uso de medicamentos contínuos -->
        <div class="col-span-1 md:col-span-2">
          <label
            for="medications"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Uso de medicamentos contínuos</label
          >
          <textarea
            id="medications"
            formControlName="medications"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>

        <!-- Histórico médico e odontológico -->
        <div class="col-span-1 md:col-span-2">
          <label
            for="medicalHistory"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Histórico médico e odontológico</label
          >
          <textarea
            id="medicalHistory"
            formControlName="medicalHistory"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>

        <!-- Hábitos -->
        <div>
          <label
            for="habits"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Hábitos</label
          >
          <select
            id="habits"
            formControlName="habits"
            multiple
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Tabagismo">Tabagismo</option>
            <option value="Etilismo">Etilismo</option>
            <option value="Bruxismo">Bruxismo</option>
            <option value="Onicofagia">Onicofagia (roer unhas)</option>
            <option value="Respiração bucal">Respiração bucal</option>
            <option value="Outro">Outro</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Exame Clínico e Diagnóstico -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Exame Clínico e Diagnóstico</h2>
      <div class="grid grid-cols-1 gap-6">
        <!-- Exame clínico -->
        <div>
          <label
            for="clinicalExamination"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Exame clínico</label
          >
          <textarea
            id="clinicalExamination"
            formControlName="clinicalExamination"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>

        <!-- Diagnóstico -->
        <div>
          <label
            for="diagnosis"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Diagnóstico</label
          >
          <textarea
            id="diagnosis"
            formControlName="diagnosis"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Plano de Tratamento -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Plano de Tratamento</h2>
      <div class="grid grid-cols-1 gap-6">
        <!-- Procedimentos indicados -->
        <div>
          <label
            for="treatmentPlan"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Procedimentos indicados</label
          >
          <textarea
            id="treatmentPlan"
            formControlName="treatmentPlan"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>

        <!-- Riscos envolvidos -->
        <div>
          <label
            for="risks"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Riscos envolvidos</label
          >
          <textarea
            id="risks"
            formControlName="risks"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>

        <!-- Consentimento do paciente -->
        <div class="flex items-center">
          <input
            id="patientConsent"
            type="checkbox"
            formControlName="patientConsent"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="patientConsent" class="ml-2 block text-sm text-gray-900">
            Paciente consentiu com o plano de tratamento
          </label>
        </div>
      </div>
    </div>

    <!-- Evolução do Caso -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Evolução do Caso</h2>
      <div class="grid grid-cols-1 gap-6">
        <!-- Registro de cada atendimento -->
        <div>
          <label
            for="evolution"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Registro de atendimentos</label
          >
          <textarea
            id="evolution"
            formControlName="evolution"
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Exames complementares -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Exames Complementares</h2>
      <div class="grid grid-cols-1 gap-6">
        <!-- Exames realizados -->
        <div>
          <label
            for="complementaryExams"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Exames realizados</label
          >
          <select
            id="complementaryExams"
            formControlName="complementaryExams"
            multiple
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Radiografia panorâmica">
              Radiografia panorâmica
            </option>
            <option value="Radiografia periapical">
              Radiografia periapical
            </option>
            <option value="Tomografia computadorizada">
              Tomografia computadorizada
            </option>
            <option value="Fotografias intraorais">
              Fotografias intraorais
            </option>
            <option value="Modelos de gesso">Modelos de gesso</option>
            <option value="Modelos digitais">Modelos digitais</option>
            <option value="Testes laboratoriais">Testes laboratoriais</option>
            <option value="Outro">Outro</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Upload do exame -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold mb-4">Upload do Exame</h2>
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label
            for="examFile"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Upload do exame (máx. 5MB)</label
          >
          <div class="flex items-center gap-2">
            <input
              #fileInput
              type="file"
              id="examFile"
              (change)="onFileSelected($event)"
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <!-- Botão para limpar o arquivo -->
            <button
              *ngIf="selectedFile"
              type="button"
              (click)="clearFile()"
              class="px-3 py-2 text-sm text-red-600 hover:text-red-700"
              title="Remover arquivo"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
          <!-- Mostrar nome do arquivo selecionado -->
          <div *ngIf="selectedFile" class="mt-1 text-sm text-gray-500">
            Arquivo selecionado: {{ selectedFile.name }}
          </div>
          <p *ngIf="fileError" class="mt-1 text-sm text-red-600">
            {{ fileError }}
          </p>
        </div>
      </div>
    </div>

    <!-- Botões de ação -->
    <div class="p-6 flex justify-end space-x-4">
      <a
        [routerLink]="returnUrl ? returnUrl : '/medical-records'"
        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
        Cancelar
      </a>
      <button
        type="button"
        (click)="onSubmit()"
        [disabled]="isSubmitting"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
        [ngClass]="{ 'opacity-75 cursor-not-allowed': isSubmitting }"
      >
        <svg
          *ngIf="isSubmitting"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <svg
          *ngIf="!isSubmitting"
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
        </svg>
        {{ isSubmitting ? "Salvando..." : "Salvar" }}
      </button>
    </div>
  </form>
</div>
