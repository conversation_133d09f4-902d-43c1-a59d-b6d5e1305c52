import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MedicalRecordService } from '../../core/services/medical-record.service';
import { MedicalRecord } from '../../core/models/medical-record.model';
import { FormsModule } from '@angular/forms';
import { MedicalRecordModalComponent } from '../medical-record-modal/medical-record-modal.component';
import { NotificationService } from '../../core/services/notification.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-medical-record-list',
  standalone: true,
  imports: [CommonModule, FormsModule, MedicalRecordModalComponent],
  templateUrl: './medical-record-list.component.html',
  styleUrls: ['./medical-record-list.component.scss']
})
export class MedicalRecordListComponent implements OnInit {
  medicalRecords: MedicalRecord[] = [];
  filteredMedicalRecords: MedicalRecord[] = [];
  isLoading = true;
  searchTerm: string = '';

  // Controle do modal de prontuário
  showMedicalRecordModal = false;
  selectedPatientId: number | null = null;

  constructor(
    private medicalRecordService: MedicalRecordService,
    private notificationService: NotificationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadMedicalRecords();
  }

  loadMedicalRecords(): void {
    this.isLoading = true;
    this.medicalRecordService.getMedicalRecords().subscribe({
      next: (medicalRecords) => {
        this.medicalRecords = medicalRecords;
        this.filteredMedicalRecords = [...this.medicalRecords];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar prontuários:', error);
        this.isLoading = false;
      }
    });
  }

  searchMedicalRecords(event: Event): void {
    const value = (event.target as HTMLInputElement).value.toLowerCase();
    this.searchTerm = value;
    this.applyFilters();
  }

  // Método removido pois não há mais tipos de prontuário
  // filterByRecordType(recordType: string): void {
  //   this.selectedRecordType = recordType;
  //   this.applyFilters();
  // }

  applyFilters(): void {
    let filtered = [...this.medicalRecords];

    // Aplicar filtro de busca
    if (this.searchTerm) {
      filtered = filtered.filter(record =>
        record.patientName?.toLowerCase().includes(this.searchTerm) ||
        record.patientCpf?.toLowerCase().includes(this.searchTerm) ||
        record.patientPhone?.toLowerCase().includes(this.searchTerm) ||
        record.id.toString().includes(this.searchTerm)
      );
    }

    // Filtro de tipo de registro removido pois não há mais tipos de prontuário

    this.filteredMedicalRecords = filtered;
  }

  // Método removido pois não há mais tipos de prontuário
  // getRecordTypeLabel(recordType: string): string {
  //   return recordType === 'treatment' ? 'Tratamento' : 'Atendimento';
  // }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR');
  }

  /**
   * Abre o modal de prontuário completo
   * @param patientId ID do paciente
   */
  viewCompleteMedicalRecord(patientId: number | undefined): void {
    if (!patientId) return;

    this.selectedPatientId = patientId;
    this.showMedicalRecordModal = true;
  }

  /**
   * Fecha o modal de prontuário completo
   */
  closeMedicalRecordModal(): void {
    this.showMedicalRecordModal = false;
    this.selectedPatientId = null;
  }

  /**
   * Navega para a página de detalhes do paciente
   * @param patientId ID do paciente
   */
  navigateToPatientDetails(patientId: number | undefined): void {
    if (!patientId) {
      this.notificationService.error('Não foi possível encontrar o paciente.');
      return;
    }

    this.router.navigate(['/patients', patientId]);
  }

  /**
   * Exporta o prontuário como PDF
   * @param patientId ID do paciente
   */
  async exportPdf(patientId: number | undefined): Promise<void> {
    if (!patientId) return;

    try {
      // Primeiro, obtém o prontuário completo
      const medicalRecord = await firstValueFrom(this.medicalRecordService.getPatientMedicalRecord(patientId));

      if (!medicalRecord) {
        this.notificationService.error('Erro ao exportar prontuário: Não foi possível encontrar o prontuário do paciente.');
        return;
      }

      // Gera o PDF
      console.log('Iniciando geração de PDF para:', medicalRecord.patient.name);
      const pdfBlob = await this.medicalRecordService.generatePDF(medicalRecord);

      // Verificar se o blob foi gerado corretamente
      if (!pdfBlob || pdfBlob.size === 0) {
        throw new Error('PDF gerado está vazio ou corrompido');
      }

      console.log('PDF gerado com sucesso, tamanho:', pdfBlob.size, 'bytes');

      // Cria URL para download
      const url = URL.createObjectURL(pdfBlob);

      // Cria link para download
      const link = document.createElement('a');
      link.href = url;
      const fileName = `prontuario_${medicalRecord.patient.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      link.download = fileName;

      // Adicionar ao DOM temporariamente para garantir que funcione em todos os navegadores
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpar URL
      URL.revokeObjectURL(url);

      console.log('Download iniciado para:', fileName);
      this.notificationService.success('PDF gerado com sucesso! O prontuário foi exportado como PDF.');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      this.notificationService.error('Erro ao exportar prontuário: Ocorreu um erro ao gerar o PDF. Por favor, tente novamente.');
    }
  }
}
