<div class="bg-white shadow rounded-lg p-6">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Medical record details -->
  <div *ngIf="!isLoading && medicalRecord">
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <a
          [routerLink]="returnUrl ? returnUrl : '/medical-records'"
          class="mr-4"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 hover:text-gray-700"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
        </a>
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            Prontuário #{{ medicalRecord.id }}
          </h1>
          <p class="text-sm text-gray-600">
            {{ getRecordTypeLabel() }} - Paciente: {{ patientName }}
          </p>
        </div>
      </div>
      <div class="flex space-x-2">
        <button
          (click)="generatePDF()"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z"
              clip-rule="evenodd"
            />
          </svg>
          PDF
        </button>
        <button
          (click)="editMedicalRecord()"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
            />
          </svg>
          Editar
        </button>
      </div>
    </div>

    <!-- Anamnese section -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Informações Gerais
        </h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Doenças pré-existentes:</span>
            <span class="text-gray-900">{{
              medicalRecord.preExistingDiseases || "Não informado"
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Alergias:</span>
            <span class="text-gray-900">{{
              medicalRecord.allergies || "Não informado"
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Hábitos:</span>
            <span class="text-gray-900">{{
              medicalRecord.habits || "Não informado"
            }}</span>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Informações Adicionais
        </h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Tipo de Prontuário:</span>
            <span class="text-gray-900">{{ getRecordTypeLabel() }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data de Criação:</span>
            <span class="text-gray-900">{{
              formatDate(medicalRecord.createdAt)
            }}</span>
          </div>
          <!-- Removido campo de última atualização pois não existe no modelo -->
        </div>
      </div>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Uso de medicamentos contínuos
      </h2>
      <p class="text-gray-700 whitespace-pre-line">
        {{ medicalRecord.medications || "Não informado" }}
      </p>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Histórico médico e odontológico
      </h2>
      <p class="text-gray-700 whitespace-pre-line">
        {{ medicalRecord.medicalHistory || "Não informado" }}
      </p>
    </div>

    <!-- Exame Clínico e Diagnóstico -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Exame Clínico</h2>
        <p class="text-gray-700 whitespace-pre-line">
          {{ medicalRecord.clinicalExamination || "Não informado" }}
        </p>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Diagnóstico</h2>
        <p class="text-gray-700 whitespace-pre-line">
          {{ medicalRecord.diagnosis || "Não informado" }}
        </p>
      </div>
    </div>

    <!-- Plano de Tratamento -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Procedimentos Indicados
        </h2>
        <p class="text-gray-700 whitespace-pre-line">
          {{ medicalRecord.treatmentPlan || "Não informado" }}
        </p>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Riscos Envolvidos
        </h2>
        <p class="text-gray-700 whitespace-pre-line">
          {{ medicalRecord.risks || "Não informado" }}
        </p>
      </div>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Consentimento do Paciente
      </h2>
      <p class="text-gray-700">
        <span
          class="px-2 py-1 text-xs font-semibold rounded-full"
          [ngClass]="
            medicalRecord.patientConsent
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          "
        >
          {{
            medicalRecord.patientConsent
              ? "Consentimento obtido"
              : "Consentimento pendente"
          }}
        </span>
      </p>
    </div>

    <!-- Evolução do Caso -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Evolução do Caso</h2>
      <p class="text-gray-700 whitespace-pre-line">
        {{ medicalRecord.evolution || "Não informado" }}
      </p>
    </div>

    <!-- Exames complementares -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Exames Complementares
      </h2>
      <div class="space-y-4">
        <!-- Descrição dos exames -->
        <p class="text-gray-700 whitespace-pre-line">
          {{ medicalRecord.complementaryExams || "Não informado" }}
        </p>

        <!-- Arquivo do exame -->
        <div *ngIf="medicalRecord.examFilePath" class="mt-4">
          <div
            class="flex items-center space-x-2 p-2 bg-white rounded border border-gray-200"
          >
            <!-- Loading spinner -->
            <div
              *ngIf="isLoadingFile"
              class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"
            ></div>

            <!-- Arquivo -->
            <ng-container *ngIf="!isLoadingFile && examFileUrl">
              <!-- Ícone baseado no tipo do arquivo -->
              <span class="material-icons text-gray-600">
                {{ getFileIcon(medicalRecord.examFilePath) }}
              </span>

              <!-- Nome do arquivo -->
              <span class="text-sm text-gray-600">
                {{ medicalRecord.examFilePath.split("/").pop() }}
              </span>

              <!-- Botões de ação -->
              <div class="flex space-x-2 ml-auto">
                <!-- Visualizar -->
                <button
                  (click)="openPreview()"
                  class="text-blue-600 hover:text-blue-800 flex items-center"
                  title="Visualizar arquivo"
                >
                  <span class="material-icons text-sm">visualizar</span>
                </button>

                <!-- Download -->
                <a
                  [href]="examFileUrl"
                  download
                  class="text-blue-600 hover:text-blue-800 flex items-center"
                  title="Baixar arquivo"
                >
                  <span class="material-icons text-sm">download</span>
                </a>
              </div>
            </ng-container>

            <!-- Mensagem de erro -->
            <div
              *ngIf="!isLoadingFile && !examFileUrl"
              class="text-red-600 text-sm"
            >
              Erro ao carregar o arquivo
            </div>
          </div>

          <!-- Modal de visualização -->
          <div
            *ngIf="isPreviewOpen"
            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <div class="relative w-11/12 h-5/6 bg-white rounded-lg shadow-xl">
              <!-- Cabeçalho do modal -->
              <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-medium">
                  {{ medicalRecord.examFilePath.split("/").pop() }}
                </h3>
                <button
                  (click)="closePreview()"
                  class="text-gray-500 hover:text-gray-700"
                >
                  <span class="material-icons">close</span>
                </button>
              </div>

              <!-- Conteúdo do modal -->
              <div class="h-full p-4">
                <!-- Visualizador de PDF -->
                <iframe
                  *ngIf="isPDF() && sanitizedExamFileUrl"
                  [src]="sanitizedExamFileUrl"
                  class="w-full h-full"
                  frameborder="0"
                >
                </iframe>

                <!-- Visualizador de imagem -->
                <img
                  *ngIf="isImage() && safeExamFileUrl"
                  [src]="safeExamFileUrl"
                  class="max-w-full max-h-full mx-auto"
                  alt="Exame"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Agendamentos do Paciente -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Agendamentos do Paciente
      </h2>

      <!-- Loading indicator -->
      <div
        *ngIf="isLoadingSchedulings"
        class="flex justify-center items-center py-4"
      >
        <div
          class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"
        ></div>
      </div>

      <!-- Agendamentos list -->
      <div *ngIf="!isLoadingSchedulings">
        <div
          *ngIf="schedulings.length === 0"
          class="text-gray-500 text-center py-4"
        >
          Nenhum agendamento registrado para este paciente.
        </div>

        <div *ngIf="schedulings.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Data
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Dentista
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Ações
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                *ngFor="let scheduling of schedulings"
                class="hover:bg-gray-50"
              >
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(scheduling.date) }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                  {{ scheduling.dentistName || "Dentista não identificado" }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span
                    class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                    [ngClass]="{
                      'bg-yellow-100 text-yellow-800':
                        scheduling.status === 'scheduled-unconfirmed',
                      'bg-blue-100 text-blue-800':
                        scheduling.status === 'scheduled-confirmed',
                      'bg-indigo-100 text-indigo-800':
                        scheduling.status === 'in-progress',
                      'bg-green-100 text-green-800':
                        scheduling.status === 'completed',
                      'bg-red-100 text-red-800':
                        scheduling.status === 'cancelled' ||
                        scheduling.status === 'unscheduled'
                    }"
                  >
                    {{ getStatusLabel(scheduling.status) }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                  <a
                    [routerLink]="['/schedulings', scheduling.id]"
                    class="text-blue-600 hover:text-blue-900 flex items-center"
                    title="Ver"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Tratamentos do Paciente -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        Tratamentos do Paciente
      </h2>

      <!-- Loading indicator -->
      <div
        *ngIf="isLoadingTreatments"
        class="flex justify-center items-center py-4"
      >
        <div
          class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"
        ></div>
      </div>

      <!-- Tratamentos list -->
      <div *ngIf="!isLoadingTreatments">
        <div
          *ngIf="treatments.length === 0"
          class="text-gray-500 text-center py-4"
        >
          Nenhum tratamento registrado para este paciente.
        </div>

        <div *ngIf="treatments.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Tipo
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Data
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Dentista
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Ações
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let treatment of treatments" class="hover:bg-gray-50">
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                  {{ treatment.name }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(treatment.createdAt) }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                  {{ treatment.dentistName || "Dentista não identificado" }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span
                    class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                    [ngClass]="{
                      'bg-yellow-100 text-yellow-800':
                        treatment.status === 'scheduled-unconfirmed' ||
                        treatment.status === 'scheduled',
                      'bg-blue-100 text-blue-800':
                        treatment.status === 'scheduled-confirmed' ||
                        treatment.status === 'confirmed',
                      'bg-indigo-100 text-indigo-800':
                        treatment.status === 'in-progress',
                      'bg-green-100 text-green-800':
                        treatment.status === 'completed',
                      'bg-red-100 text-red-800':
                        treatment.status === 'cancelled' ||
                        treatment.status === 'unscheduled'
                    }"
                  >
                    {{ getStatusLabel(treatment.status) }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                  <a
                    [routerLink]="['/treatments', treatment.id]"
                    class="text-blue-600 hover:text-blue-900"
                    title="Ver"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 inline"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
