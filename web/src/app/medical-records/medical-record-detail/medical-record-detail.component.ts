import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MedicalRecordService } from '../../core/services/medical-record.service';
import { MedicalRecord } from '../../core/models/medical-record.model';
import { PatientService } from '../../core/services/patient.service';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { SchedulingService } from '../../core/services/scheduling.service';
import { Scheduling } from '../../core/models/scheduling.model';
import { Patient } from '../../core/models/patient.model';

import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import {
  DomSanitizer,
  SafeUrl,
  SafeResourceUrl,
} from '@angular/platform-browser';

// Interface para o modelo Treatment
interface Treatment {
  id: number;
  name: string;
  patientId: number;
  patientName?: string;
  dentistId: number;
  dentistName?: string;
  status: string;
  createdAt?: Date;
  cost?: number;
  paid?: boolean;
  notes?: string;
  [key: string]: any; // Para propriedades adicionais
}

@Component({
  selector: 'app-medical-record-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './medical-record-detail.component.html',
  styleUrls: ['./medical-record-detail.component.scss'],
})
export class MedicalRecordDetailComponent implements OnInit {
  medicalRecord: MedicalRecord | null = null;
  isLoading = true;
  patientName: string = '';
  patient: Patient | null = null;
  returnUrl: string = '';
  treatments: Treatment[] = [];
  schedulings: Scheduling[] = [];
  isLoadingTreatments = false;
  isLoadingSchedulings = false;
  examFileUrl: string | null = null;
  isLoadingFile = false;
  isPreviewOpen = false;
  safeExamFileUrl: SafeUrl | null = null;
  sanitizedExamFileUrl: SafeResourceUrl | null = null;

  constructor(
    private medicalRecordService: MedicalRecordService,
    private patientService: PatientService,
    private treatmentPlanService: TreatmentPlanService,
    private schedulingService: SchedulingService,
    private route: ActivatedRoute,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      const id = +params['id'];

      this.route.queryParams.subscribe((queryParams) => {
        this.returnUrl = queryParams['returnUrl'] || '';

        this.loadMedicalRecord(id);
      });
    });
    this.loadExamFile();
  }

  loadMedicalRecord(id: number): void {
    this.isLoading = true;
    this.medicalRecordService.getMedicalRecord(id).subscribe({
      next: (medicalRecord) => {
        this.medicalRecord = medicalRecord;
        this.examFileUrl = medicalRecord.examFilePath ?? null;
        // Carregar dados do paciente
        this.patientService
          .getPatient(medicalRecord.patientId)
          .subscribe((patient) => {
            this.patientName = patient.name;
            this.patient = patient;
            this.isLoading = false;

            // Carregar tratamentos e agendamentos do paciente
            this.loadPatientTreatments(medicalRecord.patientId);
            this.loadPatientSchedulings(medicalRecord.patientId);
            this.loadExamFile();
          });
      },
      error: (error) => {
        console.error('Erro ao carregar prontuário:', error);
        this.isLoading = false;
      },
    });
  }

  loadPatientTreatments(patientId: number): void {
    this.isLoadingTreatments = true;
    this.treatmentPlanService.getTreatmentPlansByPatient(patientId).subscribe({
      next: (plans: any[]) => {
        // Extrair procedimentos de todos os planos
        const procedures = plans.flatMap(plan =>
          (plan.procedures || []).map((proc: any) => ({
            id: proc.id,
            name: proc.name,
            patientId: plan.patientId,
            patientName: plan.patient?.name,
            dentistId: proc.professionalId,
            dentistName: proc.professional?.name,
            status: proc.status,
            createdAt: proc.createdAt,
            cost: proc.value,
            paid: proc.status === 'completed',
            notes: proc.notes
          }))
        );

        this.treatments = procedures;
        this.isLoadingTreatments = false;
      },
      error: (error: any) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoadingTreatments = false;
      },
    });
  }

  loadPatientSchedulings(patientId: number): void {
    this.isLoadingSchedulings = true;
    this.schedulingService.getSchedulingsByPatient(patientId).subscribe({
      next: (schedulings: any[]) => {
        this.schedulings = schedulings;
        this.isLoadingSchedulings = false;
      },
      error: (error: any) => {
        console.error('Erro ao carregar agendamentos:', error);
        this.isLoadingSchedulings = false;
      },
    });
  }

  getRecordTypeLabel(): string {
    return 'Prontuário do Paciente';
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR');
  }

  editMedicalRecord(): void {
    this.router.navigate(['/medical-records', this.medicalRecord?.id, 'edit'], {
      queryParams: {
        returnUrl:
          this.returnUrl || `/medical-records/${this.medicalRecord?.id}`,
      },
    });
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'Agendado';
      case 'confirmed':
        return 'Confirmado';
      default:
        return status;
    }
  }

  generatePDF(): void {
    if (!this.medicalRecord || !this.patient) return;

    // Criar um novo documento PDF
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 15;
    let y = margin;

    // Adicionar cabeçalho com estilo moderno
    doc.setFillColor(41, 128, 185); // Azul moderno
    doc.rect(0, 0, pageWidth, 25, 'F');

    // Adicionar título
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // Texto branco
    const title = `PRONTUÁRIO MÉDICO #${this.medicalRecord.id}`;
    doc.text(title, pageWidth / 2, 15, { align: 'center' });

    // Resetar cor do texto
    doc.setTextColor(0, 0, 0);
    y = 35;

    // Adicionar data
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const date = `Data de emissão: ${new Date().toLocaleDateString('pt-BR')}`;
    doc.text(date, pageWidth - margin, y, { align: 'right' });
    y += 10;

    // Caixa para dados do paciente
    doc.setDrawColor(41, 128, 185);
    doc.setFillColor(240, 248, 255); // Azul claro
    doc.roundedRect(margin, y, pageWidth - margin * 2, 50, 3, 3, 'FD');
    y += 8;

    // Título da seção
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(41, 128, 185);
    doc.text('DADOS DO PACIENTE', margin + 5, y);
    y += 8;

    // Dados do paciente em duas colunas
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0);

    const col1 = margin + 5;
    const col2 = pageWidth / 2 + 10;

    doc.text(`Nome: ${this.patient.name}`, col1, y);
    doc.text(`CPF: ${this.patient.cpf}`, col2, y);
    y += 8;

    doc.text(`Telefone: ${this.patient.phone}`, col1, y);
    doc.text(`Email: ${this.patient.email}`, col2, y);
    y += 8;

    const address = this.formatAddress(this.patient);
    doc.text(`Endereço: ${address || 'Não informado'}`, col1, y);
    doc.text(
      `Data de Nascimento: ${this.formatDate(this.patient.birthDate)}`,
      col2,
      y
    );
    y += 25; // Aumentado o espaçamento após os dados do paciente

    // Seção de Anamnese
    this.addSectionHeader(doc, 'ANAMNESE', margin, y);
    y += 10;

    // Tabela para anamnese
    const anamneseData = [];

    if (this.medicalRecord.preExistingDiseases) {
      anamneseData.push([
        'Doenças pré-existentes',
        this.medicalRecord.preExistingDiseases,
      ]);
    }

    if (this.medicalRecord.allergies) {
      anamneseData.push(['Alergias', this.medicalRecord.allergies]);
    }

    if (this.medicalRecord.medications) {
      anamneseData.push(['Medicações', this.medicalRecord.medications]);
    }

    if (this.medicalRecord.medicalHistory) {
      anamneseData.push([
        'Histórico médico',
        this.medicalRecord.medicalHistory,
      ]);
    }

    if (this.medicalRecord.habits) {
      anamneseData.push(['Hábitos', this.medicalRecord.habits]);
    }

    if (anamneseData.length > 0) {
      autoTable(doc, {
        startY: y,
        head: [['Campo', 'Informação']],
        body: anamneseData,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { fontStyle: 'bold', cellWidth: 50 },
        },
        styles: {
          overflow: 'linebreak',
          cellPadding: 5,
        },
        margin: { left: margin, right: margin },
      });

      // Obter a posição Y final da tabela
      const finalY = (doc as any).lastAutoTable.finalY;
      y = finalY + 25; // Aumentado o espaçamento após a tabela de anamnese
    } else {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'italic');
      doc.text('Nenhuma informação de anamnese registrada.', margin, y);
      y += 25; // Aumentado o espaçamento
    }

    // Seção de Exame Clínico
    this.addSectionHeader(doc, 'EXAME CLÍNICO E DIAGNÓSTICO', margin, y);
    y += 10;

    // Tabela para exame clínico
    const examData = [];

    if (this.medicalRecord.clinicalExamination) {
      examData.push(['Exame clínico', this.medicalRecord.clinicalExamination]);
    }

    if (this.medicalRecord.diagnosis) {
      examData.push(['Diagnóstico', this.medicalRecord.diagnosis]);
    }

    if (examData.length > 0) {
      autoTable(doc, {
        startY: y,
        head: [['Campo', 'Informação']],
        body: examData,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { fontStyle: 'bold', cellWidth: 50 },
        },
        styles: {
          overflow: 'linebreak',
          cellPadding: 5,
        },
        margin: { left: margin, right: margin },
      });

      // Obter a posição Y final da tabela
      const finalY = (doc as any).lastAutoTable.finalY;
      y = finalY + 25; // Aumentado o espaçamento após a tabela de exame clínico
    } else {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'italic');
      doc.text('Nenhuma informação de exame clínico registrada.', margin, y);
      y += 25; // Aumentado o espaçamento
    }

    // Seção de Plano de Tratamento
    this.addSectionHeader(doc, 'PLANO DE TRATAMENTO', margin, y);
    y += 10;

    // Tabela para plano de tratamento
    const planData = [];

    if (this.medicalRecord.treatmentPlan) {
      planData.push(['Plano de tratamento', this.medicalRecord.treatmentPlan]);
    }

    if (this.medicalRecord.risks) {
      planData.push(['Riscos', this.medicalRecord.risks]);
    }

    if (this.medicalRecord.patientConsent !== undefined) {
      planData.push([
        'Consentimento do paciente',
        this.medicalRecord.patientConsent ? 'Sim' : 'Não',
      ]);
    }

    if (this.medicalRecord.evolution) {
      planData.push(['Evolução', this.medicalRecord.evolution]);
    }

    if (this.medicalRecord.complementaryExams) {
      planData.push([
        'Exames complementares',
        this.medicalRecord.complementaryExams,
      ]);
    }

    if (planData.length > 0) {
      autoTable(doc, {
        startY: y,
        head: [['Campo', 'Informação']],
        body: planData,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { fontStyle: 'bold', cellWidth: 50 },
        },
        styles: {
          overflow: 'linebreak',
          cellPadding: 5,
        },
        margin: { left: margin, right: margin },
      });

      // Obter a posição Y final da tabela
      const finalY = (doc as any).lastAutoTable.finalY;
      y = finalY + 25; // Aumentado o espaçamento após a tabela de plano de tratamento
    } else {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'italic');
      doc.text(
        'Nenhuma informação de plano de tratamento registrada.',
        margin,
        y
      );
      y += 25; // Aumentado o espaçamento
    }

    // Verificar se precisa adicionar nova página
    if (y > pageHeight - 60) {
      doc.addPage();
      y = margin;
    }

    // Adicionar nova página para tratamentos e atendimentos
    doc.addPage();
    y = margin;

    // Cabeçalho da página 2
    doc.setFillColor(41, 128, 185);
    doc.rect(0, 0, pageWidth, 25, 'F');

    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255);
    doc.text('HISTÓRICO DE ATENDIMENTOS', pageWidth / 2, 15, {
      align: 'center',
    });

    doc.setTextColor(0, 0, 0);
    y = 35;

    // Tratamentos do Paciente
    this.addSectionHeader(doc, 'TRATAMENTOS DO PACIENTE', margin, y);
    y += 10;

    if (this.treatments.length > 0) {
      const treatmentData = this.treatments.map((treatment) => [
        treatment.name,
        this.formatDate(treatment.createdAt),
        treatment.dentistName || 'Dentista não identificado',
        this.getStatusLabel(treatment.status),
      ]);

      autoTable(doc, {
        startY: y,
        head: [['Nome', 'Data', 'Dentista', 'Status']],
        body: treatmentData,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        styles: {
          cellPadding: 5,
        },
        margin: { left: margin, right: margin },
      });

      // Obter a posição Y final da tabela
      const finalY = (doc as any).lastAutoTable.finalY;
      y = finalY + 25; // Aumentado o espaçamento após a tabela de tratamentos
    } else {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'italic');
      doc.text('Nenhum tratamento encontrado.', margin, y);
      y += 25; // Aumentado o espaçamento
    }

    // Agendamentos do Paciente
    this.addSectionHeader(doc, 'AGENDAMENTOS DO PACIENTE', margin, y);
    y += 10;

    if (this.schedulings.length > 0) {
      const schedulingData = this.schedulings.map((scheduling: any) => [
        this.formatDate(scheduling.date),
        scheduling.dentistName || 'Dentista não identificado',
        this.getStatusLabel(scheduling.status),
        scheduling.notes || '',
      ]);

      autoTable(doc, {
        startY: y,
        head: [['Data', 'Dentista', 'Status', 'Observações']],
        body: schedulingData,
        theme: 'grid',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        styles: {
          cellPadding: 5,
          overflow: 'linebreak',
        },
        margin: { left: margin, right: margin },
      });
    } else {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'italic');
      doc.text('Nenhum agendamento encontrado.', margin, y);
    }

    // Adicionar rodapé
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(100, 100, 100);
      doc.text(`Página ${i} de ${totalPages}`, pageWidth / 2, pageHeight - 10, {
        align: 'center',
      });
      doc.text(
        `CRM Odonto - Prontuário gerado em ${new Date().toLocaleDateString(
          'pt-BR'
        )}`,
        pageWidth / 2,
        pageHeight - 5,
        { align: 'center' }
      );
    }

    // Abrir o PDF em uma nova aba em vez de baixar
    const pdfOutput = doc.output('blob');
    const pdfUrl = URL.createObjectURL(pdfOutput);
    window.open(pdfUrl, '_blank');

    // Alternativamente, para baixar o PDF, descomente a linha abaixo
    // doc.save(`prontuario_${this.medicalRecord.id}_${this.patient.name.replace(/\s+/g, '_')}.pdf`);
  }

  // Método auxiliar para adicionar cabeçalhos de seção
  private addSectionHeader(
    doc: jsPDF,
    title: string,
    margin: number,
    y: number
  ): void {
    doc.setDrawColor(41, 128, 185);
    doc.setFillColor(41, 128, 185);
    doc.rect(margin, y, 5, 5, 'F');

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(41, 128, 185);
    doc.text(title, margin + 10, y + 4);

    doc.setLineWidth(0.5);
    doc.line(
      margin + 10 + doc.getTextWidth(title) + 5,
      y + 2,
      doc.internal.pageSize.getWidth() - margin,
      y + 2
    );
    doc.setTextColor(0, 0, 0);
  }

  async loadExamFile(): Promise<void> {
    if (this.medicalRecord?.examFilePath) {
      this.isLoadingFile = true;
      try {
        this.examFileUrl = await this.medicalRecordService.getExamFileUrl(
          this.medicalRecord.examFilePath
        );
        this.safeExamFileUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
          this.examFileUrl
        );
      } catch (error) {
        console.error('Erro ao carregar arquivo:', error);
      } finally {
        this.isLoadingFile = false;
      }
    }
  }

  isFileType(extension: string): boolean {
    const fileExtension = this.medicalRecord?.examFilePath
      ? this.medicalRecord.examFilePath.split('.').pop()?.toLowerCase()
      : null;
    return fileExtension === extension;
  }

  getFileIcon(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'description'; // ícone do Material para PDF
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'image'; // ícone do Material para imagens
      case 'doc':
      case 'docx':
        return 'article'; // ícone do Material para documentos
      default:
        return 'attach_file'; // ícone padrão
    }
  }

  isPDF(): boolean {
    return (
      this.medicalRecord?.examFilePath?.toLowerCase().endsWith('.pdf') ?? false
    );
  }

  isImage(): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
    return imageExtensions.some((ext) =>
      this.medicalRecord?.examFilePath?.toLowerCase().endsWith(ext)
    );
  }

  openPreview(): void {
    if (this.examFileUrl) {
      this.isPreviewOpen = true;
      if (this.isPDF()) {
        this.sanitizedExamFileUrl =
          this.sanitizer.bypassSecurityTrustResourceUrl(this.examFileUrl);
      } else if (this.isImage()) {
        this.safeExamFileUrl = this.sanitizer.bypassSecurityTrustUrl(
          this.examFileUrl
        );
      }
    }
  }

  closePreview(): void {
    this.isPreviewOpen = false;
    this.sanitizedExamFileUrl = null;
    this.safeExamFileUrl = null;
  }

  // Método para formatar o endereço completo a partir dos campos individuais
  private formatAddress(patient: any): string {
    if (!patient) return '';

    const parts = [];

    if (patient.addressStreet) {
      let streetPart = patient.addressStreet;
      if (patient.addressNumber) {
        streetPart += `, ${patient.addressNumber}`;
      }
      parts.push(streetPart);
    }

    if (patient.addressNeighborhood) {
      parts.push(patient.addressNeighborhood);
    }

    let cityState = '';
    if (patient.addressCity) {
      cityState = patient.addressCity;
      if (patient.addressState) {
        cityState += ` - ${patient.addressState}`;
      }
      parts.push(cityState);
    } else if (patient.addressState) {
      parts.push(patient.addressState);
    }

    if (patient.addressZipCode) {
      parts.push(`CEP: ${patient.addressZipCode}`);
    }

    if (patient.addressComplement) {
      parts.push(`Complemento: ${patient.addressComplement}`);
    }

    return parts.join(', ');
  }
}
