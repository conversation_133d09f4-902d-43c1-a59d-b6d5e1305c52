import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { AiSuggestion, AiSuggestionStatus } from '../models/ai-suggestion.model';
import { environment } from '../../../environments/environment';

// Interface para mapear a resposta do backend
interface SuggestionResponse {
  id: string;
  patientId: number;
  patient: {
    id: number;
    name: string;
  };
  status: string;
  iaReasoning: string;
  humanComment: string | null;
  procedures: {
    id: string;
    procedureId: number;
    expectedDate: string | null;
    notes: string | null;
  }[];
  createdAt: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class AiSuggestionApiService {
  private apiUrl = `${environment.apiUrl}/suggestions`;

  constructor(
    private http: HttpClient
  ) { }

  // Mapear status do backend para o frontend
  private mapStatus(backendStatus: string): AiSuggestionStatus {
    switch (backendStatus) {
      case 'IN_ANALYSIS':
        return AiSuggestionStatus.IN_ANALYSIS;
      case 'PENDING_REVIEW':
        return AiSuggestionStatus.IN_APPROVAL;
      case 'REJECTED':
        return AiSuggestionStatus.REJECTED;
      case 'APPROVED':
        return AiSuggestionStatus.ACCEPTED;
      default:
        return AiSuggestionStatus.IN_ANALYSIS;
    }
  }

  // Mapear status do frontend para o backend
  private mapStatusToBackend(frontendStatus: AiSuggestionStatus): string {
    switch (frontendStatus) {
      case AiSuggestionStatus.IN_ANALYSIS:
        return 'IN_ANALYSIS';
      case AiSuggestionStatus.IN_APPROVAL:
        return 'PENDING_REVIEW';
      case AiSuggestionStatus.REJECTED:
        return 'REJECTED';
      case AiSuggestionStatus.ACCEPTED:
        return 'APPROVED';
      default:
        return 'IN_ANALYSIS';
    }
  }

  // Converter resposta do backend para o modelo do frontend
  private convertToAiSuggestion(response: SuggestionResponse): AiSuggestion {
    // Extrair o nome do tratamento dos procedimentos
    let treatmentName = 'Sugestão de Tratamento';

    if (response.procedures && response.procedures.length > 0) {
      // Se tiver apenas um procedimento
      if (response.procedures.length === 1) {
        treatmentName = `Procedimento ${response.procedures[0].procedureId}`;
      }
      // Se tiver múltiplos procedimentos
      else {
        treatmentName = `${response.procedures.length} Procedimentos Sugeridos`;
      }
    }

    // Determinar datas específicas com base no status
    let approvedAt: Date | undefined;
    let rejectedAt: Date | undefined;
    let acceptedAt: Date | undefined;

    switch (this.mapStatus(response.status)) {
      case AiSuggestionStatus.REJECTED:
        rejectedAt = new Date(response.updatedAt);
        break;
      case AiSuggestionStatus.ACCEPTED:
        approvedAt = new Date(); // Data aproximada
        acceptedAt = new Date(response.updatedAt);
        break;
    }

    return {
      id: response.id, // Mantendo o ID como string (UUID)
      patientId: response.patientId,
      patientName: response.patient?.name || 'Paciente',
      treatmentName: treatmentName,
      status: this.mapStatus(response.status),
      message: response.iaReasoning,
      createdAt: new Date(response.createdAt),
      updatedAt: new Date(response.updatedAt),
      rejectionReason: response.humanComment || undefined,
      contactStatus: response.humanComment || undefined,
      approvedAt,
      rejectedAt,
      acceptedAt
    };
  }

  // Obter todas as sugestões
  getAllSuggestions(): Observable<AiSuggestion[]> {
    return this.http.get<SuggestionResponse[]>(this.apiUrl)
      .pipe(
        map(responses => responses.map(response => this.convertToAiSuggestion(response)))
      );
  }

  // Obter sugestões por paciente
  getSuggestionsByPatient(patientId: number): Observable<AiSuggestion[]> {
    return this.http.get<SuggestionResponse[]>(`${this.apiUrl}/patient/${patientId}`)
      .pipe(
        map(responses => responses.map(response => this.convertToAiSuggestion(response)))
      );
  }

  // Obter uma sugestão pelo ID
  getSuggestionById(id: string): Observable<AiSuggestion> {
    return this.http.get<SuggestionResponse>(`${this.apiUrl}/${id}`)
      .pipe(
        map(response => this.convertToAiSuggestion(response))
      );
  }

  // Atualizar o status de uma sugestão
  updateSuggestionStatus(id: string, status: AiSuggestionStatus, details?: any): Observable<AiSuggestion> {
    const updateData: any = {
      status: this.mapStatusToBackend(status)
    };

    // Adicionar comentário humano se fornecido
    if (details) {
      if (status === AiSuggestionStatus.REJECTED && details.rejectionReason) {
        updateData.humanComment = details.rejectionReason;
      } else if (status === AiSuggestionStatus.ACCEPTED && details.contactStatus) {
        updateData.humanComment = details.contactStatus;
      }

      // Se houver procedimentos, adicioná-los diretamente (sem JSON.stringify)
      if (details.procedures) {
        updateData.procedures = details.procedures;
      }
    }

    return this.http.patch<SuggestionResponse>(`${this.apiUrl}/${id}`, updateData)
      .pipe(
        map(response => this.convertToAiSuggestion(response))
      );
  }

  // Criar uma nova sugestão
  createSuggestion(patientId: number, iaReasoning: string, procedures: any[]): Observable<AiSuggestion> {
    const createData = {
      patientId,
      iaReasoning,
      procedures: procedures
    };

    return this.http.post<SuggestionResponse>(this.apiUrl, createData)
      .pipe(
        map(response => this.convertToAiSuggestion(response))
      );
  }

  // Remover uma sugestão
  removeSuggestion(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
