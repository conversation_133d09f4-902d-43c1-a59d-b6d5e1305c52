import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AiSuggestion, AiSuggestionStatus } from '../models/ai-suggestion.model';
import { AiSuggestionApiService } from './ai-suggestion-api.service';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AiSuggestionService {
  // Dados mockados para simular o funcionamento do board (fallback caso a API falhe)
  private mockSuggestions: AiSuggestion[] = [
    {
      id: '1',
      patientId: 101,
      patientName: '<PERSON>',
      treatmentName: 'Tratamento de Canal',
      status: AiSuggestionStatus.IN_ANALYSIS,
      message: 'Com base no histórico de dor persistente e na radiografia, sugiro um tratamento de canal no dente 26 para evitar complicações futuras.',
      createdAt: new Date(2023, 10, 15),
      updatedAt: new Date(2023, 10, 15)
    },
    {
      id: '2',
      patientId: 102,
      patientName: '<PERSON>',
      treatmentName: '<PERSON><PERSON><PERSON>',
      status: AiSuggestionStatus.IN_APPROVAL,
      message: 'Após análise da tomografia, recomendo um implante dentário na região do dente 36, que apresenta perda óssea moderada mas suficiente para suportar o implante.',
      createdAt: new Date(2023, 10, 10),
      updatedAt: new Date(2023, 10, 12)
    },
    {
      id: '3',
      patientId: 103,
      patientName: 'Ana Oliveira',
      treatmentName: 'Clareamento Dental',
      status: AiSuggestionStatus.ACCEPTED,
      message: 'Baseado nas fotos recentes e no histórico de consumo de café, sugiro um tratamento de clareamento dental para melhorar a estética do sorriso.',
      createdAt: new Date(2023, 10, 5),
      updatedAt: new Date(2023, 10, 8),
      approvedAt: new Date(2023, 10, 8),
      acceptedAt: new Date(2023, 10, 8),
      contactStatus: 'Paciente contatado e aceitou o tratamento.'
    },
    {
      id: '4',
      patientId: 104,
      patientName: 'Carlos Santos',
      treatmentName: 'Aparelho Ortodôntico',
      status: AiSuggestionStatus.REJECTED,
      message: 'Considerando o desalinhamento dos dentes anteriores, sugiro a instalação de aparelho ortodôntico para correção estética e funcional.',
      createdAt: new Date(2023, 9, 25),
      updatedAt: new Date(2023, 9, 30),
      rejectedAt: new Date(2023, 9, 30),
      rejectionReason: 'Paciente já está em tratamento ortodôntico com outro profissional.'
    },
    {
      id: '5',
      patientId: 105,
      patientName: 'Fernanda Lima',
      treatmentName: 'Prótese Parcial',
      status: AiSuggestionStatus.ACCEPTED,
      message: 'Após análise da condição bucal, sugiro uma prótese parcial removível para substituir os dentes 34, 35 e 36, melhorando a função mastigatória.',
      createdAt: new Date(2023, 9, 20),
      updatedAt: new Date(2023, 10, 2),
      approvedAt: new Date(2023, 9, 22),
      acceptedAt: new Date(2023, 10, 2),
      contactStatus: 'Paciente contatado por telefone e aceitou a sugestão.'
    },
    {
      id: '6',
      patientId: 106,
      patientName: 'Roberto Alves',
      treatmentName: 'Restauração de Resina',
      status: AiSuggestionStatus.IN_ANALYSIS,
      message: 'Identificada cárie incipiente no dente 17, recomendo restauração com resina composta para evitar progressão.',
      createdAt: new Date(2023, 10, 16),
      updatedAt: new Date(2023, 10, 16)
    },
    {
      id: '7',
      patientId: 107,
      patientName: 'Luciana Costa',
      treatmentName: 'Extração de Siso',
      status: AiSuggestionStatus.IN_APPROVAL,
      message: 'Com base na radiografia panorâmica, recomendo a extração dos dentes 18 e 28 que estão impactados e podem causar problemas futuros.',
      createdAt: new Date(2023, 10, 8),
      updatedAt: new Date(2023, 10, 11)
    },
    {
      id: '8',
      patientId: 108,
      patientName: 'Marcelo Souza',
      treatmentName: 'Tratamento Periodontal',
      status: AiSuggestionStatus.IN_APPROVAL,
      message: 'Detectada gengivite generalizada com acúmulo de tártaro. Recomendo tratamento periodontal com raspagem e alisamento radicular.',
      createdAt: new Date(2023, 10, 3),
      updatedAt: new Date(2023, 10, 7)
    },
    {
      id: '9',
      patientId: 109,
      patientName: 'Patrícia Mendes',
      treatmentName: 'Facetas de Porcelana',
      status: AiSuggestionStatus.REJECTED,
      message: 'Para melhorar a estética do sorriso, sugiro a colocação de facetas de porcelana nos dentes 13 a 23, corrigindo forma e cor.',
      createdAt: new Date(2023, 9, 28),
      updatedAt: new Date(2023, 10, 1),
      rejectedAt: new Date(2023, 10, 1),
      rejectionReason: 'Sugestão prematura, paciente precisa tratar cáries antes de procedimentos estéticos.'
    },
    {
      id: '10',
      patientId: 110,
      patientName: 'Gabriel Martins',
      treatmentName: 'Coroa Dentária',
      status: AiSuggestionStatus.ACCEPTED,
      message: 'Após tratamento endodôntico no dente 46, recomendo a colocação de coroa dentária para proteger o dente e restaurar função.',
      createdAt: new Date(2023, 9, 15),
      updatedAt: new Date(2023, 9, 25),
      approvedAt: new Date(2023, 9, 18),
      acceptedAt: new Date(2023, 9, 25),
      contactStatus: 'Paciente informado durante consulta e concordou com o tratamento.'
    }
  ];

  constructor(private apiService: AiSuggestionApiService) { }

  // Método para obter todas as sugestões
  getAllSuggestions(): Observable<AiSuggestion[]> {
    return this.apiService.getAllSuggestions().pipe(
      catchError(error => {
        console.error('Erro ao obter sugestões da API:', error);
        return of(this.mockSuggestions);
      })
    );
  }

  // Método para obter uma sugestão pelo ID
  getSuggestionById(id: string): Observable<AiSuggestion | undefined> {
    return this.apiService.getSuggestionById(id).pipe(
      catchError(error => {
        console.error(`Erro ao obter sugestão ${id} da API:`, error);
        const suggestion = this.mockSuggestions.find(s => s.id === id);
        return of(suggestion);
      })
    );
  }

  // Método para obter sugestões por paciente
  getSuggestionsByPatient(patientId: number): Observable<AiSuggestion[]> {
    return this.apiService.getSuggestionsByPatient(patientId).pipe(
      catchError(error => {
        console.error(`Erro ao obter sugestões do paciente ${patientId} da API:`, error);
        const suggestions = this.mockSuggestions.filter(s => s.patientId === patientId);
        return of(suggestions);
      })
    );
  }

  // Método para atualizar o status de uma sugestão
  updateSuggestionStatus(id: string, status: AiSuggestionStatus, details?: any): Observable<AiSuggestion> {
    return this.apiService.updateSuggestionStatus(id, status, details).pipe(
      catchError(error => {
        console.error(`Erro ao atualizar status da sugestão ${id} na API:`, error);

        // Fallback para o comportamento mockado
        const index = this.mockSuggestions.findIndex(s => s.id === id);
        if (index !== -1) {
          const suggestion = { ...this.mockSuggestions[index] };
          suggestion.status = status;
          suggestion.updatedAt = new Date();

          if (status === AiSuggestionStatus.REJECTED && details?.rejectionReason) {
            suggestion.rejectedAt = new Date();
            suggestion.rejectionReason = details.rejectionReason;
          } else if (status === AiSuggestionStatus.IN_APPROVAL) {
            suggestion.approvedAt = undefined;
            suggestion.rejectedAt = undefined;
          } else if (status === AiSuggestionStatus.ACCEPTED && details?.contactStatus) {
            suggestion.acceptedAt = new Date();
            suggestion.contactStatus = details.contactStatus;
          }

          this.mockSuggestions[index] = suggestion;
          return of(suggestion);
        }

        return of(this.mockSuggestions[index]);
      })
    );
  }

  // Método para criar uma nova sugestão
  createSuggestion(patientId: number, iaReasoning: string, procedures: any[]): Observable<AiSuggestion> {
    return this.apiService.createSuggestion(patientId, iaReasoning, procedures).pipe(
      catchError(error => {
        console.error('Erro ao criar sugestão na API:', error);
        // Retornar um erro ou criar uma sugestão mock
        throw new Error('Não foi possível criar a sugestão. Por favor, tente novamente.');
      })
    );
  }

  // Método para remover uma sugestão
  removeSuggestion(id: string): Observable<void> {
    return this.apiService.removeSuggestion(id).pipe(
      catchError(error => {
        console.error(`Erro ao remover sugestão ${id} na API:`, error);
        throw new Error('Não foi possível remover a sugestão. Por favor, tente novamente.');
      })
    );
  }
}
