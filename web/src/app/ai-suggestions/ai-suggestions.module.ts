import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { AiSuggestionBoardComponent } from './components/ai-suggestion-board/ai-suggestion-board.component';
import { AiSuggestionService } from './services/ai-suggestion.service';

const routes: Routes = [
  {
    path: '',
    component: AiSuggestionBoardComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    AiSuggestionBoardComponent
  ],
  providers: [
    AiSuggestionService
  ]
})
export class AiSuggestionsModule { }
