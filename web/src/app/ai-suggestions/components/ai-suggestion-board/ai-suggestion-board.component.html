<div class="bg-white shadow rounded-lg p-6">
  <div class="relative">
    <!-- Loading overlay -->
    <div *ngIf="isLoading" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div class="flex items-center space-x-2">
        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-blue-600 font-medium">Carregando sugestões...</span>
      </div>
    </div>

    <!-- Cabe<PERSON>lho -->
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Board de Sugestões da IA</h1>
        <p class="text-gray-600">Gerencie as sugestões de tratamento geradas pela IA para seus pacientes.</p>
      </div>
    </div>

    <!-- Mensagem de erro -->
    <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
      <p>{{ error }}</p>
      <button
        (click)="loadSuggestions()"
        class="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
      >
        Tentar novamente
      </button>
    </div>

    <!-- Board Kanban -->
    <div class="board-container"
         (mousedown)="startDragging($event)"
         (mousemove)="onDrag($event)"
         (mouseup)="stopDragging()"
         (mouseleave)="stopDragging()"
         [class.active]="isDraggingBoard">
      <div *ngIf="!isLoading && !error" class="board non-draggable flex flex-nowrap gap-6 ">
        <!-- Colunas para cada status -->
        <div *ngFor="let status of statusOrder" class="bg-white rounded-lg border overflow-hidden max-w-[356px] w-full flex-shrink-0 flex flex-col self-start min-h-[400px]">
          <!-- Cabeçalho da coluna -->
          <div class="px-4 py-3 flex justify-between items-center" [ngClass]="statusColors[status]">
            <div class="flex items-center">
              <span class="text-white font-semibold">{{ statusLabels[status] }}</span>
              <span class="ml-2 bg-white text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold">
                {{ getSuggestionsByStatus(status).length }}
              </span>
            </div>
          </div>

          <!-- Conteúdo da coluna -->
          <div
            cdkDropList
            [id]="'list-' + status"
            [cdkDropListData]="getSuggestionsByStatus(status)"
            [cdkDropListConnectedTo]="getConnectedLists()"
            (cdkDropListDropped)="onDrop($event)"
            class="p-4 min-h-[100px] h-[calc(100vh-220px)] overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          >
            <!-- Mensagem quando não há sugestões -->
            <div *ngIf="getSuggestionsByStatus(status).length === 0" class="p-3 bg-gray-50 rounded-md border border-gray-200 text-gray-500 text-center text-sm">
              Nenhuma sugestão
            </div>

            <!-- Cards de sugestões -->
            <div
              *ngFor="let suggestion of getSuggestionsByStatus(status)"
              cdkDrag
              (cdkDragStarted)="isDragging = true"
              (click)="openSuggestionDetails(suggestion)"
              class="cursor-move"
            >
              <app-ai-suggestion-card [suggestion]="suggestion"></app-ai-suggestion-card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de detalhes da sugestão -->
<app-ai-suggestion-detail-modal
  [suggestion]="selectedSuggestion"
  [isOpen]="isModalOpen"
  (close)="closeModal()"
  (statusChange)="updateSuggestionStatus($event)"
></app-ai-suggestion-detail-modal>
