import { Component, OnInit } from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { AiSuggestion, AiSuggestionStatus, AiSuggestionStatusColors, AiSuggestionStatusLabels } from '../../models/ai-suggestion.model';
import { AiSuggestionService } from '../../services/ai-suggestion.service';
import { AiSuggestionCardComponent } from '../ai-suggestion-card/ai-suggestion-card.component';
import { AiSuggestionDetailModalComponent } from '../ai-suggestion-detail-modal/ai-suggestion-detail-modal.component';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-ai-suggestion-board',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    AiSuggestionCardComponent,
    AiSuggestionDetailModalComponent
  ],
  providers: [
    { provide: DOCUMENT, useFactory: () => document }
  ],
  templateUrl: './ai-suggestion-board.component.html',
  styleUrls: ['./ai-suggestion-board.component.scss']
})
export class AiSuggestionBoardComponent implements OnInit {
  // Listas de sugestões por status
  inAnalysisSuggestions: AiSuggestion[] = [];
  inApprovalSuggestions: AiSuggestion[] = [];
  rejectedSuggestions: AiSuggestion[] = [];
  acceptedSuggestions: AiSuggestion[] = [];

  // Estado da UI
  isLoading = true;
  error = '';
  isDragging = false;
  isDraggingBoard = false;
  isAgentRunning = false;

  // Propriedades para arrastar o board
  private startX = 0;
  private scrollLeft = 0;
  private boardContainer: HTMLElement | null = null;

  // Modal
  isModalOpen = false;
  selectedSuggestion: AiSuggestion | null = null;

  // Enums e constantes
  statusLabels = AiSuggestionStatusLabels;
  statusColors = AiSuggestionStatusColors;

  // Ordem dos status para exibição no board
  statusOrder: AiSuggestionStatus[] = [
    AiSuggestionStatus.IN_ANALYSIS,
    AiSuggestionStatus.IN_APPROVAL,
    AiSuggestionStatus.REJECTED,
    AiSuggestionStatus.ACCEPTED
  ];

  constructor(
    private aiSuggestionService: AiSuggestionService,
    private http: HttpClient
  ) { }

  ngOnInit(): void {
    this.loadSuggestions();
  }

  // Carregar todas as sugestões
  loadSuggestions(): void {
    this.isLoading = true;
    this.aiSuggestionService.getAllSuggestions().subscribe({
      next: (suggestions) => {
        this.resetSuggestionLists();
        this.processSuggestionsData(suggestions);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar sugestões:', error);
        this.error = 'Erro ao carregar sugestões. Por favor, tente novamente.';
        this.isLoading = false;
      }
    });
  }

  // Resetar as listas de sugestões
  resetSuggestionLists(): void {
    this.inAnalysisSuggestions = [];
    this.inApprovalSuggestions = [];
    this.rejectedSuggestions = [];
    this.acceptedSuggestions = [];
  }

  // Processar os dados das sugestões
  processSuggestionsData(suggestions: AiSuggestion[]): void {
    suggestions.forEach(suggestion => {
      switch (suggestion.status) {
        case AiSuggestionStatus.IN_ANALYSIS:
          this.inAnalysisSuggestions.push(suggestion);
          break;
        case AiSuggestionStatus.IN_APPROVAL:
          this.inApprovalSuggestions.push(suggestion);
          break;
        case AiSuggestionStatus.REJECTED:
          this.rejectedSuggestions.push(suggestion);
          break;
        case AiSuggestionStatus.ACCEPTED:
          this.acceptedSuggestions.push(suggestion);
          break;
      }
    });
  }

  // Obter sugestões por status
  getSuggestionsByStatus(status: AiSuggestionStatus): AiSuggestion[] {
    switch (status) {
      case AiSuggestionStatus.IN_ANALYSIS:
        return this.inAnalysisSuggestions;
      case AiSuggestionStatus.IN_APPROVAL:
        return this.inApprovalSuggestions;
      case AiSuggestionStatus.REJECTED:
        return this.rejectedSuggestions;
      case AiSuggestionStatus.ACCEPTED:
        return this.acceptedSuggestions;
      default:
        return [];
    }
  }

  // Obter listas conectadas para drag and drop
  getConnectedLists(): string[] {
    return this.statusOrder.map(status => `list-${status}`);
  }

  // Manipular o evento de drop
  onDrop(event: CdkDragDrop<AiSuggestion[]>): void {
    if (event.previousContainer === event.container) {
      // Reordenar na mesma lista
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      // Mover para outra lista
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // Obter o novo status com base no ID do container
      const newStatus = event.container.id.replace('list-', '') as AiSuggestionStatus;
      const suggestion = event.container.data[event.currentIndex];

      // Atualizar o status da sugestão
      this.aiSuggestionService.updateSuggestionStatus(suggestion.id, newStatus).subscribe({
        next: (updatedSuggestion) => {
          // Atualizar o item na lista
          const index = event.container.data.findIndex(s => s.id === updatedSuggestion.id);
          if (index !== -1) {
            event.container.data[index] = updatedSuggestion;
          }
        },
        error: (error) => {
          console.error('Erro ao atualizar status da sugestão:', error);
          // Recarregar as sugestões em caso de erro
          this.loadSuggestions();
        }
      });
    }
  }

  // Abrir o modal de detalhes
  openSuggestionDetails(suggestion: AiSuggestion): void {
    this.selectedSuggestion = suggestion;
    this.isModalOpen = true;
  }

  // Fechar o modal
  closeModal(): void {
    this.isModalOpen = false;
    this.selectedSuggestion = null;
  }

  // Atualizar o status de uma sugestão
  updateSuggestionStatus(event: {id: string, status: AiSuggestionStatus, details?: any}): void {
    this.aiSuggestionService.updateSuggestionStatus(event.id, event.status, event.details).subscribe({
      next: () => {
        this.closeModal();
        this.loadSuggestions();
      },
      error: (error) => {
        console.error('Erro ao atualizar status da sugestão:', error);
      }
    });
  }

  // Executar o agente odontológico
  runDentalAgent(): void {
    if (this.isAgentRunning) {
      return;
    }

    this.isAgentRunning = true;
    this.http.post(`${environment.apiUrl}/agents/run-dental-agent`, {})
      .pipe(
        finalize(() => {
          // Aguardar 5 segundos antes de recarregar as sugestões
          setTimeout(() => {
            this.loadSuggestions();
            this.isAgentRunning = false;
          }, 5000);
        })
      )
      .subscribe({
        next: (response) => {
          console.log('Agente odontológico acionado com sucesso:', response);
        },
        error: (error) => {
          console.error('Erro ao acionar o agente odontológico:', error);
          this.error = 'Erro ao acionar o agente odontológico. Por favor, tente novamente.';
        }
      });
  }

  // Métodos para arrastar o board com o mouse
  startDragging(event: MouseEvent): void {
    // Verificar se o clique foi em um elemento que não deve iniciar o arraste
    const target = event.target as HTMLElement;
    if (target.closest('[cdkDrag]') || target.closest('button') || target.closest('a') || target.closest('input') || target.closest('select')) {
      return;
    }

    this.isDraggingBoard = true;
    this.boardContainer = event.currentTarget as HTMLElement;
    this.startX = event.pageX - this.boardContainer.offsetLeft;
    this.scrollLeft = this.boardContainer.scrollLeft;
  }

  onDrag(event: MouseEvent): void {
    if (!this.isDraggingBoard || !this.boardContainer) return;

    event.preventDefault();
    const x = event.pageX - this.boardContainer.offsetLeft;
    const walk = (x - this.startX) * 2; // Multiplicador para ajustar a velocidade do arraste
    this.boardContainer.scrollLeft = this.scrollLeft - walk;
  }

  stopDragging(): void {
    this.isDraggingBoard = false;
  }
}
