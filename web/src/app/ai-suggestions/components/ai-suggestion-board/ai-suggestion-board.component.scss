/* Estilos para o board de sugestões da IA */
.board-container {
  overflow-x: auto;
  padding-bottom: 1rem;
  cursor: grab;

  &.active {
    cursor: grabbing;
  }

  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }
}

.board {
  min-height: calc(100vh - 200px);
  padding: 0.5rem 0;
}

.non-draggable {
  user-select: none;
}

/* Estilos para drag and drop */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.375rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
