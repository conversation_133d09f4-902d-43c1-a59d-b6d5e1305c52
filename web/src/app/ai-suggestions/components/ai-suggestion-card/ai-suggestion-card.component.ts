import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AiSuggestion, AiSuggestionCardBorders, AiSuggestionStatus } from '../../models/ai-suggestion.model';

@Component({
  selector: 'app-ai-suggestion-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion-card.component.html',
  styleUrls: ['./ai-suggestion-card.component.scss']
})
export class AiSuggestionCardComponent {
  @Input() suggestion!: AiSuggestion;
  
  // Mapeamento de status para classes de borda
  cardBorders = AiSuggestionCardBorders;
  
  // Formatar data para exibição
  formatDate(date: Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  }
}
