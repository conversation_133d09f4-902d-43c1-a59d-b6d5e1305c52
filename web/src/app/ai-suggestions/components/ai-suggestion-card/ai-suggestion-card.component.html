<div
  class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 cursor-pointer hover:shadow-md transition-shadow"
  [ngClass]="cardBorders[suggestion.status]"
>
  <!-- Cabeçalho do card com nome do paciente -->
  <div class="flex justify-between items-start mb-2">
    <h3 class="font-medium text-gray-900 truncate">{{ suggestion.patientName }}</h3>
  </div>

  <!-- Status da sugestão -->
  <div class="flex items-center text-xs text-gray-600 mb-1">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span
      [ngClass]="{
        'text-purple-600': suggestion.status === 'in-analysis',
        'text-yellow-600': suggestion.status === 'in-approval',
        'text-red-600': suggestion.status === 'rejected',
        'text-green-600': suggestion.status === 'accepted'
      }"
    >
      {{ suggestion.status === 'in-analysis' ? 'Em Análise' :
         suggestion.status === 'in-approval' ? 'Em Aprovação/Reprovação' :
         suggestion.status === 'rejected' ? 'Recusado' : 'Aceitou Sugestão' }}
    </span>
  </div>

  <!-- Data de criação -->
  <div class="flex items-center text-xs text-gray-500">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
    <span>{{ formatDate(suggestion.createdAt) }}</span>
  </div>
</div>
