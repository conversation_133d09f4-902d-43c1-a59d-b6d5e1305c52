import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AiSuggestion, AiSuggestionStatus } from '../../models/ai-suggestion.model';
import { ModalComponent } from '../../../shared/components/modal/modal.component';

@Component({
  selector: 'app-ai-suggestion-detail-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, ModalComponent],
  templateUrl: './ai-suggestion-detail-modal.component.html',
  styleUrls: ['./ai-suggestion-detail-modal.component.scss']
})
export class AiSuggestionDetailModalComponent {
  @Input() suggestion: AiSuggestion | null = null;
  @Input() isOpen = false;
  @Output() close = new EventEmitter<void>();
  @Output() statusChange = new EventEmitter<{id: string, status: AiSuggestionStatus, details?: any}>();

  rejectionReason = '';
  contactStatus = '';

  // Expor o enum para o template
  AiSuggestionStatus = AiSuggestionStatus;

  // Fechar o modal
  closeModal(): void {
    this.close.emit();
    this.resetForm();
  }

  // Resetar o formulário
  resetForm(): void {
    this.rejectionReason = '';
    this.contactStatus = '';
  }

  // Aprovar a sugestão
  approveSuggestion(): void {
    if (this.suggestion) {
      this.statusChange.emit({
        id: this.suggestion.id,
        status: AiSuggestionStatus.ACCEPTED
      });
    }
  }

  // Rejeitar a sugestão
  rejectSuggestion(): void {
    if (this.suggestion && this.rejectionReason.trim()) {
      this.statusChange.emit({
        id: this.suggestion.id,
        status: AiSuggestionStatus.REJECTED,
        details: { rejectionReason: this.rejectionReason }
      });
    }
  }

  // Marcar como aceito pelo paciente
  markAsAccepted(): void {
    if (this.suggestion && this.contactStatus.trim()) {
      this.statusChange.emit({
        id: this.suggestion.id,
        status: AiSuggestionStatus.ACCEPTED,
        details: { contactStatus: this.contactStatus }
      });
    }
  }

  // Formatar data para exibição
  formatDate(date: Date | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('pt-BR');
  }
}
