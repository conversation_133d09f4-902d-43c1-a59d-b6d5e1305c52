<app-modal
  [title]="'Detalhes da Sugestão da IA'"
  [isOpen]="isOpen"
  (close)="closeModal()"
  [showDefaultFooter]="false"
>
  <div *ngIf="suggestion" class="p-6 max-h-[70vh] max-w-[800px] overflow-y-auto">
    <!-- Cabeçalho com informações principais -->
    <div class="mb-6 pb-4 border-b border-gray-200">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center">
        <div class="flex items-center gap-2">
          <h2 class="text-xl font-bold">{{ suggestion.patientName }}</h2>
          <a
            [href]="'/patients/' + suggestion.patientId"
            target="_blank"
            class="text-blue-600 hover:text-blue-800 transition-colors"
            title="Ver detalhes do paciente"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
            </svg>
          </a>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full"
            [ngClass]="{
              'bg-purple-100 text-purple-800': suggestion.status === AiSuggestionStatus.IN_ANALYSIS,
              'bg-yellow-100 text-yellow-800': suggestion.status === AiSuggestionStatus.IN_APPROVAL,
              'bg-red-100 text-red-800': suggestion.status === AiSuggestionStatus.REJECTED,
              'bg-green-100 text-green-800': suggestion.status === AiSuggestionStatus.ACCEPTED
            }">
            {{ suggestion.status === AiSuggestionStatus.IN_ANALYSIS ? 'Em Análise' :
               suggestion.status === AiSuggestionStatus.IN_APPROVAL ? 'Em Aprovação/Reprovação' :
               suggestion.status === AiSuggestionStatus.REJECTED ? 'Recusado' : 'Aceitou Sugestão' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Coluna da esquerda -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-3">Mensagem da IA</h3>
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
          <p class="text-gray-700">{{ suggestion.message }}</p>
        </div>

        <div class="space-y-3">
          <div class="flex items-center">
            <span class="text-gray-600 w-32">Criado em:</span>
            <span class="text-gray-900">{{ formatDate(suggestion.createdAt) }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-gray-600 w-32">Atualizado em:</span>
            <span class="text-gray-900">{{ formatDate(suggestion.updatedAt) }}</span>
          </div>
          <div *ngIf="suggestion.approvedAt" class="flex items-center">
            <span class="text-gray-600 w-32">Aprovado em:</span>
            <span class="text-gray-900">{{ formatDate(suggestion.approvedAt) }}</span>
          </div>
          <div *ngIf="suggestion.rejectedAt" class="flex items-center">
            <span class="text-gray-600 w-32">Rejeitado em:</span>
            <span class="text-gray-900">{{ formatDate(suggestion.rejectedAt) }}</span>
          </div>
          <div *ngIf="suggestion.acceptedAt" class="flex items-center">
            <span class="text-gray-600 w-32">Aceito em:</span>
            <span class="text-gray-900">{{ formatDate(suggestion.acceptedAt) }}</span>
          </div>
        </div>
      </div>

      <!-- Coluna da direita -->
      <div>
        <!-- Motivo da recusa (se aplicável) -->
        <div *ngIf="suggestion.rejectionReason" class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Motivo da Recusa</h3>
          <div class="bg-red-50 p-4 rounded-lg border border-red-200">
            <p class="text-gray-700">{{ suggestion.rejectionReason }}</p>
          </div>
        </div>

        <!-- Status da conversa (se aplicável) -->
        <div *ngIf="suggestion.contactStatus" class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Status do Contato</h3>
          <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <p class="text-gray-700">{{ suggestion.contactStatus }}</p>
          </div>
        </div>

        <!-- Formulários de ação baseados no status atual -->
        <!-- Para sugestões em análise -->
        <div *ngIf="suggestion.status === AiSuggestionStatus.IN_ANALYSIS" class="mt-4">
          <h3 class="text-lg font-medium text-gray-900 mb-3">Ações</h3>
          <button
            class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-md mb-3"
            (click)="statusChange.emit({id: suggestion.id, status: AiSuggestionStatus.IN_APPROVAL})"
          >
            Enviar para Aprovação
          </button>
        </div>

        <!-- Para sugestões em aprovação -->
        <div *ngIf="suggestion.status === AiSuggestionStatus.IN_APPROVAL" class="mt-4">
          <h3 class="text-lg font-medium text-gray-900 mb-3">Ações</h3>
          <button
            class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md mb-3"
            (click)="approveSuggestion()"
          >
            Aprovar Sugestão
          </button>

          <div class="mt-4">
            <label class="block text-gray-700 text-sm font-medium mb-2">
              Motivo da Recusa
            </label>
            <textarea
              [(ngModel)]="rejectionReason"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder="Informe o motivo da recusa..."
            ></textarea>
            <button
              [disabled]="!rejectionReason.trim()"
              [ngClass]="{'opacity-50 cursor-not-allowed': !rejectionReason.trim()}"
              class="w-full mt-2 bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md"
              (click)="rejectSuggestion()"
            >
              Recusar Sugestão
            </button>
          </div>
        </div>


      </div>
    </div>
  </div>

  <!-- Footer com botões de ação -->
  <div footer class="flex justify-end space-x-3">
    <button
      class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
      (click)="closeModal()"
    >
      Fechar
    </button>
  </div>
</app-modal>
