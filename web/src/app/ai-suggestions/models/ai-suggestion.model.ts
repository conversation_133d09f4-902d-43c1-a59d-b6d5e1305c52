// Modelo para as sugestões da IA
export enum AiSuggestionStatus {
  IN_ANALYSIS = 'in-analysis',
  IN_APPROVAL = 'in-approval',
  REJECTED = 'rejected',
  ACCEPTED = 'accepted'
}

// Labels para exibição dos status
export const AiSuggestionStatusLabels: Record<AiSuggestionStatus, string> = {
  [AiSuggestionStatus.IN_ANALYSIS]: 'Em Análise',
  [AiSuggestionStatus.IN_APPROVAL]: 'Em Aprovação/Reprovação',
  [AiSuggestionStatus.REJECTED]: 'Recusados',
  [AiSuggestionStatus.ACCEPTED]: 'Aceitou Sugestão'
};

// Cores para os cabeçalhos das colunas
export const AiSuggestionStatusColors: Record<AiSuggestionStatus, string> = {
  [AiSuggestionStatus.IN_ANALYSIS]: 'bg-purple-600',
  [AiSuggestionStatus.IN_APPROVAL]: 'bg-yellow-600',
  [AiSuggestionStatus.REJECTED]: 'bg-red-600',
  [AiSuggestionStatus.ACCEPTED]: 'bg-green-600'
};

// Cores para as bordas dos cartões
export const AiSuggestionCardBorders: Record<AiSuggestionStatus, string> = {
  [AiSuggestionStatus.IN_ANALYSIS]: 'border-purple-500',
  [AiSuggestionStatus.IN_APPROVAL]: 'border-yellow-500',
  [AiSuggestionStatus.REJECTED]: 'border-red-500',
  [AiSuggestionStatus.ACCEPTED]: 'border-green-500'
};

// Interface para o modelo de sugestão da IA
export interface AiSuggestion {
  id: string;
  patientId: number;
  patientName: string;
  treatmentName: string;
  status: AiSuggestionStatus;
  message: string;
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
  rejectionReason?: string;
  contactStatus?: string;
  acceptedAt?: Date;
}
