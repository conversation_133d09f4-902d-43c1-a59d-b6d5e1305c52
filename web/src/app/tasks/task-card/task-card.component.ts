import { Component, Input } from '@angular/core';
import { parseISO, format, isValid } from 'date-fns';
import { CommonModule } from '@angular/common';
import {
  Task,
  TaskPriority,
  taskPriorityColors,
  taskPriorityLabels,
} from '../../core/models/task.model';

@Component({
  selector: 'app-task-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './task-card.component.html',
  styleUrls: ['./task-card.component.scss'],
})
export class TaskCardComponent {
  @Input() task!: Task;

  taskPriorityLabels = taskPriorityLabels;
  taskPriorityColors = taskPriorityColors;

  get isHighPriority(): boolean {
    return (
      this.task.priority === TaskPriority.HIGH ||
      this.task.priority === TaskPriority.URGENT
    );
  }

  get isOverdue(): boolean {
    if (!this.task.dueDate) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let dueDate: Date;

    if (typeof this.task.dueDate === 'string') {
      // Se for uma string ISO, usar parseISO
      try {
        dueDate = parseISO(this.task.dueDate);
        if (!isValid(dueDate)) {
          // Se não for uma data válida, tentar criar uma nova data
          dueDate = new Date(this.task.dueDate);
        }
      } catch (e) {
        // Fallback para o construtor Date padrão
        dueDate = new Date(this.task.dueDate);
      }
    } else {
      // Se já for um objeto Date
      dueDate = this.task.dueDate;
    }

    // Resetar as horas para comparar apenas as datas
    dueDate.setHours(0, 0, 0, 0);

    return dueDate < today && !this.task.completedAt;
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return '';

    try {
      if (typeof date === 'string') {
        // Tentar usar parseISO para datas em formato ISO
        const parsedDate = parseISO(date);
        if (isValid(parsedDate)) {
          return format(parsedDate, 'dd/MM/yyyy');
        }
      }

      // Se não for uma string ISO válida ou for um objeto Date
      const dateObj = new Date(date);
      if (isValid(dateObj)) {
        return format(dateObj, 'dd/MM/yyyy');
      }
    } catch (e) {
      console.error('Erro ao formatar data:', e);
    }

    // Fallback para o método anterior
    return new Date(date).toLocaleDateString('pt-BR');
  }
}
