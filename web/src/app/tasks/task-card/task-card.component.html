<div
  class="bg-white rounded-md p-3 shadow border-t-4 hover:shadow-md transition-shadow"
  [ngClass]="{
    'border-red-500': task.priority === 'URGENT',
    'border-orange-500': task.priority === 'HIGH',
    'border-blue-500': task.priority === 'MEDIUM',
    'border-green-500': task.priority === 'LOW'
  }"
>
  <!-- Cabeçalho com título e prioridade -->
  <div class="flex justify-between items-start mb-2">
    <h3 class="font-medium text-gray-900 break-words text-sm">
      {{ task.title }}
    </h3>
  </div>

  <!-- Data limite com alerta visual se estiver atrasada -->
  <div
    *ngIf="task.dueDate"
    class="flex items-center text-xs mb-2"
    [ngClass]="{
      'text-red-600 font-medium': isOverdue,
      'text-gray-500': !isOverdue
    }"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-4 w-4 mr-1"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
    </svg>
    <span>{{ formatDate(task.dueDate) }}</span>
  </div>

  <!-- Funcionário (se houver) -->
  <div
    *ngIf="task.employee"
    class="flex items-center text-xs text-gray-500 mb-2"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-4 w-4 mr-1"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
      />
    </svg>
    <span>{{ task.employee.name }}</span>
  </div>

  <div class="flex flex-wrap gap-1 mt-2">
    <span
      class="text-xs rounded-full px-3 py-1 whitespace-nowrap"
      [ngClass]="taskPriorityColors[task.priority]"
    >
      {{ taskPriorityLabels[task.priority] }}
    </span>
  </div>
</div>
