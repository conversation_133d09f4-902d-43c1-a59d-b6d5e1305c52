import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { parseISO, format } from 'date-fns';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormControl,
} from '@angular/forms';
import {
  Task,
  TaskPriority,
  TaskStatus,
  taskPriorityLabels,
  Sector,
  sectorLabels,
} from '../../core/models/task.model';
import { TaskService } from '../../core/services/task.service';
import { EmployeeService } from '../../core/services/employee.service';
import { Employee } from '../../core/models/employee.model';

@Component({
  selector: 'app-task-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './task-form.component.html',
})
export class TaskFormComponent implements OnInit, OnChanges {
  @Input() task: Task | null = null;
  @Output() saved = new EventEmitter<Task>();
  @Output() cancelled = new EventEmitter<void>();

  taskForm: FormGroup;
  isSubmitting = false;
  taskPriorities = Object.values(TaskPriority);
  taskPriorityLabels = taskPriorityLabels;
  sectors = Object.values(Sector);
  sectorLabels = sectorLabels;
  employees: Employee[] = [];
  isLoadingEmployees = false;
  tagInputControl = new FormControl('');
  isTaskEditable = true;
  taskEditableMessage = '';

  constructor(
    private fb: FormBuilder,
    private taskService: TaskService,
    private employeeService: EmployeeService
  ) {
    this.taskForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      priority: [TaskPriority.MEDIUM, [Validators.required]],
      dueDate: ['', [Validators.required]],
      employeeId: ['', [Validators.required]],
      sector: [, [Validators.required]],
      tags: [[]],
    });
  }

  ngOnInit(): void {
    // Carregar os funcionários da API
    this.isLoadingEmployees = true;
    this.employeeService.getAllEmployees().subscribe({
      next: (employees) => {
        this.employees = employees.filter((emp) => emp.isActive);
        this.isLoadingEmployees = false;
      },
      error: (error) => {
        console.error('Erro ao carregar funcionários:', error);
        this.isLoadingEmployees = false;
      },
    });

    this.loadTaskData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Quando a propriedade task muda, atualizar o formulário
    if (changes['task']) {
      // Se task mudou para null, significa que o modal foi fechado
      if (changes['task'].currentValue === null) {
        // Resetar o formulário completamente, incluindo o estado de validação
        this.resetForm();
        // Resetar o estado de todos os campos (não tocados, não sujos)
        this.taskForm.markAsUntouched();
        this.taskForm.markAsPristine();
      } else {
        this.loadTaskData();
      }
    }
  }

  private loadTaskData(): void {
    // Resetar o formulário primeiro
    this.resetForm();

    // Se tiver uma tarefa, preencher o formulário com seus dados
    if (this.task) {
      // Verificar se a tarefa pode ser editada
      this.checkTaskEditability();

      // Verificar o formato da data
      let formattedDate = '';
      if (this.task.dueDate) {
        // Pode ser um objeto Date ou uma string no formato ISO
        formattedDate = this.formatDateForInput(this.task.dueDate);
        console.log('Data original:', this.task.dueDate);
        console.log('Data formatada para o formulário:', formattedDate);
      }

      this.taskForm.patchValue({
        title: this.task.title,
        description: this.task.description || '',
        priority: this.task.priority,
        dueDate: formattedDate,
        employeeId: this.task.employee?.id || '',
        sector: this.task.sector,
        tags: this.task.tags || [],
      });

      // Se a tarefa não for editável, desabilitar o formulário
      if (!this.isTaskEditable) {
        this.taskForm.disable();
        this.tagInputControl.disable();
      }
    }
  }

  private checkTaskEditability(): void {
    if (!this.task) {
      this.isTaskEditable = true;
      this.taskEditableMessage = '';
      return;
    }

    // Verificar se a tarefa está concluída ou cancelada
    if (
      this.task.status === TaskStatus.DONE ||
      this.task.status === TaskStatus.CANCELLED
    ) {
      this.isTaskEditable = false;
      this.taskEditableMessage =
        'Não é possível editar tarefas que estão concluídas ou canceladas.';
    } else {
      this.isTaskEditable = true;
      this.taskEditableMessage = '';
    }
  }

  // Método público para resetar o formulário completamente
  public resetFormCompletely(): void {
    this.taskForm.reset({
      title: '',
      description: '',
      priority: TaskPriority.MEDIUM,
      dueDate: '',
      employeeId: '',
      sector: '',
      tags: [],
    });
    this.tagInputControl.setValue('');

    // Resetar o estado de validação
    this.taskForm.markAsUntouched();
    this.taskForm.markAsPristine();

    // Resetar cada controle individualmente
    Object.keys(this.taskForm.controls).forEach((key) => {
      const control = this.taskForm.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
      control?.setErrors(null);
    });

    // Garantir que o formulário esteja habilitado para novas tarefas
    this.taskForm.enable();
    this.tagInputControl.enable();
    this.isTaskEditable = true;
    this.taskEditableMessage = '';
  }

  // Método privado para uso interno
  private resetForm(): void {
    this.resetFormCompletely();
  }

  onSubmit(): void {
    // Verificar se a tarefa pode ser editada
    if (!this.isTaskEditable) {
      return;
    }

    // Marcar todos os campos como tocados para mostrar erros de validação
    if (this.taskForm.invalid) {
      Object.keys(this.taskForm.controls).forEach((key) => {
        const control = this.taskForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    if (this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;
    const formData = this.taskForm.value;

    // Tratar o campo de data
    // Verificar se o campo dueDate está presente e não vazio (é obrigatório)
    if ('dueDate' in formData) {
      if (!formData.dueDate) {
        // Se o campo estiver vazio, marcar como inválido e retornar
        this.taskForm.get('dueDate')?.setErrors({ required: true });
        this.taskForm.get('dueDate')?.markAsTouched();
        this.isSubmitting = false;
        return;
      }

      // Garantir que a data esteja no formato correto
      const dateObj = new Date(formData.dueDate);
      // Verificar se a data é válida
      if (!isNaN(dateObj.getTime())) {
        // Formatar a data como YYYY-MM-DD (sem hora, sem fuso)
        const formattedDate = dateObj.toISOString().split('T')[0];
        console.log('Data original:', formData.dueDate);
        console.log('Data formatada (YYYY-MM-DD):', formattedDate);
        formData.dueDate = formattedDate;
      } else {
        console.error('Data inválida:', formData.dueDate);
        this.isSubmitting = false;
        return;
      }
    }

    if (this.task) {
      // Atualizar tarefa existente
      this.taskService.updateTask(this.task.id, formData).subscribe({
        next: (updatedTask) => {
          this.isSubmitting = false;
          this.saved.emit(updatedTask);
        },
        error: (error) => {
          console.error('Erro ao atualizar tarefa:', error);
          this.isSubmitting = false;
        },
      });
    } else {
      // Criar nova tarefa
      this.taskService
        .createTask({
          ...formData,
          status: TaskStatus.TODO, // Novas tarefas sempre começam como TODO
        })
        .subscribe({
          next: (newTask) => {
            this.isSubmitting = false;
            this.saved.emit(newTask);
          },
          error: (error) => {
            console.error('Erro ao criar tarefa:', error);
            this.isSubmitting = false;
          },
        });
    }
  }

  // Método público para submeter o formulário externamente
  submitForm(): void {
    this.onSubmit();
  }

  // Método público para verificar se todos os campos obrigatórios estão preenchidos
  areRequiredFieldsFilled(): boolean {
    const titleControl = this.taskForm.get('title');
    const employeeIdControl = this.taskForm.get('employeeId');
    const dueDateControl = this.taskForm.get('dueDate');

    return !!(
      titleControl?.value &&
      employeeIdControl?.value &&
      dueDateControl?.value
    );
  }

  onCancel(): void {
    this.cancelled.emit();
  }

  addTag(): void {
    if (!this.isTaskEditable || !this.tagInputControl.value?.trim()) {
      return;
    }

    const currentTags = this.taskForm.get('tags')?.value || [];
    if (!currentTags.includes(this.tagInputControl.value.trim())) {
      this.taskForm.patchValue({
        tags: [...currentTags, this.tagInputControl.value.trim()],
      });
    }

    this.tagInputControl.setValue('');
  }

  removeTag(tag: string): void {
    if (!this.isTaskEditable) {
      return;
    }

    const currentTags = this.taskForm.get('tags')?.value || [];
    this.taskForm.patchValue({
      tags: currentTags.filter((t: string) => t !== tag),
    });
  }

  clearDueDate(): void {
    if (!this.isTaskEditable) {
      return;
    }

    // Limpar o campo, mesmo sendo obrigatório
    // O botão de envio ficará desabilitado até que o usuário preencha o campo
    this.taskForm.patchValue({
      dueDate: '',
    });
    // Marca o campo como tocado para ativar a validação
    this.taskForm.get('dueDate')?.markAsTouched();
  }

  private formatDateForInput(date: Date | string): string {
    // Se a data já for uma string no formato YYYY-MM-DD, retornar diretamente
    if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return date;
    }

    // Usar date-fns para garantir o formato correto
    if (typeof date === 'string') {
      // Se for uma string ISO, usar parseISO
      try {
        return format(parseISO(date), 'yyyy-MM-dd');
      } catch (e) {
        console.error('Erro ao parsear data:', e);
      }
    }

    // Se for um objeto Date ou se o parseISO falhar
    const d = new Date(date);

    // Criar uma data UTC para evitar problemas de fuso horário
    const utcDate = new Date(
      Date.UTC(d.getFullYear(), d.getMonth(), d.getDate())
    );

    // Retornar no formato YYYY-MM-DD
    return format(utcDate, 'yyyy-MM-dd');
  }
}
