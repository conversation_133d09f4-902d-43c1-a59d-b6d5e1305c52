<div
  *ngIf="!isTaskEditable"
  class="mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700"
>
  <div class="flex">
    <div class="flex-shrink-0">
      <svg
        class="h-5 w-5 text-yellow-400"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"
        />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm">
        {{ taskEditableMessage }}
      </p>
    </div>
  </div>
</div>

<form [formGroup]="taskForm" (ngSubmit)="onSubmit()" class="px-2">
  <!-- Layout de duas colunas -->
  <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
    <!-- Coluna 1 (maior): Título e Descrição -->
    <div class="md:col-span-8 space-y-4">
      <!-- Título -->
      <div class="w-full">
        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
          Título <span class="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="title"
          formControlName="title"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-500':
              taskForm.get('title')?.invalid && taskForm.get('title')?.touched
          }"
        />
        <div
          *ngIf="
            taskForm.get('title')?.invalid && taskForm.get('title')?.touched
          "
          class="text-red-500 text-sm mt-1"
        >
          <div *ngIf="taskForm.get('title')?.errors?.['required']">
            Título é obrigatório
          </div>
          <div *ngIf="taskForm.get('title')?.errors?.['minlength']">
            Título deve ter pelo menos 3 caracteres
          </div>
        </div>
      </div>

      <!-- Descrição -->
      <div class="w-full">
        <label
          for="description"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Descrição
        </label>
        <textarea
          id="description"
          formControlName="description"
          rows="10"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none h-full"
        ></textarea>
      </div>
    </div>

    <!-- Coluna 2 (menor): Categoria, Prioridade, Data Limite e Tags -->
    <div class="md:col-span-4 space-y-4">
      <!-- Funcionário -->
      <div class="w-full">
        <label
          for="employeeId"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Funcionário <span class="text-red-500">*</span>
        </label>
        <select
          id="employeeId"
          formControlName="employeeId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-500':
              taskForm.get('employeeId')?.invalid &&
              taskForm.get('employeeId')?.touched
          }"
        >
          <option value="">Selecione um funcionário</option>
          <option
            *ngFor="let employee of employees"
            [value]="employee.id"
            [selected]="task?.employee?.id === employee.id"
          >
            {{ employee.name }}
          </option>
        </select>
        <div *ngIf="isLoadingEmployees" class="text-sm text-gray-500 mt-1">
          Carregando funcionários...
        </div>
        <div
          *ngIf="
            taskForm.get('employeeId')?.invalid &&
            taskForm.get('employeeId')?.touched
          "
          class="text-red-500 text-sm mt-1"
        >
          <div *ngIf="taskForm.get('employeeId')?.errors?.['required']">
            Funcionário é obrigatório
          </div>
        </div>
      </div>

      <!-- Prioridade -->
      <div class="w-full">
        <label
          for="priority"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Prioridade <span class="text-red-500">*</span>
        </label>
        <select
          id="priority"
          formControlName="priority"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option *ngFor="let priority of taskPriorities" [value]="priority">
            {{ taskPriorityLabels[priority] }}
          </option>
        </select>
      </div>

      <!-- Setor -->
      <div class="w-full">
        <label
          for="sector"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Setor responsável<span class="text-red-500">*</span>
        </label>
        <select
          id="sector"
          formControlName="sector"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-500':
              taskForm.get('sector')?.invalid && taskForm.get('sector')?.touched
          }"
        >
          <option value="">Selecione um setor</option>
          <option *ngFor="let sector of sectors" [value]="sector">
            {{ sectorLabels[sector] }}
          </option>
        </select>
        <div
          *ngIf="
            taskForm.get('sector')?.invalid && taskForm.get('sector')?.touched
          "
          class="text-red-500 text-sm mt-1"
        >
          <div *ngIf="taskForm.get('sector')?.errors?.['required']">
            Setor é obrigatório
          </div>
        </div>
      </div>

      <!-- Data Limite -->
      <div class="w-full">
        <label
          for="dueDate"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Data de conclusão <span class="text-red-500">*</span>
        </label>
        <div class="flex">
          <input
            type="date"
            id="dueDate"
            formControlName="dueDate"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500':
                taskForm.get('dueDate')?.invalid &&
                taskForm.get('dueDate')?.touched
            }"
          />
          <button
            type="button"
            (click)="clearDueDate()"
            class="px-3 py-2 bg-gray-200 text-gray-700 rounded-r-md hover:bg-gray-300 focus:outline-none flex items-center justify-center"
            title="Limpar data"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div
          *ngIf="
            taskForm.get('dueDate')?.invalid && taskForm.get('dueDate')?.touched
          "
          class="text-red-500 text-sm mt-1"
        >
          <div *ngIf="taskForm.get('dueDate')?.errors?.['required']">
            Data de conclusão é obrigatória
          </div>
        </div>
      </div>

      <!-- Tags -->
      <div class="w-full">
        <label class="block text-sm font-medium text-gray-700 mb-1">Tags</label>
        <div class="flex mb-2">
          <input
            type="text"
            [formControl]="tagInputControl"
            placeholder="Adicionar tag"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            (keyup.enter)="addTag()"
          />
          <button
            type="button"
            (click)="addTag()"
            class="pl-2 -ml-1 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
        <div
          class="flex flex-wrap gap-2 mb-3 min-h-[40px] p-2 bg-gray-50 rounded-md border border-gray-200"
        >
          <div
            *ngFor="let tag of taskForm.get('tags')?.value"
            class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center"
          >
            {{ tag }}
            <button
              type="button"
              (click)="removeTag(tag)"
              class="ml-1 text-blue-600 hover:text-blue-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
          <div
            *ngIf="(taskForm.get('tags')?.value?.length || 0) === 0"
            class="text-gray-400 text-sm italic"
          >
            Nenhuma tag adicionada
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
