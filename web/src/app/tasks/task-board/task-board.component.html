<div class="bg-white shadow rounded-lg p-6">
  <!-- Cabeçalho com título e botão de nova demanda -->
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Quadro de Demandas</h1>
    <button
      (click)="openNewTaskModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Nova Demanda
    </button>
  </div>

  <!-- Filtros -->
  <div class="bg-white rounded-lg mb-6 relative pb-3">
    <!-- Overlay de carregamento para filtros -->
    <div
      *ngIf="isFilterLoading"
      class="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10 rounded-lg"
    >
      <div class="flex items-center space-x-2">
        <svg
          class="animate-spin h-5 w-5 text-blue-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <span class="text-blue-600 font-medium">Filtrando...</span>
      </div>
    </div>

    <div class="flex flex-col md:flex-row gap-4">
      <!-- Filtro por título -->
      <div class="flex-1">
        <label for="title" class="block text-sm font-medium text-gray-700 mb-1"
          >Buscar demandas</label
        >
        <div class="relative">
          <div
            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <input
            type="text"
            id="search"
            [(ngModel)]="searchFilter"
            (ngModelChange)="onFilterChange()"
            placeholder="Buscar por título, funcionário ou tags..."
            class="pl-10 pr-4 py-1.5 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <!-- Filtro por prioridade -->
      <div class="md:w-64">
        <label
          for="priority"
          class="block text-sm font-medium text-gray-700 mb-1"
          >Prioridade</label
        >
        <select
          id="priority"
          [(ngModel)]="priorityFilter"
          (ngModelChange)="onFilterChange()"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Todas as prioridades</option>
          <option value="URGENT">Urgente</option>
          <option value="HIGH">Alta</option>
          <option value="MEDIUM">Média</option>
          <option value="LOW">Baixa</option>
        </select>
      </div>

      <!-- Filtro por setor -->
      <div class="md:w-64">
        <label for="sector" class="block text-sm font-medium text-gray-700 mb-1"
          >Setor</label
        >
        <select
          id="sector"
          [(ngModel)]="sectorFilter"
          (ngModelChange)="onFilterChange()"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Todos os setores</option>
          <option *ngFor="let sector of sectors" [value]="sector">
            {{ sectorLabels[sector] }}
          </option>
        </select>
      </div>

      <!-- Botão de limpar filtros -->
      <div class="md:flex items-end">
        <button
          (click)="clearFilters()"
          class="w-full md:w-auto px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
          Limpar Filtros
        </button>
      </div>
    </div>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Mensagem de erro -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
  </div>

  <!-- Kanban Board -->
  <div class="overflow-x-auto pb-4 relative">
    <!-- Overlay de carregamento para o quadro durante a filtragem -->
    <div
      *ngIf="isFilterLoading && !isLoading"
      class="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10 rounded-lg"
    >
      <div
        class="flex items-center space-x-2 bg-white p-3 rounded-lg border-md"
      >
        <svg
          class="animate-spin h-5 w-5 text-blue-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <span class="text-blue-600 font-medium">Atualizando quadro...</span>
      </div>
    </div>
    <div
      *ngIf="!isLoading && !error"
      class="flex flex-nowrap lg:flex-wrap gap-4 items-start min-w-max lg:min-w-0"
    >
      <!-- Coluna: A Fazer (TODO) -->
      <div
        class="bg-white rounded-lg border overflow-hidden w-[280px] flex-shrink-0 lg:w-[calc(20%-0.8rem)] lg:flex-shrink flex flex-col self-start min-h-[400px]"
      >
        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-gray-700 font-semibold">A Fazer</span>
            <span
              class="ml-2 bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold"
            >
              {{ todoTasks.length }}
            </span>
          </div>
          <button
            (click)="openNewTaskModal()"
            class="text-blue-600 hover:text-blue-800"
            title="Nova demanda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div
          cdkDropList
          id="todo-list"
          [cdkDropListData]="todoTasks"
          [cdkDropListConnectedTo]="[
            'in-progress-list',
            'waiting-list',
            'done-list',
            'cancelled-list'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 min-h-[100px] max-h-[500px] overflow-y-auto space-y-3 scrollbar-ultra-thin"
        >
          <div
            *ngFor="let task of todoTasks"
            cdkDrag
            class="cursor-move"
            (click)="openEditTaskModal(task)"
          >
            <app-task-card [task]="task"></app-task-card>
          </div>

          <!-- Botão de adicionar no final da lista -->
          <button
            *ngIf="todoTasks.length === 0"
            (click)="openNewTaskModal()"
            class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>

        <!-- Botão de adicionar no rodapé da coluna -->
        <div class="p-3 border-t border-gray-200">
          <button
            (click)="openNewTaskModal()"
            class="w-full py-2 text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>
      </div>

      <!-- Coluna: Em Andamento (IN_PROGRESS) -->
      <div
        class="bg-white rounded-lg border overflow-hidden w-[280px] flex-shrink-0 lg:w-[calc(20%-0.8rem)] lg:flex-shrink flex flex-col self-start min-h-[400px]"
      >
        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-gray-700 font-semibold">Em Andamento</span>
            <span
              class="ml-2 bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold"
            >
              {{ inProgressTasks.length }}
            </span>
          </div>
          <button
            (click)="openNewTaskModal()"
            class="text-blue-600 hover:text-blue-800"
            title="Nova demanda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div
          cdkDropList
          id="in-progress-list"
          [cdkDropListData]="inProgressTasks"
          [cdkDropListConnectedTo]="[
            'todo-list',
            'waiting-list',
            'done-list',
            'cancelled-list'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 min-h-[100px] max-h-[500px] overflow-y-auto space-y-3 scrollbar-ultra-thin"
        >
          <div
            *ngFor="let task of inProgressTasks"
            cdkDrag
            class="cursor-move"
            (click)="openEditTaskModal(task)"
          >
            <app-task-card [task]="task"></app-task-card>
          </div>

          <!-- Botão de adicionar no final da lista -->
          <button
            *ngIf="inProgressTasks.length === 0"
            (click)="openNewTaskModal()"
            class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>

        <!-- Botão de adicionar no rodapé da coluna -->
        <div class="p-3 border-t border-gray-200">
          <button
            (click)="openNewTaskModal()"
            class="w-full py-2 text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>
      </div>

      <!-- Coluna: Aguardando (WAITING) -->
      <div
        class="bg-white rounded-lg border overflow-hidden w-[280px] flex-shrink-0 lg:w-[calc(20%-0.8rem)] lg:flex-shrink flex flex-col self-start min-h-[400px]"
      >
        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-gray-700 font-semibold">Aguardando</span>
            <span
              class="ml-2 bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold"
            >
              {{ waitingTasks.length }}
            </span>
          </div>
          <button
            (click)="openNewTaskModal()"
            class="text-blue-600 hover:text-blue-800"
            title="Nova demanda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div
          cdkDropList
          id="waiting-list"
          [cdkDropListData]="waitingTasks"
          [cdkDropListConnectedTo]="[
            'todo-list',
            'in-progress-list',
            'done-list',
            'cancelled-list'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 min-h-[100px] max-h-[500px] overflow-y-auto space-y-3 scrollbar-ultra-thin"
        >
          <div
            *ngFor="let task of waitingTasks"
            cdkDrag
            class="cursor-move"
            (click)="openEditTaskModal(task)"
          >
            <app-task-card [task]="task"></app-task-card>
          </div>

          <!-- Botão de adicionar no final da lista -->
          <button
            *ngIf="waitingTasks.length === 0"
            (click)="openNewTaskModal()"
            class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>

        <!-- Botão de adicionar no rodapé da coluna -->
        <div class="p-3 border-t border-gray-200">
          <button
            (click)="openNewTaskModal()"
            class="w-full py-2 text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>
      </div>

      <!-- Coluna: Concluída (DONE) -->
      <div
        class="bg-white rounded-lg border overflow-hidden w-[280px] flex-shrink-0 lg:w-[calc(20%-0.8rem)] lg:flex-shrink flex flex-col self-start min-h-[400px]"
      >
        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-gray-700 font-semibold">Concluída</span>
            <span
              class="ml-2 bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold"
            >
              {{ doneTasks.length }}
            </span>
          </div>
          <button
            (click)="openNewTaskModal()"
            class="text-blue-600 hover:text-blue-800"
            title="Nova demanda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div
          cdkDropList
          id="done-list"
          [cdkDropListData]="doneTasks"
          [cdkDropListConnectedTo]="[
            'todo-list',
            'in-progress-list',
            'waiting-list',
            'cancelled-list'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 min-h-[100px] max-h-[500px] overflow-y-auto space-y-3 scrollbar-ultra-thin"
        >
          <div
            *ngFor="let task of doneTasks"
            cdkDrag
            class="cursor-move"
            (click)="openEditTaskModal(task)"
          >
            <app-task-card [task]="task"></app-task-card>
          </div>

          <!-- Botão de adicionar no final da lista -->
          <button
            *ngIf="doneTasks.length === 0"
            (click)="openNewTaskModal()"
            class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>

        <!-- Botão de adicionar no rodapé da coluna -->
        <div class="p-3 border-t border-gray-200">
          <button
            (click)="openNewTaskModal()"
            class="w-full py-2 text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>
      </div>

      <!-- Coluna: Cancelada (CANCELLED) -->
      <div
        class="bg-white rounded-lg border overflow-hidden w-[280px] flex-shrink-0 lg:w-[calc(20%-0.8rem)] lg:flex-shrink flex flex-col self-start min-h-[400px]"
      >
        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-gray-700 font-semibold">Cancelada</span>
            <span
              class="ml-2 bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold"
            >
              {{ cancelledTasks.length }}
            </span>
          </div>
          <button
            (click)="openNewTaskModal()"
            class="text-blue-600 hover:text-blue-800"
            title="Nova demanda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div
          cdkDropList
          id="cancelled-list"
          [cdkDropListData]="cancelledTasks"
          [cdkDropListConnectedTo]="[
            'todo-list',
            'in-progress-list',
            'waiting-list',
            'done-list'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 min-h-[100px] max-h-[500px] overflow-y-auto space-y-3 scrollbar-ultra-thin"
        >
          <div
            *ngFor="let task of cancelledTasks"
            cdkDrag
            class="cursor-move"
            (click)="openEditTaskModal(task)"
          >
            <app-task-card [task]="task"></app-task-card>
          </div>

          <!-- Botão de adicionar no final da lista -->
          <button
            *ngIf="cancelledTasks.length === 0"
            (click)="openNewTaskModal()"
            class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>

        <!-- Botão de adicionar no rodapé da coluna -->
        <div class="p-3 border-t border-gray-200">
          <button
            (click)="openNewTaskModal()"
            class="w-full py-2 text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Demanda
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para criar/editar tarefa -->
<app-modal [isOpen]="isModalOpen" [title]="modalTitle" (close)="closeModal()">
  <!-- Conteúdo do modal com scroll -->
  <div class="overflow-y-auto p-4 max-h-[calc(80vh-120px)] scrollbar-thin">
    <app-task-form
      [task]="selectedTask"
      (saved)="onTaskSaved($event)"
      (cancelled)="closeModal()"
    ></app-task-form>
  </div>

  <!-- Footer fixo -->
  <div footer class="flex justify-end space-x-3 bg-white">
    <button
      type="button"
      (click)="closeModal()"
      class="px-5 py-2.5 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none transition-colors"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="saveTask()"
      [disabled]="isSubmitting || !checkFormValidity()"
      class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none flex items-center transition-colors"
      [ngClass]="{
        'opacity-50 cursor-not-allowed': isSubmitting || !checkFormValidity()
      }"
    >
      <svg
        *ngIf="isSubmitting"
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {{ isSubmitting ? "Salvando..." : selectedTask ? "Atualizar" : "Criar" }}
    </button>
  </div>
</app-modal>
