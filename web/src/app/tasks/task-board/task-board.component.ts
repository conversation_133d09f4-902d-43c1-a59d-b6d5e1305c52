import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  CdkDragDrop,
  DragDropModule,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { TaskService } from '../../core/services/task.service';
import {
  Task,
  TaskPriority,
  TaskStatus,
  taskStatusLabels,
} from '../../core/models/task.model';
import { TaskCardComponent } from '../task-card/task-card.component';
import { TaskFormComponent } from '../task-form/task-form.component';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { NotificationService } from '../../core/services/notification.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { EmployeeService } from '../../core/services/employee.service';
import { Employee } from '../../core/models/employee.model';
import { Sector, sectorLabels } from '../../core/models/task.model';

@Component({
  selector: 'app-task-board',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    TaskCardComponent,
    TaskFormComponent,
    ModalComponent,
  ],
  providers: [
    { provide: DOCUMENT, useFactory: () => document }
  ],
  templateUrl: './task-board.component.html',
  styleUrls: ['./task-board.component.scss'],
})
export class TaskBoardComponent implements OnInit, OnDestroy {
  @ViewChild(TaskFormComponent) taskFormComponent!: TaskFormComponent;
  // Listas de tarefas por status
  todoTasks: Task[] = [];
  inProgressTasks: Task[] = [];
  waitingTasks: Task[] = [];
  doneTasks: Task[] = [];
  cancelledTasks: Task[] = [];

  // Filtros
  searchFilter: string = '';
  priorityFilter: TaskPriority | '' = '';
  sectorFilter: Sector | '' = '';
  private filterChange$ = new Subject<void>();
  private destroy$ = new Subject<void>();

  // Funcionários disponíveis
  employees: Employee[] = [];
  isLoadingEmployees = false;

  // Status do carregamento e erros
  isLoading = true;
  isFilterLoading = false;
  error = '';

  // Modal
  isModalOpen = false;
  modalTitle = '';
  selectedTask: Task | null = null;
  isSubmitting = false;

  // Enums e labels
  taskStatusLabels = taskStatusLabels;
  taskPriorities = Object.values(TaskPriority);
  sectors = Object.values(Sector);
  sectorLabels = sectorLabels;

  constructor(
    private taskService: TaskService,
    private employeeService: EmployeeService,
    private notificationService: NotificationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Carregar funcionários
    this.isLoadingEmployees = true;
    this.employeeService.getAllEmployees().subscribe({
      next: (employees) => {
        this.employees = employees.filter((emp) => emp.isActive);
        this.isLoadingEmployees = false;
      },
      error: (error) => {
        console.error('Erro ao carregar funcionários:', error);
        this.isLoadingEmployees = false;
      },
    });

    // Configurar o debounce para os filtros
    this.filterChange$
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateUrlWithFilters();
        this.loadTasks();
      });

    // Carregar filtros da URL
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        const search = params['search'];
        const priority = params['priority'];
        const sector = params['sector'];

        // Atualizar filtros apenas se forem diferentes dos atuais
        let filtersChanged = false;

        if (search !== undefined && this.searchFilter !== search) {
          this.searchFilter = search;
          filtersChanged = true;
        }

        if (priority !== undefined && this.priorityFilter !== priority) {
          this.priorityFilter = priority as TaskPriority | '';
          filtersChanged = true;
        }

        if (sector !== undefined && this.sectorFilter !== sector) {
          this.sectorFilter = sector as Sector | '';
          filtersChanged = true;
        }

        // Carregar tarefas apenas se os filtros mudaram
        if (filtersChanged) {
          this.loadTasks();
        } else if (!search && !priority && !sector) {
          // Caso inicial sem filtros
          this.loadTasks();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadTasks(): void {
    this.isLoading = true;
    this.taskService
      .getTasks(
        undefined,
        this.priorityFilter || undefined,
        undefined,
        this.searchFilter || undefined,
        this.searchFilter || undefined, // Usando o mesmo campo de busca para tags
        this.sectorFilter || undefined
      )
      .subscribe({
        next: (tasks) => {
          this.resetTaskLists();
          this.processTasksData(tasks);
          this.isLoading = false;
          this.isFilterLoading = false;
        },
        error: (error) => {
          console.error('Erro ao carregar tarefas:', error);
          this.error = 'Erro ao carregar tarefas. Por favor, tente novamente.';
          this.isLoading = false;
          this.isFilterLoading = false;
        },
      });
  }

  private resetTaskLists(): void {
    this.todoTasks = [];
    this.inProgressTasks = [];
    this.waitingTasks = [];
    this.doneTasks = [];
    this.cancelledTasks = [];
    // Não resetamos availableCategories pois queremos manter todas as categorias do enum
  }

  private processTasksData(tasks: Task[]): void {
    // Filtrar tarefas
    const filteredTasks = this.applyFilters(tasks);

    // Distribuir tarefas por status
    filteredTasks.forEach((task) => {
      // Adicionar à lista correta
      this.addTaskToCorrectList(task);
    });

    // Ordenar tarefas por prioridade e data
    this.sortAllTaskLists();
  }

  private applyFilters(tasks: Task[]): Task[] {
    return tasks.filter((task) => {
      // Filtro por busca (título, nome do funcionário ou tags)
      if (this.searchFilter) {
        const searchLower = this.searchFilter.toLowerCase();

        // Verificar correspondência no título
        const titleMatch = task.title.toLowerCase().includes(searchLower);

        // Verificar correspondência no nome do funcionário
        const employeeMatch = task.employee?.name
          ?.toLowerCase()
          .includes(searchLower);

        // Verificar correspondência nas tags
        let tagMatch = false;
        if (task.tags && task.tags.length > 0) {
          const taskTagsLower = task.tags.map((t) => t.toLowerCase());
          tagMatch = taskTagsLower.some((tag) => tag.includes(searchLower));
        }

        // Se não corresponder a nenhum dos critérios, filtrar
        if (!titleMatch && !employeeMatch && !tagMatch) {
          return false;
        }
      }

      // Filtro por prioridade
      if (this.priorityFilter && task.priority !== this.priorityFilter) {
        return false;
      }

      // Filtro por setor
      if (this.sectorFilter && task.sector !== this.sectorFilter) {
        return false;
      }

      return true;
    });
  }

  private addTaskToCorrectList(task: Task): void {
    switch (task.status) {
      case TaskStatus.TODO:
        this.todoTasks.push(task);
        break;
      case TaskStatus.IN_PROGRESS:
        this.inProgressTasks.push(task);
        break;
      case TaskStatus.WAITING:
        this.waitingTasks.push(task);
        break;
      case TaskStatus.DONE:
        this.doneTasks.push(task);
        break;
      case TaskStatus.CANCELLED:
        this.cancelledTasks.push(task);
        break;
    }
  }

  private sortAllTaskLists(): void {
    const sortByPriorityAndDate = (a: Task, b: Task) => {
      // Primeiro por prioridade (URGENT > HIGH > MEDIUM > LOW)
      const priorityOrder = { URGENT: 0, HIGH: 1, MEDIUM: 2, LOW: 3 };
      const priorityDiff =
        priorityOrder[a.priority] - priorityOrder[b.priority];

      if (priorityDiff !== 0) {
        return priorityDiff;
      }

      // Depois por data de vencimento (mais próxima primeiro)
      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }

      // Tarefas com data de vencimento vêm antes das sem data
      if (a.dueDate && !b.dueDate) return -1;
      if (!a.dueDate && b.dueDate) return 1;

      // Por último, ordenar por data de criação (mais recente primeiro)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    };

    this.todoTasks.sort(sortByPriorityAndDate);
    this.inProgressTasks.sort(sortByPriorityAndDate);
    this.waitingTasks.sort(sortByPriorityAndDate);
    this.doneTasks.sort(sortByPriorityAndDate);
    this.cancelledTasks.sort(sortByPriorityAndDate);
  }

  onDrop(event: CdkDragDrop<Task[]>): void {
    if (event.previousContainer === event.container) {
      // Reordenar na mesma lista
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      // Mover para outra lista
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // Atualizar o status da tarefa
      const task = event.container.data[event.currentIndex];
      const newStatus = this.getStatusFromContainerId(event.container.id);

      if (newStatus && task.status !== newStatus) {
        this.updateTaskStatus(task, newStatus);
      }
    }
  }

  private getStatusFromContainerId(containerId: string): TaskStatus | null {
    switch (containerId) {
      case 'todo-list':
        return TaskStatus.TODO;
      case 'in-progress-list':
        return TaskStatus.IN_PROGRESS;
      case 'waiting-list':
        return TaskStatus.WAITING;
      case 'done-list':
        return TaskStatus.DONE;
      case 'cancelled-list':
        return TaskStatus.CANCELLED;
      default:
        return null;
    }
  }

  private updateTaskStatus(task: Task, newStatus: TaskStatus): void {
    this.taskService
      .updateTask(task.id, {
        status: newStatus,
        title: task.title,
        dueDate: task.dueDate,
      })
      .subscribe({
        next: (updatedTask) => {
          // Atualizar a tarefa na lista com os dados atualizados
          Object.assign(task, updatedTask);
        },
        error: (error) => {
          console.error('Erro ao atualizar status da tarefa:', error);
          this.notificationService.error(
            'Erro ao atualizar status da tarefa. Por favor, tente novamente.'
          );
          // Recarregar as tarefas para garantir consistência
          this.loadTasks();
        },
      });
  }

  openNewTaskModal(): void {
    // Resetar o estado antes de abrir o modal
    this.selectedTask = null;
    this.modalTitle = 'Nova Demanda';
    this.isModalOpen = true;

    // Resetar o formulário após o modal ser aberto (usando setTimeout para garantir que o componente esteja disponível)
    setTimeout(() => {
      if (this.taskFormComponent) {
        this.taskFormComponent.resetFormCompletely();
      }
    }, 0);
  }

  openEditTaskModal(task: Task): void {
    this.selectedTask = task;
    this.modalTitle = 'Editar Demanda';
    this.isModalOpen = true;
  }

  closeModal(): void {
    // Resetar o formulário antes de fechar o modal
    if (this.taskFormComponent) {
      this.taskFormComponent.resetFormCompletely();
    }

    this.isModalOpen = false;

    // Limpar a tarefa selecionada e resetar o estado após fechar o modal
    setTimeout(() => {
      this.selectedTask = null;
      this.isSubmitting = false;
    }, 300); // Pequeno atraso para garantir que o modal seja fechado primeiro
  }

  onTaskSaved(task: Task): void {
    this.isSubmitting = false;

    // Resetar o formulário explicitamente antes de fechar o modal
    if (this.taskFormComponent) {
      this.taskFormComponent.resetFormCompletely();
    }

    this.closeModal();
    this.loadTasks(); // Recarregar todas as tarefas para garantir consistência
  }

  isFormValid = false;

  saveTask(): void {
    // Usar a referência direta ao componente do formulário
    if (this.taskFormComponent) {
      // Verificar se a tarefa pode ser editada
      if (
        this.selectedTask &&
        (this.selectedTask.status === TaskStatus.DONE ||
          this.selectedTask.status === TaskStatus.CANCELLED)
      ) {
        return; // Não permitir salvar tarefas concluídas ou canceladas
      }

      this.isSubmitting = true;

      // Chamar o método submitForm diretamente no componente
      this.taskFormComponent.submitForm();
    } else {
      // Fallback para o método anterior
      const taskFormElement = document.querySelector('app-task-form');

      if (taskFormElement) {
        this.isSubmitting = true;

        // Usar o método nativo do Angular para disparar o evento de submit
        const form = taskFormElement.querySelector('form');
        if (form) {
          const submitEvent = new Event('submit', {
            bubbles: true,
            cancelable: true,
          });
          form.dispatchEvent(submitEvent);
        } else {
          // Se não encontrar o formulário, resetar o estado de submissão
          console.error('Formulário não encontrado');
          this.isSubmitting = false;
        }
      } else {
        console.error('Componente do formulário não encontrado');
        this.isSubmitting = false;
      }
    }
  }

  // Método para verificar se estamos editando uma tarefa existente
  isEditingTask(): boolean {
    return this.selectedTask !== null;
  }

  // Método para verificar se o formulário é válido
  checkFormValidity(): boolean {
    // Usar a referência direta ao componente do formulário
    if (this.taskFormComponent) {
      // Se estamos editando uma tarefa existente, permitir o botão de atualizar
      if (this.isEditingTask()) {
        return this.taskFormComponent.taskForm.valid;
      }

      // Para nova tarefa, verificar se todos os campos obrigatórios estão preenchidos
      return (
        this.taskFormComponent.areRequiredFieldsFilled() &&
        this.taskFormComponent.taskForm.valid
      );
    }

    // Fallback para o método anterior
    const taskFormElement = document.querySelector('app-task-form');
    if (!taskFormElement) return false;

    // Tentar acessar o formulário
    const formElement = taskFormElement.querySelector('form');
    if (!formElement) return false;

    // Verificar se o formulário tem a classe ng-invalid
    return !formElement.classList.contains('ng-invalid');
  }

  // Método para verificar se o botão de salvar deve estar habilitado
  isSaveButtonEnabled(): boolean {
    // Se estamos editando uma tarefa concluída ou cancelada, desabilitar o botão
    if (
      this.selectedTask &&
      (this.selectedTask.status === TaskStatus.DONE ||
        this.selectedTask.status === TaskStatus.CANCELLED)
    ) {
      return false;
    }

    // Caso contrário, verificar a validade do formulário
    return this.checkFormValidity();
  }

  onFilterChange(): void {
    this.isFilterLoading = true;
    this.filterChange$.next();
  }

  clearFilters(): void {
    this.isFilterLoading = true;
    this.searchFilter = '';
    this.priorityFilter = '';
    this.sectorFilter = '';

    // Limpar completamente os query params da URL
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {},
      replaceUrl: true,
    });

    this.loadTasks();
  }

  private updateUrlWithFilters(): void {
    // Construir objeto de query params
    const queryParams: any = {};

    if (this.searchFilter) {
      queryParams.search = this.searchFilter;
    }

    if (this.priorityFilter) {
      queryParams.priority = this.priorityFilter;
    }

    if (this.sectorFilter) {
      queryParams.sector = this.sectorFilter;
    }

    // Verificar se estamos limpando todos os filtros
    const isClearing =
      !this.searchFilter && !this.priorityFilter && !this.sectorFilter;

    // Atualizar URL sem recarregar a página
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Se estamos limpando, não manter nenhum parâmetro existente
      queryParamsHandling: isClearing ? '' : 'merge',
      replaceUrl: true,
    });
  }
}
