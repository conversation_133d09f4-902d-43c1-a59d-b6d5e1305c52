<div class="bg-white shadow rounded-lg p-6">
  <div
    class="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6"
  >
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Dentistas</h1>
      <p class="text-gray-600"><PERSON>adastre e gerencie os dentistas da clínica</p>
    </div>

    <a
      routerLink="/dentists/new"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center md:justify-start"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      <PERSON><PERSON>
    </a>
  </div>

  <!-- Filtros -->
  <div class="pb-6">
    <!-- Campo de busca e filtro de status -->
    <div class="flex flex-col md:flex-row gap-4 mb-4">
      <!-- Campo de busca -->
      <div class="relative flex-1">
        <div
          class="absolute z-[2] inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="applySearch()"
          placeholder="Buscar dentistas por nome, CRO, especialidade ou email"
          class="pl-10 pr-4 py-1.5 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- Filtro de status -->
      <div class="flex items-center">
        <label class="inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            [checked]="showOnlyActive"
            (change)="toggleActiveFilter()"
            class="sr-only peer"
          />
          <div
            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
          ></div>
          <span class="ms-3 text-sm font-medium text-gray-700">
            Mostrar apenas ativos
          </span>
        </label>
      </div>
    </div>

    <!-- Filtro por especialidades -->
    <div class="flex flex-wrap gap-2" *ngIf="availableSpecialties.length > 0">
      <span class="text-sm font-medium text-gray-700 mr-2"
        >Filtrar por especialidade:</span
      >
      <button
        *ngFor="let specialty of availableSpecialties"
        (click)="toggleSpecialty(specialty)"
        class="px-3 py-1 text-sm rounded-full transition-colors"
        [ngClass]="
          isSpecialtySelected(specialty)
            ? 'bg-blue-100 text-blue-800'
            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        "
      >
        {{ specialty }}
      </button>
    </div>
  </div>

  <!-- Loading state (fora da tabela) -->
  <div
    *ngIf="isLoading && dentists.length === 0"
    class="bg-white rounded-lg shadow-md p-6 flex justify-center items-center"
  >
    <div class="flex flex-col items-center">
      <svg
        class="animate-spin h-10 w-10 text-blue-600 mb-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <p class="text-gray-600">Carregando dentistas...</p>
    </div>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6"
    role="alert"
  >
    <p>{{ error }}</p>
    <button
      (click)="loadDentists()"
      class="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
    >
      Tentar novamente
    </button>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!isLoading && !error && dentists.length === 0"
    class="bg-white rounded-lg shadow-md p-6 text-center"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-16 w-16 text-gray-400 mx-auto mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
      />
    </svg>
    <h2 class="text-xl font-semibold text-gray-700 mb-2">
      Nenhum dentista encontrado
    </h2>
    <p class="text-gray-500 mb-4">
      Comece adicionando um novo dentista ao sistema.
    </p>
    <a
      routerLink="/dentists/new"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors inline-flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Adicionar Dentista
    </a>
  </div>

  <!-- Dentists list -->
  <div
    *ngIf="!error && (dentists.length > 0 || isLoading)"
    class="bg-white rounded-lg shadow-sm overflow-hidden mb-6 relative"
  >
    <!-- Overlay de carregamento moderno -->
    <div
      *ngIf="isLoading"
      class="absolute inset-0 bg-white bg-opacity-70 backdrop-blur-[1px] z-10 flex justify-center items-center transition-all duration-300 ease-in-out"
    >
      <div
        class="bg-white/90 p-4 rounded-xl shadow-lg flex items-center space-x-3 border border-gray-100 animate-fadeIn"
      >
        <div class="relative">
          <div
            class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"
          ></div>
          <div
            class="absolute inset-0 rounded-full border-2 border-blue-100 animate-pulse"
          ></div>
        </div>
        <span class="text-gray-700 font-medium">Carregando dados...</span>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Nome
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              CRO
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Especialidade
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Contato
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Ações
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let dentist of dentists" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">
                {{ dentist.name }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500">{{ dentist.cro }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500">{{ dentist.specialty }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500">{{ dentist.phone }}</div>
              <div class="text-sm text-gray-500">{{ dentist.email }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                [ngClass]="{
                  'bg-green-100 text-green-800': dentist.active,
                  'bg-red-100 text-red-800': !dentist.active
                }"
              >
                {{ dentist.active ? "Ativo" : "Inativo" }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <a
                  [routerLink]="['/dentists', dentist.id]"
                  class="text-blue-600 hover:text-blue-900"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <a
                  [routerLink]="['/dentists/edit', dentist.id]"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
                    />
                  </svg>
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginação -->
    <div
      *ngIf="totalItems > 0"
      class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner"
    >
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <!-- Informações de paginação e seletor de itens por página -->
        <div class="flex flex-wrap items-center text-sm text-gray-600">
          <div
            class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md shadow-sm"
          >
            <span>Mostrando</span>
            <span class="font-medium mx-1 text-blue-600">{{
              (currentPage - 1) * itemsPerPage + 1
            }}</span>
            <span>-</span>
            <span class="font-medium mx-1 text-blue-600">{{
              currentPage * itemsPerPage > totalItems
                ? totalItems
                : currentPage * itemsPerPage
            }}</span>
            <span>de</span>
            <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
          </div>

          <!-- Seletor de itens por página -->
          <div class="ml-3 flex items-center">
            <label for="itemsPerPage" class="mr-2 text-sm text-gray-500"
              >Itens por página:</label
            >
            <div class="relative">
              <select
                id="itemsPerPage"
                [(ngModel)]="itemsPerPage"
                (change)="changeItemsPerPage()"
                class="appearance-none bg-white border border-gray-200 rounded-md pl-3 pr-8 py-1.5 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option [value]="6">6</option>
                <option [value]="10">10</option>
                <option [value]="20">20</option>
                <option [value]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Controles de paginação -->
        <div class="flex items-center pagination-controls">
          <div class="flex rounded-lg shadow-sm overflow-hidden">
            <!-- Primeira página -->
            <button
              (click)="firstPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Primeira página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Página anterior -->
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Página anterior"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Números de página -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page !== '...'">
                <button
                  (click)="goToPage(page)"
                  [disabled]="isLoading"
                  class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  [ngClass]="
                    currentPage === page
                      ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                      : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  "
                >
                  {{ page }}
                </button>
              </ng-container>
              <div
                *ngIf="page === '...'"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm border-r border-gray-200 bg-white text-gray-500"
              >
                ...
              </div>
            </ng-container>

            <!-- Próxima página -->
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Próxima página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- Última página -->
            <button
              (click)="lastPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Última página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
