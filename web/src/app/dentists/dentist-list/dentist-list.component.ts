import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DentistService } from '../../core/services/dentist.service';
import { Dentist } from '../../core/models/dentist.model';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-dentist-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './dentist-list.component.html',
  styleUrl: './dentist-list.component.scss',
})
export class DentistListComponent implements OnInit {
  dentists: Dentist[] = [];
  isLoading = true;
  error: string | null = null;
  searchTerm: string = '';

  // Filtros
  showOnlyActive: boolean = false;
  selectedSpecialties: string[] = [];
  availableSpecialties: string[] = [];

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;

  constructor(private dentistService: DentistService) {}

  ngOnInit(): void {
    this.loadAvailableSpecialties();
    this.loadDentists();
  }

  loadDentists(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.error = null;

    this.dentistService
      .getDentists(
        page,
        this.itemsPerPage,
        this.searchTerm,
        this.showOnlyActive ? true : undefined,
        this.selectedSpecialties.length > 0 ? this.selectedSpecialties : undefined
      )
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response: PaginatedResponse<Dentist>) => {
          this.dentists = response.data;
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },
        error: (err) => {
          console.error('Erro ao carregar dentistas:', err);
          this.error =
            'Não foi possível carregar a lista de dentistas. Por favor, tente novamente mais tarde.';
        },
      });
  }

  loadAvailableSpecialties(): void {
    this.dentistService.getAvailableSpecialties().subscribe({
      next: (specialties) => {
        this.availableSpecialties = specialties;
      },
      error: (err) => {
        console.error('Erro ao carregar especialidades:', err);
      }
    });
  }

  applySearch(): void {
    // Quando aplicamos filtros, voltamos para a primeira página
    this.currentPage = 1;
    this.loadDentists(this.currentPage);
  }

  toggleActiveFilter(): void {
    this.showOnlyActive = !this.showOnlyActive;
    this.applyFilters();
  }

  toggleSpecialty(specialty: string): void {
    const index = this.selectedSpecialties.indexOf(specialty);
    if (index > -1) {
      this.selectedSpecialties.splice(index, 1);
    } else {
      this.selectedSpecialties.push(specialty);
    }
    this.applyFilters();
  }

  isSpecialtySelected(specialty: string): boolean {
    return this.selectedSpecialties.includes(specialty);
  }

  applyFilters(): void {
    // Quando aplicamos filtros, voltamos para a primeira página
    this.currentPage = 1;
    this.loadDentists(this.currentPage);
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadDentists(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadDentists(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }
}
