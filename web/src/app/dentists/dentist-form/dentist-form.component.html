<div class="container bg-white shadow rounded-lg p-6 mx-auto">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-6">
      <a routerLink="/dentists" class="text-blue-600 hover:text-blue-800 mr-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </a>
      <h1 class="text-2xl font-bold text-gray-800">
        {{ isEditMode ? "Editar" : "Novo" }} Dentista
      </h1>
    </div>
  </div>

  <!-- Loading state -->
  <div
    *ngIf="isLoading"
    class="bg-white rounded-lg shadow-md p-6 flex justify-center items-center"
  >
    <div class="flex flex-col items-center">
      <svg
        class="animate-spin h-10 w-10 text-blue-600 mb-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <p class="text-gray-600">Carregando dados do dentista...</p>
    </div>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6"
    role="alert"
  >
    <p>{{ error }}</p>
    <button
      *ngIf="isEditMode && dentistId"
      (click)="loadDentist(dentistId)"
      class="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
    >
      Tentar novamente
    </button>
  </div>

  <!-- Form -->
  <form
    *ngIf="!isLoading"
    [formGroup]="dentistForm"
    (ngSubmit)="onSubmit()"
    class="bg-white rounded-lg overflow-hidden"
  >
    <div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Nome -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
            >Nome*</label
          >
          <input
            type="text"
            id="name"
            formControlName="name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': nameControl?.invalid && nameControl?.touched
            }"
          />
          <div
            *ngIf="nameControl?.invalid && nameControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="nameControl?.errors?.['required']"
              >Nome é obrigatório</span
            >
            <span *ngIf="nameControl?.errors?.['maxlength']"
              >Nome deve ter no máximo 100 caracteres</span
            >
          </div>
        </div>

        <!-- CRO -->
        <div>
          <label for="cro" class="block text-sm font-medium text-gray-700 mb-1"
            >CRO*</label
          >
          <input
            type="text"
            id="cro"
            formControlName="cro"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': croControl?.invalid && croControl?.touched
            }"
          />
          <div
            *ngIf="croControl?.invalid && croControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="croControl?.errors?.['required']"
              >CRO é obrigatório</span
            >
            <span *ngIf="croControl?.errors?.['maxlength']"
              >CRO deve ter no máximo 20 caracteres</span
            >
          </div>
        </div>

        <!-- Especialidade -->
        <div>
          <label
            for="specialty"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Especialidade*</label
          >
          <input
            type="text"
            id="specialty"
            formControlName="specialty"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500':
                specialtyControl?.invalid && specialtyControl?.touched
            }"
          />
          <div
            *ngIf="specialtyControl?.invalid && specialtyControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="specialtyControl?.errors?.['required']"
              >Especialidade é obrigatória</span
            >
            <span *ngIf="specialtyControl?.errors?.['maxlength']"
              >Especialidade deve ter no máximo 100 caracteres</span
            >
          </div>
        </div>

        <!-- Telefone -->
        <div>
          <label
            for="phone"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Telefone*</label
          >
          <input
            type="tel"
            id="phone"
            formControlName="phone"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': phoneControl?.invalid && phoneControl?.touched
            }"
          />
          <div
            *ngIf="phoneControl?.invalid && phoneControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="phoneControl?.errors?.['required']"
              >Telefone é obrigatório</span
            >
            <span *ngIf="phoneControl?.errors?.['maxlength']"
              >Telefone deve ter no máximo 20 caracteres</span
            >
          </div>
        </div>

        <!-- Email -->
        <div>
          <label
            for="email"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Email*</label
          >
          <input
            type="email"
            id="email"
            formControlName="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': emailControl?.invalid && emailControl?.touched
            }"
          />
          <div
            *ngIf="emailControl?.invalid && emailControl?.touched"
            class="text-red-500 text-sm mt-1"
          >
            <span *ngIf="emailControl?.errors?.['required']"
              >Email é obrigatório</span
            >
            <span *ngIf="emailControl?.errors?.['email']">Email inválido</span>
            <span *ngIf="emailControl?.errors?.['maxlength']"
              >Email deve ter no máximo 100 caracteres</span
            >
          </div>
        </div>

        <!-- Status -->
        <div>
          <div class="flex items-center mt-6">
            <input
              type="checkbox"
              id="active"
              formControlName="active"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="active" class="ml-2 block text-sm text-gray-900"
              >Dentista ativo</label
            >
          </div>
        </div>
      </div>

      <!-- Observações -->
      <div class="mt-6">
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1"
          >Observações</label
        >
        <textarea
          id="notes"
          formControlName="notes"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-500': notesControl?.invalid && notesControl?.touched
          }"
        ></textarea>
        <div
          *ngIf="notesControl?.invalid && notesControl?.touched"
          class="text-red-500 text-sm mt-1"
        >
          <span *ngIf="notesControl?.errors?.['maxlength']"
            >Observações devem ter no máximo 255 caracteres</span
          >
        </div>
      </div>
    </div>

    <!-- Form actions -->
    <div class="py-4 border-gray-200 flex justify-end space-x-3">
      <a
        routerLink="/dentists"
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Cancelar
      </a>
      <button
        type="submit"
        [disabled]="isSubmitting"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
        [ngClass]="{ 'opacity-50 cursor-not-allowed': isSubmitting }"
      >
        <svg
          *ngIf="isSubmitting"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isSubmitting ? "Salvando..." : "Salvar" }}
      </button>
    </div>
  </form>
</div>
