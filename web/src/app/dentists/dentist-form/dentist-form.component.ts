import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { DentistService } from '../../core/services/dentist.service';
import { Dentist } from '../../core/models/dentist.model';

@Component({
  selector: 'app-dentist-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './dentist-form.component.html',
  styleUrl: './dentist-form.component.scss'
})
export class DentistFormComponent implements OnInit {
  dentistForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;
  isEditMode = false;
  dentistId?: number;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dentistService: DentistService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Verificar se estamos no modo de edição
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.dentistId = +id;
      this.loadDentist(this.dentistId);
    }
  }

  initForm(): void {
    this.dentistForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      cro: ['', [Validators.required, Validators.maxLength(20)]],
      specialty: ['', [Validators.required, Validators.maxLength(100)]],
      phone: ['', [Validators.required, Validators.maxLength(20)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(100)]],
      notes: ['', Validators.maxLength(255)],
      active: [true]
    });
  }

  loadDentist(id: number): void {
    this.isLoading = true;
    this.error = null;

    this.dentistService.getDentist(id).subscribe({
      next: (dentist) => {
        this.dentistForm.patchValue({
          name: dentist.name,
          cro: dentist.cro,
          specialty: dentist.specialty,
          phone: dentist.phone,
          email: dentist.email,
          notes: dentist.notes,
          active: dentist.active
        });
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar dentista:', err);
        this.error = 'Não foi possível carregar os dados do dentista. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.dentistForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.dentistForm.controls).forEach(key => {
        const control = this.dentistForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    const dentistData = this.dentistForm.value;

    if (this.isEditMode && this.dentistId) {
      // Modo de edição
      this.dentistService.updateDentist(this.dentistId, dentistData).subscribe({
        next: () => {
          this.router.navigate(['/dentists', this.dentistId]);
        },
        error: (err) => {
          console.error('Erro ao atualizar dentista:', err);
          this.error = 'Não foi possível atualizar o dentista. Por favor, tente novamente mais tarde.';
          this.isSubmitting = false;
        }
      });
    } else {
      // Modo de criação
      this.dentistService.createDentist(dentistData).subscribe({
        next: (dentist) => {
          this.router.navigate(['/dentists', dentist.id]);
        },
        error: (err) => {
          console.error('Erro ao criar dentista:', err);
          this.error = 'Não foi possível criar o dentista. Por favor, tente novamente mais tarde.';
          this.isSubmitting = false;
        }
      });
    }
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get nameControl() { return this.dentistForm.get('name'); }
  get croControl() { return this.dentistForm.get('cro'); }
  get specialtyControl() { return this.dentistForm.get('specialty'); }
  get phoneControl() { return this.dentistForm.get('phone'); }
  get emailControl() { return this.dentistForm.get('email'); }
  get notesControl() { return this.dentistForm.get('notes'); }
  get activeControl() { return this.dentistForm.get('active'); }
}
