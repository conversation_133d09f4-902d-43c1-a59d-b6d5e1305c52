import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./dentist-list/dentist-list.component').then(m => m.DentistListComponent)
  },
  {
    path: 'new',
    loadComponent: () => import('./dentist-form/dentist-form.component').then(m => m.DentistFormComponent)
  },
  {
    path: ':id',
    loadComponent: () => import('./dentist-detail/dentist-detail.component').then(m => m.DentistDetailComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./dentist-form/dentist-form.component').then(m => m.DentistFormComponent)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DentistsRoutingModule { }
