import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { DentistService } from '../../core/services/dentist.service';
import { Dentist } from '../../core/models/dentist.model';

@Component({
  selector: 'app-dentist-detail',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './dentist-detail.component.html',
  styleUrl: './dentist-detail.component.scss'
})
export class DentistDetailComponent implements OnInit {
  dentist: Dentist | null = null;
  isLoading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dentistService: DentistService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadDentist(+id);
    } else {
      this.router.navigate(['/dentists']);
    }
  }

  loadDentist(id: number): void {
    this.isLoading = true;
    this.error = null;

    this.dentistService.getDentist(id).subscribe({
      next: (dentist) => {
        this.dentist = dentist;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar dentista:', err);
        this.error = 'Não foi possível carregar os detalhes do dentista. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      }
    });
  }

  deleteDentist(): void {
    if (!this.dentist) return;

    if (confirm(`Tem certeza que deseja excluir o dentista ${this.dentist.name}?`)) {
      this.isLoading = true;
      this.error = null;

      this.dentistService.deleteDentist(this.dentist.id).subscribe({
        next: () => {
          this.router.navigate(['/dentists']);
        },
        error: (err) => {
          console.error('Erro ao excluir dentista:', err);
          this.error = 'Não foi possível excluir o dentista. Por favor, tente novamente mais tarde.';
          this.isLoading = false;
        }
      });
    }
  }
}
