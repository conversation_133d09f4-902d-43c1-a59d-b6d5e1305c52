import { Routes } from '@angular/router';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { ProLayoutComponent } from './layouts/pro-layout/pro-layout.component';
import { LeadFormPageComponent } from './leads/lead-form-page/lead-form-page.component';

export const routes: Routes = [
  {
    path: 'formulario',
    component: LeadFormPageComponent,
  },
  {
    path: '',
    component: ProLayoutComponent,
    children: [
      {
        path: '',
        loadChildren: () =>
          import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
      },
      {
        path: 'patients',
        loadChildren: () =>
          import('./patients/patients.module').then((m) => m.PatientsModule),
      },
      {
        path: 'schedulings',
        loadChildren: () =>
          import('./schedulings/schedulings.module').then(
            (m) => m.SchedulingsModule
          ),
      },
      // Rota de tratamentos removida - substituída por procedure-board
      {
        path: 'reports',
        loadChildren: () =>
          import('./reports/reports.module').then((m) => m.ReportsModule),
      },
      {
        path: 'medical-records',
        loadChildren: () =>
          import('./medical-records/medical-records.module').then(
            (m) => m.MedicalRecordsModule
          ),
      },
      {
        path: 'dentists',
        loadChildren: () =>
          import('./dentists/dentists.module').then((m) => m.DentistsModule),
      },
      {
        path: 'schedule',
        loadChildren: () =>
          import('./schedule/schedule.module').then((m) => m.ScheduleModule),
      },
      {
        path: 'schedule-v2',
        loadChildren: () =>
          import('./schedule-v2/schedule-v2.module').then(
            (m) => m.ScheduleV2Module
          ),
      },
      {
        path: 'crc',
        loadChildren: () => import('./crc/crc.module').then((m) => m.CrcModule),
      },
      {
        path: 'employees',
        loadChildren: () =>
          import('./employees/employees.module').then((m) => m.EmployeesModule),
      },
      {
        path: 'appointments',
        redirectTo: 'schedulings',
        pathMatch: 'full',
      },
      {
        path: 'attendances',
        redirectTo: 'schedulings',
        pathMatch: 'full',
      },
      {
        path: 'funcionarios',
        redirectTo: 'employees',
        pathMatch: 'full',
      },
      {
        path: 'tasks',
        loadChildren: () =>
          import('./tasks/tasks.routes').then((m) => m.TASKS_ROUTES),
      },

      {
        path: 'settings/patient-types',
        loadChildren: () =>
          import('./patient-types/patient-types.module').then(
            (m) => m.PatientTypesModule
          ),
      },
      {
        path: 'settings/procedures',
        loadChildren: () =>
          import('./procedures/procedures.module').then(
            (m) => m.ProceduresModule
          ),
      },
      {
        path: 'settings/treatment-flow',
        loadChildren: () =>
          import('./treatment-flow/treatment-flow.module').then(
            (m) => m.TreatmentFlowModule
          ),
      },
      {
        path: 'settings/appointment-categories',
        loadChildren: () =>
          import('./appointment-categories/appointment-categories.module').then(
            (m) => m.AppointmentCategoriesModule
          ),
      },
      {
        path: 'leads',
        loadChildren: () =>
          import('./leads/leads.module').then((m) => m.LeadsModule),
      },
      {
        path: 'ai-suggestions',
        loadChildren: () =>
          import('./ai-suggestions/ai-suggestions.module').then(
            (m) => m.AiSuggestionsModule
          ),
      },
      {
        path: 'dentist-schedules',
        loadChildren: () =>
          import('./dentist-schedules/dentist-schedules.module').then(
            (m) => m.DentistSchedulesModule
          ),
      },
    ],
  },
  {
    path: 'auth',
    component: AuthLayoutComponent,
    loadChildren: () => import('./auth/auth.module').then((m) => m.AuthModule),
  },
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full',
  },
];
