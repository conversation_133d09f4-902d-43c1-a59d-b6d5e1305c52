import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MockDataService, Treatment } from '../../core/services/mock-data.service';

interface TreatmentReport extends Treatment {
  treatmentName: string;
}

interface ReportSummary {
  totalTreatments: number;
  completedTreatments: number;
  inProgressTreatments: number;
  scheduledTreatments: number;
  cancelledTreatments: number;
  totalRevenue: number;
  pendingRevenue: number;
  receivedRevenue: number;
}

@Component({
  selector: 'app-treatment-report',
  standalone: true,
  imports: [CommonModule, FormsModule, DatePipe],
  templateUrl: './treatment-report.component.html',
  styleUrl: './treatment-report.component.scss'
})
export class TreatmentReportComponent implements OnInit {
  treatments: TreatmentReport[] = [];
  filteredTreatments: TreatmentReport[] = [];
  summary: ReportSummary = {
    totalTreatments: 0,
    completedTreatments: 0,
    inProgressTreatments: 0,
    scheduledTreatments: 0,
    cancelledTreatments: 0,
    totalRevenue: 0,
    pendingRevenue: 0,
    receivedRevenue: 0
  };
  
  // Filtros
  startDate: string = '';
  endDate: string = '';
  selectedPatient: string = '';
  selectedStatus: string = '';
  selectedPaymentStatus: string = '';
  
  isLoading = true;

  constructor(private mockDataService: MockDataService) { }

  ngOnInit(): void {
    // Inicializa as datas para o mês atual
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    this.startDate = this.formatDateForInput(firstDay);
    this.endDate = this.formatDateForInput(lastDay);
    
    // Carrega os dados do serviço
    this.mockDataService.getTreatments().subscribe(treatments => {
      this.treatments = treatments.map(t => ({
        ...t,
        treatmentName: t.name
      }));
      
      this.applyFilters();
      this.isLoading = false;
    });
  }

  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  applyFilters(): void {
    this.filteredTreatments = this.treatments.filter(treatment => {
      // Filtro por período
      let dateMatch = true;
      if (this.startDate || this.endDate) {
        const treatmentDate = new Date(treatment.treatmentDate);
        
        if (this.startDate) {
          const startDate = new Date(this.startDate);
          dateMatch = dateMatch && treatmentDate >= startDate;
        }
        
        if (this.endDate) {
          const endDate = new Date(this.endDate);
          // Ajusta para o final do dia
          endDate.setHours(23, 59, 59, 999);
          dateMatch = dateMatch && treatmentDate <= endDate;
        }
      }
      
      // Filtro por paciente
      const patientMatch = !this.selectedPatient || treatment.patientId.toString() === this.selectedPatient;
      
      // Filtro por status
      const statusMatch = !this.selectedStatus || treatment.status === this.selectedStatus;
      
      // Filtro por status de pagamento
      let paymentMatch = true;
      if (this.selectedPaymentStatus === 'paid') {
        paymentMatch = treatment.paid === true;
      } else if (this.selectedPaymentStatus === 'pending') {
        paymentMatch = treatment.paid !== true;
      }
      
      return dateMatch && patientMatch && statusMatch && paymentMatch;
    });
    
    this.calculateSummary();
  }

  calculateSummary(): void {
    this.summary = {
      totalTreatments: this.filteredTreatments.length,
      completedTreatments: this.filteredTreatments.filter(t => t.status === 'completed').length,
      inProgressTreatments: this.filteredTreatments.filter(t => t.status === 'in-progress').length,
      scheduledTreatments: this.filteredTreatments.filter(t => t.status === 'scheduled').length,
      cancelledTreatments: this.filteredTreatments.filter(t => t.status === 'cancelled').length,
      totalRevenue: this.filteredTreatments.reduce((sum, t) => sum + (t.cost || 0), 0),
      pendingRevenue: this.filteredTreatments.filter(t => t.paid === false).reduce((sum, t) => sum + (t.cost || 0), 0),
      receivedRevenue: this.filteredTreatments.filter(t => t.paid === true).reduce((sum, t) => sum + (t.cost || 0), 0)
    };
  }

  resetFilters(): void {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    this.startDate = this.formatDateForInput(firstDay);
    this.endDate = this.formatDateForInput(lastDay);
    this.selectedPatient = '';
    this.selectedStatus = '';
    this.selectedPaymentStatus = '';
    
    this.applyFilters();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'in-progress':
        return 'Em andamento';
      case 'scheduled':
        return 'Agendado';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  }

  // Retorna uma lista única de pacientes para o filtro
  get patients() {
    const uniquePatients = new Map<number, {id: number, name: string}>();
    this.treatments.forEach(treatment => {
      if (!uniquePatients.has(treatment.patientId)) {
        uniquePatients.set(treatment.patientId, {
          id: treatment.patientId,
          name: treatment.patientName
        });
      }
    });
    return Array.from(uniquePatients.values());
  }

  // Formata valor monetário
  formatCurrency(value: number): string {
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  // Exporta o relatório para CSV
  exportCSV(): void {
    if (this.filteredTreatments.length === 0) {
      alert('Não há dados para exportar.');
      return;
    }

    const headers = ['ID', 'Paciente', 'Tratamento', 'Data', 'Retorno', 'Dentista', 'Status', 'Valor', 'Pago'];
    
    const csvData = this.filteredTreatments.map(t => {
      return [
        t.id,
        t.patientName,
        t.treatmentName,
        new Date(t.treatmentDate).toLocaleDateString('pt-BR'),
        t.returnDate ? new Date(t.returnDate).toLocaleDateString('pt-BR') : 'N/A',
        t.dentist,
        this.getStatusText(t.status),
        (t.cost || 0).toFixed(2).replace('.', ','),
        t.paid === true ? 'Sim' : 'Não'
      ].join(',');
    });
    
    const csvContent = [headers.join(','), ...csvData].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `relatorio_tratamentos_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
