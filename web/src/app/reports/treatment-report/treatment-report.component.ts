import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { SchedulingService } from '../../core/services/scheduling.service';
import {
  ReportService,
  FinancialReport,
} from '../../core/services/report.service';
import { Scheduling } from '../../core/models/scheduling.model';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { forkJoin } from 'rxjs';

// Interface para substituir o modelo Treatment removido
interface TreatmentData {
  id: number;
  name: string;
  patientId: number;
  patientName?: string;
  dentistId: number;
  dentistName?: string;
  status: string;
  createdAt?: Date;
  cost?: number;
  paid?: boolean;
  notes?: string;
  [key: string]: any;
}

interface TreatmentReport {
  id: number;
  patientId: number;
  patientName: string;
  treatmentName: string;
  dentistId?: number;
  dentistName: string;
  status:
    | 'scheduled-unconfirmed'
    | 'scheduled-confirmed'
    | 'unscheduled'
    | 'in-progress'
    | 'completed'
    | 'cancelled'
    | 'scheduled'
    | 'confirmed';
  notes?: string;
  createdAt?: Date;
  cost?: number;
  paid?: boolean;
  type: 'treatment' | 'attendance' | 'scheduling';
  // Campos opcionais para compatibilidade
  name?: string;
  description?: string;
  treatment?: string;
  // Campos opcionais para prontuário
  clinicalExamination?: string;
  diagnosis?: string;
  treatmentPlan?: any; // Alterado de string para any para aceitar tanto string quanto objeto TreatmentPlan
  complementaryExams?: string;
  // Campo para relacionamento entre agendamento e tratamento
  treatmentId?: number | null;
}

interface ReportSummary {
  // Tratamentos
  totalTreatments: number;
  completedTreatments: number;
  inProgressTreatments: number;
  scheduledTreatments: number;
  cancelledTreatments: number;
  treatmentRevenue: number;
  treatmentPendingRevenue: number;
  treatmentReceivedRevenue: number;

  // Agendamentos
  totalSchedulings: number;
  completedSchedulings: number;
  confirmedSchedulings: number;
  scheduledSchedulings: number;
  cancelledSchedulings: number;
  schedulingRevenue: number;
  schedulingPendingRevenue: number;
  schedulingReceivedRevenue: number;

  // Total
  totalRevenue: number;
  totalPendingRevenue: number;
  totalReceivedRevenue: number;
}

@Component({
  selector: 'app-treatment-report',
  standalone: true,
  imports: [CommonModule, FormsModule, DatePipe],
  templateUrl: './treatment-report.component.html',
  styleUrl: './treatment-report.component.scss',
})
export class TreatmentReportComponent implements OnInit {
  treatments: TreatmentReport[] = [];
  filteredTreatments: TreatmentReport[] = [];
  displayItems: TreatmentReport[] = []; // Nova propriedade para exibir apenas itens válidos
  summary: ReportSummary = {
    // Tratamentos
    totalTreatments: 0,
    completedTreatments: 0,
    inProgressTreatments: 0,
    scheduledTreatments: 0,
    cancelledTreatments: 0,
    treatmentRevenue: 0,
    treatmentPendingRevenue: 0,
    treatmentReceivedRevenue: 0,

    // Agendamentos
    totalSchedulings: 0,
    completedSchedulings: 0,
    confirmedSchedulings: 0,
    scheduledSchedulings: 0,
    cancelledSchedulings: 0,
    schedulingRevenue: 0,
    schedulingPendingRevenue: 0,
    schedulingReceivedRevenue: 0,

    // Total
    totalRevenue: 0,
    totalPendingRevenue: 0,
    totalReceivedRevenue: 0,
  };

  // Filtros
  startDate: string = '';
  endDate: string = '';
  selectedPatient: string = '';
  selectedStatus: string = '';
  selectedPaymentStatus: string = '';
  selectedType: string = '';

  isLoading = true;

  constructor(
    private treatmentPlanService: TreatmentPlanService,
    private schedulingService: SchedulingService,
    private reportService: ReportService
  ) {}

  ngOnInit(): void {
    // Inicializa as datas para o mês atual
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    this.startDate = this.formatDateForInput(firstDay);
    this.endDate = this.formatDateForInput(lastDay);

    // Carrega os dados do serviço
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;

    // Usar forkJoin para carregar procedimentos, agendamentos e relatório financeiro em paralelo
    forkJoin({
      procedures: this.treatmentPlanService.getAllTreatmentPlans(), // Buscar planos de tratamento com procedimentos
      schedulings: this.schedulingService.getAllSchedulings(),
      financialReport: this.reportService.getFinancialReport(
        this.startDate,
        this.endDate,
        this.selectedPatient ? +this.selectedPatient : undefined,
        this.selectedStatus,
        this.selectedPaymentStatus,
        this.selectedType
      ),
    }).subscribe({
      next: (data) => {
        // Processar procedimentos de tratamento
        const treatmentReports = data.procedures.flatMap(plan =>
          (plan.procedures || []).map((t: any) => ({
          id: t.id,
          treatmentName: t.name,
          patientId: plan.patientId,
          patientName: plan.patient?.name || 'Sem nome',
          dentistId: t.professionalId,
          dentistName: t.professional?.name || 'Dentista não identificado',
          status: t.status,
          notes: t.notes,
          cost: t.value || 0,
          paid: t.status === 'completed', // Consideramos pago se estiver concluído
          createdAt: t.createdAt,
          type: 'treatment' as const,
        })));

        // Verificar se data.schedulings é um array ou um objeto paginado
        let schedulings: Scheduling[] = [];

        if (Array.isArray(data.schedulings)) {
          schedulings = data.schedulings;
        } else if (
          data.schedulings &&
          typeof data.schedulings === 'object' &&
          'data' in data.schedulings
        ) {
          schedulings = (data.schedulings as PaginatedResponse<Scheduling>)
            .data;
        }

        // Processar agendamentos (apenas os que não estão relacionados a tratamentos)
        console.log('Total de agendamentos recebidos:', schedulings.length);

        // Verificar detalhadamente os agendamentos recebidos
        schedulings.forEach((a: Scheduling) => {
          console.log(
            `Agendamento ID ${a.id}: treatmentId = ${a.treatmentId}`
          );
        });

        // FILTRAR RIGOROSAMENTE - Remover TODOS os agendamentos com treatmentId
        // Verificar se treatmentId é ESTRITAMENTE nulo
        const filteredSchedulings: Scheduling[] = [];

        for (const a of schedulings) {
          // Verificar ESTRITAMENTE se o treatmentId é nulo
          // Não aceitar undefined ou outros valores falsy
          if (a.treatmentId === null) {
            console.log(
              `MANTENDO Agendamento ID ${a.id} com treatmentId = ${
                a.treatmentId
              } (${typeof a.treatmentId})`
            );
            filteredSchedulings.push(a);
          } else {
            console.log(
              `EXCLUINDO Agendamento ID ${a.id} com treatmentId = ${
                a.treatmentId
              } (${typeof a.treatmentId})`
            );
          }
        }

        console.log(
          'Agendamentos com treatmentId:',
          schedulings.filter((a: Scheduling) => a.treatmentId).length
        );
        console.log(
          'Agendamentos sem treatmentId:',
          filteredSchedulings.length
        );
        console.log(
          'Agendamentos a serem incluídos no relatório:',
          filteredSchedulings.length
        );

        // Mapear os agendamentos filtrados para o formato do relatório
        const schedulingReports = filteredSchedulings.map((a) => ({
          ...a,
          id: a.id,
          patientId: a.patientId,
          patientName: a.patientName || 'Sem nome',
          treatmentName: 'Agendamento',
          // Usar a data do agendamento como data de tratamento para compatibilidade
          dentistId: a.dentistId || undefined,
          dentistName: a.dentistName || (a.dentistId ? 'Dentista não identificado' : 'Sem dentista definido'),
          status: a.status as
            | 'scheduled'
            | 'in-progress'
            | 'completed'
            | 'cancelled',
          notes: a.notes,
          cost: typeof a.cost === 'string' ? parseFloat(a.cost) : a.cost || 0,
          paid: a.paid,
          createdAt: a.createdAt,
          type: 'scheduling' as const,
        }));

        // Combinar os dois conjuntos de dados
        this.treatments = [...treatmentReports, ...schedulingReports];

        // Aplicar os filtros
        this.applyFilters();

        // Preparar os itens para exibição
        this.prepareDisplayItems();

        // Calcular o resumo
        this.calculateSummary();

        // Usar os dados do relatório financeiro da API
        this.summary = {
          // Tratamentos
          totalTreatments: data.financialReport.totalTreatments,
          completedTreatments: data.financialReport.completedTreatments,
          inProgressTreatments: data.financialReport.inProgressTreatments,
          scheduledTreatments: data.financialReport.scheduledTreatments,
          cancelledTreatments: data.financialReport.cancelledTreatments,
          treatmentRevenue: data.financialReport.treatmentRevenue,
          treatmentPendingRevenue: data.financialReport.treatmentPendingRevenue,
          treatmentReceivedRevenue:
            data.financialReport.treatmentReceivedRevenue,

          // Agendamentos
          totalSchedulings: data.financialReport.totalSchedulings,
          completedSchedulings: data.financialReport.completedSchedulings,
          confirmedSchedulings: data.financialReport.confirmedSchedulings,
          scheduledSchedulings: data.financialReport.scheduledSchedulings,
          cancelledSchedulings: data.financialReport.cancelledSchedulings,
          schedulingRevenue: data.financialReport.schedulingRevenue,
          schedulingPendingRevenue:
            data.financialReport.schedulingPendingRevenue,
          schedulingReceivedRevenue:
            data.financialReport.schedulingReceivedRevenue,

          // Total
          totalRevenue: data.financialReport.totalRevenue,
          totalPendingRevenue: data.financialReport.totalPendingRevenue,
          totalReceivedRevenue: data.financialReport.totalReceivedRevenue,
        };

        this.applyFilters();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar dados:', error);
        this.isLoading = false;
      },
    });
  }

  // Mapeia os status de atendimento para os status de tratamento para facilitar o filtro
  mapAttendanceStatus(
    status: string
  ):
    | 'scheduled-unconfirmed'
    | 'scheduled-confirmed'
    | 'in-progress'
    | 'completed'
    | 'cancelled'
    | 'unscheduled' {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'scheduled-unconfirmed';
      case 'scheduled-confirmed':
        return 'scheduled-confirmed';
      case 'in-progress':
        return 'in-progress';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      case 'unscheduled':
        return 'unscheduled';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'scheduled-unconfirmed';
      case 'confirmed':
        return 'scheduled-confirmed';
      default:
        return 'scheduled-unconfirmed';
    }
  }

  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  applyFilters(): void {
    // PRIMEIRO PASSO: Filtrar DIRETAMENTE para incluir apenas tratamentos e agendamentos sem tratamento associado
    const validTreatments = [];

    // Usar um loop for tradicional para maior clareza e controle
    for (const item of this.treatments) {
      // Se for um tratamento, sempre incluir
      if (item.type === 'treatment') {
        validTreatments.push(item);
        continue;
      }

      // Se for um agendamento, verificar diretamente se não tem tratamento associado
      if (item.type === 'scheduling') {
        // Verificar ESTRITAMENTE se treatmentId é nulo
        // Não aceitar undefined ou outros valores falsy
        if (item.treatmentId === null) {
          console.log(
            `INCLUINDO em applyFilters: Agendamento ID ${item.id} sem treatmentId`
          );
          validTreatments.push(item);
        } else {
          console.log(
            `EXCLUINDO em applyFilters: Agendamento ID ${
              item.id
            } com treatmentId = ${
              item.treatmentId
            } (${typeof item.treatmentId})`
          );
        }
      }
    }

    console.log(
      `Itens no relatório após filtragem inicial: ${validTreatments.length}`
    );
    console.log(
      `Tratamentos: ${
        validTreatments.filter((i) => i.type === 'treatment').length
      }`
    );
    console.log(
      `Agendamentos: ${
        validTreatments.filter((i) => i.type === 'scheduling').length
      }`
    );

    // SEGUNDO PASSO: Aplicar os filtros selecionados pelo usuário
    this.filteredTreatments = validTreatments.filter((item) => {
      // Filtro por tipo (tratamento ou agendamento)
      if (this.selectedType) {
        if (this.selectedType === 'treatment' && item.type !== 'treatment')
          return false;
        if (this.selectedType === 'scheduling' && item.type !== 'scheduling')
          return false;
      }

      // Filtro por período
      let dateMatch = true;
      if (this.startDate || this.endDate) {
        // Usar a data de criação como substituto para a data do tratamento
        const itemDate = item.createdAt ? new Date(item.createdAt) : new Date();

        if (this.startDate) {
          const startDate = new Date(this.startDate);
          dateMatch = dateMatch && itemDate >= startDate;
        }

        if (this.endDate) {
          const endDate = new Date(this.endDate);
          // Ajusta para o final do dia
          endDate.setHours(23, 59, 59, 999);
          dateMatch = dateMatch && itemDate <= endDate;
        }
      }

      // Filtro por paciente
      const patientMatch =
        !this.selectedPatient ||
        item.patientId.toString() === this.selectedPatient;

      // Filtro por status
      const statusMatch =
        !this.selectedStatus || item.status === this.selectedStatus;

      // Filtro por status de pagamento
      let paymentMatch = true;
      if (this.selectedPaymentStatus === 'paid') {
        paymentMatch = item.paid === true;
      } else if (this.selectedPaymentStatus === 'pending') {
        paymentMatch = item.paid !== true;
      }

      return dateMatch && patientMatch && statusMatch && paymentMatch;
    });

    // Preparar os itens para exibição após aplicar os filtros
    this.prepareDisplayItems();

    // Recarregar os dados do relatório financeiro quando os filtros são alterados
    this.reportService
      .getFinancialReport(
        this.startDate,
        this.endDate,
        this.selectedPatient ? +this.selectedPatient : undefined,
        this.selectedStatus,
        this.selectedPaymentStatus,
        this.selectedType
      )
      .subscribe({
        next: (data) => {
          this.summary = {
            // Tratamentos
            totalTreatments: data.totalTreatments,
            completedTreatments: data.completedTreatments,
            inProgressTreatments: data.inProgressTreatments,
            scheduledTreatments: data.scheduledTreatments,
            cancelledTreatments: data.cancelledTreatments,
            treatmentRevenue: data.treatmentRevenue,
            treatmentPendingRevenue: data.treatmentPendingRevenue,
            treatmentReceivedRevenue: data.treatmentReceivedRevenue,

            // Agendamentos
            totalSchedulings: data.totalSchedulings,
            completedSchedulings: data.completedSchedulings,
            confirmedSchedulings: data.confirmedSchedulings,
            scheduledSchedulings: data.scheduledSchedulings,
            cancelledSchedulings: data.cancelledSchedulings,
            schedulingRevenue: data.schedulingRevenue,
            schedulingPendingRevenue: data.schedulingPendingRevenue,
            schedulingReceivedRevenue: data.schedulingReceivedRevenue,

            // Total
            totalRevenue: data.totalRevenue,
            totalPendingRevenue: data.totalPendingRevenue,
            totalReceivedRevenue: data.totalReceivedRevenue,
          };
        },
        error: (error) => {
          console.error('Erro ao carregar dados do relatório:', error);
        },
      });
  }

  calculateSummary(): void {
    // Usar a lista displayItems que já contém apenas tratamentos e agendamentos sem tratamento
    const treatments = this.displayItems.filter(
      (item) => item.type === 'treatment'
    );
    const attendances = this.displayItems.filter(
      (item) => item.type === 'scheduling'
    );

    console.log(
      `Calculando resumo para ${treatments.length} tratamentos e ${attendances.length} agendamentos`
    );

    // Calcular estatísticas de tratamentos
    // Receita total de tratamentos (apenas não cancelados, pagos ou pendentes)
    const treatmentRevenue = treatments
      .filter((t) => t.status !== 'cancelled' && t.status !== 'unscheduled')
      .reduce((sum, t) => {
        const cost =
          typeof t.cost === 'string' ? parseFloat(t.cost) : t.cost || 0;
        return sum + cost;
      }, 0);

    const treatmentPendingRevenue = treatments
      .filter(
        (t) =>
          t.paid === false &&
          t.status !== 'cancelled' &&
          t.status !== 'unscheduled'
      )
      .reduce((sum, t) => {
        const cost =
          typeof t.cost === 'string' ? parseFloat(t.cost) : t.cost || 0;
        return sum + cost;
      }, 0);

    // Receita paga de tratamentos (apenas não cancelados e pagos)
    const treatmentReceivedRevenue = treatments
      .filter(
        (t) =>
          t.paid === true &&
          t.status !== 'cancelled' &&
          t.status !== 'unscheduled'
      )
      .reduce((sum, t) => {
        const cost =
          typeof t.cost === 'string' ? parseFloat(t.cost) : t.cost || 0;
        return sum + cost;
      }, 0);

    // Calcular estatísticas de agendamentos
    // Receita total de agendamentos (apenas não cancelados, pagos ou pendentes)
    const attendanceRevenue = attendances
      .filter((a) => a.status !== 'cancelled' && a.status !== 'unscheduled')
      .reduce((sum, a) => {
        const cost =
          typeof a.cost === 'string' ? parseFloat(a.cost) : a.cost || 0;
        return sum + cost;
      }, 0);

    const schedulingPendingRevenue = attendances
      .filter(
        (a) =>
          a.paid === false &&
          a.status !== 'cancelled' &&
          a.status !== 'unscheduled'
      )
      .reduce((sum, a) => {
        const cost =
          typeof a.cost === 'string' ? parseFloat(a.cost) : a.cost || 0;
        return sum + cost;
      }, 0);

    // Receita paga de agendamentos (apenas não cancelados e pagos)
    const schedulingReceivedRevenue = attendances
      .filter(
        (a) =>
          a.paid === true &&
          a.status !== 'cancelled' &&
          a.status !== 'unscheduled'
      )
      .reduce((sum, a) => {
        const cost =
          typeof a.cost === 'string' ? parseFloat(a.cost) : a.cost || 0;
        return sum + cost;
      }, 0);

    this.summary = {
      // Tratamentos
      totalTreatments: treatments.length,
      completedTreatments: treatments.filter((t) => t.status === 'completed')
        .length,
      inProgressTreatments: treatments.filter((t) => t.status === 'in-progress')
        .length,
      scheduledTreatments: treatments.filter(
        (t) =>
          t.status === 'scheduled-unconfirmed' ||
          t.status === 'scheduled-confirmed' ||
          t.status === 'scheduled'
      ).length,
      cancelledTreatments: treatments.filter(
        (t) => t.status === 'cancelled' || t.status === 'unscheduled'
      ).length,
      treatmentRevenue,
      treatmentPendingRevenue,
      treatmentReceivedRevenue,

      // Agendamentos
      totalSchedulings: attendances.length,
      completedSchedulings: attendances.filter((a) => a.status === 'completed')
        .length,
      confirmedSchedulings: attendances.filter(
        (a) => a.status === 'in-progress' || a.status === 'scheduled-confirmed'
      ).length,
      scheduledSchedulings: attendances.filter(
        (a) => a.status === 'scheduled-unconfirmed' || a.status === 'scheduled'
      ).length,
      cancelledSchedulings: attendances.filter(
        (a) => a.status === 'cancelled' || a.status === 'unscheduled'
      ).length,
      schedulingRevenue: attendanceRevenue,
      schedulingPendingRevenue,
      schedulingReceivedRevenue,

      // Total
      totalRevenue: treatmentRevenue + attendanceRevenue,
      totalPendingRevenue: treatmentPendingRevenue + schedulingPendingRevenue,
      totalReceivedRevenue:
        treatmentReceivedRevenue + schedulingReceivedRevenue,
    };
  }

  resetFilters(): void {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    this.startDate = this.formatDateForInput(firstDay);
    this.endDate = this.formatDateForInput(lastDay);
    this.selectedPatient = '';
    this.selectedStatus = '';
    this.selectedPaymentStatus = '';
    this.selectedType = '';

    this.loadData();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled-confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'unscheduled':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'in-progress':
        return 'Em andamento';
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'cancelled':
        return 'Cancelado';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'Agendado';
      case 'confirmed':
        return 'Confirmado';
      default:
        return status;
    }
  }

  // Retorna uma lista única de pacientes para o filtro
  get patients() {
    const uniquePatients = new Map<number, { id: number; name: string }>();
    this.treatments.forEach((treatment) => {
      if (!uniquePatients.has(treatment.patientId)) {
        uniquePatients.set(treatment.patientId, {
          id: treatment.patientId,
          name: treatment.patientName,
        });
      }
    });
    return Array.from(uniquePatients.values());
  }

  // Formata valor monetário
  formatCurrency(value: number): string {
    // Garantir que o valor seja um número
    const numValue = typeof value === 'string' ? parseFloat(value) : value || 0;
    return numValue.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  }

  getTypeText(type: string): string {
    if (type === 'treatment') return 'Tratamento';
    if (type === 'scheduling') return 'Agendamento';
    return 'Atendimento';
  }

  // Método para verificar se um item deve ser exibido na tabela
  shouldShowInTable(item: TreatmentReport): boolean {
    // Se for um tratamento, sempre exibir
    if (item.type === 'treatment') return true;

    // Se for um agendamento, verificar ESTRITAMENTE se não tem tratamento associado
    if (item.type === 'scheduling') {
      return item.treatmentId === null;
    }

    return false;
  }

  // Método para preparar os itens para exibição
  prepareDisplayItems(): void {
    console.log('Preparando itens para exibição...');

    // Limpar a lista de itens para exibição
    this.displayItems = [];

    // Adicionar todos os tratamentos
    const treatments = this.filteredTreatments.filter(
      (item) => item.type === 'treatment'
    );
    this.displayItems.push(...treatments);

    // Adicionar APENAS agendamentos sem tratamento associado
    // Usar um loop for tradicional para maior controle e depuração
    const schedulings = [];
    for (const item of this.filteredTreatments) {
      if (item.type !== 'scheduling') continue;

      // Verificar RIGOROSAMENTE se o agendamento não tem tratamento associado
      // Considerar apenas agendamentos com treatmentId ESTRITAMENTE nulo
      if (item.treatmentId === null) {
        console.log(`INCLUINDO: Agendamento ID ${item.id} sem treatmentId`);
        schedulings.push(item);
      } else {
        console.log(
          `EXCLUINDO: Agendamento ID ${item.id} com treatmentId = ${
            item.treatmentId
          } (${typeof item.treatmentId})`
        );
      }
    }

    // Adicionar os agendamentos filtrados à lista de exibição
    this.displayItems.push(...schedulings);

    console.log(`Itens preparados para exibição: ${this.displayItems.length}`);
    console.log(`- Tratamentos: ${treatments.length}`);
    console.log(`- Agendamentos sem tratamento: ${schedulings.length}`);

    // Verificar se há agendamentos com treatmentId que não foram incluídos
    const schedulingsWithTreatment = this.filteredTreatments.filter(
      (item) => item.type === 'scheduling' && item.treatmentId !== null
    );

    if (schedulingsWithTreatment.length > 0) {
      console.log(
        `Agendamentos com treatmentId (não exibidos): ${schedulingsWithTreatment.length}`
      );
      schedulingsWithTreatment.forEach((item) => {
        console.log(
          `ID: ${item.id}, treatmentId: ${
            item.treatmentId
          }, tipo: ${typeof item.treatmentId}`
        );
      });
    }

    // Verificar novamente se há agendamentos com treatmentId na lista de exibição
    const displaySchedulingsWithTreatment = this.displayItems.filter(
      (item) => item.type === 'scheduling' && item.treatmentId !== null
    );

    if (displaySchedulingsWithTreatment.length > 0) {
      console.error(
        `ERRO: Ainda há ${displaySchedulingsWithTreatment.length} agendamentos com treatmentId na lista de exibição!`
      );

      // Remover esses agendamentos da lista de exibição
      this.displayItems = this.displayItems.filter(
        (item) => item.type !== 'scheduling' || item.treatmentId === null
      );

      console.log(
        `Lista de exibição corrigida: ${this.displayItems.length} itens`
      );
    }
  }

  // Exporta o relatório para CSV
  exportCSV(): void {
    if (this.displayItems.length === 0) {
      alert('Não há dados para exportar.');
      return;
    }

    const headers = [
      'ID',
      'Tipo',
      'Paciente',
      'Tratamento',
      'Data',
      'Dentista',
      'Status',
      'Valor',
      'Pago',
    ];

    console.log(`Exportando ${this.displayItems.length} itens para CSV`);
    console.log(
      `- Tratamentos: ${
        this.displayItems.filter((i) => i.type === 'treatment').length
      }`
    );
    console.log(
      `- Agendamentos: ${
        this.displayItems.filter((i) => i.type === 'scheduling').length
      }`
    );

    const csvData = this.displayItems.map((item) => {
      return [
        item.id,
        this.getTypeText(item.type),
        item.patientName,
        item.treatmentName,
        item.createdAt
          ? new Date(item.createdAt).toLocaleDateString('pt-BR')
          : 'N/A',
        item.dentistName,
        this.getStatusText(item.status),
        this.formatCurrency(item.cost || 0)
          .replace('R$', '')
          .trim(),
        item.paid === true ? 'Sim' : 'Não',
      ].join(',');
    });

    const csvContent = [headers.join(','), ...csvData].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute(
      'download',
      `relatorio_financeiro_${new Date().toISOString().split('T')[0]}.csv`
    );
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
