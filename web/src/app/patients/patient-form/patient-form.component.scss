/* Estilos específicos para o formulário de paciente */
:host {
  display: block;
  width: 100%;
}

/* Estilos para contêineres de formulário */
.form-container {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 2rem;
  width: 100%;
}

/* Melhorar a aparência dos campos de formulário */
input, select, textarea {
  transition: all 0.2s ease-in-out;
  border-radius: 0.375rem;
  height: 42px; /* Altura padronizada para todos os campos */
  padding: 0.5rem 0.75rem;
  box-sizing: border-box;

  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  &:hover:not(:focus) {
    border-color: #93c5fd;
  }
}

/* Ajuste específico para textareas */
textarea {
  height: auto;
  min-height: 100px;
}

/* Ajuste específico para selects */
select {
  appearance: none;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Estilizar labels */
label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.375rem;
  display: block;
}

/* Estilizar os ícones nas seções */
svg {
  transition: all 0.3s ease;
}

div[class*="bg-white"]:hover svg {
  transform: scale(1.2);
}

/* Melhorar a aparência dos botões */
button, a[routerLink] {
  transition: all 0.2s;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }

  &:active {
    transform: translateY(1px);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Estilizar campos obrigatórios */
label:has(+ input:required)::after,
label:has(+ select:required)::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

