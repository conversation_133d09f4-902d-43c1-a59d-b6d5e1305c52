<div class="form-container">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-6">
      <a
        routerLink="/patients"
        class="text-blue-600 hover:text-blue-800 mr-2 transition-all duration-200 hover:scale-110"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </a>
      <h1 class="text-2xl font-bold text-gray-800">
        {{ isEditMode ? "Editar Paciente" : "Novo Paciente" }}
      </h1>
    </div>
  </div>

  <!-- Alerta de cadastro incompleto -->
  <div
    *ngIf="isEditMode && currentPatient?.isIncomplete"
    class="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg"
  >
    <div class="flex items-center">
      <svg
        class="h-5 w-5 text-orange-400 mr-2"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"
        />
      </svg>
      <div>
        <h3 class="text-sm font-medium text-orange-800">Cadastro Incompleto</h3>
        <p class="text-sm text-orange-700 mt-1">
          Este paciente foi criado via sistema externo. Complete os dados
          obrigatórios para finalizar o cadastro.
        </p>
      </div>
    </div>
  </div>

  <div>
    <form [formGroup]="patientForm" (ngSubmit)="onSubmit()" class="space-y-8">
      <!-- Seção: Dados Cadastrais -->
      <div class="py-4">
        <h2
          class="text-lg font-semibold text-gray-800 mb-6 pb-2 border-b border-gray-200 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-blue-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path
              fill-rule="evenodd"
              d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
              clip-rule="evenodd"
            />
          </svg>
          Dados Cadastrais
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Nome -->
          <div class="col-span-1 lg:col-span-2">
            <label
              for="name"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Nome Completo*</label
            >
            <input
              type="text"
              id="name"
              formControlName="name"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{
                'border-red-500': nameControl?.invalid && nameControl?.touched
              }"
            />
            <div
              *ngIf="nameControl?.invalid && nameControl?.touched"
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="nameControl?.errors?.['required']"
                >Nome é obrigatório</span
              >
              <span *ngIf="nameControl?.errors?.['minlength']"
                >Nome deve ter pelo menos 3 caracteres</span
              >
            </div>
          </div>

          <div class="col-span-1 flex items-start gap-2 w-full">
            <!-- Data de Nascimento -->
            <div class="w-2/3">
              <label
                for="birthDate"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Data de Nascimento*</label
              >
              <input
                type="date"
                id="birthDate"
                formControlName="birthDate"
                class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{
                  'border-red-500':
                    birthDateControl?.invalid && birthDateControl?.touched
                }"
                (change)="onBirthDateChange($event)"
              />
              <div
                *ngIf="birthDateControl?.invalid && birthDateControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                <span *ngIf="birthDateControl?.errors?.['required']"
                  >Data de nascimento é obrigatória</span
                >
              </div>
            </div>

            <!-- Idade (calculada) -->
            <div class="w-1/3">
              <label
                for="age"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Idade</label
              >
              <input
                type="text"
                id="age"
                formControlName="age"
                class="w-full px-3 border border-gray-300 rounded-md bg-gray-100"
                readonly
              />
            </div>
          </div>

          <!-- Gênero -->
          <div class="col-span-1">
            <label
              for="gender"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Gênero</label
            >
            <select
              id="gender"
              formControlName="gender"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option [ngValue]="null">Selecione</option>
              <option
                *ngFor="let gender of patientGenders"
                [value]="gender.value"
              >
                {{ gender.label }}
              </option>
            </select>
          </div>

          <!-- CPF -->
          <div class="col-span-1">
            <label
              for="cpf"
              class="block text-sm font-medium text-gray-700 mb-1"
              >CPF*</label
            >
            <input
              type="text"
              id="cpf"
              formControlName="cpf"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{
                'border-red-500': cpfControl?.invalid && cpfControl?.touched
              }"
              (input)="onCpfInput($event)"
            />
            <div
              *ngIf="cpfControl?.invalid && cpfControl?.touched"
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="cpfControl?.errors?.['required']"
                >CPF é obrigatório</span
              >
            </div>
          </div>

          <!-- Como conheceu -->
          <div class="col-span-1">
            <label
              for="howDidYouFindUs"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Como nos conheceu</label
            >
            <select
              id="howDidYouFindUs"
              formControlName="howDidYouFindUs"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option [value]="''">Selecione</option>
              <option
                *ngFor="let option of howDidYouFindUsOptions"
                [value]="option"
              >
                {{ option }}
              </option>
            </select>
          </div>

          <!-- Indicado por (apenas visível quando "Como nos conheceu" é "Indicação") -->
          <div
            class="col-span-1"
            *ngIf="howDidYouFindUsControl?.value === 'Indicação'"
          >
            <label
              for="referredById"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Indicado por</label
            >
            <app-patient-selector
              formControlName="referredById"
              [required]="false"
              [invalid]="false"
              [touched]="referredByIdControl?.touched"
              [placeholder]="'Selecione o paciente que fez a indicação'"
            ></app-patient-selector>
          </div>

          <!-- Data de Registro -->
          <div class="col-span-1" *ngIf="isEditMode">
            <label
              for="registrationDate"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Data de Registro</label
            >
            <input
              type="text"
              id="registrationDate"
              formControlName="registrationDate"
              class="w-full px-3 border border-gray-300 rounded-md bg-gray-100"
              readonly
            />
          </div>
        </div>
      </div>

      <!-- Seção: Contato -->
      <div class="py-4">
        <h2
          class="text-lg font-semibold text-gray-800 mb-6 pb-2 border-b border-gray-200 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-green-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
            />
          </svg>
          Contato
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            class="col-span-1 md:col-span-2 lg:col-span-3 gap-6 flex flex-col lg:flex-row"
          >
            <!-- Email -->
            <div class="w-full lg:w-1/2">
              <label
                for="email"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Email*</label
              >
              <input
                type="email"
                id="email"
                formControlName="email"
                class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{
                  'border-red-500':
                    emailControl?.invalid && emailControl?.touched
                }"
              />
              <div
                *ngIf="emailControl?.invalid && emailControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                <span *ngIf="emailControl?.errors?.['required']"
                  >Email é obrigatório</span
                >
                <span *ngIf="emailControl?.errors?.['email']"
                  >Email inválido</span
                >
              </div>
            </div>

            <div class="w-full lg:w-1/2 flex gap-6 flex-col lg:flex-row">
              <!-- Telefone -->
              <div class="w-full">
                <label
                  for="phone"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Telefone*</label
                >
                <input
                  type="tel"
                  id="phone"
                  formControlName="phone"
                  class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [ngClass]="{
                    'border-red-500':
                      phoneControl?.invalid && phoneControl?.touched
                  }"
                  (input)="onPhoneInput($event)"
                />
                <div
                  *ngIf="phoneControl?.invalid && phoneControl?.touched"
                  class="text-red-500 text-sm mt-1"
                >
                  <span *ngIf="phoneControl?.errors?.['required']"
                    >Telefone é obrigatório</span
                  >
                </div>
              </div>

              <!-- WhatsApp -->
              <div class="w-full">
                <div class="flex flex-col">
                  <label
                    for="whatsapp"
                    class="block text-sm font-medium text-gray-700 mb-1"
                    >WhatsApp</label
                  >

                  <input
                    type="tel"
                    id="whatsapp"
                    formControlName="whatsapp"
                    class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    (input)="onWhatsappInput($event)"
                  />

                  <div class="flex items-center lg:-mb-8">
                    <input
                      type="checkbox"
                      id="isWhatsapp"
                      formControlName="isWhatsapp"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      (change)="onIsWhatsappChange($event)"
                    />
                    <label for="isWhatsapp" class="ml-2 text-sm text-gray-600">
                      Mesmo número do telefone
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- CEP -->
          <div class="col-span-1 w-full">
            <label
              for="addressZipCode"
              class="block text-sm font-medium text-gray-700 mb-1"
              >CEP</label
            >
            <div class="flex">
              <input
                type="text"
                id="addressZipCode"
                formControlName="addressZipCode"
                class="w-full px-3 border border-gray-300 rounded-l-md rounded-r-none focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                (input)="onCepInput($event)"
              />
              <button
                type="button"
                class="px-3 py-2 bg-blue-500 -ml-1 text-white rounded-r-md hover:bg-blue-600 focus:outline-none"
                (click)="buscarCep()"
                [disabled]="isLoadingCep"
              >
                <span *ngIf="!isLoadingCep">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span *ngIf="isLoadingCep" class="flex items-center">
                  <svg
                    class="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                </span>
              </button>
            </div>
          </div>

          <!-- Cidade -->
          <div class="col-span-1">
            <label
              for="addressCity"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Cidade</label
            >
            <input
              type="text"
              id="addressCity"
              formControlName="addressCity"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Estado -->
          <div class="col-span-1">
            <label
              for="addressState"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Estado</label
            >
            <select
              id="addressState"
              formControlName="addressState"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Selecione</option>
              <option *ngFor="let estado of estados" [value]="estado.sigla">
                {{ estado.sigla }} - {{ estado.nome }}
              </option>
            </select>
          </div>

          <!-- Rua -->
          <div class="col-span-1 lg:col-span-2">
            <label
              for="addressStreet"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Endereço</label
            >
            <input
              type="text"
              id="addressStreet"
              formControlName="addressStreet"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Número -->
          <div class="col-span-1">
            <label
              for="addressNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Número</label
            >
            <input
              type="text"
              id="addressNumber"
              formControlName="addressNumber"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Bairro -->
          <div class="col-span-1">
            <label
              for="addressNeighborhood"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Bairro</label
            >
            <input
              type="text"
              id="addressNeighborhood"
              formControlName="addressNeighborhood"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Complemento -->
          <div class="col-span-1 lg:col-span-2">
            <label
              for="addressComplement"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Complemento</label
            >
            <input
              type="text"
              id="addressComplement"
              formControlName="addressComplement"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      <!-- Seção: Dados Complementares -->
      <div class="py-4">
        <h2
          class="text-lg font-semibold text-gray-800 mb-6 pb-2 border-b border-gray-200 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-purple-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
              clip-rule="evenodd"
            />
          </svg>
          Dados Complementares
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Profissão -->
          <div class="col-span-1">
            <label
              for="profession"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Profissão</label
            >
            <input
              type="text"
              id="profession"
              formControlName="profession"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Número do Prontuário -->
          <div class="col-span-1">
            <label
              for="medicalRecordNumber"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Número do Prontuário</label
            >
            <input
              type="text"
              id="medicalRecordNumber"
              formControlName="medicalRecordNumber"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Categoria -->
          <div class="col-span-1">
            <label
              for="category"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Categoria*</label
            >
            <select
              id="category"
              formControlName="category"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option
                *ngFor="let category of patientCategories"
                [value]="category"
              >
                {{ category }}
              </option>
            </select>
          </div>

          <!-- Tipo de Paciente -->
          <div class="col-span-1">
            <app-patient-type-selector
              formControlName="patientTypeId"
              [required]="false"
              [invalid]="false"
              [touched]="
                patientTypeIdControl ? patientTypeIdControl.touched : false
              "
              [placeholder]="'Selecione um tipo'"
            ></app-patient-type-selector>
          </div>

          <!-- Observações -->
          <div class="col-span-1 lg:col-span-4">
            <label
              for="notes"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Observações</label
            >
            <textarea
              id="notes"
              formControlName="notes"
              rows="4"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>
        </div>
      </div>

      <div
        class="mt-8 mb-0 flex justify-end space-x-3 bg-gray-50 p-4 rounded-lg"
      >
        <a
          routerLink="/patients"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center transition-all duration-200 hover:scale-105"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
          Cancelar
        </a>
        <button
          type="button"
          (click)="onSubmit()"
          [disabled]="isSubmitting"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center transition-all duration-200 hover:scale-105"
          [ngClass]="{ 'opacity-75 cursor-not-allowed': isSubmitting }"
        >
          <svg
            *ngIf="isSubmitting"
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <svg
            *ngIf="!isSubmitting"
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clip-rule="evenodd"
            />
          </svg>
          {{ isSubmitting ? "Salvando..." : "Salvar" }}
        </button>
      </div>
    </form>
  </div>
</div>
