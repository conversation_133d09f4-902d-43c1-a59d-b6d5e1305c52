import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { PatientService } from '../../core/services/patient.service';
import { PatientTypeService } from '../../core/services/patient-type.service';
import { CepService } from '../../core/services/cep.service';
import {
  Patient,
  PatientCategory,
  PatientGender,
} from '../../core/models/patient.model';
import { PatientType } from '../../core/models/patient-type.model';
import { ESTADOS_BRASILEIROS, Estado } from '../../core/models/estados';
import { PatientTypeSelectorComponent } from '../../shared/patient-type-selector/patient-type-selector.component';
import { PatientSelectorComponent } from '../../shared/patient-selector/patient-selector.component';
import { maskCPF, maskPhone, maskCEP } from '../../core/utils/input-masks';

@Component({
  selector: 'app-patient-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    PatientTypeSelectorComponent,
    PatientSelectorComponent,
  ],
  templateUrl: './patient-form.component.html',
  styleUrl: './patient-form.component.scss',
})
export class PatientFormComponent implements OnInit {
  patientForm: FormGroup;
  isEditMode = false;
  patientId: number | null = null;
  isSubmitting = false;
  isLoadingCep = false;
  currentPatient: Patient | null = null;
  patientCategories: PatientCategory[] = ['Urgente', 'Rotina', 'Follow-up'];
  patientGenders: { value: PatientGender; label: string }[] = [
    { value: 'male', label: 'Masculino' },
    { value: 'female', label: 'Feminino' },
    { value: 'other', label: 'Outro' },
  ];
  howDidYouFindUsOptions: string[] = [
    'Indicação',
    'Google',
    'Instagram',
    'Facebook',
    'Outro',
  ];
  estados: Estado[] = ESTADOS_BRASILEIROS;
  calculatedAge: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private patientService: PatientService,
    private patientTypeService: PatientTypeService,
    private cepService: CepService
  ) {
    this.patientForm = this.fb.group({
      // Dados Cadastrais
      name: ['', [Validators.required, Validators.minLength(3)]],
      birthDate: ['', [Validators.required]],
      gender: [null],
      cpf: ['', [Validators.required]],
      howDidYouFindUs: [''],
      notes: [''],

      // Contato
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      isWhatsapp: [false], // Novo campo para marcar se o telefone é WhatsApp
      whatsapp: [''],
      addressZipCode: [''],
      addressStreet: [''],
      addressNumber: [''],
      addressNeighborhood: [''],
      addressCity: [''],
      addressState: [''],
      addressComplement: [''],

      // Dados Complementares
      profession: [''],
      medicalRecordNumber: [''],
      category: ['Rotina'],
      patientTypeId: [null],
      referredById: [null],

      // Campos calculados
      age: [{ value: null, disabled: true }],
      registrationDate: [{ value: null, disabled: true }],
    });
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.patientId = +id;
      this.loadPatientData(this.patientId);
    }

    // Configurar o listener para o checkbox de WhatsApp
    this.setupWhatsappSync();
  }

  setupWhatsappSync(): void {
    // Quando o checkbox de WhatsApp mudar
    this.isWhatsappControl?.valueChanges.subscribe((isWhatsapp) => {
      if (isWhatsapp) {
        // Se marcado, copiar o valor do telefone para o WhatsApp
        const phoneValue = this.phoneControl?.value;
        if (phoneValue) {
          this.whatsappControl?.setValue(phoneValue);
        }
      }
    });

    // Quando o telefone mudar e o checkbox estiver marcado
    this.phoneControl?.valueChanges.subscribe((phoneValue) => {
      if (this.isWhatsappControl?.value && phoneValue) {
        this.whatsappControl?.setValue(phoneValue);
      }
    });
  }

  loadPatientData(id: number): void {
    this.patientService.getPatient(id).subscribe(
      (patient) => {
        console.log('Paciente carregado:', patient);
        this.currentPatient = patient;

        // Formatando a data para o formato aceito pelo input type="date"
        const birthDate = patient.birthDate
          ? this.formatDateForInput(new Date(patient.birthDate))
          : '';

        // Calcular a idade com base na data de nascimento
        if (patient.birthDate) {
          this.calculateAge(new Date(patient.birthDate));
        }

        // Formatar a data de registro para exibição
        const registrationDate = patient.registrationDate
          ? this.formatDateForDisplay(new Date(patient.registrationDate))
          : '';

        // Determinar o ID do tipo de paciente
        const patientTypeId = patient.patientType?.id || null;
        console.log('Tipo de paciente ID determinado:', patientTypeId);
        console.log('Objeto patientType:', patient.patientType);

        // Verificar se o WhatsApp é igual ao telefone
        const isWhatsapp =
          patient.whatsapp &&
          patient.phone &&
          patient.whatsapp === patient.phone;

        // Preencher o formulário com os dados do paciente
        // Se for um paciente incompleto, mostrar campos em branco para valores padrão
        const isIncompletePatient = patient.isIncomplete;

        this.patientForm.patchValue({
          // Dados Cadastrais
          name: patient.name,
          birthDate:
            isIncompletePatient &&
            patient.birthDate?.toString() === '1900-01-01'
              ? ''
              : birthDate,
          gender: patient.gender,
          cpf:
            isIncompletePatient && patient.cpf === '000.000.000-00'
              ? ''
              : patient.cpf,
          howDidYouFindUs: patient.howDidYouFindUs,
          notes: patient.notes,

          // Contato
          email:
            isIncompletePatient &&
            patient.email?.includes('temp_') &&
            patient.email?.includes('@temp.com')
              ? ''
              : patient.email,
          phone: patient.phone,
          isWhatsapp: isWhatsapp,
          whatsapp: patient.whatsapp,
          addressZipCode: patient.addressZipCode,
          addressStreet: patient.addressStreet,
          addressNumber: patient.addressNumber,
          addressNeighborhood: patient.addressNeighborhood,
          addressCity: patient.addressCity,
          addressState: patient.addressState,
          addressComplement: patient.addressComplement,

          // Dados Complementares
          profession: patient.profession,
          medicalRecordNumber: patient.medicalRecordNumber,
          category: patient.category || 'Rotina',
          patientTypeId: patientTypeId,
          referredById: patient.referredById,

          // Campos calculados
          age: this.calculatedAge,
          registrationDate: registrationDate,
        });

        // Verificar se o paciente tem um tipo associado
        if (patientTypeId) {
          console.log(
            'Tipo de paciente ID definido no formulário:',
            patientTypeId
          );
        }
      },
      (error) => {
        console.error('Erro ao carregar paciente:', error);
        this.router.navigate(['/patients']);
      }
    );
  }

  calculateAge(birthDate: Date): void {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    this.calculatedAge = age;
    this.patientForm.get('age')?.setValue(age);
  }

  // Método para calcular a idade quando a data de nascimento mudar
  onBirthDateChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.value) {
      this.calculateAge(new Date(input.value));
    } else {
      this.calculatedAge = null;
      this.patientForm.get('age')?.setValue(null);
    }
  }

  // Método para buscar endereço pelo CEP
  buscarCep(): void {
    const cep = this.patientForm.get('addressZipCode')?.value;
    if (!cep || cep.length < 8) return;

    this.isLoadingCep = true;

    this.cepService.consultarCep(cep).subscribe({
      next: (resultado) => {
        if (resultado) {
          this.patientForm.patchValue({
            addressStreet: resultado.logradouro,
            addressNeighborhood: resultado.bairro,
            addressCity: resultado.localidade,
            addressState: resultado.uf,
          });
        }
        this.isLoadingCep = false;
      },
      error: () => {
        this.isLoadingCep = false;
      },
    });
  }

  // Método para atualizar o campo de WhatsApp quando o checkbox mudar
  onIsWhatsappChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    const phoneValue = this.patientForm.get('phone')?.value;

    if (checkbox.checked && phoneValue) {
      this.patientForm.get('whatsapp')?.setValue(phoneValue);
    } else if (checkbox.checked && !phoneValue) {
      // Se o telefone estiver vazio, desmarcar o checkbox
      this.patientForm.get('isWhatsapp')?.setValue(false);
    } else {
      // Se desmarcar o checkbox, limpar o campo de WhatsApp
      this.patientForm.get('whatsapp')?.setValue('');
    }
  }

  // Métodos para aplicar máscaras
  onCpfInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCPF(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.patientForm.get('cpf')?.setValue(maskedValue, { emitEvent: false });
  }

  onPhoneInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.patientForm.get('phone')?.setValue(maskedValue, { emitEvent: false });

    // Se o checkbox estiver marcado, atualiza também o campo de WhatsApp
    if (this.patientForm.get('isWhatsapp')?.value) {
      this.patientForm.get('whatsapp')?.setValue(maskedValue);
    }
  }

  onWhatsappInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.patientForm
      .get('whatsapp')
      ?.setValue(maskedValue, { emitEvent: false });
  }

  onCepInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCEP(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.patientForm
      .get('addressZipCode')
      ?.setValue(maskedValue, { emitEvent: false });
  }

  formatDateForDisplay(date: Date): string {
    return date.toLocaleDateString('pt-BR');
  }

  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onSubmit(): void {
    if (this.patientForm.invalid) {
      // Marca todos os campos como touched para mostrar os erros
      Object.keys(this.patientForm.controls).forEach((key) => {
        const control = this.patientForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    const formValues = this.patientForm.value;
    console.log('Valores do formulário:', formValues);
    console.log('Tipo de paciente selecionado (ID):', formValues.patientTypeId);

    // Certifique-se de que todos os campos obrigatórios estão presentes
    if (
      !formValues.name ||
      !formValues.email ||
      !formValues.phone ||
      !formValues.cpf ||
      !formValues.birthDate
    ) {
      console.error('Campos obrigatórios ausentes:', {
        name: formValues.name,
        email: formValues.email,
        phone: formValues.phone,
        cpf: formValues.cpf,
        birthDate: formValues.birthDate,
      });
      this.isSubmitting = false;
      return;
    }

    // Atualizar a idade calculada quando o usuário submeter o formulário
    if (formValues.birthDate) {
      this.calculateAge(new Date(formValues.birthDate));
    }

    const patientData = {
      // Dados Cadastrais
      name: formValues.name,
      birthDate: new Date(formValues.birthDate),
      gender: formValues.gender,
      cpf: formValues.cpf,
      howDidYouFindUs: formValues.howDidYouFindUs || '',
      notes: formValues.notes || '',

      // Contato
      email: formValues.email,
      phone: formValues.phone,
      whatsapp: formValues.whatsapp || '',
      addressZipCode: formValues.addressZipCode || '',
      addressStreet: formValues.addressStreet || '',
      addressNumber: formValues.addressNumber || '',
      addressNeighborhood: formValues.addressNeighborhood || '',
      addressCity: formValues.addressCity || '',
      addressState: formValues.addressState || '',
      addressComplement: formValues.addressComplement || '',

      // Dados Complementares
      profession: formValues.profession || '',
      medicalRecordNumber: formValues.medicalRecordNumber || '',
      category: formValues.category || 'Rotina',
      patientTypeId:
        formValues.patientTypeId !== undefined
          ? formValues.patientTypeId
          : null,
      referredById:
        formValues.referredById !== undefined ? formValues.referredById : null,
    };

    console.log('Dados do paciente a serem salvos:', patientData);

    try {
      if (this.isEditMode && this.patientId) {
        this.patientService
          .updatePatient(this.patientId, patientData)
          .subscribe({
            next: (updatedPatient) => {
              console.log('Paciente atualizado com sucesso:', updatedPatient);
              console.log(
                'Tipo de paciente após atualização:',
                updatedPatient.patientTypeId
              );
              this.isSubmitting = false;
              // Redirecionar para a página de detalhes do paciente em vez da lista
              this.router.navigate(['/patients', this.patientId]);
            },
            error: (error) => {
              console.error('Erro ao atualizar paciente:', error);
              this.isSubmitting = false;
            },
          });
      } else {
        this.patientService.createPatient(patientData).subscribe({
          next: (newPatient) => {
            console.log('Paciente criado com sucesso:', newPatient);
            console.log(
              'Tipo de paciente do novo paciente:',
              newPatient.patientTypeId
            );
            this.isSubmitting = false;
            this.router.navigate(['/patients', newPatient.id]);
          },
          error: (error) => {
            console.error('Erro ao criar paciente:', error);
            this.isSubmitting = false;
          },
        });
      }
    } catch (error) {
      console.error('Erro ao salvar paciente:', error);
      this.isSubmitting = false;
    }
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  // Dados Cadastrais
  get nameControl() {
    return this.patientForm.get('name');
  }
  get birthDateControl() {
    return this.patientForm.get('birthDate');
  }
  get genderControl() {
    return this.patientForm.get('gender');
  }
  get cpfControl() {
    return this.patientForm.get('cpf');
  }
  get howDidYouFindUsControl() {
    return this.patientForm.get('howDidYouFindUs');
  }
  get notesControl() {
    return this.patientForm.get('notes');
  }

  // Contato
  get emailControl() {
    return this.patientForm.get('email');
  }
  get phoneControl() {
    return this.patientForm.get('phone');
  }
  get whatsappControl() {
    return this.patientForm.get('whatsapp');
  }

  get isWhatsappControl() {
    return this.patientForm.get('isWhatsapp');
  }
  get addressZipCodeControl() {
    return this.patientForm.get('addressZipCode');
  }
  get addressStreetControl() {
    return this.patientForm.get('addressStreet');
  }
  get addressNumberControl() {
    return this.patientForm.get('addressNumber');
  }
  get addressNeighborhoodControl() {
    return this.patientForm.get('addressNeighborhood');
  }
  get addressCityControl() {
    return this.patientForm.get('addressCity');
  }
  get addressStateControl() {
    return this.patientForm.get('addressState');
  }
  get addressComplementControl() {
    return this.patientForm.get('addressComplement');
  }

  // Dados Complementares
  get professionControl() {
    return this.patientForm.get('profession');
  }
  get medicalRecordNumberControl() {
    return this.patientForm.get('medicalRecordNumber');
  }
  get categoryControl() {
    return this.patientForm.get('category');
  }
  get patientTypeIdControl() {
    return this.patientForm.get('patientTypeId');
  }

  get referredByIdControl() {
    return this.patientForm.get('referredById');
  }

  // Campos calculados
  get ageControl() {
    return this.patientForm.get('age');
  }
  get registrationDateControl() {
    return this.patientForm.get('registrationDate');
  }
}
