import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PatientService } from '../../core/services/patient.service';
import { Patient, PatientCategory } from '../../core/models/patient.model';
import { FormsModule } from '@angular/forms';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-patient-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './patient-list.component.html',
  styleUrl: './patient-list.component.scss',
})
export class PatientListComponent implements OnInit {
  patients: Patient[] = [];
  filteredPatients: Patient[] = [];
  searchTerm: string = '';
  selectedCategory: string = '';
  patientCategories: PatientCategory[] = ['Urgente', 'Rotina', 'Follow-up'];

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;
  isLoading: boolean = false;

  constructor(private patientService: PatientService) {}

  ngOnInit(): void {
    // Carrega os dados do serviço
    this.loadPatients();
  }

  loadPatients(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.patientService
      .getPatients(
        page,
        this.itemsPerPage,
        this.searchTerm,
        this.selectedCategory
      )
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response: PaginatedResponse<Patient>) => {
          this.patients = response.data;
          this.filteredPatients = [...this.patients];
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },
        error: (error) => {
          console.error('Erro ao carregar pacientes:', error);
        },
      });
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadPatients(this.currentPage);
  }

  searchPatients(event: Event): void {
    const value = (event.target as HTMLInputElement).value.toLowerCase();
    this.searchTerm = value;
    this.applyFilters();
  }

  filterByCategory(category: string): void {
    this.selectedCategory = category;
    this.applyFilters();
  }

  applyFilters(): void {
    // Quando aplicamos filtros, voltamos para a primeira página
    this.currentPage = 1;
    this.loadPatients(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadPatients(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  deletePatient(id: number): void {
    if (confirm('Tem certeza que deseja excluir este paciente?')) {
      this.patientService.deletePatient(id).subscribe(() => {
        // Após excluir, recarregar a página atual (ou a primeira, se não houver mais itens)
        this.loadPatients(this.currentPage);
      });
    }
  }

  // Método para formatar o endereço completo a partir dos campos individuais
  formatAddress(patient: any): string {
    if (!patient) return '';

    const parts = [];

    if (patient.addressStreet) {
      let streetPart = patient.addressStreet;
      if (patient.addressNumber) {
        streetPart += `, ${patient.addressNumber}`;
      }
      parts.push(streetPart);
    }

    if (patient.addressNeighborhood) {
      parts.push(patient.addressNeighborhood);
    }

    let cityState = '';
    if (patient.addressCity) {
      cityState = patient.addressCity;
      if (patient.addressState) {
        cityState += ` - ${patient.addressState}`;
      }
      parts.push(cityState);
    } else if (patient.addressState) {
      parts.push(patient.addressState);
    }

    return parts.join(', ');
  }
}
