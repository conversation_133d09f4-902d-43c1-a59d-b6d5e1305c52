import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewInit,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Patient } from '../../core/models/patient.model';
import { PatientService } from '../../core/services/patient.service';
import { MedicalRecordService } from '../../core/services/medical-record.service';
import { catchError, of, Subscription, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TreatmentPlanUpdateService } from '../../core/services/treatment-plan-update.service';
import { PatientRegistrationComponent } from './tabs/patient-registration/patient-registration.component';
import { PatientAppointmentsComponent } from './tabs/patient-appointments/patient-appointments.component';
import { PatientBudgetsComponent } from './tabs/patient-budgets/patient-budgets.component';
import { PatientClinicalRecordComponent } from './tabs/patient-clinical-record/patient-clinical-record.component';
import { PatientReceiptsComponent } from './tabs/patient-receipts/patient-receipts.component';
import { PatientIndicationsComponent } from './tabs/patient-indications/patient-indications.component';
import { PatientAnamnesisComponent } from './tabs/patient-anamnesis/patient-anamnesis.component';
import { PatientPhotosComponent } from './tabs/patient-photos/patient-photos.component';
import { PatientExamsComponent } from './tabs/patient-exams/patient-exams.component';
import { PatientDocumentsComponent } from './tabs/patient-documents/patient-documents.component';
import { PatientOdontogramComponent } from './tabs/patient-odontogram/patient-odontogram.component';
import { MedicalRecordModalComponent } from '../../medical-records/medical-record-modal/medical-record-modal.component';
import {
  TreatmentPlan,
  TreatmentPlanStatus,
} from '../../core/models/treatment-plan.model';
import {
  TreatmentProcedure,
  TreatmentProcedureStatus,
} from '../../core/models/treatment-procedure.model';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { DentistService } from '../../core/services/dentist.service';
import { Dentist } from '../../core/models/dentist.model';
import { ActionButtonComponent } from '../../shared/components/action-button/action-button.component';

@Component({
  selector: 'app-patient-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    PatientRegistrationComponent,
    PatientAppointmentsComponent,
    PatientBudgetsComponent,
    PatientClinicalRecordComponent,
    PatientReceiptsComponent,
    PatientIndicationsComponent,
    PatientAnamnesisComponent,
    PatientPhotosComponent,
    PatientExamsComponent,
    PatientDocumentsComponent,
    PatientOdontogramComponent,
    MedicalRecordModalComponent,
    ActionButtonComponent,
  ],
  templateUrl: './patient-detail.component.html',
  styleUrl: './patient-detail.component.scss',
})
export class PatientDetailComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @ViewChild('tabsContainer') tabsContainer!: ElementRef<HTMLDivElement>;

  patient: Patient | null = null;
  activeTab: string = 'cadastro';
  isLoading = true;

  // Caminhos SVG para os ícones
  SVG_PATHS = {
    delete:
      'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16',
    block:
      'M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636',
    print:
      'M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z',
    export:
      'M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4',
    whatsapp:
      'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
  };

  // Controle do modal de prontuário
  showMedicalRecordModal = false;
  hasMedicalRecord = false;
  isCheckingMedicalRecord = false;

  // Propriedades para a aba "Plano e Ficha Clínica"
  treatmentPlans: TreatmentPlan[] = [];
  expandedPlanId: number | null = null;
  dentists: Dentist[] = [];

  // Ficha clínica
  allProcedures: TreatmentProcedure[] = [];
  filteredProcedures: TreatmentProcedure[] = [];
  statusFilter: TreatmentProcedureStatus | 'all' = 'all';

  // Subscription para gerenciar a inscrição no serviço de atualização
  private treatmentPlanUpdateSubscription: Subscription | null = null;

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 1;

  // Referência ao objeto Math para usar no template
  Math = Math;

  // Adicionar propriedade para controlar o dropdown
  isActionMenuOpen = false;

  // Subject para gerenciar inscrições
  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private patientService: PatientService,
    private medicalRecordService: MedicalRecordService,
    private treatmentPlanService: TreatmentPlanService,
    private dentistService: DentistService,
    private treatmentPlanUpdateService: TreatmentPlanUpdateService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      // Inscrever-se para receber atualizações do plano de tratamento
      this.treatmentPlanUpdateSubscription =
        this.treatmentPlanUpdateService.treatmentPlanUpdated$.subscribe(
          (updatedPlan) => {
            console.log(
              'Plano de tratamento atualizado recebido no componente de detalhes do paciente:',
              updatedPlan
            );
            if (updatedPlan && this.treatmentPlans.length > 0) {
              // Encontrar e atualizar o plano na lista
              const index = this.treatmentPlans.findIndex(
                (plan) => plan.id === updatedPlan.id
              );
              if (index !== -1) {
                console.log(
                  'Atualizando plano de tratamento na interface de detalhes do paciente'
                );
                this.treatmentPlans[index] = updatedPlan;
              }
            }
          }
        );

      this.loadPatientData(+id);
      this.loadDentists();
    } else {
      this.router.navigate(['/patients']);
    }
  }

  ngAfterViewInit(): void {
    // Após a renderização, rolar até a aba ativa
    setTimeout(() => {
      this.scrollToActiveTab();
    }, 300);
  }

  ngOnDestroy(): void {
    // Cancelar a inscrição para evitar vazamentos de memória
    if (this.treatmentPlanUpdateSubscription) {
      this.treatmentPlanUpdateSubscription.unsubscribe();
    }

    // Completar o Subject para cancelar todas as inscrições
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Métodos para controlar a rolagem do carrossel
  scrollTabsLeft(): void {
    if (!this.tabsContainer) return;

    const container = this.tabsContainer.nativeElement;
    const scrollAmount = container.clientWidth * 0.75; // Rolar 75% da largura visível
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
  }

  scrollTabsRight(): void {
    if (!this.tabsContainer) return;

    const container = this.tabsContainer.nativeElement;
    const scrollAmount = container.clientWidth * 0.75; // Rolar 75% da largura visível
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }

  /**
   * Verifica se o paciente já possui prontuário
   */
  checkIfPatientHasMedicalRecord(): void {
    if (!this.patient) return;

    this.isCheckingMedicalRecord = true;

    // Armazenar o ID do paciente em uma variável local para evitar problemas de tipo
    const patientId = this.patient.id;

    // Usar o método getMedicalRecordsByPatient diretamente para garantir resultados precisos
    this.medicalRecordService
      .getMedicalRecordsByPatient(patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (records) => {
          // Verificar se há registros e atualizar o estado
          const hasRecords = records && records.length > 0;
          console.log(
            `Paciente ${patientId} ${
              hasRecords ? 'possui' : 'não possui'
            } prontuário.`
          );
          this.hasMedicalRecord = hasRecords;
          this.isCheckingMedicalRecord = false;
        },
        error: (error) => {
          console.error('Erro ao verificar prontuário:', error);
          this.hasMedicalRecord = false;
          this.isCheckingMedicalRecord = false;
        },
      });
  }

  scrollToActiveTab(): void {
    if (!this.tabsContainer) return;

    const container = this.tabsContainer.nativeElement;
    const activeTabButton = container.querySelector(
      `button[class*="border-blue-500"]`
    ) as HTMLElement;

    if (activeTabButton) {
      // Calcular a posição para centralizar a aba ativa
      const containerWidth = container.clientWidth;
      const buttonLeft = activeTabButton.offsetLeft;
      const buttonWidth = activeTabButton.offsetWidth;

      const scrollPosition = buttonLeft - containerWidth / 2 + buttonWidth / 2;
      container.scrollTo({
        left: Math.max(0, scrollPosition),
        behavior: 'smooth',
      });
    }
  }

  // Métodos para a aba "Plano e Ficha Clínica"
  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
      },
    });
  }

  loadTreatmentPlans(): void {
    if (!this.patient?.id) return;

    this.treatmentPlanService
      .getTreatmentPlansByPatient(this.patient.id)
      .subscribe({
        next: (plans) => {
          this.treatmentPlans = plans;
        },
        error: (error) => {
          console.error('Erro ao carregar planos de tratamento:', error);
        },
      });
  }

  loadAllProcedures(): void {
    if (!this.patient?.id) return;

    this.treatmentPlanService
      .getAllProceduresByPatient(this.patient.id)
      .subscribe({
        next: (procedures) => {
          this.allProcedures = procedures;
          this.applyFilters();
        },
        error: (error) => {
          console.error('Erro ao carregar procedimentos:', error);
        },
      });
  }

  applyFilters(): void {
    let filtered = [...this.allProcedures];

    // Aplicar filtro de status
    if (this.statusFilter !== 'all') {
      filtered = filtered.filter((proc) => proc.status === this.statusFilter);
    }

    // Ordenar por data de execução (mais recentes primeiro)
    filtered.sort((a, b) => {
      const dateA = a.executionDate ? new Date(a.executionDate).getTime() : 0;
      const dateB = b.executionDate ? new Date(b.executionDate).getTime() : 0;
      return dateB - dateA;
    });

    this.totalItems = filtered.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);

    // Aplicar paginação
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.filteredProcedures = filtered.slice(
      startIndex,
      startIndex + this.pageSize
    );
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.applyFilters();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1; // Resetar para a primeira página
    this.applyFilters();
  }

  togglePlanDetails(planId: number): void {
    this.expandedPlanId = this.expandedPlanId === planId ? null : planId;
  }

  getPendingProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    return plan.procedures.filter(
      (proc) =>
        proc.status === TreatmentProcedureStatus.PENDING ||
        proc.status === TreatmentProcedureStatus.IN_PROGRESS
    );
  }

  getCompletedProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    return plan.procedures.filter(
      (proc) => proc.status === TreatmentProcedureStatus.COMPLETED
    );
  }

  getPlanStatusClass(status: TreatmentPlanStatus): string {
    switch (status) {
      case TreatmentPlanStatus.OPEN:
        return 'bg-blue-100 text-blue-800';
      case TreatmentPlanStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TreatmentPlanStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getProcedureStatusClass(status: TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentProcedureStatus.PENDING:
        return 'bg-blue-100 text-blue-800';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'bg-orange-100 text-orange-800';
      case TreatmentProcedureStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TreatmentProcedureStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPlanStatusLabel(status: TreatmentPlanStatus): string {
    switch (status) {
      case TreatmentPlanStatus.OPEN:
        return 'Em aberto';
      case TreatmentPlanStatus.COMPLETED:
        return 'Concluído';
      case TreatmentPlanStatus.CANCELLED:
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  getProcedureStatusLabel(status: TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentProcedureStatus.PENDING:
        return 'Pendente';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'Em andamento';
      case TreatmentProcedureStatus.COMPLETED:
        return 'Concluído';
      case TreatmentProcedureStatus.CANCELLED:
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  formatValue(value: number): string {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }

  formatDate(date: Date | string | null | undefined): string {
    if (!date) return 'Não informado';
    return new Date(date).toLocaleDateString('pt-BR');
  }

  formatBudgetNotes(notes: string | undefined | null): string {
    if (!notes) return 'Sem observações';
    try {
      return notes.length > 30 ? notes.substring(0, 30) + '...' : notes;
    } catch (error) {
      return 'Sem observações';
    }
  }

  getDentistName(dentistId: number): string {
    const dentist = this.dentists.find((d) => d.id === dentistId);
    return dentist ? dentist.name : 'Não informado';
  }

  onDentistChange(procedure: TreatmentProcedure, event: Event): void {
    const select = event.target as HTMLSelectElement;
    const dentistId = Number(select.value);

    if (!procedure.id) return;

    const updateDto = {
      professionalId: dentistId,
    };

    this.treatmentPlanService
      .updateProcedure(procedure.id, updateDto)
      .subscribe({
        next: (updatedProcedure) => {
          // Atualizar o procedimento na lista
          const plan = this.treatmentPlans.find(
            (p) => p.id === procedure.treatmentPlanId
          );
          if (plan) {
            const index = plan.procedures.findIndex(
              (p) => p.id === procedure.id
            );
            if (index !== -1) {
              plan.procedures[index] = updatedProcedure;
            }
          }
        },
        error: (error) => {
          console.error('Erro ao atualizar dentista do procedimento:', error);
        },
      });
  }

  executeProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    const updateDto = {
      status: TreatmentProcedureStatus.IN_PROGRESS,
      executionDate: new Date(),
    };

    this.treatmentPlanService
      .updateProcedure(procedure.id, updateDto)
      .subscribe({
        next: (updatedProcedure) => {
          // Não precisamos atualizar manualmente, pois o serviço já notifica sobre a atualização
          console.log('Procedimento atualizado com sucesso:', updatedProcedure);
        },
        error: (error) => {
          console.error('Erro ao executar procedimento:', error);
        },
      });
  }

  finishProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    const updateDto = {
      status: TreatmentProcedureStatus.COMPLETED,
    };

    this.treatmentPlanService
      .updateProcedure(procedure.id, updateDto)
      .subscribe({
        next: (updatedProcedure) => {
          // Não precisamos atualizar manualmente, pois o serviço já notifica sobre a atualização
          console.log('Procedimento finalizado com sucesso:', updatedProcedure);
        },
        error: (error) => {
          console.error('Erro ao finalizar procedimento:', error);
        },
      });
  }

  cancelExecution(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    if (
      confirm(
        `Tem certeza que deseja cancelar a execução do procedimento "${procedure.name}"?`
      )
    ) {
      const updateDto = {
        status: TreatmentProcedureStatus.PENDING,
        executionDate: null,
      };

      this.treatmentPlanService
        .updateProcedure(procedure.id, updateDto)
        .subscribe({
          next: (updatedProcedure) => {
            // Não precisamos atualizar manualmente, pois o serviço já notifica sobre a atualização
            console.log(
              'Execução do procedimento cancelada com sucesso:',
              updatedProcedure
            );
          },
          error: (error) => {
            console.error('Erro ao cancelar execução do procedimento:', error);
          },
        });
    }
  }

  deleteProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    if (
      confirm(
        `Tem certeza que deseja remover o procedimento "${procedure.name}" do plano de tratamento?`
      )
    ) {
      this.treatmentPlanService.deleteProcedure(procedure.id).subscribe({
        next: () => {
          // Não precisamos atualizar manualmente, pois o serviço já notifica sobre a atualização
          console.log('Procedimento removido com sucesso');
        },
        error: (error) => {
          console.error('Erro ao remover procedimento:', error);
        },
      });
    }
  }

  openProcedureModal(procedure: TreatmentProcedure): void {
    alert(
      `Funcionalidade em desenvolvimento: Edição do procedimento "${procedure.name}"`
    );
    // Aqui implementaremos a abertura do modal de edição de procedimento
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];

    if (this.totalPages <= 5) {
      // Se tiver 5 páginas ou menos, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Se tiver mais de 5 páginas, mostrar estrategicamente
      if (this.currentPage <= 3) {
        // Início: mostrar as 3 primeiras, ... e a última
        pages.push(1, 2, 3, this.totalPages);
      } else if (this.currentPage >= this.totalPages - 2) {
        // Final: mostrar a primeira, ... e as 3 últimas
        pages.push(
          1,
          this.totalPages - 2,
          this.totalPages - 1,
          this.totalPages
        );
      } else {
        // Meio: mostrar a primeira, a atual e suas adjacentes, e a última
        pages.push(
          1,
          this.currentPage - 1,
          this.currentPage,
          this.currentPage + 1,
          this.totalPages
        );
      }
    }

    return pages;
  }

  loadPatientData(id: number): void {
    this.isLoading = true;

    this.patientService
      .getPatient(id)
      .pipe(
        catchError((error) => {
          console.error('Erro ao carregar paciente:', error);
          return of(null);
        })
      )
      .subscribe({
        next: (patient) => {
          this.patient = patient;
          this.isLoading = false;

          // Verificar se o paciente tem prontuário
          this.checkIfPatientHasMedicalRecord();

          // Carregar planos de tratamento e procedimentos para a aba "Plano e Ficha Clínica"
          if (this.activeTab === 'clinical-record') {
            this.loadTreatmentPlans();
            this.loadAllProcedures();
          }
        },
        error: (error) => {
          console.error('Erro ao carregar dados do paciente:', error);
          this.isLoading = false;
          this.router.navigate(['/patients']);
        },
      });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Carregar dados específicos da aba quando ela for selecionada
    if (tab === 'clinical-record' && this.patient) {
      this.loadTreatmentPlans();
      this.loadAllProcedures();
    }

    // Rolar até a aba selecionada após uma pequena pausa para permitir a renderização
    setTimeout(() => {
      this.scrollToActiveTab();
    }, 100);

    console.log('Aba ativa:', tab);
  }

  openMedicalRecordModal(): void {
    if (!this.patient) return;

    if (this.hasMedicalRecord) {
      // Se já tem prontuário, apenas abrir o modal
      this.showMedicalRecordModal = true;
    } else {
      // Se não tem prontuário, criar um novo
      this.createAndOpenMedicalRecord();
    }
  }

  closeMedicalRecordModal(): void {
    this.showMedicalRecordModal = false;

    // Verificar novamente se o paciente tem prontuário após fechar o modal
    // Isso garante que o estado seja atualizado se um prontuário foi criado
    if (this.patient) {
      this.checkIfPatientHasMedicalRecord();
    }
  }

  /**
   * Cria um novo prontuário para o paciente e abre o modal
   */
  createAndOpenMedicalRecord(): void {
    if (!this.patient) return;

    this.isCheckingMedicalRecord = true;

    // Armazenar o ID do paciente em uma variável local para evitar problemas de tipo
    const patientId = this.patient.id;

    // Primeiro, verificar se o paciente já possui prontuário
    this.medicalRecordService
      .checkPatientHasMedicalRecord(patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (hasMedicalRecord) => {
          if (hasMedicalRecord) {
            // Se já tem prontuário, apenas atualizar o estado e abrir o modal
            this.hasMedicalRecord = true;
            this.isCheckingMedicalRecord = false;
            this.showMedicalRecordModal = true;
          } else {
            // Se não tem prontuário, criar um novo
            this.medicalRecordService
              .createPatientMedicalRecord(patientId)
              .pipe(takeUntil(this.destroy$))
              .subscribe({
                next: () => {
                  this.hasMedicalRecord = true;
                  this.isCheckingMedicalRecord = false;
                  this.showMedicalRecordModal = true;
                },
                error: (error) => {
                  console.error('Erro ao criar prontuário:', error);
                  this.isCheckingMedicalRecord = false;
                  alert(
                    'Não foi possível criar o prontuário. Por favor, tente novamente.'
                  );
                },
              });
          }
        },
        error: (error) => {
          console.error('Erro ao verificar prontuário:', error);
          this.isCheckingMedicalRecord = false;
          alert(
            'Não foi possível verificar se o paciente já possui prontuário. Por favor, tente novamente.'
          );
        },
      });
  }

  // Método antigo - mantido para compatibilidade
  openMedicalRecord(): void {
    if (!this.patient) return;

    const patientId = this.patient.id;

    // Verificar se já existe um prontuário para este paciente
    this.medicalRecordService.getMedicalRecordsByPatient(patientId).subscribe({
      next: (medicalRecords) => {
        if (medicalRecords && medicalRecords.length > 0) {
          // Se já existe um prontuário, redirecionar para a visualização
          this.router.navigate(['/medical-records', medicalRecords[0].id], {
            queryParams: { returnUrl: `/patients/${patientId}` },
          });
        } else {
          // Se não existe, redirecionar para a criação
          this.router.navigate(['/medical-records/new'], {
            queryParams: {
              patientId: patientId,
              returnUrl: `/patients/${patientId}`,
            },
          });
        }
      },
      error: () => {
        // Em caso de erro, redirecionar para a criação
        this.router.navigate(['/medical-records/new'], {
          queryParams: {
            patientId: patientId,
            returnUrl: `/patients/${patientId}`,
          },
        });
      },
    });
  }

  // Adicionar métodos para controlar o dropdown
  toggleActionMenu(event: Event): void {
    event.stopPropagation(); // Impedir que o clique se propague para o documento
    this.isActionMenuOpen = !this.isActionMenuOpen;
  }

  closeActionMenu(): void {
    this.isActionMenuOpen = false;
  }

  // Adicionar listener para fechar o dropdown ao clicar fora
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Verificar se o clique foi fora do dropdown
    const target = event.target as HTMLElement;
    if (!target.closest('.relative') && this.isActionMenuOpen) {
      this.isActionMenuOpen = false;
    }
  }

  deletePatient(): void {
    if (
      confirm(
        'Tem certeza que deseja excluir este paciente? Esta ação não pode ser desfeita.'
      )
    ) {
      // Implementar lógica de exclusão
      console.log('Excluindo paciente:', this.patient?.id);
      // this.patientService.deletePatient(this.patient.id).subscribe(...)
    }
  }

  togglePatientStatus(): void {
    // Implementar lógica para bloquear/desbloquear paciente
    console.log('Alterando status do paciente:', this.patient?.id);
    // this.patientService.toggleStatus(this.patient.id).subscribe(...)
  }

  sendWhatsAppMessage(): void {
    if (this.patient?.phone) {
      // Formatar número de telefone (remover caracteres não numéricos)
      const phoneNumber = this.patient.phone.replace(/\D/g, '');
      // Abrir WhatsApp com o número do paciente
      window.open(`https://wa.me/55${phoneNumber}`, '_blank');
    } else {
      alert('Este paciente não possui número de telefone cadastrado.');
    }
  }

  printPatientData(): void {
    if (!this.patient) return;

    // Criar um iframe oculto para impressão
    const printFrame = document.createElement('iframe');
    printFrame.style.position = 'absolute';
    printFrame.style.width = '0';
    printFrame.style.height = '0';
    printFrame.style.border = 'none';
    printFrame.style.visibility = 'hidden';

    document.body.appendChild(printFrame);

    // Obter o documento do iframe
    const frameDoc =
      printFrame.contentDocument || printFrame.contentWindow?.document;
    if (!frameDoc) {
      alert('Falha ao criar documento de impressão.');
      return;
    }

    // Inserir o conteúdo gerado no documento do iframe
    frameDoc.open();
    frameDoc.write(this.generatePrintContent());
    frameDoc.close();

    // Imprimir quando o conteúdo estiver carregado
    printFrame.onload = () => {
      setTimeout(() => {
        if (printFrame.contentWindow) {
          printFrame.contentWindow.print();
        }

        // Remover o iframe após a impressão
        setTimeout(() => {
          document.body.removeChild(printFrame);
        }, 500);
      }, 100);
    };
  }

  private generatePrintContent(): string {
    if (!this.patient) return '';

    // Data atual formatada
    const currentDate = new Date().toLocaleDateString('pt-BR');

    // Gerar nome do arquivo PDF para o título
    const patientName = this.patient.name || 'paciente';
    const sanitizedPatientName = patientName
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '_');
    const isoDate = new Date().toISOString().slice(0, 10); // formato YYYY-MM-DD
    const fileName = `Paciente_${sanitizedPatientName}_${isoDate}`;

    const baseUrl = window.location.origin;
    const logoPath = `${baseUrl}/assets/logo-moderno.svg`;

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${fileName}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              font-size: 12px;
              line-height: 1.4;
            }
            .container {
              max-width: 800px;
              margin: auto;
              border: 1px solid #ddd;
              padding: 20px;
            }
            .header {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-bottom: 10px;
              border-bottom: 1px solid #eee;
              padding-bottom: 10px;
              text-align: center;
            }
            .logo-container {
              width: 100%;
              display: flex;
              justify-content: center;
              margin-bottom: 5px;
            }
            .logo {
              height: 120px;
            }
            .date-section {
              width: auto;
              text-align: right;
              float: right;
            }
            .title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 15px;
              display: inline-block;
            }
            .header-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              width: 100%;
              margin-bottom: 20px;
            }
            .clinic-info {
              margin-bottom: 15px;
            }
            .patient-info {
              margin-bottom: 10px;
            }
            .section-title {
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
            }
            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 10px;
              margin-bottom: 15px;
            }
            .info-item {
              margin-bottom: 5px;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
            }
            .page-number {
              text-align: right;
              margin-top: 20px;
              font-size: 10px;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="container">
            
            <div class="header">
              <div class="logo-container">
                <img class="logo" src="${logoPath}" alt="Logo CRM Odonto">
              </div>
            </div>
            
            <div class="header-row">
              <div class="title">Ficha do Paciente</div>
              <div class="date-section">
                <strong>Data:</strong> ${currentDate}
              </div>
            </div>
            
            <div class="section-title">Dados Básicos</div>
            <div class="info-grid">
              <div class="info-item"><strong>Nome:</strong> ${
                this.patient.name
              }</div>
              <div class="info-item"><strong>Nascimento:</strong> ${this.formatDate(
                this.patient.birthDate
              )}</div>
              <div class="info-item"><strong>CPF:</strong> ${this.getDisplayValue(
                this.patient.cpf
              )}</div>
              <div class="info-item"><strong>Idade:</strong> ${this.calculateAge(
                this.patient.birthDate
              )} anos</div>
              <div class="info-item"><strong>Sexo:</strong> ${
                this.patient.gender?.charAt(0).toUpperCase() || 'Não informado'
              }</div>
              <div class="info-item"><strong>Como conheceu:</strong> ${
                this.patient.howDidYouFindUs || 'Não informado'
              }</div>
              <div class="info-item"><strong>Cadastrado em:</strong> ${this.formatDateTime(
                this.patient.registrationDate
              )}</div>
            </div>
            
            <div class="section-title">Contato</div>
            <div class="info-grid">
              <div class="info-item"><strong>Celular:</strong> ${
                this.patient.phone || 'Não informado'
              }</div>
              <div class="info-item"><strong>Email:</strong> ${this.getDisplayValue(
                this.patient.email
              )}</div>
              <div class="info-item"><strong>Endereço:</strong> ${
                this.patient.addressStreet || 'Não informado'
              }</div>
              <div class="info-item"><strong>Número:</strong> ${
                this.patient.addressNumber || 'Não informado'
              }</div>
              <div class="info-item"><strong>Bairro:</strong> ${
                this.patient.addressNeighborhood || 'Não informado'
              }</div>
              <div class="info-item"><strong>Cidade:</strong> ${
                this.patient.addressCity || 'Não informado'
              }</div>
              <div class="info-item"><strong>Estado:</strong> ${
                this.patient.addressState || 'Não informado'
              }</div>
            </div>
            
            <div class="section-title">Dados Adicionais</div>
            <div class="info-grid">
              <div class="info-item"><strong>Número de Prontuário:</strong> ${
                this.patient.medicalRecordNumber || 'Não atribuído'
              }</div>
            </div>
            
            <div class="section-title">Observações</div>
            <div>${this.patient.notes || 'Sem observações'}</div>
            
            <div class="footer">
              https://sistema.clinicorp.com
            </div>
            
            <div class="page-number">1/1</div>
          </div>
        </body>
      </html>
    `;
  }

  formatDateTime(date: Date | undefined): string {
    if (!date) return '';
    const dateObj = new Date(date);
    return dateObj.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false, // Formato 24 horas
    });
  }

  private calculateAge(birthDate: Date | undefined): any {
    if (!birthDate) return undefined;
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }
    return age;
  }

  // Método para exibir valores, retornando "Não informado" quando vazio
  getDisplayValue(value: string | undefined): string {
    return value || 'Não informado';
  }
}
