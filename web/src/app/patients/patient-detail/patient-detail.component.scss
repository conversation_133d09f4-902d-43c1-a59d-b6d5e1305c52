/* Patient Detail - Estilos customizados mínimos */

/* Carousel de tabs */
.tabs-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.carousel-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #578cd6;
}

.carousel-nav-btn:hover {
  background-color: #f3f4f6;
  color: #1e40af;
}

.carousel-nav-left {
  left: 0;
}

.carousel-nav-right {
  right: 0;
}

/* Container de tabs */
.tabs-container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  scroll-behavior: smooth;
  padding: 0 36px;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  flex: 1;
}

.tabs-container::-webkit-scrollbar {
  display: none;
}

.tabs-container button {
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.tabs-container button:focus {
  outline: none;
}

.tabs-container button.border-blue-500 {
  font-weight: 600;
}

/* Textarea mínima */
textarea {
  min-height: 100px;
}

/* Checkbox */
.checkbox {
  width: 20px !important;
}

/* Dropdown animação */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.absolute {
  animation: fadeIn 0.2s ease-out;
}

/* Mobile responsivo */
@media (max-width: 768px) {
  .tabs-container button {
    padding-left: 12px;
    padding-right: 12px;
    font-size: 0.8rem;
  }

  .carousel-nav-btn {
    width: 24px !important;
    height: 24px !important;
  }

  .carousel-nav-btn svg {
    width: 16px;
    height: 16px;
  }
}
