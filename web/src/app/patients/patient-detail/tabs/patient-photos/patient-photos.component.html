<div class="photos-container">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="p-4 bg-red-50 text-red-700 rounded-md">
    <p class="font-medium">{{ error }}</p>
  </div>

  <!-- Actions toolbar -->
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800">Fotos do Paciente</h2>
    <div class="flex space-x-2">
      <button
        (click)="openCreateFolderModal()"
        class="px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
          />
          <path
            fill-rule="evenodd"
            d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
            clip-rule="evenodd"
          />
        </svg>
        Nova Pasta
      </button>
      <button
        (click)="openPhotoUploadModal()"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
        Upload de Fotos
      </button>
      <button
        (click)="openEnhancedCameraModal()"
        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
            clip-rule="evenodd"
          />
        </svg>
        Abrir Câmera
      </button>
    </div>
  </div>

  <!-- Folders and photos -->
  <div class="space-y-6">
    <!-- Folders -->
    <div *ngIf="folders.length > 0" class="space-y-4">
      <div
        *ngFor="let folder of folders"
        class="border border-gray-200 rounded-lg overflow-hidden"
      >
        <!-- Folder header -->
        <div
          (click)="toggleFolder(folder.id)"
          class="flex justify-between items-center p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          (dragover)="onDragOver($event)"
          (drop)="onDrop($event, folder.id)"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-yellow-500 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
              />
            </svg>
            <span class="font-medium">{{ folder.name }}</span>
            <span class="ml-2 text-sm text-gray-500">
              ({{
                folderPhotos[folder.id] ? folderPhotos[folder.id].length : 0
              }}
              fotos)
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button
              (click)="openPhotoUploadModal(folder.id); $event.stopPropagation()"
              title="Upload de fotos para esta pasta"
              class="text-green-500 hover:text-green-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
            <button
              (click)="openEnhancedCameraModal(folder.id); $event.stopPropagation()"
              title="Abrir câmera para esta pasta"
              class="text-purple-500 hover:text-purple-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
            <button
              (click)="deleteFolder(folder.id, $event)"
              class="text-red-500 hover:text-red-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400 transition-transform"
              [ngClass]="{ 'rotate-180': expandedFolderId === folder.id }"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>

        <!-- Folder content -->
        <div
          *ngIf="expandedFolderId === folder.id"
          class="p-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
        >
          <div
            *ngIf="
              !folderPhotos[folder.id] || folderPhotos[folder.id].length === 0
            "
            class="col-span-full text-center py-4 text-gray-500"
          >
            Nenhuma foto nesta pasta
          </div>
          <div
            *ngIf="
              !folderPhotos[folder.id] || folderPhotos[folder.id].length === 0
            "
            class="mb-4 border-2 border-dashed border-gray-200 rounded-lg p-6 text-center"
          >
            <div class="text-center py-8 text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-12 w-12 mx-auto mb-2 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <p>Arraste e solte imagens aqui</p>
              <label
                class="mt-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 inline-block cursor-pointer"
              >
                Selecionar Arquivos
                <input
                  type="file"
                  class="hidden"
                  accept="image/*"
                  (change)="onFileSelected($event, folder.id)"
                  multiple
                />
              </label>
            </div>
          </div>

          <div
            *ngFor="let photo of folderPhotos[folder.id]"
            class="relative group"
            draggable="true"
            (dragstart)="onDragStart(photo.id)"
          >
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md photo-card">
              <!-- Image container -->
              <div
                class="aspect-square overflow-hidden cursor-pointer"
                (click)="openPhotoViewer(photo.id, folder.id)"
              >
                <img
                  [src]="photo.url"
                  [alt]="photo.filename"
                  class="w-full h-full object-cover"
                />
                <!-- Hover overlay -->
                <div
                  class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100"
                >
                  <button
                    (click)="
                      openPhotoViewer(photo.id, folder.id); $event.stopPropagation()
                    "
                    class="p-1 bg-white rounded-full mx-1"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-blue-600"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fill-rule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Caption area -->
              <div class="caption-area p-2 bg-gray-50">
                <p *ngIf="photo.caption" class="text-xs text-gray-700 line-clamp-2 leading-tight caption-text" [title]="photo.caption">
                  {{ photo.caption }}
                </p>
                <p *ngIf="!photo.caption" class="text-xs text-gray-400 italic">
                  Sem legenda
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Unfiled photos -->
    <div class="border border-gray-200 rounded-lg overflow-hidden">
      <!-- Unfiled header -->
      <div
        class="flex justify-between items-center p-4 bg-gray-50"
        (dragover)="onDragOver($event)"
        (drop)="onDrop($event, null)"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4a.5.5 0 01-.5-.5V5a.5.5 0 01.5-.5h12a.5.5 0 01.5.5v9.5a.5.5 0 01-.5.5z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="font-medium">Fotos sem pasta</span>
          <span class="ml-2 text-sm text-gray-500">
            ({{ unfiledPhotos.length }} fotos)
          </span>
        </div>
      </div>

      <!-- Unfiled content -->
      <div
        class="p-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
      >
        <div
          *ngIf="unfiledPhotos.length === 0"
          class="mb-4 border-2 border-dashed border-gray-200 rounded-lg p-6 text-center"
        >
          <div class="text-center py-8 text-gray-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-12 w-12 mx-auto mb-2 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p>Arraste e solte imagens aqui</p>
            <label
              class="mt-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 inline-block cursor-pointer"
            >
              Selecionar Arquivos
              <input
                type="file"
                class="hidden"
                accept="image/*"
                (change)="onFileSelected($event)"
                multiple
              />
            </label>
          </div>
        </div>

        <div
          *ngFor="let photo of unfiledPhotos"
          class="relative group"
          draggable="true"
          (dragstart)="onDragStart(photo.id)"
        >
          <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md photo-card">
            <!-- Image container -->
            <div
              class="aspect-square overflow-hidden cursor-pointer"
              (click)="openPhotoViewer(photo.id)"
            >
              <img
                [src]="photo.url"
                [alt]="photo.filename"
                class="w-full h-full object-cover"
              />
              <!-- Hover overlay -->
              <div
                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100"
              >
                <button
                  (click)="openPhotoViewer(photo.id); $event.stopPropagation()"
                  class="p-1 bg-white rounded-full mx-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-blue-600"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Caption area -->
            <div class="caption-area p-2 bg-gray-50">
              <p *ngIf="photo.caption" class="text-xs text-gray-700 line-clamp-2 leading-tight caption-text" [title]="photo.caption">
                {{ photo.caption }}
              </p>
              <p *ngIf="!photo.caption" class="text-xs text-gray-400 italic">
                Sem legenda
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Photo Viewer Modal -->
<app-photo-viewer-modal
  [isOpen]="isViewerModalOpen"
  [photoId]="selectedPhotoId"
  [photos]="currentPhotos"
  (close)="closePhotoViewer()"
  (photoDeleted)="onPhotoDeleted($event)"
></app-photo-viewer-modal>

<!-- Create Folder Modal -->
<app-create-folder-modal
  [isOpen]="isFolderModalOpen"
  [patientId]="patient?.id || null"
  (close)="closeCreateFolderModal()"
  (folderCreated)="onFolderCreated($event)"
></app-create-folder-modal>

<!-- Diálogo de confirmação para excluir pasta -->
<app-confirmation-dialog
  [isOpen]="isDeleteFolderConfirmationOpen"
  title="Excluir Pasta"
  message="Tem certeza que deseja excluir esta pasta? As fotos serão mantidas, mas ficarão sem pasta."
  confirmButtonText="Excluir"
  cancelButtonText="Cancelar"
  confirmButtonClass="bg-red-600 hover:bg-red-700"
  type="danger"
  (confirm)="confirmDeleteFolder()"
  (cancel)="closeDeleteFolderConfirmation()"
></app-confirmation-dialog>

<!-- Camera Modal -->
<app-camera-modal
  [isOpen]="isCameraModalOpen"
  [patientId]="patient?.id || null"
  [folderId]="selectedFolderForCamera"
  (close)="closeCameraModal()"
  (photoCaptured)="onPhotoCaptured($event)"
></app-camera-modal>

<!-- Enhanced Camera Modal -->
<app-enhanced-camera-modal
  [isOpen]="isEnhancedCameraModalOpen"
  [patientId]="patient?.id || null"
  [folderId]="selectedFolderForCamera"
  (close)="closeEnhancedCameraModal()"
  (photosUploaded)="onMultiplePhotosCaptured($event)"
></app-enhanced-camera-modal>

<!-- Photo Upload Modal -->
<div *ngIf="isPhotoUploadModalOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
    <app-photo-upload
      [patientId]="patient?.id || null"
      [folderId]="selectedFolderForUpload"
      [config]="photoUploadConfig"
      (photosUploaded)="onPhotosUploaded($event)"
      (close)="closePhotoUploadModal()"
    ></app-photo-upload>
  </div>
</div>
