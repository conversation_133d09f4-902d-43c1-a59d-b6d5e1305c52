<div
  *ngIf="isOpen"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
    <!-- Header -->
    <div
      class="px-6 py-4 border-b border-gray-200 flex justify-between items-center"
    >
      <h3 class="text-lg font-semibold text-gray-900">Capturar Foto</h3>
      <button
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-500 focus:outline-none"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Body -->
    <div class="px-6 py-4">
      <!-- Error message -->
      <div *ngIf="error" class="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
        <p class="text-sm font-medium">{{ error }}</p>
      </div>

      <!-- Camera view -->
      <div class="relative">
        <!-- Loading indicator -->
        <div *ngIf="isCapturing" class="flex justify-center items-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
          ></div>
          <span class="ml-2 text-gray-600">Iniciando câmera...</span>
        </div>

        <!-- Video element (shown when not captured) -->
        <div
          *ngIf="!isCaptured && !isCapturing"
          class="bg-black rounded-lg overflow-hidden"
        >
          <video
            #videoElement
            autoplay
            playsinline
            muted
            class="w-full h-auto max-h-[60vh]"
          ></video>

          <!-- Camera switch button -->
          <div *ngIf="multipleWebcamsAvailable" class="absolute top-2 right-2">
            <button
              class="p-2 bg-gray-800 bg-opacity-50 text-white rounded-full hover:bg-opacity-70 focus:outline-none"
              (click)="switchCamera()"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Preview da imagem capturada -->
        <div *ngIf="isCaptured" class="bg-black rounded-lg overflow-hidden">
          <img
            *ngIf="capturedImageData"
            [src]="capturedImageData"
            alt="Foto capturada"
            class="w-full h-auto max-h-[60vh]"
          />
        </div>

        <!-- Hidden canvas for capturing -->
        <canvas #canvasElement class="hidden"></canvas>
      </div>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
      <button
        type="button"
        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
        (click)="closeModal()"
      >
        Cancelar
      </button>

      <div class="flex space-x-3">
        <button
          *ngIf="!isCaptured && !isCapturing"
          type="button"
          (click)="capturePhoto()"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1 inline-block"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
              clip-rule="evenodd"
            />
          </svg>
          Capturar
        </button>

        <button
          *ngIf="isCaptured"
          type="button"
          (click)="retakePhoto()"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
          [disabled]="isSubmitting"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1 inline-block"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
              clip-rule="evenodd"
            />
          </svg>
          Nova Foto
        </button>

        <button
          *ngIf="isCaptured"
          type="button"
          (click)="savePhoto()"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none flex items-center"
          [disabled]="isSubmitting"
        >
          <span *ngIf="isSubmitting" class="mr-2">
            <svg
              class="animate-spin h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </span>
          <span *ngIf="!isSubmitting" class="mr-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 inline-block"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
          Salvar
        </button>
      </div>
    </div>
  </div>
</div>
