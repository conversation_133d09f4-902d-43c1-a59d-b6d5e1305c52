import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PatientPhoto } from '../../../../../core/models/photo.model';
import { PhotoService } from '../../../../../core/services/photo.service';
import { NotificationService } from '../../../../../core/services/notification.service';

@Component({
  selector: 'app-camera-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './camera-modal.component.html',
  styleUrls: ['./camera-modal.component.scss'],
})
export class CameraModalComponent implements OnChanges, OnDestroy {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  @Input() folderId: string | null = null;

  @Output() close = new EventEmitter<void>();
  @Output() photoCaptured = new EventEmitter<PatientPhoto>();

  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvasElement') canvasElement!: ElementRef<HTMLCanvasElement>;

  // Camera properties
  stream: MediaStream | null = null;
  isCapturing = false;
  isCaptured = false;
  isSubmitting = false;
  error: string | null = null;
  capturedImageData: string | null = null;

  // Available video devices
  multipleWebcamsAvailable = false;
  currentCameraIndex = 0;
  availableCameras: MediaDeviceInfo[] = [];

  constructor(
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen']) {
      if (changes['isOpen'].currentValue) {
        this.startCamera();
      } else {
        this.stopCamera();
      }
    }
  }

  ngOnDestroy(): void {
    this.stopCamera();
  }

  async startCamera(): Promise<void> {
    console.log('Iniciando câmera');
    this.isCapturing = true;
    this.error = null;
    this.capturedImageData = null;
    this.isCaptured = false;

    try {
      // Enumerar dispositivos de câmera disponíveis
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableCameras = devices.filter(
        (device) => device.kind === 'videoinput'
      );
      this.multipleWebcamsAvailable = this.availableCameras.length > 1;
      console.log(
        `${this.availableCameras.length} dispositivos de câmera encontrados`
      );

      // Iniciar a câmera
      await this.startCameraWithIndex(this.currentCameraIndex);
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  async startCameraWithIndex(index: number): Promise<void> {
    try {
      // Parar qualquer stream existente
      this.stopCameraStream();

      // Configurações da câmera
      const constraints: MediaStreamConstraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          deviceId:
            this.availableCameras.length > 0
              ? { exact: this.availableCameras[index]?.deviceId }
              : undefined,
        },
        audio: false,
      };

      console.log('Solicitando acesso à câmera com restrições:', constraints);

      // Solicitar acesso à câmera
      this.stream = await navigator.mediaDevices.getUserMedia(constraints);

      // Configurar o elemento de vídeo
      if (this.videoElement && this.videoElement.nativeElement) {
        const video = this.videoElement.nativeElement;
        video.srcObject = this.stream;

        // Aguardar o carregamento dos metadados do vídeo
        video.onloadedmetadata = () => {
          console.log('Metadados de vídeo carregados, iniciando reprodução');
          video
            .play()
            .then(() => {
              console.log('Reprodução de vídeo iniciada com sucesso');
              this.isCapturing = false;
            })
            .catch((err) => {
              console.error('Erro ao iniciar reprodução de vídeo:', err);
              this.error =
                'Erro ao iniciar a câmera. Por favor, tente novamente.';
              this.isCapturing = false;
            });
        };
      } else {
        throw new Error('Elemento de vídeo não encontrado');
      }
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  handleCameraError(error: unknown): void {
    console.error('Erro ao acessar a câmera:', error);

    let errorMessage = 'Não foi possível acessar a câmera. ';

    if (error instanceof Error) {
      if (
        error.name === 'NotAllowedError' ||
        error.name === 'PermissionDeniedError'
      ) {
        errorMessage +=
          'Permissão negada. Por favor, permita o acesso à câmera nas configurações do navegador.';
      } else if (error.name === 'NotFoundError') {
        errorMessage +=
          'Nenhuma câmera encontrada. Verifique se sua câmera está conectada.';
      } else if (
        error.name === 'NotReadableError' ||
        error.name === 'TrackStartError'
      ) {
        errorMessage +=
          'Sua câmera pode estar sendo usada por outro aplicativo.';
      } else {
        errorMessage +=
          error.message || 'Verifique as permissões do navegador.';
      }
    } else {
      errorMessage += 'Verifique as permissões do navegador.';
    }

    this.error = errorMessage;
    this.isCapturing = false;
  }

  stopCamera(): void {
    console.log('Parando câmera');
    this.stopCameraStream();
  }

  stopCameraStream(): void {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;
    }
  }

  switchCamera(): void {
    if (!this.multipleWebcamsAvailable) return;

    this.currentCameraIndex =
      (this.currentCameraIndex + 1) % this.availableCameras.length;
    console.log(
      `Alternando para câmera ${this.currentCameraIndex + 1} de ${
        this.availableCameras.length
      }`
    );
    this.startCameraWithIndex(this.currentCameraIndex);
  }

  capturePhoto(): void {
    if (!this.videoElement || !this.canvasElement) {
      console.error('Elementos de vídeo ou canvas não encontrados');
      this.error =
        'Não foi possível capturar a foto. Elementos não encontrados.';
      return;
    }

    const video = this.videoElement.nativeElement;
    const canvas = this.canvasElement.nativeElement;
    const context = canvas.getContext('2d');

    if (!context) {
      console.error('Não foi possível obter o contexto 2D do canvas');
      this.error =
        'Não foi possível capturar a foto. Erro no contexto do canvas.';
      return;
    }

    try {
      console.log(
        'Capturando foto do vídeo:',
        video.videoWidth,
        'x',
        video.videoHeight
      );

      // Verificar se o vídeo está pronto
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.error(
          'Dimensões de vídeo inválidas:',
          video.videoWidth,
          'x',
          video.videoHeight
        );
        this.error =
          'Não foi possível capturar a foto. Câmera não está pronta.';
        return;
      }

      // Definir as dimensões do canvas para corresponder ao vídeo
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Desenhar o quadro atual do vídeo no canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Obter a imagem como base64 com qualidade específica
      this.capturedImageData = canvas.toDataURL('image/jpeg', 0.9);

      console.log(
        'Imagem capturada com sucesso. Tamanho da string base64:',
        this.capturedImageData ? this.capturedImageData.length : 0
      );

      this.isCaptured = true;
    } catch (error: unknown) {
      console.error('Erro ao capturar foto:', error);

      // Tratamento seguro do erro com verificação de tipo
      let errorMessage = 'Erro ao capturar foto: ';

      if (error instanceof Error) {
        errorMessage += error.message;
      } else if (typeof error === 'string') {
        errorMessage += error;
      } else {
        errorMessage += 'Erro desconhecido';
      }

      this.error = errorMessage;
    }
  }

  retakePhoto(): void {
    this.isCaptured = false;
    this.capturedImageData = null;

    // Reiniciar a câmera se o stream foi parado
    if (!this.stream) {
      this.startCamera();
    }
  }

  savePhoto(): void {
    if (!this.capturedImageData || !this.patientId || this.isSubmitting) {
      console.error('Dados inválidos para salvar foto:', {
        capturedImageData: this.capturedImageData ? 'presente' : 'ausente',
        patientId: this.patientId,
        isSubmitting: this.isSubmitting,
      });
      this.error = 'Não foi possível salvar a foto. Dados inválidos.';
      return;
    }

    // Verificar se a string base64 está no formato correto
    if (!this.capturedImageData.startsWith('data:image/')) {
      console.error(
        'Formato de imagem base64 inválido:',
        this.capturedImageData.substring(0, 30) + '...'
      );
      this.error = 'Formato de imagem inválido. Por favor, tente novamente.';
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    console.log('Enviando foto para o servidor...');

    this.photoService
      .capturePhoto(
        this.patientId,
        this.capturedImageData,
        this.folderId || undefined
      )
      .subscribe({
        next: (photo) => {
          console.log('Foto salva com sucesso:', photo);
          this.isSubmitting = false;
          this.photoCaptured.emit(photo);
          this.notificationService.success('Foto capturada com sucesso');
        },
        error: (error: unknown) => {
          console.error('Erro ao salvar foto:', error);

          // Mensagem de erro mais específica
          let errorMessage = 'Não foi possível salvar a foto. ';

          // Tratamento seguro do erro com verificação de tipo
          if (typeof error === 'object' && error !== null) {
            const err = error as any; // Conversão segura após verificação

            if (
              err.error &&
              typeof err.error === 'object' &&
              err.error.message
            ) {
              errorMessage += err.error.message;
            } else if (err.message && typeof err.message === 'string') {
              errorMessage += err.message;
            } else {
              errorMessage += 'Por favor, tente novamente mais tarde.';
            }
          } else {
            errorMessage += 'Por favor, tente novamente mais tarde.';
          }

          this.error = errorMessage;
          this.isSubmitting = false;
          this.notificationService.error('Erro ao salvar foto');
        },
      });
  }

  closeModal(): void {
    this.stopCamera();
    this.isOpen = false;
    this.close.emit();
  }
}
