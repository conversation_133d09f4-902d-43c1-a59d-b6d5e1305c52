.photos-container {
  /* Scrollbar customization */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

/* Drag and drop styling */
[draggable="true"] {
  cursor: move;
}

.drag-over {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
}

/* Caption styling */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Photo card styling */
.photo-card {
  transition: all 0.2s ease-in-out;
}

.photo-card:hover {
  transform: translateY(-1px);
}

/* Caption area styling */
.caption-area {
  min-height: 2rem; /* Ensure consistent height */
  display: flex;
  align-items: center;
}

.caption-text {
  word-break: break-word;
  hyphens: auto;
}
