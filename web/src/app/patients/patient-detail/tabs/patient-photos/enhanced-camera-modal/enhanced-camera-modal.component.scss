// Modal backdrop
.modal-backdrop {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Modal content
.modal-content {
  animation: slideInScale 0.3s ease-out;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Video element styles
video {
  border-radius: 8px;
  background-color: #000;
  min-height: 300px;
}

// Canvas hidden styles
canvas.hidden {
  display: none !important;
}

// Capture button animation
.capture-button {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Photo preview animations
.photo-item {
  transition: all 0.3s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Loading spinner
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Error message animation
.error-message {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Success message animation
.success-message {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Photo order badge
.order-badge {
  @apply bg-blue-600 text-white text-xs font-bold;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Button hover effects
.btn-hover {
  transition: all 0.2s ease-in-out;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

// Caption input focus styles
.caption-input {
  transition: all 0.2s ease-in-out;
  
  &:focus {
    @apply ring-2 ring-blue-500 border-blue-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

// Disabled state styles
.disabled {
  @apply opacity-50 cursor-not-allowed;
  pointer-events: none;
}

// Loading state
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Focus styles
.focus-ring {
  &:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }
}

// Smooth transitions
.transition-all {
  transition: all 0.2s ease-in-out;
}

// Custom scrollbar for photos section
.photos-section {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded;
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded;
  }

  &::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .modal-content {
    max-height: 95vh;
  }
  
  video {
    max-height: 40vh;
  }
}

@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    max-height: 95vh;
  }
  
  .flex-row {
    flex-direction: column;
  }
  
  video {
    max-height: 35vh;
  }
  
  .photos-section {
    max-height: 30vh;
  }
}

// Camera switch button
.camera-switch {
  backdrop-filter: blur(8px);
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.05);
  }
}

// Photo controls
.photo-controls {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

// Reorder buttons
.reorder-btn {
  transition: all 0.2s ease-in-out;
  
  &:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
  
  &:disabled {
    opacity: 0.3;
  }
}

// Global caption section
.global-caption {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

// Photo grid animations
.photo-grid-enter {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.photo-grid-leave {
  animation: slideOutRight 0.3s ease-out;
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

// Checkbox custom styles
input[type="checkbox"] {
  &:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }
  
  &:focus {
    ring-color: #3b82f6;
    ring-offset-width: 2px;
  }
}
