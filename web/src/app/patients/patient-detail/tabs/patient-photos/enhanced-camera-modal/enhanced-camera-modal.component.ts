import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PatientPhoto } from '../../../../../core/models/photo.model';
import { PhotoService } from '../../../../../core/services/photo.service';
import { NotificationService } from '../../../../../core/services/notification.service';

interface CapturedPhoto {
  id: string;
  imageBase64: string;
  preview: string;
  caption: string;
  order: number;
}

@Component({
  selector: 'app-enhanced-camera-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './enhanced-camera-modal.component.html',
  styleUrls: ['./enhanced-camera-modal.component.scss'],
})
export class EnhancedCameraModalComponent implements OnChanges, OnDestroy {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  @Input() folderId: string | null = null;

  @Output() close = new EventEmitter<void>();
  @Output() photosUploaded = new EventEmitter<PatientPhoto[]>();

  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvasElement') canvasElement!: ElementRef<HTMLCanvasElement>;

  // Camera properties
  stream: MediaStream | null = null;
  isCapturing = false;
  isSubmitting = false;
  error: string | null = null;

  // Available video devices
  multipleWebcamsAvailable = false;
  currentCameraIndex = 0;
  availableCameras: MediaDeviceInfo[] = [];
  currentFacingMode: 'user' | 'environment' = 'environment'; // Inicia com traseira
  isMobile = false;

  // Captured photos
  capturedPhotos: CapturedPhoto[] = [];
  globalCaption = '';
  useGlobalCaption = false;

  // UI states
  showCapturedPhotos = false;
  photoCounter = 0;

  constructor(
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen']) {
      if (changes['isOpen'].currentValue) {
        // Aguardar o próximo ciclo de detecção de mudanças para garantir que o DOM foi renderizado
        setTimeout(() => {
          this.startCamera();
        }, 0);
      } else {
        this.stopCamera();
        this.resetModal();
      }
    }
  }

  ngOnDestroy(): void {
    this.stopCamera();
  }

  async startCamera(): Promise<void> {
    console.log('Iniciando câmera');
    this.isCapturing = true;
    this.error = null;

    try {
      // Detectar se é mobile
      this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      console.log('Dispositivo mobile detectado:', this.isMobile);

      // Aguardar um pouco para garantir que o DOM foi renderizado
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verificar se os elementos estão disponíveis
      if (!this.videoElement || !this.canvasElement) {
        throw new Error('Elementos de vídeo ou canvas não encontrados. Aguardando renderização...');
      }

      // Enumerar dispositivos de câmera disponíveis
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableCameras = devices.filter(
        (device) => device.kind === 'videoinput'
      );

      console.log('Todos os dispositivos encontrados:', devices);
      console.log('Dispositivos de vídeo filtrados:', this.availableCameras);

      // No mobile, às vezes precisamos solicitar permissão primeiro para obter labels
      if (this.availableCameras.length > 0 && !this.availableCameras[0].label) {
        console.log('Labels vazios detectados, solicitando permissão para obter informações completas...');
        try {
          // Solicitar permissão temporária para obter labels
          const tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
          tempStream.getTracks().forEach(track => track.stop());

          // Enumerar novamente após obter permissão
          const devicesWithLabels = await navigator.mediaDevices.enumerateDevices();
          this.availableCameras = devicesWithLabels.filter(
            (device) => device.kind === 'videoinput'
          );
          console.log('Dispositivos após obter permissão:', this.availableCameras);
        } catch (error) {
          console.log('Não foi possível obter labels das câmeras:', error);
        }
      }

      // No mobile, sempre assumir que há múltiplas câmeras (frontal/traseira)
      if (this.isMobile) {
        this.multipleWebcamsAvailable = true;
        console.log('Mobile detectado: assumindo múltiplas câmeras disponíveis');
      } else {
        this.multipleWebcamsAvailable = this.availableCameras.length > 1;
      }

      console.log(
        `${this.availableCameras.length} dispositivos de câmera encontrados (multipleWebcamsAvailable: ${this.multipleWebcamsAvailable}):`
      );

      // Log das câmeras disponíveis para debug
      this.availableCameras.forEach((camera, index) => {
        const label = camera.label || `Câmera ${index + 1}`;
        const isFront = label.toLowerCase().includes('front') ||
                       label.toLowerCase().includes('user') ||
                       label.toLowerCase().includes('frontal');
        const isBack = label.toLowerCase().includes('back') ||
                      label.toLowerCase().includes('environment') ||
                      label.toLowerCase().includes('traseira');

        console.log(`  ${index + 1}. ID: ${camera.deviceId}, Label: "${label}" ${isFront ? '(Frontal)' : isBack ? '(Traseira)' : ''}`);
      });

      // Iniciar a câmera
      if (this.isMobile) {
        await this.startCameraWithFacingMode(this.currentFacingMode);
      } else {
        await this.startCameraWithIndex(this.currentCameraIndex);
      }
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  async startCameraWithIndex(index: number): Promise<void> {
    try {
      // Parar qualquer stream existente
      this.stopCameraStream();

      // Configurações da câmera
      const videoConstraints: any = {
        width: { ideal: 1280 },
        height: { ideal: 720 },
      };

      // Adicionar deviceId apenas se disponível e válido
      if (this.availableCameras.length > 0 &&
          this.availableCameras[index] &&
          this.availableCameras[index].deviceId) {
        videoConstraints.deviceId = { exact: this.availableCameras[index].deviceId };
      }

      const constraints: MediaStreamConstraints = {
        video: videoConstraints,
        audio: false,
      };

      console.log('Solicitando acesso à câmera com restrições:', constraints);

      // Solicitar acesso à câmera
      try {
        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (error: any) {
        // Se falhar com deviceId específico, tentar sem deviceId
        if (error.name === 'OverconstrainedError' && videoConstraints.deviceId) {
          console.log('Tentando novamente sem deviceId específico...');
          delete videoConstraints.deviceId;
          const fallbackConstraints: MediaStreamConstraints = {
            video: videoConstraints,
            audio: false,
          };
          this.stream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        } else {
          throw error;
        }
      }

      // Verificar novamente se os elementos estão disponíveis
      if (!this.videoElement || !this.videoElement.nativeElement) {
        throw new Error('Elemento de vídeo não encontrado');
      }

      if (!this.canvasElement || !this.canvasElement.nativeElement) {
        throw new Error('Elemento canvas não encontrado');
      }

      // Configurar o elemento de vídeo
      const video = this.videoElement.nativeElement;
      video.srcObject = this.stream;

      // Aguardar o carregamento dos metadados do vídeo
      video.onloadedmetadata = () => {
        console.log('Metadados de vídeo carregados, iniciando reprodução');
        video
          .play()
          .then(() => {
            console.log('Reprodução de vídeo iniciada com sucesso');
            this.isCapturing = false;
          })
          .catch((err) => {
            console.error('Erro ao iniciar reprodução de vídeo:', err);
            this.error =
              'Erro ao iniciar a câmera. Por favor, tente novamente.';
            this.isCapturing = false;
          });
      };
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  handleCameraError(error: unknown): void {
    console.error('Erro ao acessar a câmera:', error);

    let errorMessage = 'Não foi possível acessar a câmera. ';

    if (error instanceof Error) {
      if (
        error.name === 'NotAllowedError' ||
        error.name === 'PermissionDeniedError'
      ) {
        errorMessage +=
          'Permissão negada. Por favor, permita o acesso à câmera nas configurações do navegador.';
      } else if (error.name === 'NotFoundError') {
        errorMessage +=
          'Nenhuma câmera encontrada. Verifique se sua câmera está conectada.';
      } else if (
        error.name === 'NotReadableError' ||
        error.name === 'TrackStartError'
      ) {
        errorMessage +=
          'Sua câmera pode estar sendo usada por outro aplicativo.';
      } else {
        errorMessage +=
          error.message || 'Verifique as permissões do navegador.';
      }
    } else {
      errorMessage += 'Verifique as permissões do navegador.';
    }

    this.error = errorMessage;
    this.isCapturing = false;
  }

  stopCamera(): void {
    console.log('Parando câmera');
    this.stopCameraStream();
  }

  stopCameraStream(): void {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;
    }
  }

  async startCameraWithFacingMode(facingMode: 'user' | 'environment'): Promise<void> {
    try {
      // Parar qualquer stream existente
      this.stopCameraStream();

      // Configurações da câmera para mobile usando facingMode
      const constraints: MediaStreamConstraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: { exact: facingMode }
        },
        audio: false,
      };

      console.log(`Solicitando acesso à câmera mobile com facingMode: ${facingMode}`, constraints);

      // Solicitar acesso à câmera
      try {
        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (error: any) {
        // Se falhar com exact, tentar sem exact
        if (error.name === 'OverconstrainedError') {
          console.log('Tentando novamente sem exact facingMode...');
          const fallbackConstraints: MediaStreamConstraints = {
            video: {
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: facingMode // Sem exact
            },
            audio: false,
          };
          this.stream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        } else {
          throw error;
        }
      }

      // Verificar novamente se os elementos estão disponíveis
      if (!this.videoElement || !this.videoElement.nativeElement) {
        throw new Error('Elemento de vídeo não encontrado');
      }

      if (!this.canvasElement || !this.canvasElement.nativeElement) {
        throw new Error('Elemento canvas não encontrado');
      }

      // Configurar o elemento de vídeo
      const video = this.videoElement.nativeElement;
      video.srcObject = this.stream;

      // Aguardar o carregamento dos metadados do vídeo
      video.onloadedmetadata = () => {
        console.log(`Metadados de vídeo carregados para facingMode: ${facingMode}`);
        video
          .play()
          .then(() => {
            console.log(`Reprodução de vídeo iniciada com sucesso (${facingMode})`);
            this.isCapturing = false;
          })
          .catch((err) => {
            console.error('Erro ao iniciar reprodução de vídeo:', err);
            this.error =
              'Erro ao iniciar a câmera. Por favor, tente novamente.';
            this.isCapturing = false;
          });
      };
    } catch (error: unknown) {
      this.handleCameraError(error);
    }
  }

  switchCamera(): void {
    if (!this.multipleWebcamsAvailable) return;

    if (this.isMobile) {
      // No mobile, alternar entre facingMode
      this.currentFacingMode = this.currentFacingMode === 'environment' ? 'user' : 'environment';
      const cameraType = this.currentFacingMode === 'user' ? 'Frontal' : 'Traseira';

      console.log(`Alternando para câmera ${cameraType} (facingMode: ${this.currentFacingMode})`);

      this.startCameraWithFacingMode(this.currentFacingMode);
    } else {
      // No desktop, usar deviceId
      this.currentCameraIndex =
        (this.currentCameraIndex + 1) % this.availableCameras.length;

      const currentCamera = this.availableCameras[this.currentCameraIndex];
      const label = currentCamera?.label || `Câmera ${this.currentCameraIndex + 1}`;
      const isFront = label.toLowerCase().includes('front') ||
                     label.toLowerCase().includes('user') ||
                     label.toLowerCase().includes('frontal');
      const isBack = label.toLowerCase().includes('back') ||
                    label.toLowerCase().includes('environment') ||
                    label.toLowerCase().includes('traseira');

      const cameraType = isFront ? ' (Frontal)' : isBack ? ' (Traseira)' : '';

      console.log(
        `Alternando para câmera ${this.currentCameraIndex + 1} de ${
          this.availableCameras.length
        }: ${label}${cameraType}`
      );

      this.startCameraWithIndex(this.currentCameraIndex);
    }
  }

  selectCamera(facingMode: 'user' | 'environment'): void {
    if (!this.isMobile) return;

    this.currentFacingMode = facingMode;
    const cameraType = facingMode === 'user' ? 'Frontal' : 'Traseira';

    console.log(`Selecionando câmera ${cameraType} (facingMode: ${facingMode})`);

    this.startCameraWithFacingMode(facingMode);
  }

  getCameraButtonClass(facingMode: 'user' | 'environment'): string {
    const isActive = this.currentFacingMode === facingMode;

    if (isActive) {
      return 'bg-blue-600 text-white border-blue-600';
    } else {
      return 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 border';
    }
  }

  capturePhoto(): void {
    if (!this.videoElement || !this.canvasElement) {
      console.error('Elementos de vídeo ou canvas não encontrados');
      this.error =
        'Não foi possível capturar a foto. Elementos não encontrados.';
      return;
    }

    const video = this.videoElement.nativeElement;
    const canvas = this.canvasElement.nativeElement;
    const context = canvas.getContext('2d');

    if (!context) {
      console.error('Não foi possível obter o contexto 2D do canvas');
      this.error =
        'Não foi possível capturar a foto. Erro no contexto do canvas.';
      return;
    }

    try {
      console.log(
        'Capturando foto do vídeo:',
        video.videoWidth,
        'x',
        video.videoHeight
      );

      // Verificar se o vídeo está pronto
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.error(
          'Dimensões de vídeo inválidas:',
          video.videoWidth,
          'x',
          video.videoHeight
        );
        this.error =
          'Não foi possível capturar a foto. Câmera não está pronta.';
        return;
      }

      // Configurar dimensões otimizadas do canvas
      const maxWidth = 1920;
      const maxHeight = 1080;
      let { videoWidth, videoHeight } = video;

      // Redimensionar se necessário mantendo a proporção
      if (videoWidth > maxWidth || videoHeight > maxHeight) {
        const ratio = Math.min(maxWidth / videoWidth, maxHeight / videoHeight);
        videoWidth = Math.floor(videoWidth * ratio);
        videoHeight = Math.floor(videoHeight * ratio);
      }

      canvas.width = videoWidth;
      canvas.height = videoHeight;

      // Desenhar o quadro atual do vídeo no canvas com redimensionamento
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Obter a imagem como base64 com compressão otimizada
      const imageBase64 = canvas.toDataURL('image/jpeg', 0.7);

      console.log(
        `Imagem capturada com sucesso. Dimensões: ${videoWidth}x${videoHeight}, Tamanho: ${Math.round((imageBase64?.length || 0) / 1024)}KB`
      );

      // Adicionar à lista de fotos capturadas
      const capturedPhoto: CapturedPhoto = {
        id: this.generateUniqueId(),
        imageBase64,
        preview: imageBase64,
        caption: '',
        order: this.capturedPhotos.length + 1,
      };

      this.capturedPhotos.push(capturedPhoto);
      this.photoCounter++;
      this.showCapturedPhotos = true;

      this.notificationService.success('Foto capturada com sucesso!');
    } catch (error: unknown) {
      console.error('Erro ao capturar foto:', error);

      // Tratamento seguro do erro com verificação de tipo
      let errorMessage = 'Erro ao capturar foto: ';

      if (error instanceof Error) {
        errorMessage += error.message;
      } else if (typeof error === 'string') {
        errorMessage += error;
      } else {
        errorMessage += 'Erro desconhecido';
      }

      this.error = errorMessage;
    }
  }

  removePhoto(index: number): void {
    this.capturedPhotos.splice(index, 1);
    this.reorderPhotos();
    
    if (this.capturedPhotos.length === 0) {
      this.showCapturedPhotos = false;
    }
  }

  private reorderPhotos(): void {
    this.capturedPhotos.forEach((photo, index) => {
      photo.order = index + 1;
    });
  }

  movePhotoUp(index: number): void {
    if (index > 0) {
      const temp = this.capturedPhotos[index];
      this.capturedPhotos[index] = this.capturedPhotos[index - 1];
      this.capturedPhotos[index - 1] = temp;
      this.reorderPhotos();
    }
  }

  movePhotoDown(index: number): void {
    if (index < this.capturedPhotos.length - 1) {
      const temp = this.capturedPhotos[index];
      this.capturedPhotos[index] = this.capturedPhotos[index + 1];
      this.capturedPhotos[index + 1] = temp;
      this.reorderPhotos();
    }
  }

  applyGlobalCaption(): void {
    if (this.useGlobalCaption && this.globalCaption.trim()) {
      this.capturedPhotos.forEach(photo => {
        if (!photo.caption.trim()) {
          photo.caption = this.globalCaption.trim();
        }
      });
    }
  }

  async savePhotos(): Promise<void> {
    if (!this.patientId || this.capturedPhotos.length === 0 || this.isSubmitting) {
      console.error('Dados inválidos para salvar fotos:', {
        patientId: this.patientId,
        photosCount: this.capturedPhotos.length,
        isSubmitting: this.isSubmitting,
      });
      this.error = 'Não foi possível salvar as fotos. Dados inválidos.';
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      // Aplicar legenda global se necessário
      this.applyGlobalCaption();

      // Preparar dados das fotos
      const photosData = this.capturedPhotos.map(photo => ({
        imageBase64: photo.imageBase64,
        caption: photo.caption || undefined,
        order: photo.order,
      }));

      console.log('Enviando fotos para o servidor...');

      const savedPhotos = await this.photoService
        .captureMultiplePhotos(
          this.patientId,
          photosData,
          this.folderId || undefined
        )
        .toPromise();

      if (savedPhotos) {
        console.log('Fotos salvas com sucesso:', savedPhotos);
        this.photosUploaded.emit(savedPhotos);
        this.notificationService.success(
          `${savedPhotos.length} foto(s) salva(s) com sucesso!`
        );
        this.closeModal();
      }
    } catch (error: unknown) {
      console.error('Erro ao salvar fotos:', error);

      // Mensagem de erro mais específica
      let errorMessage = 'Não foi possível salvar as fotos. ';

      // Tratamento seguro do erro com verificação de tipo
      if (typeof error === 'object' && error !== null) {
        const err = error as any; // Conversão segura após verificação

        if (
          err.error &&
          typeof err.error === 'object' &&
          err.error.message
        ) {
          errorMessage += err.error.message;
        } else if (err.message && typeof err.message === 'string') {
          errorMessage += err.message;
        } else {
          errorMessage += 'Por favor, tente novamente mais tarde.';
        }
      } else {
        errorMessage += 'Por favor, tente novamente mais tarde.';
      }

      this.error = errorMessage;
      this.notificationService.error('Erro ao salvar fotos');
    } finally {
      this.isSubmitting = false;
    }
  }

  private generateUniqueId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private resetModal(): void {
    this.capturedPhotos = [];
    this.globalCaption = '';
    this.useGlobalCaption = false;
    this.showCapturedPhotos = false;
    this.photoCounter = 0;
    this.error = null;
  }

  closeModal(): void {
    this.stopCamera();
    this.resetModal();
    this.close.emit();
  }

  getPhotoCount(): string {
    return `${this.capturedPhotos.length} foto(s) capturada(s)`;
  }
}
