<div
  *ngIf="isOpen"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
>
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">Capturar Fotos</h3>
        <p class="text-sm text-gray-500 mt-1">{{ getPhotoCount() }}</p>
      </div>
      <button
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-500 focus:outline-none"
      >
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Body -->
    <div class="flex flex-col lg:flex-row h-full max-h-[calc(90vh-140px)]">
      <!-- Camera Section -->
      <div class="flex-1 p-6">
        <!-- Error message -->
        <div *ngIf="error" class="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
          <p class="text-sm font-medium">{{ error }}</p>
        </div>

        <!-- Camera view -->
        <div class="relative">
          <!-- Loading indicator -->
          <div
            *ngIf="isCapturing"
            class="absolute inset-0 flex justify-center items-center bg-black bg-opacity-50 z-10 rounded-lg"
          >
            <div class="flex flex-col items-center text-white">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
              <span class="mt-2 text-sm">Iniciando câmera...</span>
            </div>
          </div>

          <!-- Video element -->
          <div class="bg-black rounded-lg overflow-hidden relative">
            <video
              #videoElement
              autoplay
              playsinline
              muted
              class="w-full h-auto max-h-[50vh] object-cover"
              [class.opacity-0]="isCapturing"
            ></video>

            <!-- Camera switch button (desktop only) -->
            <div *ngIf="!isMobile && multipleWebcamsAvailable" class="absolute top-2 right-2">
              <button
                class="p-2 bg-gray-800 bg-opacity-50 text-white rounded-full hover:bg-opacity-70 focus:outline-none transition-all"
                (click)="switchCamera()"
                title="Trocar câmera"
              >
                <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  <path d="M7 16l-4-4m0 0l4-4m-4 4h18"/>
                </svg>
              </button>
            </div>

            <!-- Capture button overlay -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <button
                type="button"
                (click)="capturePhoto()"
                [disabled]="isCapturing"
                class="p-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
              >
                <svg class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Hidden canvas for capturing -->
          <canvas #canvasElement class="hidden"></canvas>
        </div>

        <!-- Camera selection buttons (mobile) -->
        <div *ngIf="isMobile && multipleWebcamsAvailable && !isCapturing && !error" class="mt-4 flex justify-center space-x-4">
          <button
            (click)="selectCamera('environment')"
            [class]="getCameraButtonClass('environment')"
            class="flex items-center px-4 py-2 rounded-lg font-medium transition-all"
          >
            <svg class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
              <circle cx="12" cy="13" r="4"/>
            </svg>
            Câmera Traseira
          </button>
          <button
            (click)="selectCamera('user')"
            [class]="getCameraButtonClass('user')"
            class="flex items-center px-4 py-2 rounded-lg font-medium transition-all"
          >
            <svg class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            Câmera Frontal
          </button>
        </div>
      </div>

      <!-- Captured Photos Section -->
      <div 
        *ngIf="showCapturedPhotos" 
        class="w-full lg:w-96 border-t lg:border-t-0 lg:border-l border-gray-200 p-6 overflow-y-auto"
      >
        <h4 class="text-md font-medium text-gray-900 mb-4">
          Fotos Capturadas ({{ capturedPhotos.length }})
        </h4>

        <!-- Global Caption -->
        <div class="mb-4">
          <div class="flex items-center mb-2">
            <input
              type="checkbox"
              id="useGlobalCaption"
              [(ngModel)]="useGlobalCaption"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="useGlobalCaption" class="ml-2 text-sm font-medium text-gray-700">
              Usar legenda para todas
            </label>
          </div>
          <input
            *ngIf="useGlobalCaption"
            type="text"
            [(ngModel)]="globalCaption"
            placeholder="Digite uma legenda para todas as fotos"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          />
        </div>

        <!-- Photos List -->
        <div class="space-y-3">
          <div
            *ngFor="let photo of capturedPhotos; let i = index"
            class="border border-gray-200 rounded-lg overflow-hidden"
          >
            <!-- Photo Preview -->
            <div class="relative">
              <img
                [src]="photo.preview"
                [alt]="photo.caption || 'Foto ' + (i + 1)"
                class="w-full h-24 object-cover"
              />
              
              <!-- Order Badge -->
              <div class="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-bold">
                {{ photo.order }}
              </div>

              <!-- Remove Button -->
              <button
                (click)="removePhoto(i)"
                class="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
              >
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>

            <!-- Photo Controls -->
            <div class="p-3">
              <!-- Caption Input -->
              <input
                type="text"
                [(ngModel)]="photo.caption"
                placeholder="Legenda da foto"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm mb-2"
              />

              <!-- Reorder Buttons -->
              <div class="flex justify-center space-x-2">
                <button
                  (click)="movePhotoUp(i)"
                  [disabled]="i === 0"
                  class="p-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                </button>
                <button
                  (click)="movePhotoDown(i)"
                  [disabled]="i === capturedPhotos.length - 1"
                  class="p-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
      <div class="text-sm text-gray-500">
        <span *ngIf="capturedPhotos.length === 0">Nenhuma foto capturada</span>
        <span *ngIf="capturedPhotos.length > 0">{{ capturedPhotos.length }} foto(s) pronta(s) para salvar</span>
      </div>

      <div class="flex space-x-3">
        <button
          type="button"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
          (click)="closeModal()"
        >
          Cancelar
        </button>

        <button
          *ngIf="capturedPhotos.length > 0"
          type="button"
          (click)="savePhotos()"
          [disabled]="isSubmitting || !patientId"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <span *ngIf="isSubmitting" class="mr-2">
            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <span *ngIf="!isSubmitting">
            <svg class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </span>
          {{ isSubmitting ? 'Salvando...' : 'Salvar Fotos' }}
        </button>
      </div>
    </div>
  </div>
</div>
