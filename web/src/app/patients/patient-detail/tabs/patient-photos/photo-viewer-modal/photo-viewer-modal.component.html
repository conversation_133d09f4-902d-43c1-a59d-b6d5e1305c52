<div
  *ngIf="isOpen"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
  (keydown)="onKeyDown($event)"
  tabindex="0"
>
  <!-- Loading indicator -->
  <div
    *ngIf="isLoading"
    class="absolute inset-0 flex items-center justify-center"
  >
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"
    ></div>
  </div>

  <!-- Error message -->
  <div
    *ngIf="error"
    class="absolute top-4 left-1/2 transform -translate-x-1/2 p-4 bg-red-50 text-red-700 rounded-md"
  >
    <p class="font-medium">{{ error }}</p>
  </div>

  <!-- Close button -->
  <button
    (click)="closeModal()"
    class="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-8 w-8"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M6 18L18 6M6 6l12 12"
      />
    </svg>
  </button>

  <!-- Navigation buttons -->
  <button
    *ngIf="currentIndex > 0"
    (click)="navigateToPrevious()"
    class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M15 19l-7-7 7-7"
      />
    </svg>
  </button>

  <button
    *ngIf="currentIndex < photos.length - 1"
    (click)="navigateToNext()"
    class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 5l7 7-7 7"
      />
    </svg>
  </button>

  <!-- Delete button -->
  <button
    (click)="deletePhoto()"
    class="absolute bottom-4 right-4 text-white hover:text-red-300 z-10"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-8 w-8"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      />
    </svg>
  </button>

  <!-- Photo display -->
  <div
    *ngIf="currentPhoto"
    class="w-full h-full flex items-center justify-center p-4"
  >
    <img
      [src]="currentPhoto.url"
      [alt]="currentPhoto.filename"
      class="max-w-full max-h-full object-contain"
    />
  </div>

  <!-- Counter -->
  <div
    *ngIf="photos.length > 0"
    class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-3 py-1 rounded-full"
  >
    {{ currentIndex + 1 }} / {{ photos.length }}
  </div>
</div>

<!-- Diálogo de confirmação para excluir foto -->
<app-confirmation-dialog
  [isOpen]="isDeletePhotoConfirmationOpen"
  title="Excluir Foto"
  message="Tem certeza que deseja excluir esta foto? Esta ação não poderá ser desfeita."
  confirmButtonText="Excluir"
  cancelButtonText="Cancelar"
  confirmButtonClass="bg-red-600 hover:bg-red-700"
  type="danger"
  (confirm)="confirmDeletePhoto()"
  (cancel)="closeDeletePhotoConfirmation()"
></app-confirmation-dialog>
