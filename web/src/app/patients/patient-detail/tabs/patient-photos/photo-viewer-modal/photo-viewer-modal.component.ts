import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PatientPhoto } from '../../../../../core/models/photo.model';
import { PhotoService } from '../../../../../core/services/photo.service';
import { NotificationService } from '../../../../../core/services/notification.service';
import { ConfirmationDialogComponent } from '../../../../../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-photo-viewer-modal',
  standalone: true,
  imports: [CommonModule, ConfirmationDialogComponent],
  templateUrl: './photo-viewer-modal.component.html',
  styleUrls: ['./photo-viewer-modal.component.scss'],
})
export class PhotoViewerModalComponent implements OnChanges {
  @Input() isOpen = false;
  @Input() photoId: string | null = null;
  @Input() photos: PatientPhoto[] = [];

  @Output() close = new EventEmitter<void>();
  @Output() photoDeleted = new EventEmitter<string | null>();

  currentPhoto: PatientPhoto | null = null;
  currentIndex = -1;
  isLoading = false;
  error: string | null = null;

  // Diálogo de confirmação
  isDeletePhotoConfirmationOpen = false;

  constructor(
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && changes['isOpen'].currentValue && this.photoId) {
      this.loadPhoto();
    }

    if (changes['photos'] || changes['photoId']) {
      this.updateCurrentIndex();
    }
  }

  loadPhoto(): void {
    if (!this.photoId) return;

    this.isLoading = true;
    this.error = null;

    // Se a foto já está na lista de fotos, use-a
    const photoFromList = this.photos.find((p) => p.id === this.photoId);
    if (photoFromList) {
      this.currentPhoto = photoFromList;
      this.isLoading = false;
      return;
    }

    // Caso contrário, busque do servidor
    this.photoService.getPhoto(this.photoId).subscribe({
      next: (photo) => {
        this.currentPhoto = photo;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar foto:', err);
        this.error =
          'Não foi possível carregar a foto. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar foto');
      },
    });
  }

  updateCurrentIndex(): void {
    if (!this.photoId || this.photos.length === 0) {
      this.currentIndex = -1;
      return;
    }

    this.currentIndex = this.photos.findIndex((p) => p.id === this.photoId);
  }

  closeModal(): void {
    this.isOpen = false;
    this.currentPhoto = null;
    this.error = null;
    this.close.emit();
  }

  navigateToPrevious(): void {
    if (this.currentIndex <= 0 || this.photos.length === 0) return;

    this.currentIndex--;
    this.photoId = this.photos[this.currentIndex].id;
    this.currentPhoto = this.photos[this.currentIndex];
  }

  navigateToNext(): void {
    if (this.currentIndex >= this.photos.length - 1 || this.photos.length === 0)
      return;

    this.currentIndex++;
    this.photoId = this.photos[this.currentIndex].id;
    this.currentPhoto = this.photos[this.currentIndex];
  }

  deletePhoto(): void {
    if (!this.photoId || !this.currentPhoto) return;

    // Abrir o diálogo de confirmação
    this.isDeletePhotoConfirmationOpen = true;
  }

  confirmDeletePhoto(): void {
    if (!this.photoId || !this.currentPhoto) return;

    this.isLoading = true;
    this.photoService.deletePhoto(this.photoId).subscribe({
      next: () => {
        this.isLoading = false;
        this.photoDeleted.emit(this.photoId);
        this.notificationService.success('Foto excluída com sucesso');
        this.closeDeletePhotoConfirmation();
      },
      error: (err) => {
        console.error('Erro ao excluir foto:', err);
        this.isLoading = false;
        this.notificationService.error('Erro ao excluir foto');
        this.closeDeletePhotoConfirmation();
      },
    });
  }

  closeDeletePhotoConfirmation(): void {
    this.isDeletePhotoConfirmationOpen = false;
  }

  // Manipuladores de eventos de teclado para navegação
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isOpen) return;

    switch (event.key) {
      case 'Escape':
        this.closeModal();
        break;
      case 'ArrowLeft':
        this.navigateToPrevious();
        break;
      case 'ArrowRight':
        this.navigateToNext();
        break;
    }
  }
}
