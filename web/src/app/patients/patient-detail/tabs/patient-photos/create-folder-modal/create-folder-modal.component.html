<div
  *ngIf="isOpen"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Nova Pasta</h3>
    </div>

    <!-- Body -->
    <div class="px-6 py-4">
      <!-- Error message -->
      <div *ngIf="error" class="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
        <p class="text-sm font-medium">{{ error }}</p>
      </div>

      <form [formGroup]="folderForm" (ngSubmit)="onSubmit()">
        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
            Nome da Past<PERSON>
          </label>
          <input
            type="text"
            id="name"
            formControlName="name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Ex: Radiografias"
            [class.border-red-500]="folderForm.get('name')?.invalid && folderForm.get('name')?.touched"
          />
          <div
            *ngIf="folderForm.get('name')?.invalid && folderForm.get('name')?.touched"
            class="mt-1 text-sm text-red-600"
          >
            <span *ngIf="folderForm.get('name')?.errors?.['required']">
              O nome da pasta é obrigatório.
            </span>
            <span *ngIf="folderForm.get('name')?.errors?.['maxlength']">
              O nome da pasta não pode ter mais de 255 caracteres.
            </span>
          </div>
        </div>
      </form>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button
        type="button"
        (click)="closeModal()"
        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
        [disabled]="isSubmitting"
      >
        Cancelar
      </button>
      <button
        type="submit"
        (click)="onSubmit()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none flex items-center"
        [disabled]="folderForm.invalid || isSubmitting"
      >
        <span *ngIf="isSubmitting" class="mr-2">
          <svg
            class="animate-spin h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
        Criar Pasta
      </button>
    </div>
  </div>
</div>
