import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { PhotoFolder } from '../../../../../core/models/photo.model';
import { PhotoService } from '../../../../../core/services/photo.service';
import { NotificationService } from '../../../../../core/services/notification.service';

@Component({
  selector: 'app-create-folder-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './create-folder-modal.component.html',
  styleUrls: ['./create-folder-modal.component.scss'],
})
export class CreateFolderModalComponent implements OnChanges {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  
  @Output() close = new EventEmitter<void>();
  @Output() folderCreated = new EventEmitter<PhotoFolder>();

  folderForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  constructor(
    private fb: FormBuilder,
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {
    this.folderForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(255)]],
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && changes['isOpen'].currentValue) {
      this.resetForm();
    }
  }

  resetForm(): void {
    this.folderForm.reset();
    this.error = null;
  }

  closeModal(): void {
    if (this.isSubmitting) return;
    
    this.isOpen = false;
    this.close.emit();
  }

  onSubmit(): void {
    if (this.folderForm.invalid || !this.patientId || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    const { name } = this.folderForm.value;

    this.photoService.createFolder(this.patientId, name).subscribe({
      next: (folder) => {
        this.isSubmitting = false;
        this.folderCreated.emit(folder);
        this.notificationService.success('Pasta criada com sucesso');
      },
      error: (err) => {
        console.error('Erro ao criar pasta:', err);
        this.error = 'Não foi possível criar a pasta. Por favor, tente novamente mais tarde.';
        this.isSubmitting = false;
        this.notificationService.error('Erro ao criar pasta');
      },
    });
  }
}
