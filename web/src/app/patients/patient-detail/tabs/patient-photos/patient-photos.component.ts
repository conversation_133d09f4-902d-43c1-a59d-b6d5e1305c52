import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Patient } from '../../../../core/models/patient.model';
import { PatientPhoto, PhotoFolder } from '../../../../core/models/photo.model';
import { PhotoService } from '../../../../core/services/photo.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { PhotoViewerModalComponent } from './photo-viewer-modal/photo-viewer-modal.component';
import { CreateFolderModalComponent } from './create-folder-modal/create-folder-modal.component';
import { CameraModalComponent } from './camera-modal/camera-modal.component';
import { EnhancedCameraModalComponent } from './enhanced-camera-modal/enhanced-camera-modal.component';
import { PhotoUploadComponent, PhotoUploadConfig } from '../../../../shared/components/photo-upload/photo-upload.component';
import { ConfirmationDialogComponent } from '../../../../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-patient-photos',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PhotoViewerModalComponent,
    CreateFolderModalComponent,
    CameraModalComponent,
    EnhancedCameraModalComponent,
    PhotoUploadComponent,
    ConfirmationDialogComponent,
  ],
  templateUrl: './patient-photos.component.html',
  styleUrls: ['./patient-photos.component.scss'],
})
export class PatientPhotosComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  // Dados
  folders: PhotoFolder[] = [];
  unfiledPhotos: PatientPhoto[] = [];
  folderPhotos: { [folderId: string]: PatientPhoto[] } = {};

  // Estados
  isLoading = false;
  error: string | null = null;
  expandedFolderId: string | null = null;

  // Modais
  isViewerModalOpen = false;
  selectedPhotoId: string | null = null;
  currentFolderId: string | null = null;
  currentPhotos: PatientPhoto[] = [];

  isFolderModalOpen = false;

  isCameraModalOpen = false;
  selectedFolderForCamera: string | null = null;

  // Novos modais
  isEnhancedCameraModalOpen = false;
  isPhotoUploadModalOpen = false;
  selectedFolderForUpload: string | null = null;

  // Configuração do componente de upload
  photoUploadConfig: PhotoUploadConfig = {
    mode: 'both',
    allowMultiple: true,
    maxFiles: 10,
    showCaptions: true,
    showReorder: true,
    globalCaption: true,
  };

  // Drag & Drop
  draggedPhotoId: string | null = null;

  // Diálogos de confirmação
  isDeleteFolderConfirmationOpen = false;
  folderToDelete: string | null = null;

  constructor(
    private photoService: PhotoService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadPhotos();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadPhotos();
    }
  }

  loadPhotos(): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.error = null;

    this.photoService.getPhotosByPatient(this.patient.id).subscribe({
      next: (data) => {
        this.folders = data.folders;
        this.unfiledPhotos = data.unfiled;
        this.isLoading = false;

        // Carregar fotos de cada pasta
        this.folders.forEach((folder) => {
          this.loadFolderPhotos(folder.id);
        });
      },
      error: (err) => {
        console.error('Erro ao carregar fotos:', err);
        this.error =
          'Não foi possível carregar as fotos. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar fotos');
      },
    });
  }

  loadFolderPhotos(folderId: string): void {
    this.photoService.getPhotosByFolder(folderId).subscribe({
      next: (photos) => {
        this.folderPhotos[folderId] = photos;
      },
      error: (err) => {
        console.error(`Erro ao carregar fotos da pasta ${folderId}:`, err);
        this.notificationService.error('Erro ao carregar fotos da pasta');
      },
    });
  }

  toggleFolder(folderId: string): void {
    this.expandedFolderId =
      this.expandedFolderId === folderId ? null : folderId;
  }

  // Métodos para upload de fotos
  onFileSelected(event: Event, folderId: string | null = null): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length || !this.patient?.id) return;

    const file = input.files[0];
    this.uploadFile(file, folderId);

    // Limpar o input para permitir selecionar o mesmo arquivo novamente
    input.value = '';
  }

  uploadFile(file: File, folderId: string | null = null): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.photoService
      .uploadPhoto(this.patient.id, file, folderId || undefined)
      .subscribe({
        next: (photo) => {
          this.notificationService.success('Foto enviada com sucesso');
          this.isLoading = false;

          // Atualizar a lista de fotos
          if (folderId) {
            if (this.folderPhotos[folderId]) {
              this.folderPhotos[folderId].unshift(photo);
            } else {
              this.loadFolderPhotos(folderId);
            }
          } else {
            this.unfiledPhotos.unshift(photo);
          }
        },
        error: (err) => {
          console.error('Erro ao enviar foto:', err);
          this.notificationService.error('Erro ao enviar foto');
          this.isLoading = false;
        },
      });
  }

  // Métodos para pastas
  openCreateFolderModal(): void {
    this.isFolderModalOpen = true;
  }

  closeCreateFolderModal(): void {
    this.isFolderModalOpen = false;
  }

  onFolderCreated(folder: PhotoFolder): void {
    this.folders.push(folder);
    this.folderPhotos[folder.id] = [];
    this.closeCreateFolderModal();
    this.notificationService.success('Pasta criada com sucesso');
  }

  deleteFolder(folderId: string, event: Event): void {
    event.stopPropagation(); // Evitar que o clique expanda/recolha a pasta

    // Abrir o diálogo de confirmação
    this.folderToDelete = folderId;
    this.isDeleteFolderConfirmationOpen = true;
  }

  confirmDeleteFolder(): void {
    if (!this.folderToDelete) return;

    const folderId = this.folderToDelete;

    this.photoService.deleteFolder(folderId).subscribe({
      next: () => {
        // Mover as fotos da pasta para a lista de fotos sem pasta
        if (this.folderPhotos[folderId]) {
          this.unfiledPhotos = [
            ...this.unfiledPhotos,
            ...this.folderPhotos[folderId],
          ];
          delete this.folderPhotos[folderId];
        }

        // Remover a pasta da lista
        this.folders = this.folders.filter((f) => f.id !== folderId);

        this.notificationService.success('Pasta excluída com sucesso');
        this.closeDeleteFolderConfirmation();
      },
      error: (err) => {
        console.error('Erro ao excluir pasta:', err);
        this.notificationService.error('Erro ao excluir pasta');
        this.closeDeleteFolderConfirmation();
      },
    });
  }

  closeDeleteFolderConfirmation(): void {
    this.isDeleteFolderConfirmationOpen = false;
    this.folderToDelete = null;
  }

  // Métodos para visualização de fotos
  openPhotoViewer(photoId: string, folderId: string | null = null): void {
    this.selectedPhotoId = photoId;
    this.currentFolderId = folderId;

    // Definir a lista de fotos atual para navegação
    if (folderId) {
      this.currentPhotos = this.folderPhotos[folderId] || [];
    } else {
      this.currentPhotos = this.unfiledPhotos;
    }

    this.isViewerModalOpen = true;
  }

  closePhotoViewer(): void {
    this.isViewerModalOpen = false;
    this.selectedPhotoId = null;
    this.currentFolderId = null;
    this.currentPhotos = [];
  }

  onPhotoDeleted(photoId: string | null): void {
    if (!photoId) return;

    // Remover a foto da lista apropriada
    if (this.currentFolderId) {
      this.folderPhotos[this.currentFolderId] = this.folderPhotos[
        this.currentFolderId
      ].filter((p) => p.id !== photoId);
    } else {
      this.unfiledPhotos = this.unfiledPhotos.filter((p) => p.id !== photoId);
    }

    this.closePhotoViewer();
    this.notificationService.success('Foto excluída com sucesso');
  }

  // Métodos para câmera
  openCameraModal(folderId: string | null = null): void {
    this.selectedFolderForCamera = folderId;
    this.isCameraModalOpen = true;
  }

  closeCameraModal(): void {
    this.isCameraModalOpen = false;
    this.selectedFolderForCamera = null;
  }

  onPhotoCaptured(photo: PatientPhoto): void {
    // Adicionar a foto à lista apropriada
    if (photo.folderId) {
      if (this.folderPhotos[photo.folderId]) {
        this.folderPhotos[photo.folderId].unshift(photo);
      } else {
        this.loadFolderPhotos(photo.folderId);
      }
    } else {
      this.unfiledPhotos.unshift(photo);
    }

    this.closeCameraModal();
    this.notificationService.success('Foto capturada com sucesso');
  }

  // Métodos para o modal de câmera melhorado
  openEnhancedCameraModal(folderId: string | null = null): void {
    this.selectedFolderForCamera = folderId;
    this.isEnhancedCameraModalOpen = true;
  }

  closeEnhancedCameraModal(): void {
    this.isEnhancedCameraModalOpen = false;
    this.selectedFolderForCamera = null;
  }

  onMultiplePhotosCaptured(photos: PatientPhoto[]): void {
    // Adicionar as fotos às listas apropriadas
    photos.forEach(photo => {
      if (photo.folderId) {
        if (this.folderPhotos[photo.folderId]) {
          this.folderPhotos[photo.folderId].unshift(photo);
        } else {
          this.loadFolderPhotos(photo.folderId);
        }
      } else {
        this.unfiledPhotos.unshift(photo);
      }
    });

    this.closeEnhancedCameraModal();
    this.notificationService.success(`${photos.length} foto(s) capturada(s) com sucesso`);
  }

  // Métodos para o componente de upload de fotos
  openPhotoUploadModal(folderId: string | null = null): void {
    this.selectedFolderForUpload = folderId;
    this.isPhotoUploadModalOpen = true;
  }

  closePhotoUploadModal(): void {
    this.isPhotoUploadModalOpen = false;
    this.selectedFolderForUpload = null;
  }

  onPhotosUploaded(photos: PatientPhoto[]): void {
    // Adicionar as fotos às listas apropriadas
    photos.forEach(photo => {
      if (photo.folderId) {
        if (this.folderPhotos[photo.folderId]) {
          this.folderPhotos[photo.folderId].unshift(photo);
        } else {
          this.loadFolderPhotos(photo.folderId);
        }
      } else {
        this.unfiledPhotos.unshift(photo);
      }
    });

    this.closePhotoUploadModal();
    this.notificationService.success(`${photos.length} foto(s) enviada(s) com sucesso`);
  }

  // Métodos para drag & drop
  onDragStart(photoId: string): void {
    this.draggedPhotoId = photoId;
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  onDrop(event: DragEvent, targetFolderId: string | null): void {
    event.preventDefault();

    if (!this.draggedPhotoId) return;

    // Mover a foto para a pasta de destino
    this.photoService.movePhoto(this.draggedPhotoId, targetFolderId).subscribe({
      next: (photo) => {
        // Encontrar a foto na lista de origem
        let sourceList: PatientPhoto[] = [];
        let sourceId: string | null = null;

        // Verificar se a foto está em alguma pasta
        for (const folderId in this.folderPhotos) {
          const index = this.folderPhotos[folderId].findIndex(
            (p) => p.id === this.draggedPhotoId
          );
          if (index !== -1) {
            sourceList = this.folderPhotos[folderId];
            sourceId = folderId;
            break;
          }
        }

        // Se não estiver em nenhuma pasta, está na lista de fotos sem pasta
        if (!sourceId) {
          sourceList = this.unfiledPhotos;
        }

        // Remover a foto da lista de origem
        const photoIndex = sourceList.findIndex(
          (p) => p.id === this.draggedPhotoId
        );
        if (photoIndex !== -1) {
          const movedPhoto = sourceList.splice(photoIndex, 1)[0];

          // Adicionar a foto à lista de destino
          if (targetFolderId) {
            if (!this.folderPhotos[targetFolderId]) {
              this.folderPhotos[targetFolderId] = [];
            }
            this.folderPhotos[targetFolderId].unshift(movedPhoto);
          } else {
            this.unfiledPhotos.unshift(movedPhoto);
          }
        }

        this.draggedPhotoId = null;
        this.notificationService.success('Foto movida com sucesso');
      },
      error: (err) => {
        console.error('Erro ao mover foto:', err);
        this.notificationService.error('Erro ao mover foto');
        this.draggedPhotoId = null;
      },
    });
  }
}
