<div class="p-4">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800">Documentos</h2>
    <button
      (click)="openUploadModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-1"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
          clip-rule="evenodd"
        />
      </svg>
      Adicionar Documento
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Error message -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
  >
    {{ error }}
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!isLoading && !error && documents.length === 0"
    class="bg-gray-50 p-6 rounded-lg text-center"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12 mx-auto text-gray-400 mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
    <p class="text-gray-600">Nenhum documento encontrado para este paciente.</p>
    <button
      (click)="openUploadModal()"
      class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
    >
      Adicionar Documento
    </button>
  </div>

  <!-- Documents table -->
  <div
    *ngIf="!isLoading && !error && documents.length > 0"
    class="overflow-x-auto"
  >
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Nome
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Arquivo
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data de Envio
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Ações
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let document of documents">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">{{ document.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">{{ document.fileName }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">
              {{ formatDate(document.uploadedAt) }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button
              (click)="downloadDocument(document.fileUrl, document.fileName)"
              class="text-blue-600 hover:text-blue-900 mr-3"
              title="Baixar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
            <button
              (click)="confirmDeleteDocument(document.id)"
              class="text-red-600 hover:text-red-900"
              title="Excluir"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Upload Modal -->
<app-document-upload-modal
  [isOpen]="isUploadModalOpen"
  [patientId]="patient?.id || null"
  (close)="closeUploadModal()"
  (documentUploaded)="onDocumentUploaded($event)"
></app-document-upload-modal>

<!-- Confirmation Dialog for Delete -->
<app-confirmation-dialog
  [isOpen]="isDeleteDocumentConfirmationOpen"
  title="Confirmar exclusão"
  message="Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita."
  confirmButtonText="Excluir"
  cancelButtonText="Cancelar"
  (confirm)="deleteDocument()"
  (cancel)="cancelDeleteDocument()"
></app-confirmation-dialog>
