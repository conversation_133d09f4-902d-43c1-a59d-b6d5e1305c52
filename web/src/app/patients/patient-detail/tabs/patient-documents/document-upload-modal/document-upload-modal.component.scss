/* Estilos específicos para o modal de upload de documentos */
:host ::ng-deep app-modal {
  .bg-white {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  /* Estilos para o input de arquivo */
  input[type="file"] {
    padding: 0.5rem;
    line-height: 1.5;
  }

  /* Estilos para o botão de envio */
  button[type="button"] {
    transition: all 0.2s ease-in-out;

    &:not(:disabled):hover {
      transform: translateY(-1px);
    }
  }

  /* Garantir que o footer tenha espaço suficiente */
  div[footer] {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    button {
      white-space: nowrap;
      width: 6rem; /* 96px - w-24 */
      text-align: center;
      justify-content: center;

      /* Garantir que o texto não quebre em telas pequenas */
      @media (max-width: 640px) {
        font-size: 0.875rem;
      }
    }
  }
}
