import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DocumentService } from '../../../../../core/services/document.service';
import { NotificationService } from '../../../../../core/services/notification.service';
import { PatientDocument } from '../../../../../core/models/document.model';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

@Component({
  selector: 'app-document-upload-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent],
  templateUrl: './document-upload-modal.component.html',
  styleUrls: ['./document-upload-modal.component.scss'],
})
export class DocumentUploadModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;

  @Output() close = new EventEmitter<void>();
  @Output() documentUploaded = new EventEmitter<PatientDocument>();

  @ViewChild('fileInput') fileInput!: ElementRef;

  documentForm: FormGroup;
  isSubmitting = false;
  selectedFile: File | null = null;
  fileError: string | null = null;

  readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB em bytes
  readonly ALLOWED_FILE_TYPES = [
    'application/pdf',
    'image/jpeg',
    'image/png',
  ];

  constructor(
    private fb: FormBuilder,
    private documentService: DocumentService,
    private notificationService: NotificationService
  ) {
    this.documentForm = this.fb.group({
      name: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file) {
      this.clearFile();
      return;
    }

    // Validar tamanho do arquivo
    if (file.size > this.MAX_FILE_SIZE) {
      this.fileError = 'O arquivo excede o tamanho máximo permitido de 5MB';
      this.clearFile();
      return;
    }

    // Validar tipo do arquivo
    if (!this.ALLOWED_FILE_TYPES.includes(file.type)) {
      this.fileError = 'Apenas arquivos PDF, JPG, JPEG e PNG são permitidos';
      this.clearFile();
      return;
    }

    this.selectedFile = file;
    this.fileError = null;
  }

  clearFile(): void {
    this.selectedFile = null;
    this.fileError = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  onSubmit(): void {
    if (this.documentForm.invalid || !this.selectedFile || !this.patientId) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.documentForm.controls).forEach((key) => {
        const control = this.documentForm.get(key);
        control?.markAsTouched();
      });

      if (!this.selectedFile) {
        this.fileError = 'Selecione um arquivo';
      }

      return;
    }

    this.isSubmitting = true;
    const formValues = this.documentForm.value;

    this.documentService
      .uploadDocument(this.patientId, formValues.name, this.selectedFile)
      .subscribe({
        next: (document) => {
          this.documentUploaded.emit(document);
          this.resetForm();
          this.isSubmitting = false;
        },
        error: (err) => {
          console.error('Erro ao enviar documento:', err);
          this.notificationService.error('Erro ao enviar documento');
          this.isSubmitting = false;
        },
      });
  }

  resetForm(): void {
    this.documentForm.reset();
    this.clearFile();
  }

  closeModal(): void {
    this.resetForm();
    this.close.emit();
  }
}
