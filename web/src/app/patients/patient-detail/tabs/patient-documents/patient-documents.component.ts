import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Patient } from '../../../../core/models/patient.model';
import { PatientDocument } from '../../../../core/models/document.model';
import { DocumentService } from '../../../../core/services/document.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { DocumentUploadModalComponent } from './document-upload-modal/document-upload-modal.component';
import { ConfirmationDialogComponent } from '../../../../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-patient-documents',
  standalone: true,
  imports: [
    CommonModule,
    DocumentUploadModalComponent,
    ConfirmationDialogComponent,
  ],
  templateUrl: './patient-documents.component.html',
  styleUrls: ['./patient-documents.component.scss'],
})
export class PatientDocumentsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  // Dados
  documents: PatientDocument[] = [];

  // Estados
  isLoading = false;
  error: string | null = null;

  // Modais
  isUploadModalOpen = false;

  // Diálogos de confirmação
  isDeleteDocumentConfirmationOpen = false;
  documentToDelete: string | null = null;

  constructor(
    private documentService: DocumentService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    if (this.patient?.id) {
      this.loadDocuments();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && this.patient?.id) {
      this.loadDocuments();
    }
  }

  loadDocuments(): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.error = null;

    this.documentService.getDocumentsByPatient(this.patient.id).subscribe({
      next: (documents) => {
        this.documents = documents;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar documentos:', err);
        this.error =
          'Não foi possível carregar os documentos. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar documentos');
      },
    });
  }

  openUploadModal(): void {
    this.isUploadModalOpen = true;
  }

  closeUploadModal(): void {
    this.isUploadModalOpen = false;
  }

  onDocumentUploaded(document: PatientDocument): void {
    this.documents.unshift(document);
    this.closeUploadModal();
    this.notificationService.success('Documento enviado com sucesso');
  }

  downloadDocument(fileUrl: string, fileName: string): void {
    // Criar um link temporário para download
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  confirmDeleteDocument(documentId: string): void {
    this.documentToDelete = documentId;
    this.isDeleteDocumentConfirmationOpen = true;
  }

  cancelDeleteDocument(): void {
    this.documentToDelete = null;
    this.isDeleteDocumentConfirmationOpen = false;
  }

  deleteDocument(): void {
    if (!this.documentToDelete) return;

    this.isLoading = true;
    this.documentService.deleteDocument(this.documentToDelete).subscribe({
      next: () => {
        this.documents = this.documents.filter(
          (document) => document.id !== this.documentToDelete
        );
        this.notificationService.success('Documento removido com sucesso');
        this.isLoading = false;
        this.cancelDeleteDocument();
      },
      error: (err) => {
        console.error('Erro ao remover documento:', err);
        this.notificationService.error('Erro ao remover documento');
        this.isLoading = false;
        this.cancelDeleteDocument();
      },
    });
  }

  // Formatação de data
  formatDate(date: Date | string): string {
    if (!date) return '';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('pt-BR');
  }
}
