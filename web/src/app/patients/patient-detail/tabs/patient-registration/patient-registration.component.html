<!-- Al<PERSON>a de cadastro incompleto -->
<div
  *ngIf="patient?.isIncomplete"
  class="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg"
>
  <div class="flex items-center">
    <svg
      class="h-5 w-5 text-orange-400 mr-2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path
        fill-rule="evenodd"
        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
        clip-rule="evenodd"
      />
    </svg>
    <div>
      <h3 class="text-sm font-medium text-orange-800">Cadastro Incompleto</h3>
      <p class="text-sm text-orange-700 mt-1">
        Este paciente foi criado via sistema externo. Complete os dados
        obrigatórios para finalizar o cadastro.
      </p>
    </div>
  </div>
</div>

<div class="two-column-layout bg-white rounded-lg">
  <!-- Coluna esquerda: Navegação vertical -->
  <div class="left-column">
    <div class="vertical-nav">
      <div
        class="nav-item"
        [class.active]="activeCadastroSection === 'dados-cadastrais'"
        (click)="setCadastroSection('dados-cadastrais')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="nav-icon h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
          <path
            fill-rule="evenodd"
            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="nav-text">Dados Cadastrais</span>
      </div>

      <div
        class="nav-item"
        [class.active]="activeCadastroSection === 'contato'"
        (click)="setCadastroSection('contato')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="nav-icon h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
          />
        </svg>
        <span class="nav-text">Contato</span>
      </div>

      <div
        class="nav-item"
        [class.active]="activeCadastroSection === 'dados-complementares'"
        (click)="setCadastroSection('dados-complementares')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="nav-icon h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="nav-text">Dados Complementares</span>
      </div>
    </div>
  </div>

  <!-- Coluna direita: Conteúdo da seção selecionada -->
  <div class="right-column">
    <!-- Seção: Dados Cadastrais -->
    <div
      *ngIf="activeCadastroSection === 'dados-cadastrais'"
      class="form-section"
    >
      <form
        [formGroup]="dadosCadastraisForm"
        (ngSubmit)="saveDadosCadastrais()"
      >
        <h2 class="form-title">Dados Cadastrais</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Nome -->
          <div class="col-span-2">
            <label for="name">Nome Completo*</label>
            <input
              type="text"
              id="name"
              formControlName="name"
              [ngClass]="{
                'border-red-500':
                  dadosCadastraisForm.get('name')?.invalid &&
                  dadosCadastraisForm.get('name')?.touched
              }"
            />
            <div
              *ngIf="
                dadosCadastraisForm.get('name')?.invalid &&
                dadosCadastraisForm.get('name')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              <span
                *ngIf="dadosCadastraisForm.get('name')?.errors?.['required']"
                >Nome é obrigatório</span
              >
              <span
                *ngIf="dadosCadastraisForm.get('name')?.errors?.['minlength']"
                >Nome deve ter pelo menos 3 caracteres</span
              >
            </div>
          </div>

          <!-- Data de Nascimento e Idade -->
          <div>
            <label for="birthDate">Data de Nascimento*</label>
            <input
              type="date"
              id="birthDate"
              formControlName="birthDate"
              (change)="onBirthDateChange($event)"
              [ngClass]="{
                'border-red-500':
                  dadosCadastraisForm.get('birthDate')?.invalid &&
                  dadosCadastraisForm.get('birthDate')?.touched
              }"
            />
            <div
              *ngIf="
                dadosCadastraisForm.get('birthDate')?.invalid &&
                dadosCadastraisForm.get('birthDate')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              <span
                *ngIf="dadosCadastraisForm.get('birthDate')?.errors?.['required']"
                >Data de nascimento é obrigatória</span
              >
            </div>
          </div>

          <div>
            <label for="age">Idade</label>
            <input
              type="text"
              id="age"
              formControlName="age"
              class="disabled-field"
              readonly
            />
          </div>

          <!-- Gênero -->
          <div>
            <label for="gender">Gênero</label>
            <select id="gender" formControlName="gender">
              <option [ngValue]="null">Selecione</option>
              <option
                *ngFor="let gender of patientGenders"
                [value]="gender.value"
              >
                {{ gender.label }}
              </option>
            </select>
          </div>

          <!-- CPF -->
          <div>
            <label for="cpf">CPF*</label>
            <input
              type="text"
              id="cpf"
              formControlName="cpf"
              [ngClass]="{
                'border-red-500':
                  dadosCadastraisForm.get('cpf')?.invalid &&
                  dadosCadastraisForm.get('cpf')?.touched
              }"
              (input)="onCpfInput($event)"
            />
            <div
              *ngIf="
                dadosCadastraisForm.get('cpf')?.invalid &&
                dadosCadastraisForm.get('cpf')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="dadosCadastraisForm.get('cpf')?.errors?.['required']"
                >CPF é obrigatório</span
              >
            </div>
          </div>

          <!-- Como conheceu -->
          <div>
            <label for="howDidYouFindUs">Como nos conheceu</label>
            <select id="howDidYouFindUs" formControlName="howDidYouFindUs">
              <option [value]="''">Selecione</option>
              <option
                *ngFor="let option of howDidYouFindUsOptions"
                [value]="option"
              >
                {{ option }}
              </option>
            </select>
          </div>

          <!-- Indicado por (sempre visível quando o paciente foi indicado por alguém) -->
          <div>
            <app-patient-selector
              formControlName="referredById"
              [label]="'Indicado por'"
              [placeholder]="'Selecione o paciente que fez a indicação'"
            ></app-patient-selector>
          </div>

          <!-- Data de Cadastro -->
          <div *ngIf="patient?.registrationDate">
            <label for="registrationDate">Data de Cadastro</label>
            <input
              type="text"
              id="registrationDate"
              [value]="patient?.registrationDate | date : 'dd/MM/yyyy'"
              class="disabled-field"
              readonly
            />
          </div>

          <!-- Observações -->
          <div class="col-span-2">
            <label for="notes">Observações</label>
            <textarea id="notes" formControlName="notes" rows="8"></textarea>
          </div>
        </div>

        <div class="form-footer">
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="dadosCadastraisForm.invalid || isSavingDadosCadastrais"
            [class.btn-loading]="isSavingDadosCadastrais"
          >
            <span *ngIf="isSavingDadosCadastrais" class="mr-2">
              <svg
                class="animate-spin h-4 w-4 text-white inline-block"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </span>
            Salvar
          </button>
        </div>
      </form>
    </div>

    <!-- Seção: Contato -->
    <div *ngIf="activeCadastroSection === 'contato'" class="form-section">
      <form [formGroup]="contatoForm" (ngSubmit)="saveContato()">
        <h2 class="form-title">Contato</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Email -->
          <div class="col-span-2">
            <label for="email">Email*</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              [ngClass]="{
                'border-red-500':
                  contatoForm.get('email')?.invalid &&
                  contatoForm.get('email')?.touched
              }"
            />
            <div
              *ngIf="
                contatoForm.get('email')?.invalid &&
                contatoForm.get('email')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="contatoForm.get('email')?.errors?.['required']"
                >Email é obrigatório</span
              >
              <span *ngIf="contatoForm.get('email')?.errors?.['email']"
                >Email inválido</span
              >
            </div>
          </div>

          <!-- Telefone -->
          <div>
            <label for="phone">Telefone*</label>
            <input
              type="tel"
              id="phone"
              formControlName="phone"
              [ngClass]="{
                'border-red-500':
                  contatoForm.get('phone')?.invalid &&
                  contatoForm.get('phone')?.touched
              }"
              (input)="onPhoneInput($event)"
            />

            <div
              *ngIf="
                contatoForm.get('phone')?.invalid &&
                contatoForm.get('phone')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="contatoForm.get('phone')?.errors?.['required']"
                >Telefone é obrigatório</span
              >
            </div>
          </div>

          <!-- WhatsApp -->
          <div class="flex flex-col">
            <label for="whatsapp">WhatsApp</label>
            <input
              type="tel"
              id="whatsapp"
              formControlName="whatsapp"
              (input)="onWhatsappInput($event)"
            />
            <div class="flex items-center mt-1">
              <input
                type="checkbox"
                id="isWhatsapp"
                formControlName="isWhatsapp"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded checkbox"
                (change)="onIsWhatsappChange($event)"
              />
              <label for="isWhatsapp" class="ml-2 block text-sm text-gray-700">
                Mesmo número para WhatsApp
              </label>
            </div>
          </div>

          <!-- CEP -->
          <div>
            <label for="addressZipCode">CEP</label>
            <div class="flex">
              <input
                type="text"
                id="addressZipCode"
                formControlName="addressZipCode"
                class="w-full px-3 border border-gray-300 rounded-l-md rounded-r-none focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                (input)="onCepInput($event)"
              />
              <button
                type="button"
                class="px-3 py-2 bg-blue-500 text-white -ml-1 rounded-r-md hover:bg-blue-600 focus:outline-none"
                (click)="buscarCep()"
                [disabled]="isLoadingCep"
              >
                <span *ngIf="!isLoadingCep">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span *ngIf="isLoadingCep" class="flex items-center">
                  <svg
                    class="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                </span>
              </button>
            </div>
          </div>

          <!-- Endereço -->
          <div class="col-span-2">
            <label for="addressStreet">Endereço</label>
            <input
              type="text"
              id="addressStreet"
              formControlName="addressStreet"
            />
          </div>

          <!-- Número -->
          <div>
            <label for="addressNumber">Número</label>
            <input
              type="text"
              id="addressNumber"
              formControlName="addressNumber"
            />
          </div>

          <!-- Bairro -->
          <div>
            <label for="addressNeighborhood">Bairro</label>
            <input
              type="text"
              id="addressNeighborhood"
              formControlName="addressNeighborhood"
            />
          </div>

          <!-- Cidade -->
          <div>
            <label for="addressCity">Cidade</label>
            <input type="text" id="addressCity" formControlName="addressCity" />
          </div>

          <!-- Estado -->
          <div>
            <label for="addressState">Estado</label>
            <select
              id="addressState"
              formControlName="addressState"
              class="w-full px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Selecione</option>
              <option *ngFor="let estado of estados" [value]="estado.sigla">
                {{ estado.sigla }} - {{ estado.nome }}
              </option>
            </select>
          </div>

          <!-- Complemento -->
          <div class="col-span-2">
            <label for="addressComplement">Complemento</label>
            <input
              type="text"
              id="addressComplement"
              formControlName="addressComplement"
            />
          </div>
        </div>

        <div class="form-footer">
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="contatoForm.invalid || isSavingContato"
            [class.btn-loading]="isSavingContato"
          >
            <span *ngIf="isSavingContato" class="mr-2">
              <svg
                class="animate-spin h-4 w-4 text-white inline-block"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </span>
            Salvar
          </button>
        </div>
      </form>
    </div>

    <!-- Seção: Dados Complementares -->
    <div
      *ngIf="activeCadastroSection === 'dados-complementares'"
      class="form-section"
    >
      <form
        [formGroup]="dadosComplementaresForm"
        (ngSubmit)="saveDadosComplementares()"
      >
        <h2 class="form-title">Dados Complementares</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Profissão -->
          <div>
            <label for="profession">Profissão</label>
            <input type="text" id="profession" formControlName="profession" />
          </div>

          <!-- Número do Prontuário -->
          <div>
            <label for="medicalRecordNumber">Número do Prontuário</label>
            <input
              type="text"
              id="medicalRecordNumber"
              formControlName="medicalRecordNumber"
            />
          </div>

          <!-- Categoria -->
          <div>
            <label for="category">Categoria</label>
            <select id="category" formControlName="category">
              <option
                *ngFor="let category of patientCategories"
                [value]="category"
              >
                {{ category }}
              </option>
            </select>
          </div>

          <!-- Tipo de Paciente -->
          <div>
            <app-patient-type-selector
              formControlName="patientTypeId"
              [required]="false"
              [invalid]="false"
              [touched]="false"
              [placeholder]="'Selecione um tipo'"
            ></app-patient-type-selector>
          </div>
        </div>

        <div class="form-footer">
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="
              dadosComplementaresForm.invalid || isSavingDadosComplementares
            "
            [class.btn-loading]="isSavingDadosComplementares"
          >
            <span *ngIf="isSavingDadosComplementares" class="mr-2">
              <svg
                class="animate-spin h-4 w-4 text-white inline-block"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </span>
            Salvar
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
