import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Patient,
  PatientCategory,
  PatientGender,
} from '../../../../core/models/patient.model';
import { PatientService } from '../../../../core/services/patient.service';
import { CepService } from '../../../../core/services/cep.service';
import { ESTADOS_BRASILEIROS, Estado } from '../../../../core/models/estados';
import { PatientTypeSelectorComponent } from '../../../../shared/patient-type-selector/patient-type-selector.component';
import { PatientSelectorComponent } from '../../../../shared/patient-selector/patient-selector.component';
import {
  maskCPF,
  maskPhone,
  maskCEP,
} from '../../../../core/utils/input-masks';

@Component({
  selector: 'app-patient-registration',
  standalone: true,
  imports: [
    CommonModule,
    DatePipe,
    ReactiveFormsModule,
    PatientTypeSelectorComponent,
    PatientSelectorComponent,
  ],
  templateUrl: './patient-registration.component.html',
  styleUrl: './patient-registration.component.scss',
})
export class PatientRegistrationComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  activeCadastroSection: string = 'dados-cadastrais';
  isLoadingCep = false;

  // Formulários para cada seção
  dadosCadastraisForm: FormGroup;
  contatoForm: FormGroup;
  dadosComplementaresForm: FormGroup;

  // Opções para selects
  patientCategories: PatientCategory[] = ['Urgente', 'Rotina', 'Follow-up'];
  patientGenders: { value: PatientGender; label: string }[] = [
    { value: 'male', label: 'Masculino' },
    { value: 'female', label: 'Feminino' },
    { value: 'other', label: 'Outro' },
  ];
  howDidYouFindUsOptions: string[] = [
    'Indicação',
    'Google',
    'Instagram',
    'Facebook',
    'Outro',
  ];
  estados: Estado[] = ESTADOS_BRASILEIROS;

  // Estado dos formulários
  isSavingDadosCadastrais = false;
  isSavingContato = false;
  isSavingDadosComplementares = false;

  // Idade calculada
  calculatedAge: number | null = null;

  constructor(
    private patientService: PatientService,
    private fb: FormBuilder,
    private cepService: CepService
  ) {
    // Inicializar formulários
    this.dadosCadastraisForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      birthDate: ['', [Validators.required]],
      gender: [null],
      cpf: ['', [Validators.required]],
      howDidYouFindUs: [''],
      referredById: [null],
      notes: [''],
      age: [{ value: null, disabled: true }],
    });

    this.contatoForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      whatsapp: [''],
      isWhatsapp: [false],
      addressZipCode: [''],
      addressStreet: [''],
      addressNumber: [''],
      addressNeighborhood: [''],
      addressCity: [''],
      addressState: [''],
      addressComplement: [''],
    });

    this.dadosComplementaresForm = this.fb.group({
      profession: [''],
      medicalRecordNumber: [''],
      category: ['Rotina'],
      patientTypeId: [null],
    });
  }

  ngOnInit(): void {
    if (this.patient) {
      this.populateForms();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.populateForms();
    }
  }

  // Preencher os formulários com os dados do paciente
  populateForms(): void {
    if (!this.patient) return;

    // Formatando a data para o formato aceito pelo input type="date"
    const birthDate = this.patient.birthDate
      ? this.formatDateForInput(new Date(this.patient.birthDate))
      : '';

    // Preencher formulário de Dados Cadastrais
    this.dadosCadastraisForm.patchValue({
      name: this.patient.name,
      birthDate: birthDate,
      gender: this.patient.gender,
      cpf: this.patient.cpf,
      howDidYouFindUs: this.patient.howDidYouFindUs,
      referredById: this.patient.referredById,
      notes: this.patient.notes,
      age: this.calculatedAge,
    });

    // Calcular a idade com base na data de nascimento (apenas se não for data padrão)
    if (this.patient.birthDate && birthDate !== '') {
      this.calculateAge(new Date(this.patient.birthDate));
    }

    // Preencher formulário de Contato
    this.contatoForm.patchValue({
      email: this.patient.email,
      phone: this.patient.phone,
      whatsapp: this.patient.whatsapp,
      addressZipCode: this.patient.addressZipCode,
      addressStreet: this.patient.addressStreet,
      addressNumber: this.patient.addressNumber,
      addressNeighborhood: this.patient.addressNeighborhood,
      addressCity: this.patient.addressCity,
      addressState: this.patient.addressState,
      addressComplement: this.patient.addressComplement,
    });

    // Verificar se o telefone é o mesmo que o WhatsApp
    if (
      this.patient.phone &&
      this.patient.whatsapp &&
      this.patient.phone === this.patient.whatsapp
    ) {
      this.contatoForm.patchValue({
        isWhatsapp: true,
      });
    }

    // Verificar se o paciente tem um tipo de paciente associado
    let patientTypeIdToUse = this.patient.patientTypeId;
    if (!patientTypeIdToUse && this.patient.patientType) {
      patientTypeIdToUse = this.patient.patientType.id;
    }

    // Preencher formulário de Dados Complementares
    this.dadosComplementaresForm.patchValue({
      profession: this.patient.profession,
      medicalRecordNumber: this.patient.medicalRecordNumber,
      category: this.patient.category || 'Rotina',
      patientTypeId: patientTypeIdToUse,
    });

    // Verificar se o tipo de paciente foi definido corretamente
    setTimeout(() => {
      // Se o valor ainda não estiver definido, tentar definir novamente
      if (
        !this.dadosComplementaresForm.get('patientTypeId')?.value &&
        patientTypeIdToUse
      ) {
        this.dadosComplementaresForm
          .get('patientTypeId')
          ?.setValue(patientTypeIdToUse);
      }
    }, 100);
  }

  // Formatar data para input
  formatDateForInput(date: Date): string {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }

  // Calcular idade a partir da data de nascimento
  calculateAge(birthDate: Date): void {
    const today = new Date();
    const birthDateObj = new Date(birthDate);
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDiff = today.getMonth() - birthDateObj.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDateObj.getDate())
    ) {
      age--;
    }

    this.calculatedAge = age;
    this.dadosCadastraisForm.get('age')?.setValue(age);
  }

  setCadastroSection(section: string): void {
    this.activeCadastroSection = section;
  }

  // Métodos para salvar cada seção
  saveDadosCadastrais(): void {
    if (!this.patient || this.dadosCadastraisForm.invalid) return;

    this.isSavingDadosCadastrais = true;

    // Obter valores do formulário
    const formValues = this.dadosCadastraisForm.value;

    // Preparar objeto para atualização
    const updateData: Partial<Patient> = {
      name: formValues.name,
      birthDate: formValues.birthDate,
      gender: formValues.gender,
      cpf: formValues.cpf,
      howDidYouFindUs: formValues.howDidYouFindUs,
      referredById: formValues.referredById,
      notes: formValues.notes,
    };

    // Se o paciente era incompleto e agora tem dados válidos, marcar como completo
    if (
      this.patient?.isIncomplete &&
      formValues.cpf &&
      formValues.cpf !== '000.000.000-00' &&
      formValues.birthDate &&
      formValues.birthDate !== '1900-01-01'
    ) {
      updateData.isIncomplete = false;
    }

    // Enviar atualização para a API
    this.patientService.updatePatient(this.patient.id, updateData).subscribe({
      next: (updatedPatient) => {
        this.patient = updatedPatient;
        this.isSavingDadosCadastrais = false;
      },
      error: (error) => {
        console.error('Erro ao atualizar dados cadastrais:', error);
        this.isSavingDadosCadastrais = false;
      },
    });
  }

  saveContato(): void {
    if (!this.patient || this.contatoForm.invalid) return;

    this.isSavingContato = true;

    // Obter valores do formulário
    const formValues = this.contatoForm.value;

    // Preparar objeto para atualização
    const updateData: Partial<Patient> = {
      email: formValues.email,
      phone: formValues.phone,
      whatsapp: formValues.whatsapp,
      addressZipCode: formValues.addressZipCode,
      addressStreet: formValues.addressStreet,
      addressNumber: formValues.addressNumber,
      addressNeighborhood: formValues.addressNeighborhood,
      addressCity: formValues.addressCity,
      addressState: formValues.addressState,
      addressComplement: formValues.addressComplement,
    };

    // Se o paciente era incompleto e agora tem email válido, verificar se pode marcar como completo
    if (
      this.patient?.isIncomplete &&
      formValues.email &&
      !formValues.email.includes('temp_') &&
      !formValues.email.includes('@temp.com')
    ) {
      // Verificar se os outros campos obrigatórios também estão preenchidos
      if (
        this.patient.cpf &&
        this.patient.cpf !== '000.000.000-00' &&
        this.patient.birthDate &&
        new Date(this.patient.birthDate).toISOString().split('T')[0] !==
          '1900-01-01'
      ) {
        updateData.isIncomplete = false;
      }
    }

    // Enviar atualização para a API
    this.patientService.updatePatient(this.patient.id, updateData).subscribe({
      next: (updatedPatient) => {
        this.patient = updatedPatient;
        this.isSavingContato = false;
      },
      error: (error) => {
        console.error('Erro ao atualizar dados de contato:', error);
        this.isSavingContato = false;
      },
    });
  }

  saveDadosComplementares(): void {
    if (!this.patient || this.dadosComplementaresForm.invalid) return;

    this.isSavingDadosComplementares = true;

    // Obter valores do formulário
    const formValues = this.dadosComplementaresForm.value;

    // Preparar objeto para atualização
    const updateData: Partial<Patient> = {
      profession: formValues.profession,
      medicalRecordNumber: formValues.medicalRecordNumber,
      category: formValues.category,
      patientTypeId: formValues.patientTypeId,
    };

    // Enviar atualização para a API
    this.patientService.updatePatient(this.patient.id, updateData).subscribe({
      next: (updatedPatient) => {
        this.patient = updatedPatient;
        this.isSavingDadosComplementares = false;
      },
      error: (error) => {
        console.error('Erro ao atualizar dados complementares:', error);
        this.isSavingDadosComplementares = false;
      },
    });
  }

  // Método para atualizar a idade quando a data de nascimento mudar
  onBirthDateChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.value) {
      this.calculateAge(new Date(input.value));
    } else {
      this.calculatedAge = null;
      this.dadosCadastraisForm.get('age')?.setValue(null);
    }
  }

  // Método para buscar endereço pelo CEP
  buscarCep(): void {
    const cep = this.contatoForm.get('addressZipCode')?.value;
    if (!cep || cep.length < 8) return;

    this.isLoadingCep = true;

    this.cepService.consultarCep(cep).subscribe({
      next: (resultado) => {
        if (resultado) {
          this.contatoForm.patchValue({
            addressStreet: resultado.logradouro,
            addressNeighborhood: resultado.bairro,
            addressCity: resultado.localidade,
            addressState: resultado.uf,
          });
        }
        this.isLoadingCep = false;
      },
      error: () => {
        this.isLoadingCep = false;
      },
    });
  }

  // Método para atualizar o campo de WhatsApp quando o checkbox mudar
  onIsWhatsappChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    const phoneValue = this.contatoForm.get('phone')?.value;

    if (checkbox.checked && phoneValue) {
      this.contatoForm.get('whatsapp')?.setValue(phoneValue);
    } else if (checkbox.checked && !phoneValue) {
      // Se o telefone estiver vazio, desmarcar o checkbox
      this.contatoForm.get('isWhatsapp')?.setValue(false);
    } else {
      // Se desmarcar o checkbox, limpar o campo de WhatsApp
      this.contatoForm.get('whatsapp')?.setValue('');
    }
  }

  // Métodos para aplicar máscaras
  onCpfInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCPF(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.dadosCadastraisForm
      .get('cpf')
      ?.setValue(maskedValue, { emitEvent: false });
  }

  onPhoneInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.contatoForm.get('phone')?.setValue(maskedValue, { emitEvent: false });

    // Se o checkbox estiver marcado, atualiza também o campo de WhatsApp
    if (this.contatoForm.get('isWhatsapp')?.value) {
      this.contatoForm.get('whatsapp')?.setValue(maskedValue);
    }
  }

  onWhatsappInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.contatoForm
      .get('whatsapp')
      ?.setValue(maskedValue, { emitEvent: false });
  }

  onCepInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCEP(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.contatoForm
      .get('addressZipCode')
      ?.setValue(maskedValue, { emitEvent: false });
  }
}
