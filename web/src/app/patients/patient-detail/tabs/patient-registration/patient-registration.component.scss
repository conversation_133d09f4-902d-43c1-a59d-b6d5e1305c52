/* Estilos para a navegação vertical */
.vertical-nav {
  @apply border-r border-gray-200 h-full;

  .nav-item {
    @apply flex items-center px-4 py-3 cursor-pointer transition-all duration-200;

    &:hover {
      @apply bg-blue-50;
    }

    &.active {
      @apply bg-blue-100 border-l-4 border-blue-500;
    }

    .nav-icon {
      @apply mr-3 text-gray-500;
    }

    &.active .nav-icon {
      @apply text-blue-500;
    }

    .nav-text {
      @apply text-sm font-medium;
    }
  }
}

// Estilos para os formulários
.form-section {
  @apply p-6;

  .form-title {
    @apply text-lg font-semibold text-gray-800 mb-6;
  }

  .form-row {
    @apply mb-4;
  }

  label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  input, select, textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }

  textarea {
    @apply h-auto;
    min-height: 100px;
  }

  .disabled-field {
    @apply bg-gray-100 cursor-not-allowed;
  }

  .form-footer {
    @apply mt-6 flex justify-end;
  }
}

// Estilos para botões
.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium transition-colors;

  &.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  &.btn-secondary {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
  }

  &.btn-loading {
    @apply opacity-75 cursor-wait;
  }
}

// Estilos para o layout de duas colunas
.two-column-layout {
  @apply grid grid-cols-1 md:grid-cols-4 gap-0 h-full;

  .left-column {
    @apply col-span-1 border-r border-gray-200;
  }

  .right-column {
    @apply col-span-3 overflow-auto;
  }
}

// Estilos responsivos
@media (max-width: 768px) {
  .two-column-layout {
    @apply grid-cols-1;

    .left-column {
      @apply border-r-0 border-b border-gray-200;
    }

    .vertical-nav {
      @apply flex flex-row overflow-x-auto;

      .nav-item {
        @apply flex-shrink-0;
      }
    }
  }
}

.checkbox {
  width: 20px !important;
}
