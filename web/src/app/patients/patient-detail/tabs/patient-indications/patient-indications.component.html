<div class="p-4">
  <div class="mb-4">
    <h2 class="text-xl font-semibold text-gray-800">Indicações</h2>
    <p class="text-sm text-gray-600">
      Visualize as indicações relacionadas a este paciente
    </p>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4"
  >
    <p>{{ error }}</p>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!loading && !error && indications.length === 0"
    class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12 mx-auto text-gray-400 mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
    <p class="text-gray-600">
      Este paciente não possui indicações registradas.
    </p>
  </div>

  <!-- Data table -->
  <div
    *ngIf="!loading && !error && indications.length > 0"
    class="overflow-x-auto"
  >
    <table
      class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden"
    >
      <thead class="bg-gray-50">
        <tr>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Status
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Paciente
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Tipo
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Etiquetas
          </th>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Observação
          </th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-200">
        <tr *ngFor="let indication of indications" class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ indication.date | date : "dd/MM/yyyy" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span
              [class]="
                'px-2 py-1 text-xs font-medium rounded-full ' +
                getStatusClass(indication.status)
              "
            >
              {{ indicationStatusLabels[indication.status] }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <div *ngIf="indication.indicatedPatient">
              <span class="font-medium">Indicou:</span>
              {{ indication.indicatedPatient.name }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <span class="text-green-600">Enviada</span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ formatTags(indication.tags) }}
          </td>
          <td class="px-6 py-4 text-sm text-gray-900">
            {{ indication.observation || "-" }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
