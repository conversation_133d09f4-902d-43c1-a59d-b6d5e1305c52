import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  Indication,
  IndicationStatus,
  IndicationStatusLabels,
} from '../../../../core/models/indication.model';
import { IndicationService } from '../../../../core/services/indication.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-patient-indications',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './patient-indications.component.html',
  styleUrls: ['./patient-indications.component.scss'],
})
export class PatientIndicationsComponent implements OnInit {
  @Input() patientId!: number;

  indications: Indication[] = [];
  loading = false;
  error: string | null = null;

  // Enums e labels para o template
  indicationStatus = IndicationStatus;
  indicationStatusLabels = IndicationStatusLabels;

  constructor(
    private indicationService: IndicationService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadIndications();
  }

  loadIndications(): void {
    if (!this.patientId) {
      this.error = 'ID do paciente não fornecido';
      return;
    }

    this.loading = true;
    this.error = null;

    this.indicationService.getPatientIndications(this.patientId).subscribe({
      next: (indications) => {
        // Filtrar apenas as indicações onde o paciente atual é o que indicou (referredById)
        this.indications = indications.filter(
          (indication) => indication.referredById === this.patientId
        );
        this.loading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar indicações:', err);
        this.error =
          'Não foi possível carregar as indicações. Por favor, tente novamente mais tarde.';
        this.loading = false;
        this.notificationService.error('Erro ao carregar indicações');
      },
    });
  }

  getStatusClass(status: IndicationStatus): string {
    switch (status) {
      case IndicationStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case IndicationStatus.CONFIRMED:
        return 'bg-green-100 text-green-800';
      case IndicationStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatTags(tags?: string[]): string {
    if (!tags || tags.length === 0) return '-';
    return tags.join(', ');
  }
}
