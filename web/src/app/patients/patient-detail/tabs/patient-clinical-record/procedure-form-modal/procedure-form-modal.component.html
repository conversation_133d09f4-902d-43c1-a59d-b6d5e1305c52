<div class="p-6">
  <form [formGroup]="procedureForm" (ngSubmit)="onSubmit()" #formElement="ngForm">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Procedimento -->
      <div class="col-span-2">
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Procedimento <span class="text-red-500">*</span>
        </label>
        <app-procedure-search
          [procedures]="procedures"
          [selectedProcedure]="selectedProcedure"
          (procedureSelected)="onProcedureSelected($event)"
        ></app-procedure-search>
        <div
          *ngIf="!selectedProcedure && error && error.includes('procedimento')"
          class="text-red-500 text-xs mt-1"
        >
          Selecione um procedimento
        </div>
      </div>

      <!-- Valor -->
      <div class="col-span-1">
        <label for="value" class="block text-sm font-medium text-gray-700 mb-1">
          Valor <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <span class="absolute z-[2] inset-y-0 left-0 flex items-center pl-3 text-gray-500">R$</span>
          <input
            type="text"
            id="value"
            #valueInput
            [value]="formattedValue"
            (input)="onValueInput(valueInput.value)"
            placeholder="0,00"
            class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-300 focus:border-red-500 focus:ring-red-500':
                valueControl?.invalid && valueControl?.touched
            }"
          />
        </div>
        <div
          *ngIf="valueControl?.invalid && valueControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          <div *ngIf="valueControl?.errors?.['required']">O valor é obrigatório</div>
          <div *ngIf="valueControl?.errors?.['min']">O valor não pode ser negativo</div>
        </div>
      </div>

      <!-- Dente -->
      <div class="col-span-1">
        <label for="tooth" class="block text-sm font-medium text-gray-700 mb-1">
          Dente
        </label>
        <input
          type="text"
          id="tooth"
          formControlName="tooth"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- Profissional -->
      <div class="col-span-1 md:col-span-2">
        <label for="professionalId" class="block text-sm font-medium text-gray-700 mb-1">
          Profissional <span class="text-red-500">*</span>
        </label>
        <select
          id="professionalId"
          formControlName="professionalId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              professionalIdControl?.invalid && professionalIdControl?.touched
          }"
        >
          <option value="">Selecione um profissional</option>
          <option *ngFor="let dentist of dentists" [value]="dentist.id">
            {{ dentist.name }}
          </option>
        </select>
        <div
          *ngIf="professionalIdControl?.invalid && professionalIdControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          Selecione um profissional
        </div>
      </div>

      <!-- Observações -->
      <div class="col-span-2">
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
          Observações
        </label>
        <textarea
          id="notes"
          formControlName="notes"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <!-- Detalhes para próxima consulta -->
      <div class="col-span-2">
        <label for="nextVisitDetails" class="block text-sm font-medium text-gray-700 mb-1">
          Detalhes para próxima consulta
        </label>
        <textarea
          id="nextVisitDetails"
          formControlName="nextVisitDetails"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>
    </div>

    <!-- Mensagem de erro -->
    <div *ngIf="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
      {{ error }}
    </div>
  </form>
</div>
