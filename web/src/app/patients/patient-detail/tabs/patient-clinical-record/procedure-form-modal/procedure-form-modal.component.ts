import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { TreatmentPlanService } from '../../../../../core/services/treatment-plan.service';
import { ProcedureService } from '../../../../../core/services/procedure.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { Dentist } from '../../../../../core/models/dentist.model';
import { Procedure } from '../../../../../core/models/procedure.model';
import { TreatmentProcedure } from '../../../../../core/models/treatment-procedure.model';
import { NotificationService } from '../../../../../core/services/notification.service';
import { firstValueFrom } from 'rxjs';
import { ProcedureSearchComponent } from '../../patient-budgets/procedure-search/procedure-search.component';

@Component({
  selector: 'app-patient-procedure-form-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, ProcedureSearchComponent],
  templateUrl: './procedure-form-modal.component.html',
  styleUrls: ['./procedure-form-modal.component.scss']
})
export class PatientProcedureFormModalComponent implements OnInit {
  @Input() patientId: number | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<TreatmentProcedure>();

  procedureForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  dentists: Dentist[] = [];
  procedures: Procedure[] = [];
  selectedProcedure: Procedure | null = null;

  // Valor formatado para exibição
  formattedValue: string = '0,00';

  constructor(
    private fb: FormBuilder,
    private treatmentPlanService: TreatmentPlanService,
    private procedureService: ProcedureService,
    private dentistService: DentistService,
    private notificationService: NotificationService
  ) {
    this.procedureForm = this.fb.group({
      procedureId: [null],
      value: [0, [Validators.required, Validators.min(0)]],
      tooth: [''],
      professionalId: ['', [Validators.required]],
      notes: [''],
      nextVisitDetails: ['']
    });
  }

  ngOnInit(): void {
    this.loadDentists();
    this.loadProcedures();
  }

  async loadDentists(): Promise<void> {
    try {
      const response = await firstValueFrom(this.dentistService.getDentists(1, 1000));
      this.dentists = response.data;
    } catch (error) {
      console.error('Erro ao carregar dentistas:', error);
      this.notificationService.error('Erro ao carregar dentistas');
    }
  }

  async loadProcedures(): Promise<void> {
    try {
      const response = await firstValueFrom(this.procedureService.getProcedures(1, 1000));
      this.procedures = response.data;
    } catch (error) {
      console.error('Erro ao carregar procedimentos:', error);
      this.notificationService.error('Erro ao carregar procedimentos');
    }
  }

  onProcedureSelected(procedure: Procedure | null): void {
    this.selectedProcedure = procedure;

    if (procedure) {
      // Atualizar o valor do procedimento no formulário
      const value = procedure.defaultPrice || 0;
      this.procedureForm.patchValue({
        procedureId: procedure.id,
        value: value
      });

      // Atualizar o valor formatado
      this.formattedValue = this.formatCurrency(value);
    } else {
      // Resetar o valor se nenhum procedimento for selecionado
      this.procedureForm.patchValue({
        procedureId: null,
        value: 0
      });
      this.formattedValue = '0,00';
    }
  }

  /**
   * Formata um valor numérico para o formato de moeda brasileira
   */
  formatCurrency(value: number): string {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * Processa a entrada do usuário no campo de moeda
   */
  onValueInput(value: string): void {
    // Remove todos os caracteres não numéricos
    let numericValue = value.replace(/\D/g, '');

    // Converte para número (em centavos)
    let cents = parseInt(numericValue, 10);

    // Se não for um número válido, define como zero
    if (isNaN(cents)) {
      cents = 0;
    }

    // Converte centavos para o formato de moeda (divide por 100 para obter o valor real)
    const realValue = cents / 100;

    // Formata o valor como moeda brasileira
    this.formattedValue = this.formatCurrency(realValue);

    // Atualiza o valor no formulário
    this.procedureForm.get('value')?.setValue(realValue);
  }

  // Método público para ser chamado pelo componente pai
  async onSubmit(): Promise<void> {
    if (this.procedureForm.invalid || !this.patientId || !this.selectedProcedure) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.procedureForm.controls).forEach(key => {
        const control = this.procedureForm.get(key);
        control?.markAsTouched();
      });

      if (!this.patientId) {
        this.error = 'ID do paciente não fornecido';
      }

      if (!this.selectedProcedure) {
        this.error = 'Selecione um procedimento';
      }

      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      const formData = this.procedureForm.value;

      // Garantir que o valor seja um número válido
      let value = 0;
      if (typeof formData.value === 'number') {
        value = formData.value;
      } else if (typeof formData.value === 'string') {
        // Tentar converter string para número
        const parsedValue = parseFloat(String(formData.value).replace(/[^\d.,]/g, '').replace(',', '.'));
        value = isNaN(parsedValue) ? 0 : parsedValue;
      }

      console.log('Valor original:', formData.value);
      console.log('Valor convertido:', value);

      const procedureData = {
        patientId: Number(this.patientId),
        procedureId: Number(this.selectedProcedure.id),
        name: this.selectedProcedure.name,
        value: value,
        tooth: formData.tooth || undefined,
        professionalId: Number(formData.professionalId),
        notes: formData.notes || undefined,
        nextVisitDetails: formData.nextVisitDetails || undefined
      };

      console.log('Dados do procedimento a serem enviados:', procedureData);

      const savedProcedure = await firstValueFrom(
        this.treatmentPlanService.createProcedureWithAutoPlan(procedureData)
      );

      this.saved.emit(savedProcedure);
      this.resetForm();
    } catch (err: any) {
      console.error('Erro ao salvar procedimento:', err);

      if (err.error?.message && Array.isArray(err.error.message)) {
        this.error = err.error.message.join(', ');
      } else if (err.error?.message) {
        this.error = err.error.message;
      } else {
        this.error = 'Ocorreu um erro ao salvar o procedimento. Por favor, tente novamente.';
      }
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.procedureForm.reset({
      procedureId: null,
      value: 0,
      tooth: '',
      professionalId: '',
      notes: '',
      nextVisitDetails: ''
    });
    this.selectedProcedure = null;
    this.error = null;
  }

  onCancel(): void {
    this.close.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get valueControl() { return this.procedureForm.get('value'); }
  get toothControl() { return this.procedureForm.get('tooth'); }
  get professionalIdControl() { return this.procedureForm.get('professionalId'); }
  get notesControl() { return this.procedureForm.get('notes'); }
  get nextVisitDetailsControl() { return this.procedureForm.get('nextVisitDetails'); }
  get procedureIdControl() { return this.procedureForm.get('procedureId'); }


}
