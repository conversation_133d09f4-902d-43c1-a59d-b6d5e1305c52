<app-modal
  [title]="'Detalhes do Procedimento'"
  [isOpen]="true"
  [showDefaultFooter]="true"
  [fullscreen]="false"
  class="procedure-modal"
  (close)="closeModal()">

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Modal content -->
  <div *ngIf="!isLoading && procedure" class="max-h-[calc(100vh-180px)] overflow-y-auto p-6">
    <form [formGroup]="procedureForm">
      <div class="space-y-6">
        <!-- Informações do procedimento (não editáveis) -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="text-md font-medium text-gray-800 mb-4">Informações do Procedimento</h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Nome:</p>
              <p class="text-sm text-gray-900 font-medium">{{ procedure.name }}</p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Status:</p>
              <p class="text-sm text-gray-900">
                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                      [ngClass]="{
                        'bg-blue-100 text-blue-800': procedure.status === 'pending',
                        'bg-orange-100 text-orange-800': procedure.status === 'in_progress',
                        'bg-green-100 text-green-800': procedure.status === 'completed',
                        'bg-red-100 text-red-800': procedure.status === 'cancelled'
                      }">
                  {{ procedure.status === 'pending' ? 'Pendente' :
                     procedure.status === 'in_progress' ? 'Em andamento' :
                     procedure.status === 'completed' ? 'Concluído' :
                     procedure.status === 'cancelled' ? 'Cancelado' : 'Desconhecido' }}
                </span>
              </p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Dente:</p>
              <p class="text-sm text-gray-900">{{ procedure.tooth || 'Não informado' }}</p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Valor:</p>
              <p class="text-sm text-gray-900">R$ {{ procedure.value.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Data de Execução:</p>
              <p class="text-sm text-gray-900">{{ procedure.executionDate ? (procedure.executionDate | date: 'dd/MM/yyyy') : 'Não executado' }}</p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-700 mb-1">Dentista:</p>
              <p class="text-sm text-gray-900">{{ dentistName }}</p>
            </div>
          </div>
        </div>

        <!-- Campos editáveis -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
            <textarea
              id="notes"
              formControlName="notes"
              rows="5"
              placeholder="Adicione observações sobre o procedimento"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>

          <div>
            <label for="nextVisitDetails" class="block text-sm font-medium text-gray-700 mb-2">Detalhes para a próxima consulta</label>
            <textarea
              id="nextVisitDetails"
              formControlName="nextVisitDetails"
              rows="5"
              placeholder="Adicione detalhes para a próxima consulta"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer buttons -->
  <div footer class="flex justify-end space-x-4">
    <button
      type="button"
      class="px-5 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 min-w-[120px]"
      (click)="closeModal()">
      Cancelar
    </button>
    <button
      type="button"
      class="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed min-w-[120px]"
      [disabled]="isSubmitting || procedureForm.invalid"
      (click)="onSubmit()">
      <span *ngIf="isSubmitting" class="inline-block animate-spin mr-2">⟳</span>
      Salvar
    </button>
  </div>
</app-modal>
