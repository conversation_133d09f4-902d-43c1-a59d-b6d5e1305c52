import { Component, EventEmitter, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TreatmentProcedure } from '../../../../../core/models/treatment-procedure.model';
import { TreatmentPlanService } from '../../../../../core/services/treatment-plan.service';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { HttpClient } from '@angular/common/http';
import { NotificationService } from '../../../../../core/services/notification.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { Dentist } from '../../../../../core/models/dentist.model';

@Component({
  selector: 'app-procedure-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent
  ],
  providers: [
    TreatmentPlanService,
    HttpClient,
    NotificationService,
    DentistService
  ],
  templateUrl: './procedure-modal.component.html',
  styleUrl: './procedure-modal.component.scss'
})
export class ProcedureModalComponent implements OnInit {
  @Input() procedure: TreatmentProcedure | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() procedureUpdated = new EventEmitter<TreatmentProcedure>();

  isLoading = false;
  isSubmitting = false;
  dentists: Dentist[] = [];
  dentistName: string = 'Não informado';

  private fb = inject(FormBuilder);
  private treatmentPlanService = inject(TreatmentPlanService);
  private dentistService = inject(DentistService);

  procedureForm: FormGroup;

  constructor() {
    this.procedureForm = this.fb.group({
      notes: [''],
      nextVisitDetails: ['']
    });
  }

  ngOnInit(): void {
    // Carregar dentistas
    this.loadDentists();

    if (this.procedure) {
      console.log('Inicializando modal de procedimento com dados:', {
        id: this.procedure.id,
        name: this.procedure.name,
        professionalId: this.procedure.professionalId,
        professional: this.procedure.professional,
        notes: this.procedure.notes,
        nextVisitDetails: this.procedure.nextVisitDetails
      });

      this.procedureForm.patchValue({
        notes: this.procedure.notes || '',
        nextVisitDetails: this.procedure.nextVisitDetails || ''
      });

      // Buscar o nome do dentista
      this.updateDentistName();
    }
  }

  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
        this.updateDentistName();
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
      }
    });
  }

  updateDentistName(): void {
    if (!this.procedure || !this.procedure.professionalId) {
      this.dentistName = 'Não informado';
      return;
    }

    // Verificar se já temos o objeto professional
    if (this.procedure.professional?.name) {
      this.dentistName = this.procedure.professional.name;
      return;
    }

    // Buscar o dentista pelo ID
    const dentist = this.dentists.find(d => d.id === this.procedure?.professionalId);
    if (dentist) {
      this.dentistName = dentist.name;
    } else {
      this.dentistName = `ID: ${this.procedure.professionalId}`;
    }
  }

  onSubmit(): void {
    if (this.procedureForm.invalid || !this.procedure?.id) {
      return;
    }

    this.isSubmitting = true;
    const updateDto = {
      notes: this.procedureForm.value.notes,
      nextVisitDetails: this.procedureForm.value.nextVisitDetails
    };

    console.log('Enviando atualização para o procedimento:', this.procedure.id);
    console.log('Dados a serem enviados:', updateDto);

    this.treatmentPlanService.updateProcedure(this.procedure.id, updateDto).subscribe({
      next: (updatedProcedure) => {
        console.log('Procedimento atualizado com sucesso:', updatedProcedure);
        console.log('Campos atualizados - notes:', updatedProcedure.notes);
        console.log('Campos atualizados - nextVisitDetails:', updatedProcedure.nextVisitDetails);

        this.isSubmitting = false;
        this.procedureUpdated.emit(updatedProcedure);
        this.closeModal();
      },
      error: (error) => {
        console.error('Erro ao atualizar procedimento:', error);
        console.error('Detalhes do erro:', error.error || error.message || 'Erro desconhecido');
        console.error('Status do erro:', error.status);
        console.error('Dados enviados:', updateDto);

        this.isSubmitting = false;
      }
    });
  }

  closeModal(): void {
    this.close.emit();
  }
}
