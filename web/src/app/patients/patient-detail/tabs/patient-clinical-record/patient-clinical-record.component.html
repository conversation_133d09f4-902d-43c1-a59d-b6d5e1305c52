<div class="bg-gray-50 p-4 rounded-lg">
  <!-- Loading indicator -->
  <div *ngIf="isLoadingPlans || isLoadingProcedures" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
    <span class="ml-3 text-gray-600">Carregando...</span>
  </div>

  <div *ngIf="!isLoadingPlans && !isLoadingProcedures">
    <!-- Accordion 1: Planos de Tratamento Aprovados -->
    <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
      <div
        class="flex justify-between items-center cursor-pointer"
        (click)="togglePlansAccordion()"
      >
        <h2 class="text-lg font-medium text-gray-900">Planos de Tratamento Aprovados</h2>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-400 transition-transform duration-300"
          [ngClass]="{'rotate-180': isPlansAccordionOpen}"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <!-- Conteúdo do accordion principal -->
      <div *ngIf="isPlansAccordionOpen" class="mt-4 space-y-4 transition-all duration-300">
        <div *ngIf="treatmentPlans.length === 0" class="bg-gray-50 p-4 rounded-lg text-center text-gray-500">
          Nenhum plano de tratamento aprovado para este paciente.
        </div>

        <div *ngIf="treatmentPlans.length > 0" class="space-y-4">
          <div *ngFor="let plan of treatmentPlans" class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden" [ngClass]="{'border-blue-300 shadow-md': expandedPlanId === plan.id}">
            <!-- Cabeçalho do accordion do plano -->
            <div
              class="p-4 flex flex-col md:flex-row md:items-center justify-between cursor-pointer hover:bg-gray-100 transition-colors"
              (click)="togglePlanDetails(plan.id || 0); $event.stopPropagation()"
            >
              <div class="flex items-center space-x-3 mb-2 md:mb-0">
                <span class="px-2 py-1 text-xs font-semibold rounded-full"
                      [ngClass]="getStatusClass(plan.status)">
                  {{ getStatusLabel(plan.status) }}
                </span>
                <span class="text-sm">{{ formatDate(plan.createdAt) }}</span>
                <span class="text-sm text-gray-600">
                  <span *ngIf="plan.budget?.notes"
                        title="{{ plan.budget?.notes }}"
                        class="cursor-help">
                    {{ (plan.budget?.notes ?? '').length > 30 ? (plan.budget?.notes ?? '').substring(0, 30) + '...' : plan.budget?.notes }}
                  </span>
                  <span *ngIf="!plan.budget?.notes">Sem observações</span>
                </span>
              </div>
              <div class="flex items-center space-x-4">
                <span class="text-sm">{{ plan.dentist?.name || plan.budget?.dentist?.name || 'Dentista não informado' }}</span>
                <span class="text-sm font-medium">R$ {{ formatValue(plan.totalValue) }}</span>
                <div class="flex items-center">
                  <div class="w-20 bg-gray-200 rounded-full h-2.5 mr-2">
                    <div class="bg-blue-600 h-2.5 rounded-full" [style.width.%]="getCompletionPercentageNumber(plan)"></div>
                  </div>
                  <span class="text-xs text-gray-600">{{ getCompletionPercentage(plan) }}%</span>
                </div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-gray-400 transition-transform duration-300"
                  [ngClass]="{'rotate-180': expandedPlanId === plan.id}"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            <!-- Conteúdo do accordion do plano -->
            <div *ngIf="expandedPlanId === plan.id" class="p-4 border-t border-gray-200 bg-white transition-all duration-300">
              <div class="space-y-6">
                <!-- Mensagem quando não há procedimentos -->
                <div *ngIf="!hasProcedures(plan)" class="text-center text-gray-500 py-4">
                  Este plano de tratamento não possui procedimentos.
                </div>

                <!-- Container com scrollbar para todos os procedimentos -->
                <div *ngIf="hasProcedures(plan)" class="space-y-6 max-h-[500px] overflow-y-auto pr-2">

                  <!-- 1. Procedimentos Em Andamento -->
                  <div *ngIf="getInProgressProcedures(plan).length > 0" class="space-y-4">
                    <div class="bg-orange-100 flex items-center p-2 w-full rounded sticky top-0 z-10">
                      <h3 class="text-md font-medium text-orange-800">Em Andamento</h3>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Procedimento
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Dentista
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Valor
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Ações
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr *ngFor="let procedure of getInProgressProcedures(plan)" class="hover:bg-gray-50">
                            <td class="px-4 py-3 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                                  <div *ngIf="procedure.tooth" class="text-xs text-gray-500">Dente: {{ procedure.tooth }}</div>
                                </div>
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                    [ngClass]="getStatusClass(procedure.status)">
                                {{ getStatusLabel(procedure.status) }}
                              </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <div class="text-sm text-gray-900">{{ getDentistName(procedure.professionalId) }}</div>
                              <div class="text-xs text-gray-500 mt-1">
                                O dentista não pode ser alterado após o início do procedimento
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              R$ {{ formatValue(procedure.value) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                              <div class="flex space-x-2">
                                <button (click)="finishProcedure(procedure)"
                                       class="text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-2 py-1 rounded">
                                  Finalizar
                                </button>
                                <button (click)="deleteProcedure(procedure)"
                                       class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded">
                                  Remover
                                </button>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- 2. Procedimentos Pendentes -->
                  <div *ngIf="getPendingProcedures(plan).length > 0" class="space-y-4">
                    <div class="bg-blue-100 flex items-center p-2 w-full rounded sticky top-0 z-10">
                      <h3 class="text-md font-medium text-blue-800">Pendentes</h3>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Procedimento
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Dentista
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Valor
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Ações
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr *ngFor="let procedure of getPendingProcedures(plan)" class="hover:bg-gray-50">
                            <td class="px-4 py-3 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                                  <div *ngIf="procedure.tooth" class="text-xs text-gray-500">Dente: {{ procedure.tooth }}</div>
                                </div>
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                    [ngClass]="getStatusClass(procedure.status)">
                                {{ getStatusLabel(procedure.status) }}
                              </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <!-- Seletor de dentista (desabilitado se o procedimento estiver em andamento) -->
                              <div class="relative">
                                <select
                                  [(ngModel)]="procedure.professionalId"
                                  (ngModelChange)="onDentistChange(procedure, $event)"
                                  [name]="'dentist-' + procedure.id"
                                  class="block w-full px-3 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  [ngClass]="{'border-red-500': !procedure.professionalId, 'border-gray-300': procedure.professionalId}"
                                >
                                <option [ngValue]="null" disabled>Selecione um dentista</option>
                                <option *ngFor="let dentist of dentists" [ngValue]="dentist.id">
                                  {{ dentist.name }}
                                </option>
                              </select>
                                <div *ngIf="!procedure.professionalId" class="text-xs text-red-500 mt-1">
                                  Selecione um dentista para executar
                                </div>
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              R$ {{ formatValue(procedure.value) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                              <div class="flex space-x-2">
                                <button (click)="executeProcedure(procedure)"
                                       [disabled]="!procedure.professionalId"
                                       class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded"
                                       [ngClass]="{'opacity-50 cursor-not-allowed': !procedure.professionalId}"
                                       [title]="!procedure.professionalId ? 'Selecione um dentista antes de executar' : 'Executar procedimento'"
                                       #executeButton>
                                  Executar
                                </button>
                                <button (click)="deleteProcedure(procedure)"
                                       class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded">
                                  Remover
                                </button>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- 3. Procedimentos Concluídos -->
                  <div *ngIf="getCompletedProcedures(plan).length > 0" class="space-y-4">
                    <div class="bg-green-100 flex items-center p-2 w-full rounded sticky top-0 z-10">
                      <h3 class="text-md font-medium text-green-800">Concluídos</h3>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Procedimento
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Data da Execução
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Dentista
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Valor
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Ações
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr *ngFor="let procedure of getCompletedProcedures(plan)" class="hover:bg-gray-50">
                            <td class="px-4 py-3 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                                  <div *ngIf="procedure.tooth" class="text-xs text-gray-500">Dente: {{ procedure.tooth }}</div>
                                </div>
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                    [ngClass]="getStatusClass(procedure.status)">
                                {{ getStatusLabel(procedure.status) }}
                              </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              {{ formatDate(procedure.executionDate) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              {{ getDentistName(procedure.professionalId) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              R$ {{ formatValue(procedure.value) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                              <button (click)="cancelExecution(procedure)"
                                     class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded">
                                Cancelar Execução
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- 4. Procedimentos Cancelados -->
                  <div *ngIf="getCancelledProcedures(plan).length > 0" class="space-y-4">
                    <div class="bg-red-100 flex items-center p-2 w-full rounded sticky top-0 z-10">
                      <h3 class="text-md font-medium text-red-800">Cancelados</h3>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Procedimento
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Dentista
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Valor
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Ações
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr *ngFor="let procedure of getCancelledProcedures(plan)" class="hover:bg-gray-50">
                            <td class="px-4 py-3 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                                  <div *ngIf="procedure.tooth" class="text-xs text-gray-500">Dente: {{ procedure.tooth }}</div>
                                </div>
                              </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                    [ngClass]="getStatusClass(procedure.status)">
                                {{ getStatusLabel(procedure.status) }}
                              </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              {{ getDentistName(procedure.professionalId) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                              R$ {{ formatValue(procedure.value) }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                              <button (click)="deleteProcedure(procedure)"
                                     class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded">
                                Remover
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                </div>
            </div>
          </div>
        </div>
      </div>
    </div>



  </div>

    <!-- Accordion 2: Ficha Clínica -->
    <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
      <div class="flex justify-between items-center">
        <div
          class="flex items-center cursor-pointer"
          (click)="toggleClinicalRecordAccordion()"
        >
          <h2 class="text-lg font-medium text-gray-900">Ficha Clínica do Paciente</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-400 transition-transform duration-300 ml-2"
            [ngClass]="{'rotate-180': isClinicalRecordAccordionOpen}"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        <button
          (click)="openProcedureFormModal(); $event.stopPropagation()"
          class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-1.5 px-3 rounded-md flex items-center transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
              clip-rule="evenodd"
            />
          </svg>
          Adicionar procedimento
        </button>
      </div>

      <!-- Conteúdo do accordion da ficha clínica -->
      <div *ngIf="isClinicalRecordAccordionOpen" class="mt-4 transition-all duration-300">
        <!-- Filtros -->
        <div class="mb-4 flex items-center space-x-4">
          <div class="w-64">
            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Filtrar por status</label>
            <select
              id="statusFilter"
              [(ngModel)]="statusFilter"
              (change)="onStatusFilterChange()"
              class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">Todos</option>
              <option value="pending">A realizar</option>
              <option value="scheduled">Agendado</option>
              <option value="in_progress">Em andamento</option>
              <option value="completed">Executados</option>
              <option value="cancelled">Cancelados</option>
            </select>
          </div>
        </div>

        <!-- Tabela de procedimentos -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data da Execução
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Procedimento
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dente
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dentista
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngIf="filteredProcedures.length === 0">
                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                  Nenhum procedimento encontrado com os filtros selecionados.
                </td>
              </tr>
              <tr *ngFor="let procedure of filteredProcedures" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(procedure.executionDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                  <div *ngIf="procedure.treatmentPlanId" class="text-xs text-gray-500">
                    Plano #{{ procedure.treatmentPlanId }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ procedure.tooth || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ getDentistName(procedure.professionalId) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                        [ngClass]="getStatusClass(procedure.status)">
                    {{ getStatusLabel(procedure.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button (click)="openProcedureModal(procedure)"
                         class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded">
                    Editar
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Paginação -->
        <div *ngIf="totalItems > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> a
                <span class="font-medium">{{ Math.min(currentPage * pageSize, totalItems) }}</span> de
                <span class="font-medium">{{ totalItems }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  (click)="onPageChange(currentPage - 1)"
                  [disabled]="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  [class.opacity-50]="currentPage === 1"
                  [class.cursor-not-allowed]="currentPage === 1"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>

                <!-- Páginas -->
                <ng-container *ngFor="let page of [].constructor(Math.ceil(totalItems / pageSize)); let i = index">
                  <button
                    *ngIf="i + 1 === currentPage ||
                           i + 1 === 1 ||
                           i + 1 === Math.ceil(totalItems / pageSize) ||
                           (i + 1 >= currentPage - 1 && i + 1 <= currentPage + 1)"
                    (click)="onPageChange(i + 1)"
                    [class.bg-blue-50]="i + 1 === currentPage"
                    [class.text-blue-600]="i + 1 === currentPage"
                    [class.border-blue-500]="i + 1 === currentPage"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    {{ i + 1 }}
                  </button>

                  <!-- Ellipsis -->
                  <span
                    *ngIf="(i + 1 === 2 && currentPage > 3) ||
                           (i + 1 === Math.ceil(totalItems / pageSize) - 1 && currentPage < Math.ceil(totalItems / pageSize) - 2)"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >
                    ...
                  </span>
                </ng-container>

                <button
                  (click)="onPageChange(currentPage + 1)"
                  [disabled]="currentPage === Math.ceil(totalItems / pageSize)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  [class.opacity-50]="currentPage === Math.ceil(totalItems / pageSize)"
                  [class.cursor-not-allowed]="currentPage === Math.ceil(totalItems / pageSize)"
                >
                  <span class="sr-only">Próxima</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

<!-- Modal de Procedimento -->
<app-procedure-modal
  *ngIf="isModalOpen"
  [procedure]="selectedProcedure"
  (close)="onModalClose()"
  (procedureUpdated)="onProcedureUpdated($event)"
></app-procedure-modal>

<!-- Modal de Criação de Procedimento Avulso -->
<app-modal
  [isOpen]="isProcedureFormModalOpen"
  title="Adicionar procedimento avulso"
  (close)="closeProcedureFormModal()"
  [showDefaultFooter]="true"
>
  <app-patient-procedure-form-modal
    #procedureFormModal
    [patientId]="patient?.id || null"
    (close)="closeProcedureFormModal()"
    (saved)="onProcedureSaved($event)"
  ></app-patient-procedure-form-modal>

  <!-- Botões de ação no footer -->
  <div footer class="flex justify-end gap-3">
    <button
      type="button"
      (click)="closeProcedureFormModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="procedureFormModal.onSubmit()"
      [disabled]="procedureFormModal.isSubmitting"
      class="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
    >
      <span *ngIf="procedureFormModal.isSubmitting" class="mr-2">
        <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </span>
      Salvar
    </button>
  </div>
</app-modal>

<!-- Diálogo de Confirmação -->
<app-confirmation-dialog
  [isOpen]="isConfirmDialogOpen"
  [title]="confirmDialogTitle"
  [message]="confirmDialogMessage"
  (confirm)="onConfirmDialogClose(true)"
  (cancel)="onConfirmDialogClose(false)"
></app-confirmation-dialog>
