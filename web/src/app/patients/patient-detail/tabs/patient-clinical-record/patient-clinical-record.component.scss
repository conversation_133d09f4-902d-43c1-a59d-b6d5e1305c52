.treatment-plans-accordion {
  .mat-expansion-panel {
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .mat-expansion-panel-header {
      padding: 16px;
      
      .mat-expansion-panel-header-title,
      .mat-expansion-panel-header-description {
        margin-right: 0;
      }
      
      .mat-expansion-panel-header-description {
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}

// Ajustes para o mat-select dentro da tabela
.mat-form-field {
  margin-bottom: -1.25em;
}

// Ajustes para o mat-form-field
::ng-deep {
  .mat-form-field-wrapper {
    padding-bottom: 0;
  }
  
  .mat-form-field-infix {
    padding: 0.5em 0;
    border-top: 0.5em solid transparent;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.75em 0 0.75em;
    margin-top: -0.25em;
    height: 36px;
  }
}

// Ajustes para o mat-spinner
::ng-deep .mat-progress-spinner circle, .mat-spinner circle {
  stroke: #3b82f6;
}
