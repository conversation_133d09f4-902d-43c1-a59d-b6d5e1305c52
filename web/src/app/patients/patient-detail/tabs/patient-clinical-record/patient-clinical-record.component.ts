import { Component, Input, OnChanges, OnInit, On<PERSON>estroy, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Patient } from '../../../../core/models/patient.model';
import { TreatmentPlanService } from '../../../../core/services/treatment-plan.service';
import { TreatmentPlanUpdateService } from '../../../../core/services/treatment-plan-update.service';
import { DentistService } from '../../../../core/services/dentist.service';
import { TreatmentPlan, TreatmentPlanStatus } from '../../../../core/models/treatment-plan.model';
import { TreatmentProcedure, TreatmentProcedureStatus } from '../../../../core/models/treatment-procedure.model';
import { Dentist } from '../../../../core/models/dentist.model';
import { ProcedureModalComponent } from './procedure-modal/procedure-modal.component';
import { PatientProcedureFormModalComponent } from './procedure-form-modal/procedure-form-modal.component';
import { HttpClient } from '@angular/common/http';
import { NotificationService } from '../../../../core/services/notification.service';
import { ConfirmationDialogComponent } from '../../../../shared/components/confirmation-dialog/confirmation-dialog.component';
import { ModalComponent } from '../../../../shared/components/modal/modal.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-patient-clinical-record',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ProcedureModalComponent,
    PatientProcedureFormModalComponent,
    ConfirmationDialogComponent,
    ModalComponent
  ],
  providers: [
    TreatmentPlanService,
    DentistService,
    HttpClient,
    NotificationService
  ],
  templateUrl: './patient-clinical-record.component.html',
  styleUrl: './patient-clinical-record.component.scss'
})
export class PatientClinicalRecordComponent implements OnInit, OnChanges, OnDestroy {
  @Input() patient: Patient | null = null;

  // Referência ao objeto Math para usar no template
  Math = Math;

  // Planos de tratamento
  treatmentPlans: TreatmentPlan[] = [];
  expandedPlanId: number | null = null;
  isLoadingPlans = false;
  dentists: Dentist[] = [];
  isPlansAccordionOpen = true; // Accordion de planos de tratamento inicialmente aberto

  // Ficha clínica
  allProcedures: TreatmentProcedure[] = [];
  filteredProcedures: TreatmentProcedure[] = [];
  isLoadingProcedures = false;
  statusFilter: TreatmentProcedureStatus | 'all' = 'all';
  isClinicalRecordAccordionOpen = true; // Accordion de ficha clínica inicialmente aberto

  // Paginação da ficha clínica
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Modal de procedimento
  isModalOpen = false;
  selectedProcedure: TreatmentProcedure | null = null;

  // Modal de criação de procedimento avulso
  isProcedureFormModalOpen = false;

  // Diálogo de confirmação
  isConfirmDialogOpen = false;
  confirmDialogTitle = '';
  confirmDialogMessage = '';
  confirmDialogAction: () => void = () => {};

  // Subscription para gerenciar a inscrição no serviço de atualização
  private treatmentPlanUpdateSubscription: Subscription | null = null;

  private treatmentPlanService = inject(TreatmentPlanService);
  private dentistService = inject(DentistService);
  private notificationService = inject(NotificationService);
  private cdr = inject(ChangeDetectorRef);
  private treatmentPlanUpdateService = inject(TreatmentPlanUpdateService);

  ngOnInit(): void {
    this.loadDentists();
    if (this.patient) {
      this.loadTreatmentPlans();
      this.loadAllProcedures();
    }

    // Inscrever-se para receber atualizações do plano de tratamento
    this.treatmentPlanUpdateSubscription = this.treatmentPlanUpdateService.treatmentPlanUpdated$.subscribe(
      updatedPlan => {
        console.log('Plano de tratamento atualizado recebido no componente de ficha clínica:', updatedPlan);

        // Verificar se o plano atualizado está na lista de planos
        const index = this.treatmentPlans.findIndex(plan => plan.id === updatedPlan.id);
        if (index !== -1) {
          console.log(`Atualizando plano ${updatedPlan.id} na interface de ficha clínica`);

          // Preservar o estado de expansão do plano
          const wasExpanded = this.expandedPlanId === updatedPlan.id;

          // Atualizar o plano na lista
          this.treatmentPlans[index] = updatedPlan;

          // Restaurar o estado de expansão
          if (wasExpanded && updatedPlan.id !== undefined) {
            this.expandedPlanId = updatedPlan.id;
          }

          // Atualizar a lista de procedimentos na Ficha Clínica
          this.loadAllProcedures();

          // Forçar detecção de mudanças para atualizar a UI
          this.cdr.detectChanges();
        }
      }
    );

    // Adicionar um log para depuração após o carregamento inicial
    setTimeout(() => {
      this.logProceduresWithDentists();
    }, 2000);
  }

  ngOnDestroy(): void {
    // Cancelar a inscrição para evitar vazamentos de memória
    if (this.treatmentPlanUpdateSubscription) {
      this.treatmentPlanUpdateSubscription.unsubscribe();
    }
  }

  // Método para depuração
  logProceduresWithDentists(): void {
    console.log('=== DEPURAÇÃO DE PROCEDIMENTOS COM DENTISTAS ===');
    this.treatmentPlans.forEach(plan => {
      if (plan.procedures && plan.procedures.length > 0) {
        console.log(`Plano ${plan.id}:`);
        plan.procedures.forEach(procedure => {
          console.log(`  - Procedimento ${procedure.id}: ${procedure.name}`);
          console.log(`    Dentista ID: ${procedure.professionalId} (${typeof procedure.professionalId})`);
          console.log(`    Botão habilitado: ${!!procedure.professionalId}`);
        });
      }
    });
    console.log('=== FIM DA DEPURAÇÃO ===');
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && this.patient) {
      this.loadTreatmentPlans();
      this.loadAllProcedures();

      // Adicionar um log para depuração após a mudança
      setTimeout(() => {
        this.logProceduresWithDentists();
      }, 2000);
    }
  }

  // Método para garantir que o professionalId seja um número
  ensureProfessionalIdIsNumber(procedure: TreatmentProcedure): void {
    if (procedure.professionalId) {
      if (typeof procedure.professionalId === 'string') {
        procedure.professionalId = Number(procedure.professionalId);
      }
    }
  }

  // Método para garantir que todos os procedimentos tenham o professionalId como número
  ensureAllProfessionalIdsAreNumbers(): void {
    this.treatmentPlans.forEach(plan => {
      if (plan.procedures && plan.procedures.length > 0) {
        plan.procedures.forEach(procedure => {
          this.ensureProfessionalIdIsNumber(procedure);
        });
      }
    });

    // Forçar detecção de mudanças para atualizar a UI
    this.cdr.detectChanges();

    // Log para depuração
    this.logProceduresWithDentists();
  }

  // Método para garantir que todos os procedimentos tenham um dentista associado
  ensureProceduresHaveDentists(): void {
    this.treatmentPlans.forEach(plan => {
      if (plan.procedures && plan.procedures.length > 0) {
        console.log(`Verificando dentistas para o plano ${plan.id}`);

        // Verificar se cada procedimento tem um dentista associado
        plan.procedures.forEach(procedure => {
          // Verificar se o procedimento está com status "pending" (apenas procedimentos pendentes podem ter o dentista alterado)
          if (procedure.status !== TreatmentProcedureStatus.PENDING) {
            console.log(`Procedimento ${procedure.id} não está com status "pending", não será atualizado`);
            return; // Pular para o próximo procedimento
          }

          // Verificar se o procedimento tem um dentista associado
          if (procedure.professionalId) {
            console.log(`Procedimento ${procedure.id} já tem dentista: ${procedure.professionalId}`);

            // Não é necessário enviar para o backend, pois o dentista já está associado
            // e isso pode causar erros se o status do procedimento mudar entre a verificação e a atualização
          }
          // Se o procedimento não tem um dentista associado, verificar se o plano tem um orçamento
          else if (plan.budget && plan.budget.dentistId) {
            console.log(`Definindo dentista do orçamento (${plan.budget.dentistId}) para o procedimento ${procedure.id}`);
            procedure.professionalId = plan.budget.dentistId;

            // Atualizar o procedimento no backend
            this.treatmentPlanService.updateProcedure(procedure.id!, { professionalId: plan.budget.dentistId }).subscribe({
              next: (updatedProcedure) => {
                console.log('Dentista do procedimento atualizado com sucesso:', updatedProcedure);
              },
              error: (error) => {
                // Verificar se é o erro específico de não poder alterar o dentista
                const errorMessage = error.error?.message || '';
                const isDentistChangeRestrictionError = errorMessage.includes('Não é possível alterar o dentista de um procedimento');

                if (!isDentistChangeRestrictionError) {
                  console.error('Erro ao atualizar dentista do procedimento:', error);
                }
              }
            });
          } else {
            console.log(`Procedimento ${procedure.id} não tem dentista e não foi possível encontrar um dentista padrão`);
          }
        });
      }
    });

    // Forçar detecção de mudanças para atualizar a UI
    this.cdr.detectChanges();
  }

  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
      }
    });
  }

  loadTreatmentPlans(): void {
    if (!this.patient?.id) return;

    console.log('Carregando planos de tratamento para o paciente:', this.patient.id);
    this.isLoadingPlans = true;

    // Armazenar os planos atuais, suas porcentagens de conclusão e os dentistas selecionados
    const currentPlans = [...this.treatmentPlans];
    const currentPercentages = new Map<number, number>();

    // Mapa para armazenar os dentistas selecionados para cada procedimento
    const currentProcedureDentists = new Map<number, number>();

    // Salvar os dentistas selecionados para cada procedimento
    currentPlans.forEach(plan => {
      if (plan.id && typeof plan.completionPercentage === 'number') {
        currentPercentages.set(plan.id, plan.completionPercentage);
      }

      if (plan.procedures && Array.isArray(plan.procedures)) {
        plan.procedures.forEach(procedure => {
          if (procedure.id && procedure.professionalId) {
            console.log(`Salvando dentista atual para procedimento ${procedure.id}: ${procedure.professionalId}`);
            currentProcedureDentists.set(procedure.id, procedure.professionalId);
          }
        });
      }
    });

    this.treatmentPlanService.getTreatmentPlansByPatient(this.patient.id).subscribe({
      next: (plans) => {
        console.log('Planos recebidos do servidor:', plans);
        console.log('Total de planos recebidos:', plans.length);

        // Verificar se o dentista do orçamento está sendo carregado corretamente
        plans.forEach(plan => {
          console.log(`Plano ${plan.id} - Orçamento:`, plan.budget);
          if (plan.budget) {
            console.log(`Plano ${plan.id} - Dentista do orçamento:`, plan.budget.dentist);
          }
        });

        // Filtrar os planos
        const filteredPlans = plans.filter(plan =>
          plan.status === TreatmentPlanStatus.OPEN ||
          plan.status === TreatmentPlanStatus.COMPLETED
        );

        // Preservar as porcentagens de conclusão calculadas localmente
        filteredPlans.forEach(plan => {
          if (plan.id && currentPercentages.has(plan.id)) {
            const localPercentage = currentPercentages.get(plan.id);
            console.log(`Preservando porcentagem local para plano ${plan.id}: ${localPercentage}%`);

            // Usar a porcentagem local se for maior que a do servidor
            if (typeof localPercentage === 'number' &&
                (typeof plan.completionPercentage !== 'number' || localPercentage > plan.completionPercentage)) {
              plan.completionPercentage = localPercentage;
            }
          }
        });

        this.treatmentPlans = filteredPlans;
        console.log('Planos filtrados (apenas abertos ou concluídos):', this.treatmentPlans);
        console.log('Total de planos filtrados:', this.treatmentPlans.length);

        // Log detalhado dos procedimentos e seus dentistas
        this.treatmentPlans.forEach(plan => {
          if (plan.procedures && Array.isArray(plan.procedures)) {
            plan.procedures.forEach(procedure => {
              console.log(`Procedimento ${procedure.id} (${procedure.name}) - Dentista:`, {
                professionalId: procedure.professionalId,
                professional: procedure.professional,
                dentistName: procedure.professional?.name || this.getDentistName(procedure.professionalId)
              });
            });
          }
        });

        // Verificar se os planos têm procedimentos
        this.treatmentPlans.forEach(plan => {
          console.log(`Plano ${plan.id} - Tem procedimentos:`, !!plan.procedures);
          if (plan.procedures) {
            console.log(`Plano ${plan.id} - Número de procedimentos:`, plan.procedures.length);

            // Garantir que o professionalId seja um número para cada procedimento
            // e restaurar os dentistas selecionados anteriormente
            plan.procedures.forEach(procedure => {
              // Verificar se temos um dentista salvo para este procedimento
              if (procedure.id && currentProcedureDentists.has(procedure.id)) {
                const savedDentistId = currentProcedureDentists.get(procedure.id);
                if (savedDentistId !== undefined) {
                  console.log(`Restaurando dentista para procedimento ${procedure.id}: ${savedDentistId} (anterior: ${procedure.professionalId})`);
                  procedure.professionalId = savedDentistId;
                }
              }

              if (procedure.professionalId) {
                // Converter para número se for string
                if (typeof procedure.professionalId === 'string') {
                  procedure.professionalId = Number(procedure.professionalId);
                }
                console.log(`Procedimento ${procedure.id} - Dentista: ${procedure.professionalId} (${typeof procedure.professionalId})`);
              } else {
                console.log(`Procedimento ${procedure.id} - Sem dentista definido`);
              }
            });

            // Recalcular a porcentagem de conclusão para garantir consistência
            this.updateCompletionPercentageLocally(plan);
          } else {
            console.log(`Plano ${plan.id} - Não tem procedimentos ou não é um array`);
            console.log(`Plano ${plan.id} - Valor da propriedade procedures:`, plan.procedures);
            console.log(`Plano ${plan.id} - Valor da propriedade __procedures__:`, (plan as any).__procedures__);
          }
        });

        // Garantir que todos os professionalIds sejam números
        this.ensureAllProfessionalIdsAreNumbers();

        // Comentado para evitar tentativas automáticas de atualização de dentistas
        // this.ensureProceduresHaveDentists();

        // Forçar detecção de mudanças para atualizar a UI
        this.cdr.detectChanges();
        this.isLoadingPlans = false;
      },
      error: (error) => {
        console.error('Erro ao carregar planos de tratamento:', error);
        this.isLoadingPlans = false;
      }
    });
  }

  loadAllProcedures(): void {
    if (!this.patient?.id) return;

    this.isLoadingProcedures = true;
    this.treatmentPlanService.getAllProceduresByPatient(this.patient.id).subscribe({
      next: (procedures) => {
        // Garantir que todos os procedimentos tenham professionalId definido
        this.allProcedures = procedures.map(procedure => {
          // Garantir que o professionalId seja definido a partir do objeto professional
          const professionalId = procedure.professional?.id || procedure.professionalId;

          console.log(`Procedimento ${procedure.id} (${procedure.name}) - Mapeando dentista:`, {
            originalProfessionalId: procedure.professionalId,
            professionalFromObject: procedure.professional?.id,
            mappedProfessionalId: professionalId
          });

          return {
            ...procedure,
            professionalId: professionalId
          };
        });

        this.applyFilters();
        this.isLoadingProcedures = false;
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoadingProcedures = false;
      }
    });
  }

  applyFilters(): void {
    let filtered = [...this.allProcedures];

    // Aplicar filtro de status
    if (this.statusFilter !== 'all') {
      filtered = filtered.filter(proc => proc.status === this.statusFilter);
    }

    // Ordenar por data de execução (mais recentes primeiro)
    filtered.sort((a, b) => {
      const dateA = a.executionDate ? new Date(a.executionDate).getTime() : 0;
      const dateB = b.executionDate ? new Date(b.executionDate).getTime() : 0;
      return dateB - dateA;
    });

    this.totalItems = filtered.length;

    // Aplicar paginação
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.filteredProcedures = filtered.slice(startIndex, startIndex + this.pageSize);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.applyFilters();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1; // Resetar para a primeira página
    this.applyFilters();
  }

  togglePlansAccordion(): void {
    this.isPlansAccordionOpen = !this.isPlansAccordionOpen;
  }

  toggleClinicalRecordAccordion(): void {
    this.isClinicalRecordAccordionOpen = !this.isClinicalRecordAccordionOpen;
  }

  togglePlanDetails(planId: number): void {
    console.log('Toggling plan details for plan ID:', planId);
    console.log('Current expandedPlanId:', this.expandedPlanId);

    // Se o accordion já estiver expandido, feche-o
    if (this.expandedPlanId === planId) {
      this.expandedPlanId = null;
    } else {
      // Caso contrário, expanda-o
      this.expandedPlanId = planId;
    }

    console.log('New expandedPlanId:', this.expandedPlanId);
  }

  isPlanExpanded(planId: number): boolean {
    return this.expandedPlanId === planId;
  }

  // Obter procedimentos em andamento
  getInProgressProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    if (!plan.procedures || !Array.isArray(plan.procedures)) {
      return [];
    }

    const inProgressProcedures = plan.procedures.filter(
      proc => proc.status === TreatmentProcedureStatus.IN_PROGRESS
    );

    return inProgressProcedures;
  }

  // Obter procedimentos pendentes
  getPendingProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    console.log('getPendingProcedures para plano:', plan.id);

    if (!plan.procedures || !Array.isArray(plan.procedures)) {
      console.log('Plano não tem procedimentos ou não é um array');
      return [];
    }

    console.log('Total de procedimentos no plano:', plan.procedures.length);

    // Agora retorna apenas os procedimentos com status PENDING
    const pendingProcedures = plan.procedures.filter(
      proc => proc.status === TreatmentProcedureStatus.PENDING
    );

    console.log('Total de procedimentos pendentes:', pendingProcedures.length);
    return pendingProcedures;
  }

  // Obter procedimentos concluídos
  getCompletedProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    console.log('getCompletedProcedures para plano:', plan.id);

    if (!plan.procedures || !Array.isArray(plan.procedures)) {
      console.log('Plano não tem procedimentos ou não é um array');
      return [];
    }

    console.log('Total de procedimentos no plano:', plan.procedures.length);

    const completedProcedures = plan.procedures.filter(
      proc => proc.status === TreatmentProcedureStatus.COMPLETED
    );

    console.log('Total de procedimentos concluídos:', completedProcedures.length);
    return completedProcedures;
  }

  // Obter procedimentos cancelados
  getCancelledProcedures(plan: TreatmentPlan): TreatmentProcedure[] {
    if (!plan.procedures || !Array.isArray(plan.procedures)) {
      return [];
    }

    return plan.procedures.filter(
      proc => proc.status === TreatmentProcedureStatus.CANCELLED
    );
  }

  getStatusClass(status: TreatmentPlanStatus | TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentPlanStatus.OPEN:
      case TreatmentProcedureStatus.PENDING:
        return 'bg-blue-100 text-blue-800';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'bg-orange-100 text-orange-800';
      case TreatmentPlanStatus.COMPLETED:
      case TreatmentProcedureStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TreatmentPlanStatus.CANCELLED:
      case TreatmentProcedureStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusLabel(status: TreatmentPlanStatus | TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentPlanStatus.OPEN:
        return 'Em aberto';
      case TreatmentPlanStatus.COMPLETED:
        return 'Concluído';
      case TreatmentPlanStatus.CANCELLED:
        return 'Cancelado';
      case TreatmentProcedureStatus.PENDING:
        return 'Pendente';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'Em andamento';
      case TreatmentProcedureStatus.COMPLETED:
        return 'Concluído';
      case TreatmentProcedureStatus.CANCELLED:
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  formatValue(value: number): string {
    return value.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  getCompletionPercentage(plan: TreatmentPlan): string {
    if (typeof plan.completionPercentage === 'number') {
      return plan.completionPercentage.toFixed(0);
    }
    return '0';
  }

  getCompletionPercentageNumber(plan: TreatmentPlan): number {
    if (typeof plan.completionPercentage === 'number') {
      return plan.completionPercentage;
    }
    return 0;
  }

  hasProcedures(plan: TreatmentPlan): boolean {
    console.log('Verificando se o plano tem procedimentos:', plan.id);
    console.log('Plano completo:', plan);
    console.log('Propriedade procedures:', plan.procedures);
    console.log('Propriedade __procedures__:', (plan as any).__procedures__);

    const hasProcedures = !!(plan.procedures && Array.isArray(plan.procedures) && plan.procedures.length > 0);
    console.log('Resultado:', hasProcedures);
    return hasProcedures;
  }

  formatDate(date: Date | string | null | undefined): string {
    if (!date) return 'Não informado';
    return new Date(date).toLocaleDateString('pt-BR');
  }

  getDentistName(dentistId: number): string {
    if (!dentistId) {
      console.log('getDentistName chamado com dentistId indefinido ou nulo');
      return 'Não informado';
    }

    const dentist = this.dentists.find(d => d.id === dentistId);
    if (!dentist) {
      console.log(`Dentista com ID ${dentistId} não encontrado na lista de dentistas`);
      return 'Não informado';
    }

    return dentist.name;
  }

  onDentistChange(procedure: TreatmentProcedure, dentistId: number): void {
    console.log('onDentistChange chamado com dentistId:', dentistId, 'tipo:', typeof dentistId);
    console.log('Procedimento antes da atualização:', JSON.stringify(procedure));

    if (!procedure.id) {
      console.log('Procedimento sem ID, retornando');
      return;
    }

    // Verificar se o dentistId é válido (não é NaN, 0, etc.)
    if (!dentistId) {
      console.error('ID de dentista inválido:', dentistId);
      this.notificationService.error('Por favor, selecione um dentista válido.');
      // Forçar detecção de mudanças para atualizar a UI
      this.cdr.detectChanges();
      return;
    }

    // Garantir que o dentistId seja um número
    if (typeof dentistId === 'string') {
      dentistId = Number(dentistId);
    }

    // Atualizar o procedimento localmente
    procedure.professionalId = dentistId;

    // Garantir que o professionalId seja um número
    this.ensureProfessionalIdIsNumber(procedure);

    console.log('Procedimento após atualização local:', JSON.stringify(procedure));
    console.log('professionalId atual:', procedure.professionalId, 'tipo:', typeof procedure.professionalId);

    // Enviar para o backend
    const updateDto = {
      professionalId: dentistId
    };

    // Forçar detecção de mudanças para atualizar a UI imediatamente
    this.cdr.detectChanges();

    this.treatmentPlanService.updateProcedure(procedure.id, updateDto).subscribe({
      next: (updatedProcedure) => {
        console.log('Dentista atualizado com sucesso:', updatedProcedure);

        // Atualizar o procedimento com os dados retornados do servidor
        if (updatedProcedure.professional && updatedProcedure.professional.id) {
          procedure.professionalId = updatedProcedure.professional.id;

          // Atualizar também na lista de planos
          const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
          if (plan && plan.procedures) {
            const index = plan.procedures.findIndex(p => p.id === procedure.id);
            if (index !== -1) {
              plan.procedures[index].professionalId = updatedProcedure.professional.id;
            }
          }
        }

        console.log('Procedimento após atualização do backend:', updatedProcedure);
        console.log('professionalId atual:', procedure.professionalId, 'tipo:', typeof procedure.professionalId);

        // Garantir que o professionalId seja um número
        this.ensureProfessionalIdIsNumber(procedure);

        // Forçar detecção de mudanças para atualizar a UI
        this.cdr.detectChanges();

        // Verificar se o botão está habilitado
        setTimeout(() => {
          console.log('Botão habilitado?', !document.querySelector('button[disabled]'));
        }, 100);

        this.notificationService.success('Dentista atualizado com sucesso!');
      },
      error: (error) => {
        console.error('Erro ao atualizar dentista do procedimento:', error);

        // Verificar se é o erro específico de não poder alterar o dentista
        const errorMessage = error.error?.message || '';
        const isDentistChangeRestrictionError = errorMessage.includes('Não é possível alterar o dentista de um procedimento');

        // Restaurar o dentista original
        if (procedure.treatmentPlanId) {
          const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
          if (plan && plan.procedures) {
            const originalProcedure = plan.procedures.find(p => p.id === procedure.id);
            if (originalProcedure && originalProcedure.professional) {
              procedure.professionalId = originalProcedure.professional.id;
            }
          }
        }

        // Forçar detecção de mudanças para atualizar a UI
        this.cdr.detectChanges();

        // Exibir notificação apenas se não for o erro específico de restrição de alteração de dentista
        if (!isDentistChangeRestrictionError) {
          this.notificationService.error('Erro ao atualizar dentista do procedimento');
        }
      }
    });
  }

  executeProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    console.log('Executando procedimento:', JSON.stringify(procedure));

    // Garantir que o professionalId seja um número
    this.ensureProfessionalIdIsNumber(procedure);

    // Verificar se o procedimento tem um dentista associado
    if (!procedure.professionalId) {
      this.notificationService.error('Por favor, selecione um dentista antes de executar o procedimento.');
      return;
    }

    console.log('Dentista do procedimento:', procedure.professionalId, 'tipo:', typeof procedure.professionalId);

    // Incluir o professionalId apenas quando o procedimento está com status "pending"
    const updateDto: any = {
      status: TreatmentProcedureStatus.IN_PROGRESS,
      executionDate: new Date()
    };

    // Apenas procedimentos com status "pending" podem ter o dentista alterado
    if (procedure.status === TreatmentProcedureStatus.PENDING) {
      updateDto.professionalId = procedure.professionalId;
      console.log('Incluindo professionalId no DTO pois o status é pending');
    } else {
      console.log('Não incluindo professionalId no DTO pois o status não é pending');
    }

    console.log('Enviando DTO para executar procedimento:', updateDto);

    // Atualizar o procedimento localmente antes da chamada à API para feedback imediato
    procedure.status = TreatmentProcedureStatus.IN_PROGRESS;
    procedure.executionDate = updateDto.executionDate;

    // Forçar detecção de mudanças para atualizar a UI imediatamente
    this.cdr.detectChanges();

    this.treatmentPlanService.updateProcedure(procedure.id, updateDto).subscribe({
      next: (updatedProcedure) => {
        console.log('Procedimento atualizado com sucesso:', updatedProcedure);

        // Atualizar o procedimento na lista local com os dados retornados do servidor
        const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
        if (plan && plan.procedures) {
          const index = plan.procedures.findIndex(p => p.id === procedure.id);
          if (index !== -1) {
            // Manter as propriedades originais e atualizar com os dados do servidor
            plan.procedures[index] = {
              ...plan.procedures[index],
              status: updatedProcedure.status,
              executionDate: updatedProcedure.executionDate,
              professionalId: updatedProcedure.professional?.id || procedure.professionalId // Usar o ID do dentista retornado pelo servidor
            };

            console.log('Procedimento atualizado localmente após resposta do servidor:', plan.procedures[index]);

            // Calcular e atualizar a porcentagem de conclusão localmente
            this.updateCompletionPercentageLocally(plan);

            // Forçar detecção de mudanças para atualizar a UI
            this.cdr.detectChanges();
          }
        }

        // Atualizar a lista de procedimentos na Ficha Clínica
        this.loadAllProcedures();

        this.notificationService.success('Procedimento iniciado com sucesso!');
      },
      error: (error) => {
        console.error('Erro ao executar procedimento:', error);

        // Verificar se é o erro específico de não poder alterar o dentista
        const errorMessage = error.error?.message || '';
        const isDentistChangeRestrictionError = errorMessage.includes('Não é possível alterar o dentista de um procedimento');

        // Reverter as alterações locais em caso de erro
        procedure.status = TreatmentProcedureStatus.PENDING;
        procedure.executionDate = undefined;
        this.cdr.detectChanges();

        // Exibir notificação apenas se não for o erro específico de restrição de alteração de dentista
        if (!isDentistChangeRestrictionError) {
          this.notificationService.error('Erro ao executar procedimento');
        }
      }
    });
  }

  finishProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    console.log('Finalizando procedimento:', JSON.stringify(procedure));

    // Garantir que o professionalId seja um número
    this.ensureProfessionalIdIsNumber(procedure);

    // Verificar se o procedimento tem um dentista associado
    if (!procedure.professionalId) {
      this.notificationService.error('Este procedimento não tem um dentista associado. Por favor, selecione um dentista antes de finalizar.');
      return;
    }

    console.log('Dentista do procedimento:', procedure.professionalId, 'tipo:', typeof procedure.professionalId);

    // Não enviar o professionalId quando o procedimento já está em andamento
    // para evitar o erro de tentativa de alteração de dentista
    const updateDto = {
      status: TreatmentProcedureStatus.COMPLETED
    };

    console.log('Enviando DTO para finalizar procedimento:', updateDto);

    // Atualizar o procedimento localmente antes da chamada à API para feedback imediato
    procedure.status = TreatmentProcedureStatus.COMPLETED;

    // Forçar detecção de mudanças para atualizar a UI imediatamente
    this.cdr.detectChanges();

    this.treatmentPlanService.updateProcedure(procedure.id, updateDto).subscribe({
      next: (updatedProcedure) => {
        console.log('Procedimento finalizado com sucesso:', updatedProcedure);

        // Atualizar o procedimento na lista local com os dados retornados do servidor
        const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
        if (plan && plan.procedures) {
          const index = plan.procedures.findIndex(p => p.id === procedure.id);
          if (index !== -1) {
            // Manter as propriedades originais e atualizar com os dados do servidor
            plan.procedures[index] = {
              ...plan.procedures[index],
              status: updatedProcedure.status,
              professionalId: updatedProcedure.professional?.id || procedure.professionalId // Usar o ID do dentista retornado pelo servidor
            };

            console.log('Procedimento atualizado localmente após resposta do servidor:', plan.procedures[index]);

            // Calcular e atualizar a porcentagem de conclusão localmente
            this.updateCompletionPercentageLocally(plan);

            // Forçar detecção de mudanças para atualizar a UI
            this.cdr.detectChanges();
          }
        }

        // Atualizar a lista de procedimentos na Ficha Clínica
        this.loadAllProcedures();

        this.notificationService.success('Procedimento finalizado com sucesso!');
      },
      error: (error) => {
        console.error('Erro ao finalizar procedimento:', error);

        // Verificar se é o erro específico de não poder alterar o dentista
        const errorMessage = error.error?.message || '';
        const isDentistChangeRestrictionError = errorMessage.includes('Não é possível alterar o dentista de um procedimento');

        // Reverter as alterações locais em caso de erro
        procedure.status = TreatmentProcedureStatus.IN_PROGRESS;
        this.cdr.detectChanges();

        // Exibir notificação apenas se não for o erro específico de restrição de alteração de dentista
        if (!isDentistChangeRestrictionError) {
          this.notificationService.error('Erro ao finalizar procedimento');
        }
      }
    });
  }

  // Método para calcular e atualizar a porcentagem de conclusão localmente
  private updateCompletionPercentageLocally(plan: TreatmentPlan): void {
    if (!plan.procedures || !Array.isArray(plan.procedures) || plan.procedures.length === 0) {
      return;
    }

    // Contar procedimentos concluídos
    const completedProcedures = plan.procedures.filter(
      proc => proc.status === TreatmentProcedureStatus.COMPLETED
    ).length;

    // Calcular a porcentagem de conclusão
    const completionPercentage = (completedProcedures / plan.procedures.length) * 100;

    console.log(`Porcentagem de conclusão calculada localmente: ${completionPercentage}%`);

    // Atualizar a porcentagem de conclusão no plano
    plan.completionPercentage = completionPercentage;

    // Verificar se todos os procedimentos estão concluídos
    const allCompleted = completedProcedures === plan.procedures.length;

    // Atualizar o status do plano com base na conclusão dos procedimentos
    if (allCompleted && plan.status !== TreatmentPlanStatus.COMPLETED) {
      console.log(`Todos os procedimentos do plano ${plan.id} estão concluídos. Atualizando status para COMPLETED.`);
      plan.status = TreatmentPlanStatus.COMPLETED;
    } else if (!allCompleted && plan.status === TreatmentPlanStatus.COMPLETED) {
      console.log(`Nem todos os procedimentos do plano ${plan.id} estão concluídos. Atualizando status para OPEN.`);
      plan.status = TreatmentPlanStatus.OPEN;
    }
  }

  cancelExecution(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    this.confirmDialogTitle = 'Cancelar execução';
    this.confirmDialogMessage = `Tem certeza que deseja cancelar a execução do procedimento "${procedure.name}"?`;
    this.confirmDialogAction = () => {
      // Não enviar o professionalId para evitar o erro de tentativa de alteração de dentista
      const updateDto = {
        status: TreatmentProcedureStatus.PENDING,
        executionDate: undefined
      };

      console.log('Enviando DTO para cancelar execução do procedimento:', updateDto);

      this.treatmentPlanService.updateProcedure(procedure.id!, updateDto).subscribe({
        next: (updatedProcedure) => {
          console.log('Cancelamento de execução bem-sucedido:', updatedProcedure);

          // Atualizar o procedimento na lista local imediatamente
          const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
          if (plan && plan.procedures) {
            const index = plan.procedures.findIndex(p => p.id === procedure.id);
            if (index !== -1) {
              // Manter as propriedades originais e atualizar apenas o status e a data de execução
              plan.procedures[index] = {
                ...plan.procedures[index],
                status: TreatmentProcedureStatus.PENDING,
                executionDate: undefined
              };

              console.log('Procedimento atualizado localmente após cancelamento:', plan.procedures[index]);

              // Calcular e atualizar a porcentagem de conclusão localmente
              this.updateCompletionPercentageLocally(plan);

              // Forçar detecção de mudanças para atualizar a UI
              this.cdr.detectChanges();
            }
          }

          // Não recarregar os planos do servidor para evitar perder o dentista selecionado
          // Apenas atualizar a porcentagem de conclusão localmente

          // Atualizar a lista de procedimentos na Ficha Clínica
          this.loadAllProcedures();

          this.notificationService.success('Execução do procedimento cancelada com sucesso!');
        },
        error: (error) => {
          console.error('Erro ao cancelar execução do procedimento:', error);
          this.notificationService.error('Erro ao cancelar execução do procedimento');
        }
      });
    };
    this.isConfirmDialogOpen = true;
  }

  deleteProcedure(procedure: TreatmentProcedure): void {
    if (!procedure.id) return;

    this.confirmDialogTitle = 'Remover procedimento';
    this.confirmDialogMessage = `Tem certeza que deseja remover o procedimento "${procedure.name}" do plano de tratamento?`;
    this.confirmDialogAction = () => {
      this.treatmentPlanService.deleteProcedure(procedure.id!).subscribe({
        next: () => {
          // Remover o procedimento da lista
          const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
          if (plan) {
            plan.procedures = plan.procedures.filter(p => p.id !== procedure.id);

            // Calcular e atualizar a porcentagem de conclusão localmente
            this.updateCompletionPercentageLocally(plan);

            // Forçar detecção de mudanças para atualizar a UI
            this.cdr.detectChanges();

            // Não recarregar os planos do servidor para evitar perder o dentista selecionado
            // Apenas atualizar a porcentagem de conclusão localmente
          }

          // Atualizar a lista de procedimentos na Ficha Clínica
          this.loadAllProcedures();

          this.notificationService.success('Procedimento removido com sucesso!');
        },
        error: (error) => {
          console.error('Erro ao remover procedimento:', error);
          this.notificationService.error('Erro ao remover procedimento');
        }
      });
    };
    this.isConfirmDialogOpen = true;
  }

  openProcedureModal(procedure: TreatmentProcedure): void {
    // Garantir que o professionalId esteja definido
    const professionalId = procedure.professional?.id || procedure.professionalId;

    console.log('Abrindo modal de procedimento:', {
      procedureId: procedure.id,
      procedureName: procedure.name,
      originalProfessionalId: procedure.professionalId,
      professionalFromObject: procedure.professional?.id,
      mappedProfessionalId: professionalId
    });

    // Criar uma cópia do procedimento com o professionalId garantido
    this.selectedProcedure = {
      ...procedure,
      professionalId: professionalId
    };

    this.isModalOpen = true;
  }

  onModalClose(): void {
    this.isModalOpen = false;
    this.selectedProcedure = null;
    // Recarregar os dados
    this.loadAllProcedures();
  }

  // Método para abrir o modal de criação de procedimento avulso
  openProcedureFormModal(): void {
    this.isProcedureFormModalOpen = true;
  }

  // Método para fechar o modal de criação de procedimento avulso
  closeProcedureFormModal(): void {
    this.isProcedureFormModalOpen = false;
  }

  // Método chamado quando um procedimento é salvo no modal
  onProcedureSaved(procedure: TreatmentProcedure): void {
    this.closeProcedureFormModal();
    this.notificationService.success('Procedimento criado com sucesso!');
    // Recarregar os dados
    this.loadTreatmentPlans();
    this.loadAllProcedures();
  }

  onProcedureUpdated(procedure: TreatmentProcedure): void {
    // Atualizar o procedimento na lista
    const index = this.allProcedures.findIndex(p => p.id === procedure.id);
    if (index !== -1) {
      this.allProcedures[index] = procedure;
      this.applyFilters();
    }

    // Se o procedimento pertence a um plano, atualizar também na lista de planos
    const plan = this.treatmentPlans.find(p => p.id === procedure.treatmentPlanId);
    if (plan) {
      const planProcIndex = plan.procedures.findIndex(p => p.id === procedure.id);
      if (planProcIndex !== -1) {
        plan.procedures[planProcIndex] = procedure;
      }
    }
  }

  onConfirmDialogClose(confirmed: boolean): void {
    this.isConfirmDialogOpen = false;
    if (confirmed) {
      this.confirmDialogAction();
    }
  }
}
