import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { AnamnesisService } from '../../../../../core/services/anamnesis.service';
import { NotificationService } from '../../../../../core/services/notification.service';
import {
  Anamnesis,
  AnamnesisQuestion,
  AnamnesisQuestionType,
} from '../../../../../core/models/anamnesis.model';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { Employee } from '../../../../../core/models/employee.model';

@Component({
  selector: 'app-anamnesis-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent, DatePipe],
  templateUrl: './anamnesis-modal.component.html',
  styleUrl: './anamnesis-modal.component.scss',
})
export class AnamnesisModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  @Input() anamnesisId: string | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<void>();

  anamnesisForm: FormGroup;
  questions: AnamnesisQuestion[] = [];
  employees: Employee[] = [];
  anamnesis: Anamnesis | null = null;
  isLoading = false;
  isSubmitting = false;
  isViewMode = false;
  error: string | null = null;

  // Enum para usar no template
  questionTypes = AnamnesisQuestionType;

  constructor(
    private fb: FormBuilder,
    private anamnesisService: AnamnesisService,
    private employeeService: EmployeeService,
    private notificationService: NotificationService
  ) {
    this.anamnesisForm = this.fb.group({
      employeeId: [null],
      answers: this.fb.array([]),
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes['isOpen'] && changes['isOpen'].currentValue) ||
      (changes['anamnesisId'] && this.isOpen)
    ) {
      this.resetForm();

      if (this.anamnesisId) {
        this.isViewMode = true;
        this.loadAnamnesis();
      } else {
        this.isViewMode = false;
        this.loadQuestions();
      }
    }
  }

  get answersArray(): FormArray {
    return this.anamnesisForm.get('answers') as FormArray;
  }

  loadEmployees(): void {
    this.employeeService.getAllEmployees().subscribe({
      next: (employees) => {
        this.employees = employees;
      },
      error: (err) => {
        console.error('Erro ao carregar funcionários:', err);
        this.notificationService.error('Erro ao carregar funcionários');
      },
    });
  }

  loadQuestions(): void {
    this.isLoading = true;
    this.error = null;

    this.anamnesisService.getQuestions().subscribe({
      next: (questions) => {
        this.questions = questions;
        this.buildAnswersForm();
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar perguntas:', err);
        this.error =
          'Não foi possível carregar as perguntas. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar perguntas');
      },
    });
  }

  loadAnamnesis(): void {
    if (!this.anamnesisId) return;

    this.isLoading = true;
    this.error = null;

    this.anamnesisService.getAnamnesis(this.anamnesisId).subscribe({
      next: (anamnesis) => {
        this.anamnesis = anamnesis;
        this.questions = anamnesis.answers.map((answer) => answer.question!);
        this.populateForm(anamnesis);
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar anamnese:', err);
        this.error =
          'Não foi possível carregar a anamnese. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar anamnese');
      },
    });
  }

  buildAnswersForm(): void {
    // Limpar o FormArray
    while (this.answersArray.length !== 0) {
      this.answersArray.removeAt(0);
    }

    // Adicionar um FormGroup para cada pergunta
    this.questions.forEach((question) => {
      this.answersArray.push(
        this.fb.group({
          questionId: [question.id, Validators.required],
          answer: [''], // Removido Validators.required para tornar o campo opcional
        })
      );
    });
  }

  populateForm(anamnesis: Anamnesis): void {
    // Preencher o campo employeeId
    this.anamnesisForm.patchValue({
      employeeId: anamnesis.employeeId,
    });

    // Limpar o FormArray
    while (this.answersArray.length !== 0) {
      this.answersArray.removeAt(0);
    }

    // Adicionar um FormGroup para cada resposta
    anamnesis.answers.forEach((answer) => {
      this.answersArray.push(
        this.fb.group({
          questionId: [answer.questionId, Validators.required],
          answer: [
            { value: answer.answer, disabled: this.isViewMode },
            // Removido Validators.required para tornar o campo opcional
          ],
        })
      );
    });

    // Desabilitar o formulário se estiver em modo de visualização
    if (this.isViewMode) {
      this.anamnesisForm.disable();
    }
  }

  resetForm(): void {
    this.anamnesisForm.reset({
      employeeId: null,
      answers: [],
    });
    this.anamnesis = null;
    this.error = null;
  }

  onSubmit(): void {
    if (this.anamnesisForm.invalid) {
      this.anamnesisForm.markAllAsTouched();
      return;
    }

    if (!this.patientId) {
      this.notificationService.error('ID do paciente não informado');
      return;
    }

    this.isSubmitting = true;

    const formData = {
      patientId: this.patientId,
      employeeId: this.anamnesisForm.value.employeeId,
      answers: this.anamnesisForm.value.answers,
    };

    this.anamnesisService.createAnamnesis(formData).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.saved.emit();
      },
      error: (err) => {
        console.error('Erro ao salvar anamnese:', err);
        this.isSubmitting = false;
        this.notificationService.error('Erro ao salvar anamnese');
      },
    });
  }

  closeModal(): void {
    this.close.emit();
  }

  // Método auxiliar para obter a pergunta pelo ID
  getQuestionById(questionId: number): AnamnesisQuestion | undefined {
    return this.questions.find((q) => q.id === questionId);
  }

  // Método auxiliar para verificar se uma pergunta é do tipo booleano
  isBooleanQuestion(questionId: number): boolean {
    const question = this.getQuestionById(questionId);
    return question?.type === AnamnesisQuestionType.BOOLEAN;
  }
}
