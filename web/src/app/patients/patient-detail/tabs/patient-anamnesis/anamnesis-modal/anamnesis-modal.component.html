<app-modal
  [title]="anamnesisId ? 'Visualizar Anamnese' : 'Nova Anamnese'"
  [isOpen]="isOpen"
  [fullscreen]="true"
  (close)="closeModal()"
  [showDefaultFooter]="true"
>
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="p-4 bg-red-50 text-red-700 rounded-md">
    <p class="font-medium">{{ error }}</p>
  </div>

  <!-- Modal content -->
  <div
    *ngIf="!isLoading && !error"
    class="p-6 max-h-[calc(90vh-120px)] overflow-y-auto"
  >
    <form [formGroup]="anamnesisForm" (ngSubmit)="onSubmit()">
      <!-- Anamnesis info -->
      <div *ngIf="anamnesis" class="mb-6 p-4 bg-gray-50 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-500">Data</p>
            <p class="text-base font-medium">
              {{ anamnesis.createdAt | date : "dd/MM/yyyy HH:mm" }}
            </p>
          </div>
          <div>
            <p class="text-sm text-gray-500">Paciente</p>
            <p class="text-base font-medium">
              {{ anamnesis.patient?.name || "Não informado" }}
            </p>
          </div>
        </div>
      </div>

      <!-- Employee selection -->
      <div *ngIf="!isViewMode" class="mb-6">
        <label
          for="employeeId"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Funcionário Responsável
        </label>
        <select
          id="employeeId"
          formControlName="employeeId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option [ngValue]="null">Selecione um funcionário (opcional)</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ employee.name }}
          </option>
        </select>
      </div>

      <!-- Questions and answers -->
      <div class="space-y-6" formArrayName="answers">
        <div
          *ngFor="let answerControl of answersArray.controls; let i = index"
          [formGroupName]="i"
          class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm"
        >
          <div class="mb-2">
            <h3 class="text-base font-medium text-gray-900">
              {{
                getQuestionById(answerControl.get("questionId")?.value)?.title
              }}
            </h3>
          </div>

          <!-- Boolean question (radio buttons) -->
          <div
            *ngIf="isBooleanQuestion(answerControl.get('questionId')?.value)"
            class="mt-2"
          >
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <input
                  type="radio"
                  [id]="'answer-yes-' + i"
                  [value]="'Sim'"
                  formControlName="answer"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  [disabled]="isViewMode"
                />
                <label
                  [for]="'answer-yes-' + i"
                  class="ml-2 block text-sm text-gray-700"
                >
                  Sim
                </label>
              </div>
              <div class="flex items-center">
                <input
                  type="radio"
                  [id]="'answer-no-' + i"
                  [value]="'Não'"
                  formControlName="answer"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  [disabled]="isViewMode"
                />
                <label
                  [for]="'answer-no-' + i"
                  class="ml-2 block text-sm text-gray-700"
                >
                  Não
                </label>
              </div>
            </div>
            <div
              *ngIf="
                answerControl.get('answer')?.invalid &&
                answerControl.get('answer')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              Formato de resposta inválido
            </div>
          </div>

          <!-- Text question (textarea) -->
          <div
            *ngIf="!isBooleanQuestion(answerControl.get('questionId')?.value)"
            class="mt-2"
          >
            <textarea
              [id]="'answer-' + i"
              formControlName="answer"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [placeholder]="isViewMode ? '' : 'Digite sua resposta aqui...'"
              [disabled]="isViewMode"
            ></textarea>
            <div
              *ngIf="
                answerControl.get('answer')?.invalid &&
                answerControl.get('answer')?.touched
              "
              class="text-red-500 text-sm mt-1"
            >
              Formato de resposta inválido
            </div>
          </div>
        </div>
      </div>

      <!-- Fim do formulário -->
    </form>
  </div>

  <!-- Submit button (only shown in create mode) -->
  <div *ngIf="!isViewMode" footer class="px-3 flex gap-2 items-center">
    <button
      type="button"
      (click)="closeModal()"
      class="px-10 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="onSubmit()"
      [disabled]="anamnesisForm.invalid || isSubmitting"
      class="px-10 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
    >
      <div
        *ngIf="isSubmitting"
        class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"
      ></div>
      Salvar
    </button>
  </div>

  <!-- Close button (only shown in view mode) -->
  <div *ngIf="isViewMode" footer>
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Fechar
    </button>
  </div>
</app-modal>
