<div class="bg-gray-50 p-4 rounded-lg mb-6">
  <div class="flex justify-between items-center mb-4">
    <div>
      <h2 class="text-lg font-medium text-gray-900">Anamnese</h2>
      <p class="text-sm text-gray-500 mt-1">
        Histórico de anamneses do paciente
      </p>
    </div>
    <button
      (click)="openNewAnamnesisModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-1"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Nova Anamnese
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando anamneses...</span>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="p-4 bg-red-50 text-red-700 rounded-md">
    <p class="font-medium">{{ error }}</p>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!isLoading && !error && anamnesisList.length === 0"
    class="p-8 text-center text-gray-500 bg-white rounded-md border border-gray-200"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12 mx-auto text-gray-400 mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
    <p class="text-lg font-medium">Nenhuma anamnese encontrada</p>
    <p class="mt-1">
      Clique no botão "Nova Anamnese" para criar a primeira anamnese para este
      paciente.
    </p>
  </div>

  <!-- Anamnesis list -->
  <div
    *ngIf="!isLoading && !error && anamnesisList.length > 0"
    class="overflow-x-auto"
  >
    <table class="min-w-full divide-y divide-gray-200 bg-white rounded-md">
      <thead class="bg-gray-50">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Funcionário
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Ações
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr
          *ngFor="let anamnesis of anamnesisList"
          class="hover:bg-gray-50 transition-colors"
        >
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ anamnesis.createdAt | date : "dd/MM/yyyy" }}
            </div>
            <div class="text-sm text-gray-500">
              {{ anamnesis.createdAt | date : "HH:mm" }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ anamnesis.employee?.name || "Não informado" }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <button
              (click)="openViewAnamnesisModal(anamnesis.id)"
              class="text-blue-600 hover:text-blue-900 mr-3"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fill-rule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Anamnesis Modal -->
<app-anamnesis-modal
  [isOpen]="isModalOpen"
  [patientId]="patient?.id || null"
  [anamnesisId]="selectedAnamnesisId"
  (close)="closeModal()"
  (saved)="onAnamnesisSaved()"
></app-anamnesis-modal>
