import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Patient } from '../../../../core/models/patient.model';
import { Anamnesis } from '../../../../core/models/anamnesis.model';
import { AnamnesisService } from '../../../../core/services/anamnesis.service';
import { AnamnesisModalComponent } from './anamnesis-modal/anamnesis-modal.component';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-patient-anamnesis',
  standalone: true,
  imports: [CommonModule, DatePipe, AnamnesisModalComponent],
  templateUrl: './patient-anamnesis.component.html',
  styleUrl: './patient-anamnesis.component.scss',
})
export class PatientAnamnesisComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  anamnesisList: Anamnesis[] = [];
  isLoading = false;
  error: string | null = null;
  isModalOpen = false;
  selectedAnamnesisId: string | null = null;

  constructor(
    private anamnesisService: AnamnesisService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadAnamnesisList();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadAnamnesisList();
    }
  }

  loadAnamnesisList(): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.error = null;

    this.anamnesisService.getPatientAnamnesis(this.patient.id).subscribe({
      next: (anamnesisList) => {
        this.anamnesisList = anamnesisList;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar anamneses:', err);
        this.error = 'Não foi possível carregar as anamneses. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar anamneses');
      },
    });
  }

  openNewAnamnesisModal(): void {
    this.selectedAnamnesisId = null;
    this.isModalOpen = true;
  }

  openViewAnamnesisModal(anamnesisId: string): void {
    this.selectedAnamnesisId = anamnesisId;
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedAnamnesisId = null;
  }

  onAnamnesisSaved(): void {
    this.closeModal();
    this.loadAnamnesisList();
    this.notificationService.success('Anamnese salva com sucesso');
  }
}
