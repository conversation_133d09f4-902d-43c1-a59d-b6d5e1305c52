import {
  Component,
  OnInit,
  OnChanges,
  Input,
  Output,
  EventEmitter,
  SimpleChanges,
  HostListener,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import {
  Budget,
  BudgetStatus,
  DiscountType,
  PaymentMethod,
  PaymentMethodLabels,
} from '../../../../../core/models/budget.model';
import { BudgetService } from '../../../../../core/services/budget.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { ProcedureService } from '../../../../../core/services/procedure.service';
import { Dentist } from '../../../../../core/models/dentist.model';
import { Procedure } from '../../../../../core/models/procedure.model';
import {
  SearchableSelectComponent,
  SelectOption,
} from '../../../../../shared/components/searchable-select/searchable-select.component';
import { ProcedureSearchComponent } from '../procedure-search/procedure-search.component';
import { TeethSelectorComponent } from '../../../../../shared/components/teeth-selector/teeth-selector.component';
import { ConfirmationDialogComponent } from '../../../../../shared/components/confirmation-dialog/confirmation-dialog.component';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { BudgetPrintModalComponent } from '../budget-print-modal/budget-print-modal.component';

@Component({
  selector: 'app-budget-fullscreen-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SearchableSelectComponent,
    ProcedureSearchComponent,
    TeethSelectorComponent,
    ConfirmationDialogComponent,
    ModalComponent,
    BudgetPrintModalComponent,
  ],
  templateUrl: './budget-fullscreen-modal.component.html',
  styleUrl: './budget-fullscreen-modal.component.scss',
})
export class BudgetFullscreenModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() budget: Budget | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<Budget>();

  budgetForm: FormGroup;
  isSubmitting = false;
  isLoading = true;
  dentists: Dentist[] = [];
  procedures: Procedure[] = [];
  procedureOptions: SelectOption[] = [];
  dentistOptions: SelectOption[] = [];
  paymentMethods = Object.entries(PaymentMethodLabels).map(
    ([value, label]) => ({
      value,
      label,
    })
  );

  // Controle de UI
  selectedProcedureId: number | null = null;
  selectedProcedure: Procedure | null = null;
  selectedDentistId: number | null = null;
  selectedTeeth: string[] = [];
  procedureValue: number = 0;
  procedureUnitValue: number = 0; // Valor unitário do procedimento
  teethSelectionError: boolean = false;

  // Controle dos diálogos de confirmação
  showApproveConfirmation = false;
  showCancelConfirmation = false;

  // Adicione esta propriedade à classe
  hideValues = false;

  // Adicione estas propriedades à classe
  showPrintConfigModal = false;
  printConfig = {
    clinicInfo: true,
    dentistData: true,
    signature: true,
    permanentOdontogram: true,
    deciduousOdontogram: false,
    groupProcedures: false,
    procedureValues: true,
    procedureDescriptions: true,
    totalValue: true,
    amountPaid: true,
    installmentValue: false,
    paymentMethod: true,
    clinicLogo: true,
  };

  // Visualização modais
  showOptionsMenu = false;

  // Adicione estas propriedades à classe
  showInstallments = false;
  installmentsCount = 1;
  installmentOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  installmentValue = 0;

  // Adicione esta propriedade à classe
  showDiscountPercentageModal = false;
  showDiscountModal = false;
  discountValue = 0;
  proceduresValue = 0;
  finalValueAfterDiscount = 0;

  // Adicionar referência ao componente de seleção de dentes
  @ViewChild(TeethSelectorComponent) teethSelector!: TeethSelectorComponent;
  @ViewChild(BudgetPrintModalComponent) budgetPrint!: BudgetPrintModalComponent;

  constructor(
    private fb: FormBuilder,
    private budgetService: BudgetService,
    private dentistService: DentistService,
    private procedureService: ProcedureService
  ) {
    this.budgetForm = this.createBudgetForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnChanges(_changes: SimpleChanges): void {
    if (this.budget && this.isOpen) {
      this.populateForm();
    } else if (this.isOpen) {
      this.resetForm();
    }
  }

  createBudgetForm(): FormGroup {
    return this.fb.group({
      id: [null],
      patientId: [null, Validators.required],
      dentistId: [null, Validators.required],
      notes: [''],
      status: [BudgetStatus.OPEN],
      totalValue: [0, [Validators.required, Validators.min(0)]],
      amountPaid: [0, [Validators.required, Validators.min(0)]],
      discount: [0, [Validators.min(0)]],
      discountType: [DiscountType.NONE],
      paymentMethod: [PaymentMethod.TO_DEFINE],
      installments: [1, [Validators.min(1), Validators.max(12)]],
      items: this.fb.array([]),
    });
  }

  get itemsFormArray(): FormArray {
    return this.budgetForm.get('items') as FormArray;
  }

  createItemForm(): FormGroup {
    return this.fb.group({
      id: [null],
      procedureId: [null, Validators.required],
      tooth: [''],
      executingDentistId: [null, Validators.required],
      value: [0, [Validators.required, Validators.min(0)]],
    });
  }

  addItem(
    procedureId?: string,
    tooth?: string,
    executingDentistId?: string,
    value?: string
  ): void {
    const itemForm = this.createItemForm();

    if (procedureId && executingDentistId) {
      const procedure = this.procedures.find(
        (p) => p.id === parseInt(procedureId)
      );
      let itemValue = 0;

      // Determinar o valor do item
      if (value && !isNaN(parseFloat(value))) {
        itemValue = parseFloat(value);
      } else if (
        procedure &&
        procedure.defaultPrice !== undefined &&
        procedure.defaultPrice !== null
      ) {
        const price = Number(procedure.defaultPrice);
        itemValue = isNaN(price) ? 0 : price;
      }

      itemForm.patchValue({
        procedureId: Number(procedureId),
        tooth: tooth || '',
        executingDentistId: Number(executingDentistId),
        value: itemValue,
      });
    }

    this.itemsFormArray.push(itemForm);
    this.calculateTotal();
  }

  removeItem(index: number): void {
    this.itemsFormArray.removeAt(index);
    this.calculateTotal();
  }

  loadInitialData(): void {
    this.isLoading = true;

    // Carregar dentistas
    this.dentistService.getActiveDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;

        // Criar opções para o combobox
        this.dentistOptions = dentists.map((dentist) => ({
          value: dentist.id,
          label: dentist.name,
        }));

        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.loadProcedures();
      },
    });
  }

  loadProcedures(): void {
    // Carregar procedimentos
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.procedures = procedures;

        // Criar opções para o combobox com informações de tipo
        this.procedureOptions = procedures.map((procedure) => ({
          value: procedure.id,
          label: procedure.name,
          description: `Tipo: ${
            procedure.type || 'Não especificado'
          } - Valor: R$ ${this.formatPrice(procedure.defaultPrice)}`,
        }));

        this.isLoading = false;

        if (this.budget) {
          this.populateForm();
        }
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoading = false;

        if (this.budget) {
          this.populateForm();
        }
      },
    });
  }

  populateForm(): void {
    if (!this.budget) return;

    // Limpar os itens existentes
    while (this.itemsFormArray.length) {
      this.itemsFormArray.removeAt(0);
    }

    // Preencher o formulário com os dados do orçamento
    this.budgetForm.patchValue({
      id: this.budget.id,
      patientId: this.budget.patientId,
      dentistId: this.budget.dentistId,
      notes: this.budget.notes,
      status: this.budget.status,
      totalValue: this.budget.totalValue,
      amountPaid: this.budget.amountPaid,
      discount: this.budget.discount,
      discountType: this.budget.discountType,
      paymentMethod: this.budget.paymentMethod,
      installments: this.budget.installments || 1,
    });

    this.installmentValue = this.budget.amountPaid / this.budget.installments;
    this.discountValue = this.budget.discount;
    this.installmentsCount = this.budget.installments;
    // Adicionar os itens
    if (this.budget.items && this.budget.items.length > 0) {
      this.budget.items.forEach((item) => {
        const itemForm = this.createItemForm();
        itemForm.patchValue({
          id: item.id,
          procedureId: Number(item.procedureId),
          tooth: item.tooth,
          executingDentistId: Number(item.executingDentistId),
          value: Number(item.value),
        });
        this.itemsFormArray.push(itemForm);
      });
    }

    // Desabilitar o formulário se o orçamento estiver aprovado ou cancelado
    if (
      this.budget.status === BudgetStatus.APPROVED ||
      this.budget.status === BudgetStatus.CANCELLED
    ) {
      this.budgetForm.disable();
    } else {
      this.budgetForm.enable();
    }
  }

  resetForm(): void {
    this.budgetForm.reset({
      id: null,
      patientId: this.budget?.patientId,
      dentistId: null,
      notes: '',
      status: BudgetStatus.OPEN,
      totalValue: 0,
      amountPaid: 0,
      discount: 0,
      discountType: DiscountType.NONE,
      paymentMethod: PaymentMethod.TO_DEFINE,
      installments: 1,
    });

    // Limpar os itens existentes
    while (this.itemsFormArray.length) {
      this.itemsFormArray.removeAt(0);
    }
  }

  onProcedureSelect(index: number): void {
    const itemForm = this.itemsFormArray.at(index) as FormGroup;
    const procedureId = Number(itemForm.get('procedureId')?.value);

    if (procedureId) {
      const procedure = this.procedures.find((p) => p.id === procedureId);
      if (
        procedure &&
        procedure.defaultPrice !== undefined &&
        procedure.defaultPrice !== null
      ) {
        // Garantir que o valor seja um número válido
        const price = Number(procedure.defaultPrice);
        itemForm.patchValue({
          value: isNaN(price) ? 0 : price,
        });
        this.calculateTotal();
      }
    }
  }

  calculateTotal(): void {
    const total = this.calculateProceduresTotal();
    // Informa o tipo e o valor de desconto
    const discountValue = this.budgetForm.get('discount')?.value;
    const discountType = this.budgetForm.get('discountType')?.value;
    let discount = 0;

    // Garantir que o desconto seja um número válido
    if (
      discountValue !== undefined &&
      discountValue !== null &&
      !isNaN(Number(discountValue))
    ) {
      discount = Number(discountValue);
      if (discountType === DiscountType.PERCENTAGE) {
        discount = (discount / 100) * total;
      } else if (discountType === DiscountType.FIXED) {
        discount = Math.min(discount, total);
      }
    }

    const finalTotal = Math.max(0, total - discount);

    // Atualizar o valor total
    this.budgetForm.patchValue({
      amountPaid: finalTotal,
    });

    // Recalcular o valor das parcelas se necessário
    if (this.shouldShowInstallments()) {
      this.calculateInstallmentValue();
    }
  }

  // Métodos para mostrar os diálogos de confirmação
  showApproveBudgetConfirmation(): void {
    this.showApproveConfirmation = true;
  }

  showCancelBudgetConfirmation(): void {
    this.showCancelConfirmation = true;
  }

  // Métodos para confirmar as ações
  confirmApproveBudget(): void {
    this.showApproveConfirmation = false;
    this.budgetForm.patchValue({
      status: BudgetStatus.APPROVED,
    });
    this.onSubmit();
  }

  confirmCancelBudget(): void {
    this.showCancelConfirmation = false;
    this.budgetForm.patchValue({
      status: BudgetStatus.CANCELLED,
    });
    this.onSubmit();
  }

  // Métodos para cancelar as confirmações
  closeApproveConfirmation(): void {
    this.showApproveConfirmation = false;
  }

  closeCancelConfirmation(): void {
    this.showCancelConfirmation = false;
  }

  onSubmit(): void {
    if (this.budgetForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      this.markFormGroupTouched(this.budgetForm);
      return;
    }

    this.isSubmitting = true;
    const budgetData = this.budgetForm.value;

    // Garantir que dentistId seja um número
    if (budgetData.dentistId !== null && budgetData.dentistId !== undefined) {
      budgetData.dentistId = Number(budgetData.dentistId);
    }

    if (budgetData.id) {
      // Atualizar orçamento existente
      const budgetId = budgetData.id;

      // Criar uma cópia limpa dos dados para enviar à API
      const cleanedBudgetData = { ...budgetData };

      // Remover o campo id do objeto principal (a API não espera o id no corpo)
      delete cleanedBudgetData.id;

      // Garantir que o desconto e os valores sejam números válidos
      const discount =
        typeof cleanedBudgetData.discount === 'string'
          ? parseFloat(cleanedBudgetData.discount)
          : Number(cleanedBudgetData.discount);

      const totalValue =
        typeof cleanedBudgetData.totalValue === 'string'
          ? parseFloat(cleanedBudgetData.totalValue)
          : Number(cleanedBudgetData.totalValue);

      const amountPaid =
        typeof cleanedBudgetData.amountPaid === 'string'
          ? parseFloat(cleanedBudgetData.amountPaid)
          : Number(cleanedBudgetData.amountPaid);

      cleanedBudgetData.discount = isNaN(discount) ? 0 : Math.max(0, discount);
      cleanedBudgetData.totalValue = isNaN(totalValue)
        ? 0
        : Math.max(0, totalValue);
      cleanedBudgetData.amountPaid = isNaN(amountPaid)
        ? 0
        : Math.max(0, amountPaid);

      // Remover o campo id dos itens e garantir que os valores sejam números válidos
      if (cleanedBudgetData.items && Array.isArray(cleanedBudgetData.items)) {
        cleanedBudgetData.items = cleanedBudgetData.items.map(
          (item: { id: any; value: any; [key: string]: any }) => {
            // Criar uma cópia do item sem o campo id
            const { id, ...itemWithoutId } = item;

            // Garantir que o valor seja um número positivo válido
            const value =
              typeof itemWithoutId.value === 'string'
                ? parseFloat(itemWithoutId.value)
                : Number(itemWithoutId.value);

            return {
              ...itemWithoutId,
              value: isNaN(value) ? 0 : Math.max(0, value), // Garantir que seja um número >= 0
            };
          }
        );
      }

      this.budgetService.updateBudget(budgetId, cleanedBudgetData).subscribe({
        next: (budget) => {
          this.isSubmitting = false;
          this.saved.emit(budget);
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao atualizar orçamento:', error);
          this.isSubmitting = false;
        },
      });
    } else {
      // Criar novo orçamento

      // Criar uma cópia limpa dos dados para enviar à API
      const cleanedBudgetData = { ...budgetData };

      // Garantir que o desconto e os valores sejam números válidos
      const discount =
        typeof cleanedBudgetData.discount === 'string'
          ? parseFloat(cleanedBudgetData.discount)
          : Number(cleanedBudgetData.discount);

      const totalValue =
        typeof cleanedBudgetData.totalValue === 'string'
          ? parseFloat(cleanedBudgetData.totalValue)
          : Number(cleanedBudgetData.totalValue);

      const amountPaid =
        typeof cleanedBudgetData.amountPaid === 'string'
          ? parseFloat(cleanedBudgetData.amountPaid)
          : Number(cleanedBudgetData.amountPaid);

      cleanedBudgetData.discount = isNaN(discount) ? 0 : Math.max(0, discount);
      cleanedBudgetData.totalValue = isNaN(totalValue)
        ? 0
        : Math.max(0, totalValue);
      cleanedBudgetData.amountPaid = isNaN(amountPaid)
        ? 0
        : Math.max(0, amountPaid);

      // Remover o campo id dos itens e garantir que os valores sejam números válidos
      if (cleanedBudgetData.items && Array.isArray(cleanedBudgetData.items)) {
        cleanedBudgetData.items = cleanedBudgetData.items.map(
          (item: { id: any; value: any; [key: string]: any }) => {
            // Criar uma cópia do item sem o campo id
            const { id, ...itemWithoutId } = item;

            // Garantir que o valor seja um número positivo válido
            const value =
              typeof itemWithoutId.value === 'string'
                ? parseFloat(itemWithoutId.value)
                : Number(itemWithoutId.value);

            return {
              ...itemWithoutId,
              value: isNaN(value) ? 0 : Math.max(0, value), // Garantir que seja um número >= 0
            };
          }
        );
      }

      this.budgetService.createBudget(cleanedBudgetData).subscribe({
        next: (budget) => {
          this.isSubmitting = false;
          this.saved.emit(budget);
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao criar orçamento:', error);
          this.isSubmitting = false;
        },
      });
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }

  closeModal(): void {
    this.close.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get dentistIdControl() {
    return this.budgetForm.get('dentistId');
  }

  get notesControl() {
    return this.budgetForm.get('notes');
  }

  get discountTypeControlWeb() {
    const discountType = this.budgetForm.get('discountType')?.value;
    if (discountType === 'percentage') {
      return `(${this.formatPrice(this.discountControl?.value)}% Desconto)`;
    } else if (discountType === 'fixed') {
      return `(R$${this.formatPrice(this.discountControl?.value)} Desconto)`;
    }
    return '';
  }

  get discountTypeControl() {
    return this.budgetForm.get('discountType')?.value;
  }

  get discountControl() {
    return this.budgetForm.get('discount');
  }

  get paymentMethodControl() {
    return this.budgetForm.get('paymentMethod');
  }

  get amountPaidControl() {
    return this.budgetForm.get('amountPaid');
  }

  getItemControl(index: number, controlName: string) {
    return (this.itemsFormArray.at(index) as FormGroup).get(controlName);
  }

  isFormDisabled(): boolean {
    return this.budgetForm.disabled;
  }

  // Métodos para o novo fluxo de adição de procedimentos
  resetProcedureForm(): void {
    this.selectedProcedureId = null;
    this.selectedProcedure = null;
    this.selectedDentistId = null;
    this.selectedTeeth = [];
    this.procedureValue = 0;
    this.procedureUnitValue = 0;
  }

  onProcedureSelected(procedure: Procedure | null): void {
    this.selectedProcedure = procedure;
    this.selectedProcedureId = procedure ? procedure.id : null;

    if (this.selectedProcedure) {
      // Definir o valor unitário do procedimento
      const price = Number(this.selectedProcedure.defaultPrice);
      this.procedureUnitValue = isNaN(price) ? 0 : price;
      this.updateProcedureValue();
    } else {
      this.procedureUnitValue = 0;
      this.procedureValue = 0;
    }
  }

  updateProcedureValue(): void {
    // Calcular o valor total com base na quantidade de dentes
    const teethCount = this.selectedTeeth.length;

    // Se não houver dentes selecionados, usar o valor unitário
    if (teethCount === 0) {
      this.procedureValue = this.procedureUnitValue;
    } else {
      // Multiplicar o valor unitário pela quantidade de dentes
      this.procedureValue = this.procedureUnitValue * teethCount;
    }
  }

  onTeethChange(): void {
    this.updateProcedureValue();
  }

  addProcedureToForm(): void {
    // Validação básica para procedimento e dentista
    if (!this.selectedProcedureId || !this.selectedDentistId) {
      this.teethSelectionError = true;

      return;
    }

    // Validação para garantir que pelo menos um dente seja selecionado
    if (this.selectedTeeth.length === 0) {
      // Definir erro de seleção de dentes
      this.teethSelectionError = true;

      // Focar no campo de seleção de dentes
      setTimeout(() => {
        if (this.teethSelector && this.teethSelector.toothInput) {
          this.teethSelector.toothInput.nativeElement.focus();
        }
      }, 0);

      // Sair do método sem adicionar o procedimento
      return;
    }

    // Limpar erro de seleção de dentes, se houver
    this.teethSelectionError = false;

    const itemForm = this.createItemForm();
    itemForm.patchValue({
      procedureId: Number(this.selectedProcedureId),
      tooth: this.selectedTeeth.join(', '),
      executingDentistId: Number(this.selectedDentistId),
      value: Number(this.procedureValue),
    });

    this.itemsFormArray.push(itemForm);
    this.calculateTotal();

    // Resetar o formulário de procedimento
    this.resetProcedureForm();

    // Após resetar, focar novamente no campo de seleção de dentes para facilitar a adição de um novo procedimento
    setTimeout(() => {
      if (this.teethSelector && this.teethSelector.toothInput) {
        this.teethSelector.toothInput.nativeElement.focus();
      }
    }, 0);
  }

  // Método legado - mantido para compatibilidade
  updateInputValue(procedureId: string, valueInput: HTMLInputElement): void {
    if (procedureId) {
      const procedure = this.procedures.find(
        (p) => p.id === parseInt(procedureId)
      );
      if (
        procedure &&
        procedure.defaultPrice !== undefined &&
        procedure.defaultPrice !== null
      ) {
        // Garantir que o valor seja um número válido
        const price = Number(procedure.defaultPrice);
        valueInput.value = isNaN(price) ? '0' : price.toString();
      } else {
        valueInput.value = '0';
      }
    } else {
      valueInput.value = '';
    }
  }

  formatPrice(price: any): string {
    // Verifica se o valor é um número válido
    if (price === null || price === undefined || isNaN(Number(price))) {
      return '0.00';
    }

    // Converte para número e formata com 2 casas decimais
    return Number(price).toFixed(2);
  }

  getProcedureName(procedureId: number): string {
    if (!procedureId) return 'Procedimento não especificado';

    const procedure = this.procedures.find((p) => p.id === procedureId);
    return procedure ? procedure.name : 'Procedimento não encontrado';
  }

  getDentistName(dentistId: number): string {
    if (!dentistId) return 'Dentista não especificado';

    const dentist = this.dentists.find((d) => d.id === dentistId);
    return dentist ? dentist.name : 'Dentista não encontrado';
  }

  // Adicione este método à classe
  toggleValuesVisibility(): void {
    this.hideValues = !this.hideValues;
  }

  // Adicione estes métodos à classe
  openPrintConfigModal(): void {
    this.showPrintConfigModal = true;
  }

  closePrintConfigModal(): void {
    this.showPrintConfigModal = false;
  }

  confirmPrintConfig(): void {
    this.budgetPrint.printBudget();
    this.closePrintConfigModal();
  }

  getPaymentMethodLabel(value: string): string {
    const method = this.paymentMethods.find((m) => m.value === value);
    return method ? method.label : 'Não informado';
  }

  // Adicione este método para verificar se deve mostrar opções de parcelamento
  shouldShowInstallments(): boolean {
    const paymentMethod = this.budgetForm.get('paymentMethod')?.value;
    return paymentMethod === PaymentMethod.CREDIT_CARD;
  }

  // Adicione este método para calcular o valor das parcelas
  calculateInstallmentValue(): void {
    const totalValue = this.budgetForm.get('amountPaid')?.value || 0;
    const installments = this.budgetForm.get('installments')?.value || 1;
    this.installmentValue = totalValue / installments;
  }

  // Modifique o método onPaymentMethodChange
  onPaymentMethodChange(): void {
    this.showInstallments = this.shouldShowInstallments();

    if (this.showInstallments) {
      // Se mudar para cartão de crédito, garantir que tenha pelo menos 1 parcela
      if (this.budgetForm.get('installments')?.value < 1) {
        this.budgetForm.patchValue({
          installments: 1,
        });
        this.installmentsCount = 1;
      }
      this.calculateInstallmentValue();
    } else {
      // Se não for cartão de crédito, resetar para 1 parcela
      this.budgetForm.patchValue({
        installments: 1,
      });
      this.installmentsCount = 1;
    }
  }

  // Adicione este método para atualizar o número de parcelas
  onInstallmentsChange(event: any): void {
    const installments = parseInt(event.target.value, 10);

    // Atualizar o valor no formulário
    this.budgetForm.patchValue({
      installments: installments,
    });

    this.installmentsCount = installments;
    this.calculateInstallmentValue();
  }

  // Ação no botão de mais opções
  toggleOptionsMenu(): void {
    this.showOptionsMenu = !this.showOptionsMenu;
  }

  // Fechar o menu ao clicar fora dele
  @HostListener('document:click', ['$event'])
  clickOutside(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.dropdown-menu');
    const button = document.querySelector('[title="Mais opções"]');

    if (
      dropdown &&
      button &&
      !dropdown.contains(target) &&
      !button.contains(target)
    ) {
      this.showOptionsMenu = false;
    }
  }

  // Implementar as ações do menu
  sendEmail(): void {
    // Implementar lógica de envio de email
    this.showOptionsMenu = false;
    // Você pode mostrar um modal ou implementar envio direto de email
    console.log('Enviar email');
  }

  applyPercentageDiscount(): void {
    this.showOptionsMenu = false;

    if (
      this.budgetForm.get('discountType')?.value !== DiscountType.PERCENTAGE
    ) {
      this.discountValue = 0;

      // Definir o tipo de desconto como percentual
      this.budgetForm.patchValue({
        discountType: DiscountType.PERCENTAGE,
        discount: 0, // Resetar o valor do desconto no formulário também
      });
      this.calculateTotal();
    }

    // Definir o valor inicial como o valor total dos procedimentos
    this.proceduresValue = this.calculateProceduresTotal();
    this.finalValueAfterDiscount = this.proceduresValue;
    this.updateFinalValueAfterDiscount();
    this.showDiscountPercentageModal = true;
  }

  applyFixedDiscount(): void {
    this.showOptionsMenu = false;

    if (this.budgetForm.get('discountType')?.value !== DiscountType.FIXED) {
      this.discountValue = 0;

      this.budgetForm.patchValue({
        discountType: DiscountType.FIXED,
        discount: 0, // Resetar o valor do desconto no formulário também
      });

      this.calculateTotal();
    }

    // Definir o valor inicial como o valor total dos procedimentos
    this.proceduresValue = this.calculateProceduresTotal();
    this.finalValueAfterDiscount = this.proceduresValue;
    this.updateFinalValueAfterDiscount();
    this.showDiscountModal = true;
  }

  closeDiscountPercentageModal(): void {
    this.showDiscountPercentageModal = false;

    // Se não houver desconto, definir o tipo como NONE
    if (this.budgetForm.get('discount')?.value <= 0) {
      this.budgetForm.patchValue({
        discountType: DiscountType.NONE,
      });
    }
  }

  closeDiscountModal(): void {
    this.showDiscountModal = false;
    // Se não houver desconto, definir o tipo como NONE
    if (this.budgetForm.get('discount')?.value <= 0) {
      this.budgetForm.patchValue({
        discountType: DiscountType.NONE,
      });
    }
  }

  confirmDiscountPorcent(): void {
    // Atualizar o campo de desconto no formulário
    this.budgetForm.patchValue({
      discount: this.discountValue,
      discountType: DiscountType.PERCENTAGE,
      // Atualizar o valor total e o valor a ser pago
      totalValue: this.proceduresValue,
      amountPaid: this.finalValueAfterDiscount,
    });

    // Recalcular o total após aplicar o desconto
    this.calculateTotal();

    this.closeDiscountPercentageModal();
  }

  confirmDiscount(): void {
    // Garantir que o desconto fixo não seja maior que o valor total
    const fixedDiscount = Math.min(this.discountValue, this.proceduresValue);

    // Atualizar o campo de desconto no formulário
    this.budgetForm.patchValue({
      discount: fixedDiscount,
      discountType: DiscountType.FIXED,
      // Atualizar o valor total e o valor a ser pago
      totalValue: this.proceduresValue,
      amountPaid: this.finalValueAfterDiscount,
    });

    // Recalcular o total após aplicar o desconto
    this.calculateTotal();

    this.closeDiscountModal();
  }

  // Método para atualizar o valor final quando o percentual de desconto mudar
  updateFinalValueAfterDiscount(): void {
    const discountType = this.budgetForm.get('discountType')?.value;
    if (discountType === DiscountType.PERCENTAGE) {
      // Calcular o valor do desconto em percentual
      const discountAmount = (this.discountValue / 100) * this.proceduresValue;

      // Calcular o valor final após o desconto
      this.finalValueAfterDiscount = Math.max(
        0,
        this.proceduresValue - discountAmount
      );
    } else if (discountType === DiscountType.FIXED) {
      // Garantir que o desconto fixo não seja maior que o valor total
      const fixedDiscount = Math.min(this.discountValue, this.proceduresValue);

      // Calcular o valor final após o desconto
      this.finalValueAfterDiscount = Math.max(
        0,
        this.proceduresValue - fixedDiscount
      );
    } else {
      // Se não houver desconto, o valor final é igual ao valor dos procedimentos
      this.finalValueAfterDiscount = this.proceduresValue;
    }
  }

  // Método auxiliar para calcular apenas o total dos procedimentos sem descontos
  calculateProceduresTotal(): number {
    let total = 0;
    // Somar os valores de todos os itens
    for (let i = 0; i < this.itemsFormArray.length; i++) {
      const itemForm = this.itemsFormArray.at(i) as FormGroup;
      const value = itemForm.get('value')?.value;

      // Garantir que o valor seja um número válido
      if (value !== undefined && value !== null && !isNaN(Number(value))) {
        total += Number(value);
      }
    }
    // Atualizar o valor total
    this.budgetForm.patchValue({
      totalValue: total,
    });
    return total;
  }
}
