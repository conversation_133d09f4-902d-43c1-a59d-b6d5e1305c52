<div *ngIf="isOpen" class="fixed inset-0 z-50 flex flex-col bg-white">
  <!-- Header -->
  <div class="flex justify-between items-center p-4 border-b bg-gray-50">
    <h3 class="text-lg font-medium">
      {{ budget?.id ? "Editar Orçamento" : "Novo Orçamento" }}
    </h3>
    <button
      type="button"
      class="text-gray-400 hover:text-gray-500"
      (click)="closeModal()"
    >
      <svg
        class="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex-1 flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Content -->
  <div *ngIf="!isLoading" class="flex-1 overflow-hidden">
    <div class="h-full">
      <form [formGroup]="budgetForm" (ngSubmit)="onSubmit()" class="h-full">
        <div class="grid grid-cols-1 lg:grid-cols-2 h-full">
          <!-- Coluna 1 - Informações do Orçamento (com scroll) -->
          <div class="space-y-6 p-6 overflow-y-auto max-h-[calc(100vh-160px)]">
            <!-- Adicionar Procedimento -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-800">
                Adicionar Procedimento
              </h4>

              <!-- Formulário de adição de procedimento -->
              <div
                *ngIf="!isFormDisabled()"
                class="bg-gray-50 p-4 rounded-lg border border-gray-200"
              >
                <!-- Etapa 1: Selecionar procedimento -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Procedimento *
                  </label>
                  <app-procedure-search
                    [procedures]="procedures"
                    [selectedProcedure]="selectedProcedure"
                    (procedureSelected)="onProcedureSelected($event)"
                  ></app-procedure-search>
                  <p class="text-xs text-gray-500 mt-1">
                    Busque pelo nome do procedimento ou pelo tipo
                  </p>
                </div>

                <!-- Etapa 2: Detalhes do procedimento (aparece após selecionar um procedimento) -->
                <div
                  *ngIf="selectedProcedure"
                  class="space-y-4 mt-4 pt-4 border-t border-gray-200"
                >
                  <h5 class="font-medium text-gray-700">
                    Detalhes do Procedimento
                  </h5>

                  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Dente -->
                    <div class="space-y-2">
                      <label class="block text-sm font-medium text-gray-700">
                        Dente(s) *
                      </label>
                      <app-teeth-selector
                        [(selectedTeeth)]="selectedTeeth"
                        (selectedTeethChange)="onTeethChange()"
                      ></app-teeth-selector>

                      <!-- Mensagem de erro para seleção de dentes -->
                      <div
                        *ngIf="teethSelectionError"
                        class="text-red-500 text-xs mt-1"
                      >
                        Selecione pelo menos um dente para adicionar o
                        procedimento.
                      </div>
                    </div>

                    <!-- Dentista Executor -->
                    <div class="space-y-2 col-span-2">
                      <label class="block text-sm font-medium text-gray-700">
                        Dentista Executor *
                      </label>
                      <app-searchable-select
                        [options]="dentistOptions"
                        [placeholder]="'Selecione um dentista'"
                        [searchPlaceholder]="'Buscar dentista...'"
                        [(value)]="selectedDentistId"
                      ></app-searchable-select>
                    </div>

                    <!-- Valor -->
                    <div class="space-y-2">
                      <label class="block text-sm font-medium text-gray-700">
                        Valor (R$) *
                      </label>
                      <div class="flex flex-col space-y-2">
                        <div class="flex items-center">
                          <span class="font-medium text-blue-600">
                            {{
                              hideValues
                                ? "******"
                                : "R$ " + formatPrice(procedureValue)
                            }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Botões de ação -->
                  <div class="flex justify-end space-x-2 mt-4">
                    <button
                      type="button"
                      (click)="resetProcedureForm()"
                      class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      type="button"
                      [disabled]="!selectedProcedureId || !selectedDentistId"
                      (click)="addProcedureToForm()"
                      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Adicionar ao Orçamento
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Dentista responsável -->
            <div class="space-y-2">
              <label
                for="dentistId"
                class="block text-sm font-medium text-gray-700"
              >
                Dentista Responsável *
              </label>
              <select
                id="dentistId"
                formControlName="dentistId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{
                  'border-red-500':
                    dentistIdControl?.invalid && dentistIdControl?.touched
                }"
              >
                <option [ngValue]="null">Selecione um dentista</option>
                <option *ngFor="let dentist of dentists" [ngValue]="dentist.id">
                  {{ dentist.name }}
                </option>
              </select>
              <div
                *ngIf="dentistIdControl?.invalid && dentistIdControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                <span *ngIf="dentistIdControl?.errors?.['required']">
                  Dentista é obrigatório
                </span>
              </div>
            </div>

            <!-- Observações -->
            <div class="space-y-2">
              <label
                for="notes"
                class="block text-sm font-medium text-gray-700"
              >
                Observações
              </label>
              <textarea
                id="notes"
                formControlName="notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
            </div>
          </div>

          <!-- Coluna 2 - Quadro do Orçamento (fixo) -->
          <div class="p-6 border-l border-gray-200 h-full flex flex-col">
            <!-- Cabeçalho do Orçamento -->
            <div
              class="flex justify-between items-center p-4 bg-gray-800 text-white rounded-lg"
            >
              <h3 class="text-xl font-medium text-white">Orçamentos</h3>
              <div class="flex items-center space-x-2">
                <!-- Botão de visualização -->
                <button
                  type="button"
                  class="p-2 rounded-full hover:bg-gray-700 transition-colors"
                  title="{{
                    hideValues ? 'Mostrar valores' : 'Ocultar valores'
                  }}"
                  (click)="toggleValuesVisibility()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </button>

                <!-- Botão de impressão -->
                <button
                  type="button"
                  class="p-2 rounded-full hover:bg-gray-700 transition-colors"
                  title="Imprimir"
                  [disabled]="!budget?.id"
                  [ngClass]="{
                    'opacity-50 cursor-not-allowed': !budget?.id
                  }"
                  (click)="openPrintConfigModal()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                    />
                  </svg>
                </button>

                <!-- Botão de email -->
                <button
                  type="button"
                  class="p-2 rounded-full hover:bg-gray-700 transition-colors"
                  title="Enviar por email"
                  (click)="sendEmail()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2v-10a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </button>

                <!-- Botão de mais opções -->
                <div class="relative">
                  <button
                    type="button"
                    class="p-2 rounded-full hover:bg-gray-700 transition-colors"
                    title="Mais opções"
                    (click)="toggleOptionsMenu()"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                      />
                    </svg>
                  </button>

                  <!-- Menu dropdown -->
                  <div
                    *ngIf="showOptionsMenu"
                    class="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-50"
                  >
                    <div class="py-1">
                      <button
                        type="button"
                        class="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center"
                        (click)="applyPercentageDiscount()"
                        [disabled]="itemsFormArray.length === 0"
                        [ngClass]="{
                          'opacity-50 cursor-not-allowed':
                            itemsFormArray.length === 0
                        }"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 mr-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Desc em %
                      </button>
                      <button
                        type="button"
                        class="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center"
                        (click)="applyFixedDiscount()"
                        [disabled]="itemsFormArray.length === 0"
                        [ngClass]="{
                          'opacity-50 cursor-not-allowed':
                            itemsFormArray.length === 0
                        }"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 mr-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Desc em Real
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Lista de Procedimentos (com scroll) -->
            <div
              class="bg-gray-100 rounded-lg p-2 flex-grow overflow-y-auto max-h-[calc(100vh-450px)]"
            >
              <div formArrayName="items" class="space-y-2">
                <!-- Mensagem quando não há procedimentos -->
                <div
                  *ngIf="itemsFormArray.length === 0"
                  class="bg-white rounded-lg p-4 text-center text-gray-500 border border-gray-200"
                >
                  Nenhum procedimento adicionado ao orçamento.
                </div>

                <!-- Cards de procedimentos -->
                <div
                  *ngFor="let item of itemsFormArray.controls; let i = index"
                  [formGroupName]="i"
                  class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 relative flex gap-1"
                >
                  <!-- Botão de remover (posicionado no canto superior direito) -->
                  <button
                    *ngIf="!isFormDisabled()"
                    type="button"
                    (click)="removeItem(i)"
                    class="text-red-600 hover:text-red-800 bg-white p-2 shadow-sm hover:bg-red-50 transition-all"
                    title="Remover procedimento"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>

                  <div class="flex flex-1 flex-col md:flex-row">
                    <!-- Informações do procedimento -->
                    <div class="flex-grow p-2">
                      <!-- Nome do procedimento -->
                      <h5 class="font-medium text-gray-800 mb-2">
                        {{
                          getProcedureName(
                            getItemControl(i, "procedureId")?.value
                          )
                        }}
                      </h5>

                      <!-- Dentista -->
                      <div class="mb-2 text-sm">
                        <span class="text-gray-500">Dentista:</span>
                        <span class="ml-1 text-gray-700">{{
                          getDentistName(
                            getItemControl(i, "executingDentistId")?.value
                          )
                        }}</span>
                      </div>

                      <!-- Dentes -->
                      <div
                        class="text-sm"
                        *ngIf="getItemControl(i, 'tooth')?.value"
                      >
                        <span class="text-gray-500">Dente(s):</span>
                        <span class="ml-1 text-gray-700">{{
                          getItemControl(i, "tooth")?.value
                        }}</span>
                      </div>

                      <!-- Campos ocultos para manter os valores no formulário -->
                      <input type="hidden" formControlName="procedureId" />
                      <input
                        type="hidden"
                        formControlName="executingDentistId"
                      />
                      <input type="hidden" formControlName="tooth" />
                    </div>

                    <!-- Valor -->
                    <div
                      class="bg-blue-50 p-4 border-t md:border-t-0 md:border-l border-gray-200 flex items-center justify-center"
                    >
                      <div class="text-center">
                        <div class="text-xs font-medium text-gray-500 mb-1">
                          Valor
                        </div>
                        <div class="text-lg font-semibold text-gray-800">
                          {{
                            hideValues
                              ? "******"
                              : "R$ " +
                                formatPrice(getItemControl(i, "value")?.value)
                          }}
                        </div>
                        <!-- Campo oculto para manter o valor no formulário -->
                        <input type="hidden" formControlName="value" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Totais e Pagamento -->
            <div class="space-y-4">
              <!-- Forma de Pagamento -->
              <div class="flex justify-between items-center">
                <label
                  for="paymentMethod"
                  class="block text-sm font-medium text-gray-700"
                >
                  Forma de Pagamento:
                </label>
                <select
                  id="paymentMethod"
                  formControlName="paymentMethod"
                  class="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  (change)="onPaymentMethodChange()"
                >
                  <option
                    *ngFor="let method of paymentMethods"
                    [value]="method.value"
                  >
                    {{ method.label }}
                  </option>
                </select>
              </div>

              <!-- Opções de Parcelamento (Condicional) -->
              <div
                *ngIf="shouldShowInstallments()"
                class="mt-4 bg-blue-50 p-4 rounded-lg"
              >
                <div class="mb-3">
                  <label
                    for="installments"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Número de Parcelas:
                  </label>
                  <select
                    id="installments"
                    [(ngModel)]="installmentsCount"
                    [ngModelOptions]="{ standalone: true }"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    (change)="onInstallmentsChange($event)"
                  >
                    <option
                      *ngFor="let option of installmentOptions"
                      [value]="option"
                    >
                      {{ option }}x {{ option === 1 ? "à vista" : "sem juros" }}
                    </option>
                  </select>
                </div>

                <div class="text-sm text-gray-700">
                  <div class="flex justify-between mb-1">
                    <span>Valor da Parcela:</span>
                    <span class="font-medium"
                      >R$ {{ formatPrice(installmentValue) }}</span
                    >
                  </div>
                  <div class="flex justify-between">
                    <span>Total Parcelado:</span>
                    <span class="font-medium"
                      >R$ {{ formatPrice(amountPaidControl?.value) }}</span
                    >
                  </div>
                </div>
              </div>

              <!-- Opções para Dinheiro (Condicional) -->
              <div
                *ngIf="budgetForm.get('paymentMethod')?.value === 'cash'"
                class="mt-4 bg-green-50 p-4 rounded-lg"
              >
                <div class="text-sm text-gray-700">
                  <div class="flex justify-between mb-1">
                    <span>Valor à Vista:</span>
                    <span class="font-medium"
                      >R$ {{ formatPrice(amountPaidControl?.value) }}</span
                    >
                  </div>
                  <div class="text-xs text-gray-500 mt-2">
                    Pagamento à vista em dinheiro pode ter desconto adicional
                    conforme política da clínica.
                  </div>
                </div>
              </div>

              <!-- Opções para Boleto (Condicional) -->
              <div
                *ngIf="budgetForm.get('paymentMethod')?.value === 'bank_slip'"
                class="mt-4 bg-yellow-50 p-4 rounded-lg"
              >
                <div class="text-sm text-gray-700">
                  <div class="flex justify-between mb-1">
                    <span>Valor do Boleto:</span>
                    <span class="font-medium"
                      >R$ {{ formatPrice(amountPaidControl?.value) }}</span
                    >
                  </div>
                  <div class="text-xs text-gray-500 mt-2">
                    O boleto será gerado após a aprovação do orçamento.
                  </div>
                </div>
              </div>

              <!-- Total -->
              <div
                class="flex justify-between items-center pt-4 border-t border-gray-200"
              >
                <span class="text-lg font-bold text-gray-900">Total:</span>
                <div class="flex items-center gap-2">
                  <label
                    *ngIf="discountControl?.value > 0 && !hideValues"
                    class="block text-sm font-medium text-red-600"
                  >
                    {{ discountTypeControlWeb }}
                  </label>
                  <span class="text-lg font-bold text-gray-900">
                    {{
                      hideValues
                        ? "******"
                        : "R$ " + formatPrice(amountPaidControl?.value)
                    }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Botões de Ação -->
            <div *ngIf="!isFormDisabled()" class="flex gap-4 items-center">
              <button
                *ngIf="budget?.status === 'open'"
                type="button"
                (click)="showCancelBudgetConfirmation()"
                class="w-full px-4 py-3 h-14 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition-colors"
              >
                Cancelar Orçamento
              </button>
              <button
                *ngIf="budget?.status === 'open'"
                type="button"
                (click)="showApproveBudgetConfirmation()"
                class="w-full px-4 py-3 h-14 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium"
              >
                Aprovar Orçamento
              </button>
            </div>

            <!-- Status do Orçamento -->
            <div
              *ngIf="budget?.status === 'approved'"
              class="bg-green-100 text-green-800 p-4 rounded-lg"
            >
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span class="font-medium"
                  >Este orçamento foi aprovado e não pode ser editado.</span
                >
              </div>
            </div>

            <div
              *ngIf="budget?.status === 'cancelled'"
              class="bg-red-100 text-red-800 p-4 rounded-lg"
            >
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                <span class="font-medium"
                  >Este orçamento foi cancelado e não pode ser editado.</span
                >
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Footer -->
  <div class="p-4 border-t bg-gray-50 flex justify-end space-x-4">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
    >
      Cancelar
    </button>
    <button
      *ngIf="!isFormDisabled()"
      type="button"
      (click)="onSubmit()"
      [disabled]="isSubmitting"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <div
        *ngIf="isSubmitting"
        class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"
      ></div>
      {{ budget?.id ? "Atualizar" : "Salvar" }}
    </button>
  </div>
</div>

<!-- Diálogo de confirmação para aprovar orçamento -->
<app-confirmation-dialog
  [isOpen]="showApproveConfirmation"
  title="Aprovar Orçamento"
  message="Tem certeza que deseja APROVAR este orçamento? Esta ação não poderá ser desfeita e o orçamento não poderá mais ser editado."
  confirmButtonText="Aprovar"
  cancelButtonText="Cancelar"
  confirmButtonClass="bg-green-600 hover:bg-green-700"
  type="warning"
  (confirm)="confirmApproveBudget()"
  (cancel)="closeApproveConfirmation()"
></app-confirmation-dialog>

<!-- Diálogo de confirmação para cancelar orçamento -->
<app-confirmation-dialog
  [isOpen]="showCancelConfirmation"
  title="Cancelar Orçamento"
  message="Tem certeza que deseja CANCELAR este orçamento? Esta ação não poderá ser desfeita e o orçamento não poderá mais ser editado."
  confirmButtonText="Cancelar Orçamento"
  cancelButtonText="Voltar"
  confirmButtonClass="bg-red-600 hover:bg-red-700"
  type="danger"
  (confirm)="confirmCancelBudget()"
  (cancel)="closeCancelConfirmation()"
></app-confirmation-dialog>

<!-- Modal de configuração de desconto %-->
<app-modal
  [isOpen]="showDiscountPercentageModal"
  title="Desconto"
  (close)="closeDiscountPercentageModal()"
  [showDefaultFooter]="false"
>
  <div class="p-6 max-w-2xl">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Valor dos Procedimentos -->
      <div class="flex flex-col">
        <label class="text-sm text-gray-600 mb-1">
          Valor dos Procedimentos
        </label>
        <div class="relative">
          <span
            class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400"
          >
            R$
          </span>
          <input
            type="text"
            class="pl-8 pr-2 py-2 w-full border-b border-gray-300 text-gray-500 bg-gray-100 cursor-not-allowed"
            [value]="proceduresValue | number : '1.2-2'"
            disabled
          />
        </div>
      </div>

      <!-- Desconto % -->
      <div class="flex flex-col">
        <label class="text-sm text-gray-600 mb-1">Desconto %</label>
        <input
          type="number"
          class="py-2 px-3 border-b border-gray-300 focus:outline-none focus:ring-0"
          [(ngModel)]="discountValue"
          (ngModelChange)="updateFinalValueAfterDiscount()"
          min="0"
          max="100"
          step="0.01"
        />
      </div>
    </div>

    <!-- Valor Final -->
    <div class="mt-6">
      <label class="text-sm text-gray-600 mb-1">Valor Final</label>
      <div class="relative">
        <span
          class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400"
        >
          R$
        </span>
        <input
          type="text"
          class="pl-8 pr-2 py-2 w-full border-b border-gray-300 text-gray-500 bg-gray-100 cursor-not-allowed"
          [value]="finalValueAfterDiscount | number : '1.2-2'"
          disabled
        />
      </div>
    </div>
  </div>

  <!-- Rodapé do modal -->
  <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
    <button
      type="button"
      class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="confirmDiscountPorcent()"
    >
      Confirmar
    </button>
    <button
      type="button"
      class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="closeDiscountPercentageModal()"
    >
      Cancelar
    </button>
  </div>
</app-modal>

<!-- Modal de configuração de desconto R$-->
<app-modal
  [isOpen]="showDiscountModal"
  title="Desconto"
  (close)="closeDiscountModal()"
  [showDefaultFooter]="false"
>
  <div class="p-6 max-w-2xl">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Valor dos Procedimentos -->
      <div class="flex flex-col">
        <label class="text-sm text-gray-600 mb-1">
          Valor dos Procedimentos
        </label>
        <div class="relative">
          <span
            class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400"
          >
            R$
          </span>
          <input
            type="text"
            class="pl-8 pr-2 py-2 w-full border-b border-gray-300 text-gray-500 bg-gray-100 cursor-not-allowed"
            [value]="proceduresValue | number : '1.2-2'"
            disabled
          />
        </div>
      </div>

      <!-- Desconto R$ -->
      <div class="flex flex-col">
        <label class="text-sm text-gray-600 mb-1">Desconto R$</label>
        <input
          type="number"
          class="py-2 px-3 border-b border-gray-300 focus:outline-none focus:ring-0"
          [(ngModel)]="discountValue"
          (ngModelChange)="updateFinalValueAfterDiscount()"
          min="0"
          [max]="proceduresValue"
          step="0.5"
        />
      </div>
    </div>

    <!-- Valor Final -->
    <div class="mt-6">
      <label class="text-sm text-gray-600 mb-1">Valor Final</label>
      <div class="relative">
        <span
          class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400"
        >
          R$
        </span>
        <input
          type="text"
          class="pl-8 pr-2 py-2 w-full border-b border-gray-300 text-gray-500 bg-gray-100 cursor-not-allowed"
          [value]="finalValueAfterDiscount | number : '1.2-2'"
          disabled
        />
      </div>
    </div>
  </div>

  <!-- Rodapé do modal -->
  <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
    <button
      type="button"
      class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="confirmDiscount()"
    >
      Confirmar
    </button>
    <button
      type="button"
      class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="closeDiscountModal()"
    >
      Cancelar
    </button>
  </div>
</app-modal>

<!-- Modal de impressão -->
<app-budget-print-modal
  [isOpen]="showPrintConfigModal"
  [budget]="budget"
  [procedures]="procedures"
  [dentists]="dentists"
  (close)="closePrintConfigModal()"
></app-budget-print-modal>
