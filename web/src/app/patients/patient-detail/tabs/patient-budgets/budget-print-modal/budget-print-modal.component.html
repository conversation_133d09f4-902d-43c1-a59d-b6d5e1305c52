<app-modal
  [title]="'Configurações de Impressão'"
  [isOpen]="isOpen"
  (close)="closeModal()"
  [showDefaultFooter]="false"
>
  <div class="p-6 max-w-2xl">
    <h3 class="text-lg font-medium text-gray-900 mb-4">
      Selecione os itens a serem incluídos na impressão:
    </h3>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Opções de configuração -->
      <div class="flex items-center">
        <input
          type="checkbox"
          id="clinicInfo"
          [(ngModel)]="printConfig.clinicInfo"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="clinicInfo" class="ml-2 block text-sm text-gray-900"
          >Informações da clínica</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="dentistData"
          [(ngModel)]="printConfig.dentistData"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="dentistData" class="ml-2 block text-sm text-gray-900"
          >Dados do dentista</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="signature"
          [(ngModel)]="printConfig.signature"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="signature" class="ml-2 block text-sm text-gray-900"
          >Assinatura</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="permanentOdontogram"
          [(ngModel)]="printConfig.permanentOdontogram"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label
          for="permanentOdontogram"
          class="ml-2 block text-sm text-gray-900"
          >Odontograma permanente</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="deciduousOdontogram"
          [(ngModel)]="printConfig.deciduousOdontogram"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label
          for="deciduousOdontogram"
          class="ml-2 block text-sm text-gray-900"
          >Odontograma decíduo</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="groupProcedures"
          [(ngModel)]="printConfig.groupProcedures"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="groupProcedures" class="ml-2 block text-sm text-gray-900"
          >Agrupamento de procedimentos</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="procedureValues"
          [(ngModel)]="printConfig.procedureValues"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="procedureValues" class="ml-2 block text-sm text-gray-900"
          >Valores dos procedimentos</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="procedureDescriptions"
          [(ngModel)]="printConfig.procedureDescriptions"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label
          for="procedureDescriptions"
          class="ml-2 block text-sm text-gray-900"
          >Descrição dos procedimentos</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="totalValue"
          [(ngModel)]="printConfig.totalValue"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="totalValue" class="ml-2 block text-sm text-gray-900"
          >Valor total</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="installmentValue"
          [(ngModel)]="printConfig.installmentValue"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="installmentValue" class="ml-2 block text-sm text-gray-900"
          >Valor parcelado</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="paymentMethod"
          [(ngModel)]="printConfig.paymentMethod"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="paymentMethod" class="ml-2 block text-sm text-gray-900"
          >Forma de pagamento</label
        >
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="clinicLogo"
          [(ngModel)]="printConfig.clinicLogo"
          [ngModelOptions]="{ standalone: true }"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="clinicLogo" class="ml-2 block text-sm text-gray-900"
          >Logo da clínica</label
        >
      </div>
    </div>
  </div>

  <!-- Rodapé do modal -->
  <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
    <button
      type="button"
      class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="confirmPrintConfig()"
    >
      OK
    </button>
    <button
      type="button"
      class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
      (click)="closeModal()"
    >
      Fechar
    </button>
  </div>
</app-modal>
