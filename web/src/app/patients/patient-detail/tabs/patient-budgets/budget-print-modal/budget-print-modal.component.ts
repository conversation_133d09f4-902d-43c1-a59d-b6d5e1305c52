import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Budget, PaymentMethod } from '../../../../../core/models/budget.model';
import { Procedure } from '../../../../../core/models/procedure.model';
import { Dentist } from '../../../../../core/models/dentist.model';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

@Component({
  selector: 'app-budget-print-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, ModalComponent],
  templateUrl: './budget-print-modal.component.html',
  styleUrl: './budget-print-modal.component.scss',
})
export class BudgetPrintModalComponent {
  @Input() isOpen = false;
  @Input() budget: Budget | null = null;
  @Input() procedures: Procedure[] = [];
  @Input() dentists: Dentist[] = [];
  @Output() close = new EventEmitter<void>();

  printConfig = {
    clinicInfo: true,
    dentistData: true,
    signature: true,
    permanentOdontogram: true,
    deciduousOdontogram: false,
    groupProcedures: false,
    procedureValues: true,
    procedureDescriptions: true,
    totalValue: true,
    amountPaid: true,
    installmentValue: false,
    paymentMethod: true,
    clinicLogo: true,
  };

  closeModal(): void {
    this.close.emit();
  }

  confirmPrintConfig(): void {
    this.printBudget();
    this.closeModal();
  }

  printBudget(): void {
    if (!this.budget) return;

    // Criar um iframe invisível
    const printFrame = document.createElement('iframe');
    printFrame.style.position = 'absolute';
    printFrame.style.width = '0';
    printFrame.style.height = '0';
    printFrame.style.border = 'none';
    printFrame.style.visibility = 'hidden';

    document.body.appendChild(printFrame);

    // Obter o documento do iframe
    const doc =
      printFrame.contentDocument || printFrame.contentWindow?.document;
    if (!doc) {
      alert('Erro ao criar documento de impressão.');
      return;
    }

    // Gerar nome do arquivo PDF
    const patientName = this.budget.patient?.name || 'paciente';
    const sanitizedPatientName = patientName
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '_');
    const currentDate = new Date().toISOString().slice(0, 10); // formato YYYY-MM-DD
    const fileName = `orcamento_${sanitizedPatientName}_${currentDate}.pdf`;

    // Adicionar título ao documento para definir o nome do arquivo
    doc.title = fileName;

    // Inserir o conteúdo gerado no documento do iframe
    doc.open();
    doc.write(this.generatePrintContent());
    doc.close();

    // Imprimir quando o conteúdo estiver carregado
    printFrame.onload = () => {
      setTimeout(() => {
        // Configurar o nome do arquivo para a impressão
        if (printFrame.contentWindow) {
          // Alguns navegadores permitem definir o nome do arquivo através do título
          printFrame.contentWindow.document.title = fileName;

          printFrame.contentWindow.print();
        }

        // Remover o iframe após a impressão
        setTimeout(() => {
          document.body.removeChild(printFrame);
        }, 500);
      }, 100);
    };
  }

  generatePrintContent(): string {
    if (!this.budget) return '';

    // Data atual formatada
    const currentDate = new Date().toLocaleDateString('pt-BR');

    // Gerar nome do arquivo PDF para o título
    const patientName = this.budget.patient?.name || 'paciente';
    const sanitizedPatientName = patientName
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '_');
    const isoDate = new Date().toISOString().slice(0, 10); // formato YYYY-MM-DD
    const fileName = `Orcamento_${sanitizedPatientName}_${isoDate}`;

    // Construir o conteúdo HTML com base nas configurações selecionadas
    let proceduresHtml = '';

    // Gerar HTML para os procedimentos
    if (this.budget.items) {
      this.budget.items.forEach((item) => {
        const procedureId = item.procedureId;
        const procedure = this.procedures.find((p) => p.id === procedureId);
        const procedureName = this.getProcedureName(procedureId);
        const dentistName = this.getDentistName(item.executingDentistId);
        const tooth = item.tooth;
        const value = item.value;

        proceduresHtml += `
          <div class="procedure-item">
            <div><strong>Descrição:</strong> ${procedureName} ${
          tooth ? `(${tooth})` : ''
        } ${procedure?.type ? `(${procedure.type})` : ''}</div>
            <div><strong>Tabela:</strong> Particular</div>
            <div><strong>Valor:</strong> R$ ${this.formatPrice(value)}</div>
            <div><strong>Dentista:</strong> ${dentistName}</div>
            <div><strong>Dentes:</strong> ${tooth || '-'}</div>
          </div>
        `;
      });
    }

    // Calcular parcelas se necessário
    let installmentsHtml = '';
    let installments = 0;
    if (this.printConfig.installmentValue && this.budget) {
      const amountPaid = this.budget.amountPaid || 0;
      installments = this.budget.installments || 1;
      const installmentValue = amountPaid / installments;

      for (let i = 1; i <= installments; i++) {
        installmentsHtml += `
          <div class="installment-item">Parcela ${i}: R$ ${this.formatPrice(
          installmentValue
        )}</div>
        `;
      }
    }

    const baseUrl = window.location.origin;
    const logoPath = `${baseUrl}/assets/logo-moderno.svg`;
    // Obter o método de pagamento
    const paymentMethod = this.getPaymentMethodLabel(this.budget.paymentMethod);

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${fileName}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              font-size: 12px;
              line-height: 1.4;
            }
            .container {
              max-width: 800px;
              margin: auto;
              border: 1px solid #ddd;
              padding: 20px;
            }
            .header {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-bottom: 10px;
              border-bottom: 1px solid #eee;
              padding-bottom: 10px;
              text-align: center;
            }
            .logo-container {
              width: 100%;
              display: flex;
              justify-content: center;
              margin-bottom: 5px;
            }
            .logo {
              height: 120px;
            }
            .date-section {
              width: 100%;
              text-align: right;
              margin-top: 5px;
            }
            .title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 15px;
            }
            .clinic-info {
              margin-bottom: 15px;
            }
            .patient-info {
              margin-bottom: 10px;
            }
            .section-title {
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
            }
            .procedure-item {
              margin-bottom: 15px;
              padding: 10px;
              border: 1px solid #eee;
            }
            .payment-section {
              margin-top: 20px;
            }
            .installment-item {
              margin-bottom: 5px;
            }
            .total-value {
              margin-top: 15px;
              font-weight: bold;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
            }
            .date-section {
              text-align: right;
            }
            .page-number {
              text-align: right;
              margin-top: 20px;
              font-size: 10px;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="container">
            
            <div class="header">
              <div class="logo-container">
                ${
                  this.printConfig.clinicLogo
                    ? `<img class="logo" src="${logoPath}" alt="Logo CRM Odonto">`
                    : ''
                }
              </div>
            </div>
            
            <div class="title">Orçamento</div>
            
            <div class="date-section">
              <strong>Data:</strong> ${currentDate}
            </div>
            
            <div class="patient-info">
              <strong>Paciente:</strong> ${
                this.budget.patient?.name || 'Não informado'
              }
              ${
                this.budget.patient?.email
                  ? `<span style="margin-left: 20px;"><strong>Email:</strong> ${this.budget.patient.email}</span>`
                  : ''
              }
            </div>
            
            ${
              this.printConfig.clinicInfo
                ? `
              <div class="clinic-info">
                <strong>Clínica Dra Monique Pimentel</strong><br>
                <div>Endereço: Rua Deputado Romeu Natal, 796</div>
                <div>Email: <EMAIL></div>
                <div>Telefone: (21) 2683-5343</div>
                <div>Telefone Adicional: (21) 2683-5343</div>
              </div>
            `
                : ''
            }
            
            <div class="section-title">Procedimentos</div>
            
            ${proceduresHtml}
            
            <div class="section-title">Forma de Pagamento</div>
            
            <div>${paymentMethod}</div>

            ${
              this.printConfig.installmentValue
                ? `
              <div style="margin-top: 10px;">Parcelado/À prazo</div>
              <div> 
                <div style="margin-top: 10px;">${installments} Parcela(s)</div>
                <div style="margin-top: 10px;">${installmentsHtml}</div>
              </div>
            `
                : ''
            }

            <div class="total-value">Valor Total: R$ ${this.formatPrice(
              this.budget.totalValue || 0
            )}</div>

            ${
              this.budget.discount
                ? ` <div class="total-value">Desconto: R$${
                    this.budget.discountType === 'fixed'
                      ? ` ${this.formatPrice(this.budget.discount)}`
                      : `${this.formatPrice(
                          this.budget.totalValue - this.budget.amountPaid
                        )}`
                  }
            </div>`
                : ``
            }

            <div class="total-value">Valor á pagar R$: ${this.formatPrice(
              this.budget.amountPaid || 0
            )}</div>
            
            ${
              this.printConfig.signature
                ? `
              <div style="margin-top: 50px; text-align: center;">
                <div style="border-top: 1px solid #000; width: 200px; display: inline-block;"></div>
                <div>Assinatura do Paciente</div>
              </div>
            `
                : ''
            }
            
            <div class="footer">
              https://sistema.clinicorp.com
            </div>
            
            <div class="page-number">1/2</div>
          </div>
        </body>
      </html>
    `;
  }

  // Métodos auxiliares
  getProcedureName(procedureId: number): string {
    const procedure = this.procedures.find((p) => p.id === procedureId);
    return procedure ? procedure.name : 'Procedimento não encontrado';
  }

  getDentistName(dentistId: number): string {
    const dentist = this.dentists.find((d) => d.id === dentistId);
    return dentist ? dentist.name : 'Dentista não encontrado';
  }

  getPaymentMethodLabel(method: string): string {
    const paymentMethods: { [key: string]: string } = {
      cash: 'Dinheiro',
      credit_card: 'Cartão de Crédito',
      debit_card: 'Cartão de Débito',
      bank_transfer: 'Transferência Bancária',
      pix: 'PIX',
      check: 'Cheque',
      to_define: 'A Definir',
    };
    return paymentMethods[method] || 'Não definido';
  }

  formatPrice(price: any): string {
    // Verifica se o valor é um número válido
    if (price === null || price === undefined || isNaN(Number(price))) {
      return '0,00';
    }

    // Converte para número e formata com 2 casas decimais
    return Number(price).toFixed(2).replace('.', ',');
  }
}
