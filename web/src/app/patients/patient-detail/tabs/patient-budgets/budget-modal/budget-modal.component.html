<app-modal
  [title]="budget?.id ? 'Editar Orçamento' : 'Novo Orçamento'"
  [isOpen]="isOpen"
  [fullscreen]="true"
  (close)="closeModal()"
  [showDefaultFooter]="true"
>
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Modal content -->
  <div *ngIf="!isLoading" class="p-4 max-h-[calc(90vh-120px)] overflow-y-auto">
    <form [formGroup]="budgetForm" (ngSubmit)="onSubmit()">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Coluna 1 - Informações do Orçamento -->
        <div class="space-y-6">

          <!-- Adicionar <PERSON> -->
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-md font-medium text-gray-800">Adicionar Procedimento</h4>

              <button
                *ngIf="!isFormDisabled() && !showProcedureForm"
                type="button"
                (click)="toggleProcedureForm()"
                class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center text-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                Adicionar
              </button>
            </div>

            <!-- Formulário de adição de procedimento -->
            <div *ngIf="!isFormDisabled() && showProcedureForm" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <!-- Etapa 1: Selecionar procedimento -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Procedimento *
                </label>
                <app-searchable-select
                  [options]="procedureOptions"
                  [placeholder]="'Selecione um procedimento'"
                  [searchPlaceholder]="'Buscar procedimento...'"
                  [value]="selectedProcedureId"
                  (valueChange)="onProcedureSelected($event)"
                ></app-searchable-select>
              </div>

              <!-- Etapa 2: Detalhes do procedimento (aparece após selecionar um procedimento) -->
              <div *ngIf="selectedProcedure" class="space-y-4 mt-4 pt-4 border-t border-gray-200">
                <h5 class="font-medium text-gray-700">Detalhes do Procedimento</h5>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Dente -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                      Dente(s)
                    </label>
                    <input
                      [(ngModel)]="procedureTeeth"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Ex: 11, 21, 36..."
                    />
                    <p class="text-xs text-gray-500">Separe múltiplos dentes por vírgula</p>
                  </div>

                  <!-- Dentista Executor -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                      Dentista Executor *
                    </label>
                    <app-searchable-select
                      [options]="dentistOptions"
                      [placeholder]="'Selecione um dentista'"
                      [searchPlaceholder]="'Buscar dentista...'"
                      [(value)]="selectedDentistId"
                    ></app-searchable-select>
                  </div>

                  <!-- Valor -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                      Valor (R$) *
                    </label>
                    <input
                      [(ngModel)]="procedureValue"
                      type="number"
                      min="0"
                      step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <!-- Botões de ação -->
                <div class="flex justify-end space-x-2 mt-4">
                  <button
                    type="button"
                    (click)="toggleProcedureForm()"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="button"
                    [disabled]="!selectedProcedureId || !selectedDentistId"
                    (click)="addProcedureToForm()"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Adicionar ao Orçamento
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Dentista responsável -->
          <div class="space-y-2">
            <label for="dentistId" class="block text-sm font-medium text-gray-700">
              Dentista Responsável *
            </label>
            <select
              id="dentistId"
              formControlName="dentistId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              [ngClass]="{
                'border-red-500': dentistIdControl?.invalid && dentistIdControl?.touched
              }"
            >
              <option [ngValue]="null">Selecione um dentista</option>
              <option *ngFor="let dentist of dentists" [ngValue]="dentist.id">
                {{ dentist.name }}
              </option>
            </select>
            <div
              *ngIf="dentistIdControl?.invalid && dentistIdControl?.touched"
              class="text-red-500 text-sm mt-1"
            >
              <span *ngIf="dentistIdControl?.errors?.['required']">
                Dentista é obrigatório
              </span>
            </div>
          </div>

          <!-- Observações -->
          <div class="space-y-2">
            <label for="notes" class="block text-sm font-medium text-gray-700">
              Observações
            </label>
            <textarea
              id="notes"
              formControlName="notes"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            ></textarea>
          </div>


        </div>

        <!-- Coluna 2 - Quadro do Orçamento -->
        <div class="space-y-6">

          <!-- Lista de Procedimentos -->
          <div class="bg-gray-100 rounded-lg p-4">

            <div formArrayName="items" class="space-y-3">
              <!-- Mensagem quando não há procedimentos -->
              <div *ngIf="itemsFormArray.length === 0" class="bg-white rounded-lg p-4 text-center text-gray-500 border border-gray-200">
                Nenhum procedimento adicionado ao orçamento.
              </div>

              <!-- Cards de procedimentos -->
              <div
                *ngFor="let item of itemsFormArray.controls; let i = index"
                [formGroupName]="i"
                class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 relative"
              >
                <!-- Botão de remover (posicionado no canto superior direito) -->
                <button
                  *ngIf="!isFormDisabled()"
                  type="button"
                  (click)="removeItem(i)"
                  class="absolute top-2 right-2 text-red-600 hover:text-red-800 bg-white rounded-full p-1 shadow-sm"
                  title="Remover procedimento"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>

                <div class="flex flex-col md:flex-row">
                  <!-- Informações do procedimento -->
                  <div class="flex-grow p-4">
                    <!-- Nome do procedimento -->
                    <h5 class="font-medium text-gray-800 mb-2">
                      {{ getProcedureName(getItemControl(i, 'procedureId')?.value) }}
                    </h5>

                    <!-- Dentista -->
                    <div class="mb-2 text-sm">
                      <span class="text-gray-500">Dentista:</span>
                      <span class="ml-1 text-gray-700">{{ getDentistName(getItemControl(i, 'executingDentistId')?.value) }}</span>
                    </div>

                    <!-- Dentes -->
                    <div class="text-sm" *ngIf="getItemControl(i, 'tooth')?.value">
                      <span class="text-gray-500">Dente(s):</span>
                      <span class="ml-1 text-gray-700">{{ getItemControl(i, 'tooth')?.value }}</span>
                    </div>

                    <!-- Campos ocultos para manter os valores no formulário -->
                    <input type="hidden" formControlName="procedureId" />
                    <input type="hidden" formControlName="executingDentistId" />
                    <input type="hidden" formControlName="tooth" />
                  </div>

                  <!-- Valor -->
                  <div class="bg-blue-50 p-4 border-t md:border-t-0 md:border-l border-gray-200 flex items-center justify-center">
                    <div class="text-center">
                      <div class="text-xs font-medium text-gray-500 mb-1">Valor</div>
                      <div class="text-lg font-semibold text-gray-800">
                        R$ {{ formatPrice(getItemControl(i, 'value')?.value) }}
                      </div>
                      <!-- Campo oculto para manter o valor no formulário -->
                      <input type="hidden" formControlName="value" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Totais e Pagamento -->
          <div class="space-y-4">
            <!-- Desconto -->
            <div class="flex justify-between items-center">
              <label for="discount" class="block text-sm font-medium text-gray-700">
                Desconto (R$):
              </label>
              <input
                id="discount"
                type="number"
                formControlName="discount"
                min="0"
                step="0.01"
                class="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [ngClass]="{
                  'border-red-500': discountControl?.invalid && discountControl?.touched
                }"
                (change)="calculateTotal()"
              />
            </div>

            <!-- Forma de Pagamento -->
            <div class="flex justify-between items-center">
              <label for="paymentMethod" class="block text-sm font-medium text-gray-700">
                Forma de Pagamento:
              </label>
              <select
                id="paymentMethod"
                formControlName="paymentMethod"
                class="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option *ngFor="let method of paymentMethods" [value]="method.value">
                  {{ method.label }}
                </option>
              </select>
            </div>

            <!-- Total -->
            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
              <span class="text-lg font-bold text-gray-900">Total:</span>
              <span class="text-lg font-bold text-gray-900">
                R$ {{ formatPrice(totalValueControl?.value) }}
              </span>
            </div>
          </div>

          <!-- Botões de Ação -->
          <div *ngIf="!isFormDisabled()" class="space-y-3">
            <button
              *ngIf="budget?.status === 'open'"
              type="button"
              (click)="approveBudget()"
              class="w-full px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium"
            >
              Aprovar Orçamento
            </button>
            <button
              *ngIf="budget?.status === 'open'"
              type="button"
              (click)="cancelBudget()"
              class="w-full px-4 py-2 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition-colors"
            >
              Cancelar Orçamento
            </button>
          </div>

          <!-- Status do Orçamento -->
          <div *ngIf="budget?.status === 'approved'" class="bg-green-100 text-green-800 p-4 rounded-lg">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="font-medium">Este orçamento foi aprovado e não pode ser editado.</span>
            </div>
          </div>

          <div *ngIf="budget?.status === 'cancelled'" class="bg-red-100 text-red-800 p-4 rounded-lg">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span class="font-medium">Este orçamento foi cancelado e não pode ser editado.</span>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer -->
  <div footer class="flex justify-end space-x-4">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
    >
      Cancelar
    </button>
    <button
      *ngIf="!isFormDisabled()"
      type="button"
      (click)="onSubmit()"
      [disabled]="isSubmitting"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <div
        *ngIf="isSubmitting"
        class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"
      ></div>
      {{ budget?.id ? 'Atualizar' : 'Salvar' }}
    </button>
  </div>
</app-modal>
