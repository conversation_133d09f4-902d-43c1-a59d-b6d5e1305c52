import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { Budget, BudgetStatus, PaymentMethod, PaymentMethodLabels } from '../../../../../core/models/budget.model';
import { BudgetService } from '../../../../../core/services/budget.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { ProcedureService } from '../../../../../core/services/procedure.service';
import { Dentist } from '../../../../../core/models/dentist.model';
import { Procedure } from '../../../../../core/models/procedure.model';
import { SearchableSelectComponent, SelectOption } from '../../../../../shared/components/searchable-select/searchable-select.component';

@Component({
  selector: 'app-budget-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, ModalComponent, SearchableSelectComponent],
  templateUrl: './budget-modal.component.html',
  styleUrl: './budget-modal.component.scss'
})
export class BudgetModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() budget: Budget | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<Budget>();

  budgetForm: FormGroup;
  isSubmitting = false;
  isLoading = true;
  dentists: Dentist[] = [];
  procedures: Procedure[] = [];
  procedureOptions: SelectOption[] = [];
  dentistOptions: SelectOption[] = [];
  paymentMethods = Object.entries(PaymentMethodLabels).map(([value, label]) => ({
    value,
    label
  }));

  // Controle de UI
  showProcedureForm: boolean = false;
  selectedProcedureId: number | null = null;
  selectedProcedure: Procedure | null = null;
  selectedDentistId: number | null = null;
  procedureTeeth: string = '';
  procedureValue: number = 0;

  constructor(
    private fb: FormBuilder,
    private budgetService: BudgetService,
    private dentistService: DentistService,
    private procedureService: ProcedureService
  ) {
    this.budgetForm = this.createBudgetForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.budget && this.isOpen) {
      this.populateForm();
    } else if (this.isOpen) {
      this.resetForm();
    }
  }

  createBudgetForm(): FormGroup {
    return this.fb.group({
      id: [null],
      patientId: [null, Validators.required],
      dentistId: [null, Validators.required],
      notes: [''],
      status: [BudgetStatus.OPEN],
      totalValue: [0, [Validators.required, Validators.min(0)]],
      discount: [0, [Validators.min(0)]],
      paymentMethod: [PaymentMethod.TO_DEFINE],
      items: this.fb.array([])
    });
  }

  get itemsFormArray(): FormArray {
    return this.budgetForm.get('items') as FormArray;
  }

  createItemForm(): FormGroup {
    return this.fb.group({
      id: [null],
      procedureId: [null, Validators.required],
      tooth: [''],
      executingDentistId: [null, Validators.required],
      value: [0, [Validators.required, Validators.min(0)]]
    });
  }

  addItem(procedureId?: string, tooth?: string, executingDentistId?: string, value?: string): void {
    const itemForm = this.createItemForm();

    if (procedureId && executingDentistId) {
      const procedure = this.procedures.find(p => p.id === parseInt(procedureId));
      let itemValue = 0;

      // Determinar o valor do item
      if (value && !isNaN(parseFloat(value))) {
        itemValue = parseFloat(value);
      } else if (procedure && procedure.defaultPrice !== undefined && procedure.defaultPrice !== null) {
        const price = Number(procedure.defaultPrice);
        itemValue = isNaN(price) ? 0 : price;
      }

      itemForm.patchValue({
        procedureId: Number(procedureId),
        tooth: tooth || '',
        executingDentistId: Number(executingDentistId),
        value: itemValue
      });
    }

    this.itemsFormArray.push(itemForm);
    this.calculateTotal();
  }

  removeItem(index: number): void {
    this.itemsFormArray.removeAt(index);
    this.calculateTotal();
  }

  loadInitialData(): void {
    this.isLoading = true;

    // Carregar dentistas
    this.dentistService.getActiveDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;

        // Criar opções para o combobox
        this.dentistOptions = dentists.map(dentist => ({
          value: dentist.id,
          label: dentist.name
        }));

        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.loadProcedures();
      }
    });
  }

  loadProcedures(): void {
    // Carregar procedimentos
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.procedures = procedures;

        // Criar opções para o combobox
        this.procedureOptions = procedures.map(procedure => ({
          value: procedure.id,
          label: `${procedure.name} - R$ ${this.formatPrice(procedure.defaultPrice)}`
        }));

        this.isLoading = false;

        if (this.budget) {
          this.populateForm();
        }
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoading = false;

        if (this.budget) {
          this.populateForm();
        }
      }
    });
  }

  populateForm(): void {
    if (!this.budget) return;

    // Limpar os itens existentes
    while (this.itemsFormArray.length) {
      this.itemsFormArray.removeAt(0);
    }

    // Preencher o formulário com os dados do orçamento
    this.budgetForm.patchValue({
      id: this.budget.id,
      patientId: this.budget.patientId,
      dentistId: this.budget.dentistId,
      notes: this.budget.notes,
      status: this.budget.status,
      totalValue: this.budget.totalValue,
      discount: this.budget.discount,
      paymentMethod: this.budget.paymentMethod
    });

    // Adicionar os itens
    if (this.budget.items && this.budget.items.length > 0) {
      this.budget.items.forEach(item => {
        const itemForm = this.createItemForm();
        itemForm.patchValue({
          id: item.id,
          procedureId: Number(item.procedureId),
          tooth: item.tooth,
          executingDentistId: Number(item.executingDentistId),
          value: Number(item.value)
        });
        this.itemsFormArray.push(itemForm);
      });
    }

    // Desabilitar o formulário se o orçamento estiver aprovado ou cancelado
    if (this.budget.status === BudgetStatus.APPROVED || this.budget.status === BudgetStatus.CANCELLED) {
      this.budgetForm.disable();
    } else {
      this.budgetForm.enable();
    }
  }

  resetForm(): void {
    this.budgetForm.reset({
      id: null,
      patientId: this.budget?.patientId,
      dentistId: null,
      notes: '',
      status: BudgetStatus.OPEN,
      totalValue: 0,
      discount: 0,
      paymentMethod: PaymentMethod.TO_DEFINE
    });

    // Limpar os itens existentes
    while (this.itemsFormArray.length) {
      this.itemsFormArray.removeAt(0);
    }
  }

  onProcedureSelect(index: number): void {
    const itemForm = this.itemsFormArray.at(index) as FormGroup;
    const procedureId = Number(itemForm.get('procedureId')?.value);

    if (procedureId) {
      const procedure = this.procedures.find(p => p.id === procedureId);
      if (procedure && procedure.defaultPrice !== undefined && procedure.defaultPrice !== null) {
        // Garantir que o valor seja um número válido
        const price = Number(procedure.defaultPrice);
        itemForm.patchValue({
          value: isNaN(price) ? 0 : price
        });
        this.calculateTotal();
      }
    }
  }

  calculateTotal(): void {
    let total = 0;

    // Somar os valores de todos os itens
    for (let i = 0; i < this.itemsFormArray.length; i++) {
      const itemForm = this.itemsFormArray.at(i) as FormGroup;
      const value = itemForm.get('value')?.value;

      // Garantir que o valor seja um número válido
      if (value !== undefined && value !== null && !isNaN(Number(value))) {
        total += Number(value);
      }
    }

    // Aplicar o desconto
    const discountValue = this.budgetForm.get('discount')?.value;
    let discount = 0;

    // Garantir que o desconto seja um número válido
    if (discountValue !== undefined && discountValue !== null && !isNaN(Number(discountValue))) {
      discount = Number(discountValue);
    }

    const finalTotal = Math.max(0, total - discount);

    // Atualizar o valor total
    this.budgetForm.patchValue({
      totalValue: finalTotal
    });
  }

  approveBudget(): void {
    this.budgetForm.patchValue({
      status: BudgetStatus.APPROVED
    });
    this.onSubmit();
  }

  cancelBudget(): void {
    this.budgetForm.patchValue({
      status: BudgetStatus.CANCELLED
    });
    this.onSubmit();
  }

  onSubmit(): void {
    if (this.budgetForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      this.markFormGroupTouched(this.budgetForm);
      return;
    }

    this.isSubmitting = true;
    const budgetData = this.budgetForm.value;

    // Garantir que dentistId seja um número
    if (budgetData.dentistId !== null && budgetData.dentistId !== undefined) {
      budgetData.dentistId = Number(budgetData.dentistId);
    }

    if (budgetData.id) {
      // Atualizar orçamento existente
      this.budgetService.updateBudget(budgetData.id, budgetData).subscribe({
        next: (budget) => {
          this.isSubmitting = false;
          this.saved.emit(budget);
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao atualizar orçamento:', error);
          this.isSubmitting = false;
        }
      });
    } else {
      // Criar novo orçamento
      this.budgetService.createBudget(budgetData).subscribe({
        next: (budget) => {
          this.isSubmitting = false;
          this.saved.emit(budget);
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao criar orçamento:', error);
          this.isSubmitting = false;
        }
      });
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        for (let i = 0; i < control.length; i++) {
          const arrayControl = control.at(i);
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        }
      }
    });
  }

  closeModal(): void {
    this.close.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get dentistIdControl() {
    return this.budgetForm.get('dentistId');
  }

  get notesControl() {
    return this.budgetForm.get('notes');
  }

  get discountControl() {
    return this.budgetForm.get('discount');
  }

  get paymentMethodControl() {
    return this.budgetForm.get('paymentMethod');
  }

  get totalValueControl() {
    return this.budgetForm.get('totalValue');
  }

  getItemControl(index: number, controlName: string) {
    return (this.itemsFormArray.at(index) as FormGroup).get(controlName);
  }

  isFormDisabled(): boolean {
    return this.budgetForm.disabled;
  }

  // Métodos para o novo fluxo de adição de procedimentos
  toggleProcedureForm(): void {
    this.showProcedureForm = !this.showProcedureForm;
    if (!this.showProcedureForm) {
      this.resetProcedureForm();
    }
  }

  resetProcedureForm(): void {
    this.selectedProcedureId = null;
    this.selectedProcedure = null;
    this.selectedDentistId = null;
    this.procedureTeeth = '';
    this.procedureValue = 0;
  }

  onProcedureSelected(procedureId: number): void {
    this.selectedProcedureId = procedureId;
    this.selectedProcedure = this.procedures.find(p => p.id === procedureId) || null;

    if (this.selectedProcedure) {
      // Definir o valor padrão do procedimento
      const price = Number(this.selectedProcedure.defaultPrice);
      this.procedureValue = isNaN(price) ? 0 : price;
    }
  }

  addProcedureToForm(): void {
    if (!this.selectedProcedureId || !this.selectedDentistId) {
      return; // Validação básica
    }

    const itemForm = this.createItemForm();
    itemForm.patchValue({
      procedureId: Number(this.selectedProcedureId),
      tooth: this.procedureTeeth,
      executingDentistId: Number(this.selectedDentistId),
      value: Number(this.procedureValue)
    });

    this.itemsFormArray.push(itemForm);
    this.calculateTotal();

    // Resetar o formulário de procedimento
    this.resetProcedureForm();
    this.showProcedureForm = false;
  }

  updateProcedureValue(procedureId: string, valueInput: HTMLInputElement): void {
    if (procedureId) {
      const procedure = this.procedures.find(p => p.id === parseInt(procedureId));
      if (procedure && procedure.defaultPrice !== undefined && procedure.defaultPrice !== null) {
        // Garantir que o valor seja um número válido
        const price = Number(procedure.defaultPrice);
        valueInput.value = isNaN(price) ? '0' : price.toString();
      } else {
        valueInput.value = '0';
      }
    } else {
      valueInput.value = '';
    }
  }

  formatPrice(price: any): string {
    // Verifica se o valor é um número válido
    if (price === null || price === undefined || isNaN(Number(price))) {
      return '0.00';
    }

    // Converte para número e formata com 2 casas decimais
    return Number(price).toFixed(2);
  }

  getProcedureName(procedureId: number): string {
    if (!procedureId) return 'Procedimento não especificado';

    const procedure = this.procedures.find(p => p.id === procedureId);
    return procedure ? procedure.name : 'Procedimento não encontrado';
  }

  getDentistName(dentistId: number): string {
    if (!dentistId) return 'Dentista não especificado';

    const dentist = this.dentists.find(d => d.id === dentistId);
    return dentist ? dentist.name : 'Dentista não encontrado';
  }
}
