<div class="bg-gray-50 p-4 rounded-lg mb-6">
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-lg font-medium text-gray-900">Orçamentos</h2>
    <button
      *ngIf="patient"
      (click)="openNewBudget()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center text-sm"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Novo Orçamento
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-4">
    <div
      class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Budgets list with accordion -->
  <div *ngIf="!isLoading" class="space-y-4">
    <div *ngIf="budgets.length === 0" class="text-center text-gray-500 py-4">
      Nenhum orçamento registrado para este paciente.
    </div>

    <!-- Accordion items -->
    <div
      *ngFor="let budget of budgets"
      class="bg-white border border-gray-200 rounded-lg overflow-hidden"
    >
      <!-- Accordion header -->
      <div
        (click)="toggleBudgetDetails(budget.id!)"
        class="flex justify-between items-center p-4 cursor-pointer hover:bg-blue-50 transition-colors"
      >
        <div class="flex items-center space-x-4">
          <span
            class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
            [ngClass]="getStatusClass(budget.status)"
          >
            {{ getStatusLabel(budget.status) }}
          </span>
          <span class="text-sm font-medium text-gray-900">
            {{ budget.createdAt | date : "dd/MM/yyyy" }}
          </span>
          <span class="text-sm text-gray-600">
            R$ {{ formatValue(budget.amountPaid) }}
          </span>
        </div>
        <div class="flex items-center">
          <button
            (click)="openBudgetModal(budget); $event.stopPropagation()"
            class="text-blue-600 hover:text-blue-800 mr-4 text-sm font-medium"
          >
            Ver/Editar orçamento
          </button>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-400 transition-transform duration-200"
            [ngClass]="{ 'transform rotate-180': isBudgetExpanded(budget.id!) }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>

      <!-- Accordion content -->
      <div
        *ngIf="isBudgetExpanded(budget.id!)"
        class="p-4 bg-gray-50 border-t border-gray-200"
      >
        <div class="space-y-4">
          <!-- Informações gerais -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Coluna 1 -->
            <div class="space-y-4">
              <!-- Dentista responsável -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Dentista responsável:
                </h4>
                <p class="text-sm text-gray-600">
                  {{ budget.dentist?.name || "Não informado" }}
                </p>
              </div>

              <!-- Data de criação -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Data de criação:
                </h4>
                <p class="text-sm text-gray-600">
                  {{ budget.createdAt | date : "dd/MM/yyyy HH:mm" }}
                </p>
              </div>
            </div>

            <!-- Coluna 2 -->
            <div class="space-y-4">
              <!-- Forma de pagamento -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Forma de pagamento:
                </h4>
                <p class="text-sm text-gray-600">
                  {{ getPaymentMethodLabel(budget.paymentMethod) }}
                </p>
              </div>

              <!-- Data de atualização -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-1">
                  Última atualização:
                </h4>
                <p class="text-sm text-gray-600">
                  {{ budget.updatedAt | date : "dd/MM/yyyy HH:mm" }}
                </p>
              </div>
            </div>
          </div>

          <!-- Observações -->
          <div *ngIf="budget.notes">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Observações:</h4>
            <p class="text-sm text-gray-600 whitespace-pre-line">
              {{ budget.notes }}
            </p>
          </div>

          <!-- Procedimentos -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">
              Procedimentos:
            </h4>
            <div
              class="bg-white rounded border border-gray-200 overflow-hidden"
            >
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      Procedimento
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      Dente
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      Dentista
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      Valor
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr *ngIf="budget.items.length === 0">
                    <td
                      colspan="4"
                      class="px-4 py-2 text-sm text-center text-gray-500"
                    >
                      Nenhum procedimento adicionado.
                    </td>
                  </tr>
                  <tr
                    *ngFor="let item of budget.items"
                    class="hover:bg-gray-50"
                  >
                    <td class="px-4 py-2 text-sm text-gray-900">
                      {{ item.procedure?.name || "Não informado" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-500">
                      {{ item.tooth || "-" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-500">
                      {{ item.executingDentist?.name || "Não informado" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-500">
                      R$ {{ formatValue(item.value) }}
                    </td>
                  </tr>
                </tbody>
                <tfoot class="bg-gray-50">
                  <tr>
                    <td
                      colspan="3"
                      class="px-4 py-2 text-sm font-medium text-right text-gray-700"
                    >
                      Subtotal:
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900">
                      R$ {{ formatValue(budget.totalValue) }}
                    </td>
                  </tr>
                  <tr *ngIf="budget.discount > 0">
                    <td
                      colspan="3"
                      class="px-4 py-2 text-sm font-medium text-right text-gray-700"
                    >
                      Desconto:
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900">
                      R$
                      {{ formatValue(budget.totalValue - budget.amountPaid) }}
                    </td>
                  </tr>
                  <tr>
                    <td
                      colspan="3"
                      class="px-4 py-2 text-sm font-medium text-right text-gray-700"
                    >
                      Total:
                    </td>
                    <td class="px-4 py-2 text-sm font-bold text-gray-900">
                      R$ {{ formatValue(budget.amountPaid) }}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de Orçamento -->
<app-budget-fullscreen-modal
  [isOpen]="isModalOpen"
  [budget]="selectedBudget"
  (close)="closeModal()"
  (saved)="onBudgetSaved($event)"
></app-budget-fullscreen-modal>
