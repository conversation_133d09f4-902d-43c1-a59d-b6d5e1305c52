import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Patient } from '../../../../core/models/patient.model';
import { Budget, BudgetStatus, BudgetStatusLabels, PaymentMethod, PaymentMethodLabels } from '../../../../core/models/budget.model';
import { BudgetService } from '../../../../core/services/budget.service';
import { BudgetFullscreenModalComponent } from './budget-fullscreen-modal/budget-fullscreen-modal.component';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-patient-budgets',
  standalone: true,
  imports: [CommonModule, DatePipe, ReactiveFormsModule, BudgetFullscreenModalComponent],
  templateUrl: './patient-budgets.component.html',
  styleUrl: './patient-budgets.component.scss'
})
export class PatientBudgetsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  budgets: Budget[] = [];
  isLoading = false;
  expandedBudgetId: number | null = null;
  BudgetStatus = BudgetStatus;
  PaymentMethod = PaymentMethod;

  // Modal properties
  isModalOpen = false;
  selectedBudget: Budget | null = null;

  constructor(private budgetService: BudgetService) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadBudgets();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadBudgets();
    }
  }

  loadBudgets(): void {
    if (!this.patient) return;

    this.isLoading = true;

    this.budgetService.getBudgetsByPatient(this.patient.id).subscribe({
      next: (budgets) => {
        this.budgets = budgets;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar orçamentos:', error);
        this.isLoading = false;
      }
    });
  }

  getStatusLabel(status: string): string {
    return BudgetStatusLabels[status as BudgetStatus] || status;
  }

  getPaymentMethodLabel(method: string): string {
    return PaymentMethodLabels[method as PaymentMethod] || method;
  }

  getStatusClass(status: string): string {
    const statusClassMap: { [key: string]: string } = {
      'open': 'bg-yellow-100 text-yellow-800',
      'approved': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return statusClassMap[status] || 'bg-gray-100 text-gray-800';
  }

  toggleBudgetDetails(budgetId: number): void {
    if (this.expandedBudgetId === budgetId) {
      this.expandedBudgetId = null;
    } else {
      this.expandedBudgetId = budgetId;
    }
  }

  isBudgetExpanded(budgetId: number): boolean {
    return this.expandedBudgetId === budgetId;
  }

  openNewBudget(): void {
    if (!this.patient) return;

    // Criar um novo orçamento com valores padrão
    const newBudget: Partial<Budget> = {
      patientId: this.patient.id,
      dentistId: 1, // Valor padrão, será alterado pelo usuário no modal
      status: BudgetStatus.OPEN,
      totalValue: 0,
      discount: 0,
      paymentMethod: PaymentMethod.TO_DEFINE,
      items: []
    };

    this.openBudgetModal(newBudget as Budget);
  }

  openBudgetModal(budget: Budget): void {
    this.selectedBudget = budget;
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedBudget = null;
  }

  /**
   * Formata um valor numérico para exibição com 2 casas decimais
   * @param value O valor a ser formatado
   * @returns O valor formatado com 2 casas decimais
   */
  formatValue(value: any): string {
    // Verificar se o valor é um número válido
    if (value === null || value === undefined) {
      return '0,00';
    }

    // Converter para número se for string
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // Verificar se é um número válido após a conversão
    if (isNaN(numValue)) {
      return '0,00';
    }

    // Formatar com 2 casas decimais
    return numValue.toFixed(2).replace('.', ',');
  }

  onBudgetSaved(budget: Budget): void {
    this.loadBudgets();
  }
}
