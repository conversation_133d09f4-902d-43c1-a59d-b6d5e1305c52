<div class="bg-gray-50 p-4 rounded-lg">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
    <span class="ml-3 text-gray-600">Carregando...</span>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
    <p>{{ error }}</p>
    <button (click)="loadProcedures()" class="mt-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
      Tentar novamente
    </button>
  </div>

  <!-- Main content -->
  <div *ngIf="!isLoading && !error" class="grid grid-cols-1 md:grid-cols-10 gap-6">
    <!-- Coluna 1: Odontograma (70%) -->
    <div class="bg-white p-4 rounded-lg shadow md:col-span-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Odontograma</h2>
      <p class="text-sm text-gray-500 mb-4">
        Clique em um dente para adicionar um procedimento
      </p>

      <app-tooth-diagram
        [procedures]="allProcedures"
        (toothSelected)="onToothSelected($event)">
      </app-tooth-diagram>
    </div>

    <!-- Coluna 2: Lista de Procedimentos (30%) -->
    <div class="bg-white p-4 rounded-lg shadow md:col-span-4">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Procedimentos Realizados</h2>

      <!-- Tabela de procedimentos -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Data
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Procedimento
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dente
              </th>

            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngIf="filteredProcedures.length === 0">
              <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                Nenhum procedimento encontrado.
              </td>
            </tr>
            <tr *ngFor="let procedure of filteredProcedures" class="hover:bg-gray-50">
              <td class="px-2 py-4 whitespace-nowrap text-xs text-gray-500">
                {{ formatDate(procedure.executionDate) }}
              </td>
              <td class="px-2 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <div *ngIf="procedure.notes" class="text-xs text-gray-500 relative group">
                    <span class="cursor-help flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </span>
                    <div class="absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-300 left-0 mt-1">
                      {{ procedure.notes }}
                    </div>
                  </div>

                  <div class="text-xs font-medium text-gray-900">{{ procedure.name }}</div>

                </div>

              </td>
              <td class="px-6 py-4 whitespace-nowrap text-xs text-gray-500">
                {{ procedure.tooth || 'N/A' }}
              </td>

            </tr>
          </tbody>
        </table>
      </div>

      <!-- Paginação -->
      <div class="flex items-center mt-6 justify-between">
        <!-- Informações sobre a paginação -->
        <div class="">
          <div class="text-sm text-gray-600">
            Mostrando {{ filteredProcedures.length }} de {{ totalItems }}
          </div>
        </div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Paginação">
          <!-- Botão Anterior -->
          <button
            (click)="onPageChange(currentPage - 1)"
            [disabled]="currentPage === 1"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            [class.opacity-50]="currentPage === 1"
            [class.cursor-not-allowed]="currentPage === 1"
          >
            <span class="sr-only">Anterior</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Números de página -->
          <ng-container *ngFor="let page of getPageNumbers(); let i = index">
            <!-- Elipses -->
            <span *ngIf="page < 0" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
              ...
            </span>

            <!-- Botão de página normal -->
            <button
              *ngIf="page > 0"
              (click)="onPageChange(page)"
              [class.bg-blue-100]="currentPage === page"
              [class.text-blue-800]="currentPage === page"
              [class.border-blue-500]="currentPage === page"
              [class.font-bold]="currentPage === page"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              {{ page }}
            </button>
          </ng-container>

          <!-- Botão Próximo -->
          <button
            (click)="onPageChange(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            [class.opacity-50]="currentPage === totalPages"
            [class.cursor-not-allowed]="currentPage === totalPages"
          >
            <span class="sr-only">Próximo</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- Modal de criação de procedimento -->
<app-create-procedure-modal
  [isOpen]="isModalOpen"
  [patientId]="patient?.id || null"
  [toothNumber]="selectedToothNumber"
  (close)="onModalClose()"
  (saved)="onProcedureSaved($event)"
></app-create-procedure-modal>
