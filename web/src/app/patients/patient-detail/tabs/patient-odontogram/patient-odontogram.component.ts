import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Patient } from '../../../../core/models/patient.model';
import { TreatmentProcedure, TreatmentProcedureStatus, TreatmentProcedureStatusLabels } from '../../../../core/models/treatment-procedure.model';
import { TreatmentPlanService } from '../../../../core/services/treatment-plan.service';
import { DentistService } from '../../../../core/services/dentist.service';
import { Dentist } from '../../../../core/models/dentist.model';
import { ToothDiagramComponent } from '../../../../shared/components/tooth-diagram/tooth-diagram.component';
import { CreateProcedureModalComponent } from './create-procedure-modal/create-procedure-modal.component';
import { Subscription } from 'rxjs';
import { TreatmentPlanUpdateService } from '../../../../core/services/treatment-plan-update.service';

@Component({
  selector: 'app-patient-odontogram',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule, // Necessário para o ngModel no select de tamanho da página
    ToothDiagramComponent,
    CreateProcedureModalComponent
  ],
  templateUrl: './patient-odontogram.component.html',
  styleUrls: ['./patient-odontogram.component.scss']
})
export class PatientOdontogramComponent implements OnInit, OnChanges, OnDestroy {
  @Input() patient: Patient | null = null;

  // Procedimentos
  allProcedures: TreatmentProcedure[] = [];
  filteredProcedures: TreatmentProcedure[] = [];
  isLoading = false;
  error: string | null = null;
  dentists: Dentist[] = [];
  dentistMap: Map<number, string> = new Map();

  // Modal
  isModalOpen = false;
  selectedToothNumber: string | null = null;

  // Paginação
  currentPage = 1;
  pageSize = 10; // Padrão de 10 itens por página
  totalItems = 0;
  totalPages = 1;

  // Subscription para gerenciar a inscrição no serviço de atualização
  private treatmentPlanUpdateSubscription: Subscription | null = null;

  constructor(
    private treatmentPlanService: TreatmentPlanService,
    private dentistService: DentistService,
    private treatmentPlanUpdateService: TreatmentPlanUpdateService
  ) {}

  ngOnInit(): void {
    this.loadDentists();
    if (this.patient) {
      this.loadProcedures();
    }

    // Inscrever-se para receber atualizações do plano de tratamento
    this.treatmentPlanUpdateSubscription = this.treatmentPlanUpdateService.treatmentPlanUpdated$.subscribe(
      updatedPlan => {
        console.log('Plano de tratamento atualizado recebido no componente de odontograma:', updatedPlan);
        if (updatedPlan && this.patient) {
          this.loadProcedures();
        }
      }
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && this.patient) {
      this.loadProcedures();
    }
  }

  ngOnDestroy(): void {
    if (this.treatmentPlanUpdateSubscription) {
      this.treatmentPlanUpdateSubscription.unsubscribe();
    }
  }

  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
        // Criar um mapa para facilitar a busca por nome do dentista
        this.dentistMap.clear();
        dentists.forEach(dentist => {
          this.dentistMap.set(dentist.id, dentist.name);
        });
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
      }
    });
  }

  loadProcedures(): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.error = null;

    this.treatmentPlanService.getAllPatientProcedures(this.patient.id).subscribe({
      next: (procedures: TreatmentProcedure[]) => {
        this.allProcedures = procedures;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.error = 'Não foi possível carregar os procedimentos. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      }
    });
  }

  applyFilters(): void {
    // Ordenar por data de execução (mais recentes primeiro)
    const sorted = [...this.allProcedures].sort((a, b) => {
      const dateA = a.executionDate ? new Date(a.executionDate).getTime() : 0;
      const dateB = b.executionDate ? new Date(b.executionDate).getTime() : 0;
      return dateB - dateA;
    });

    this.totalItems = sorted.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);

    // Aplicar paginação
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.filteredProcedures = sorted.slice(startIndex, startIndex + this.pageSize);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.applyFilters();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    // Recalcular o número total de páginas
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    // Ajustar a página atual se necessário
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages || 1;
    }
    this.applyFilters();
  }

  onToothSelected(toothNumber: string): void {
    this.selectedToothNumber = toothNumber;
    this.isModalOpen = true;
  }

  onModalClose(): void {
    this.isModalOpen = false;
    this.selectedToothNumber = null;
  }

  onProcedureSaved(procedure: TreatmentProcedure): void {
    this.loadProcedures();
  }

  getDentistName(professionalId: number): string {
    return this.dentistMap.get(professionalId) || `ID: ${professionalId}`;
  }

  getStatusLabel(status: TreatmentProcedureStatus): string {
    return TreatmentProcedureStatusLabels[status] || status;
  }

  getStatusClass(status: TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentProcedureStatus.PENDING:
        return 'bg-blue-100 text-blue-800';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'bg-orange-100 text-orange-800';
      case TreatmentProcedureStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TreatmentProcedureStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('pt-BR');
  }

  formatValue(value: number): string {
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  /**
   * Gera um array com os números das páginas a serem exibidos na paginação
   * Se houver muitas páginas, mostra apenas algumas ao redor da página atual
   */
  getPageNumbers(): number[] {
    if (this.totalPages <= 1) {
      return [1];
    }

    if (this.totalPages <= 7) {
      // Se houver 7 ou menos páginas, mostra todas
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    // Se houver mais de 7 páginas, mostra um subconjunto
    let pages: number[] = [];

    // Sempre inclui a primeira página
    pages.push(1);

    // Determina o intervalo ao redor da página atual
    let start = Math.max(2, this.currentPage - 1);
    let end = Math.min(this.totalPages - 1, this.currentPage + 1);

    // Adiciona elipses se necessário
    if (start > 2) {
      pages.push(-1); // -1 representa elipses "..."
    }

    // Adiciona páginas do intervalo
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Adiciona elipses se necessário
    if (end < this.totalPages - 1) {
      pages.push(-2); // -2 representa elipses "..."
    }

    // Sempre inclui a última página
    pages.push(this.totalPages);

    return pages;
  }
}
