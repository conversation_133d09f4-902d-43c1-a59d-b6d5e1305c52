<app-modal
  [isOpen]="isOpen"
  [title]="'Adicionar Procedimento para Dente ' + toothNumber"
  (close)="closeModal()">

      <!-- Body -->
      <div class="p-6 overflow-y-auto">
        <form [formGroup]="procedureForm">
          <!-- Error message -->
          <div *ngIf="error" class="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
            <p>{{ error }}</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Procedimento -->
            <div class="col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Procedimento <span class="text-red-500">*</span>
              </label>
              <app-procedure-search
                [procedures]="procedures"
                [selectedProcedure]="selectedProcedure"
                (procedureSelected)="onProcedureSelected($event)"
              ></app-procedure-search>
              <div
                *ngIf="!selectedProcedure && error && error.includes('procedimento')"
                class="text-red-500 text-xs mt-1"
              >
                Selecione um procedimento
              </div>
            </div>

            <!-- Valor -->
            <div>
              <label for="value" class="block text-sm font-medium text-gray-700 mb-1">
                Valor <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">R$</span>
                <input
                  type="text"
                  id="value"
                  [value]="formattedValue"
                  (input)="onValueChange($event)"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="procedureForm.get('value')?.invalid && procedureForm.get('value')?.touched"
                />
              </div>
              <div
                *ngIf="procedureForm.get('value')?.invalid && procedureForm.get('value')?.touched"
                class="text-red-500 text-xs mt-1"
              >
                Valor é obrigatório e deve ser maior que zero
              </div>
            </div>

            <!-- Dentista -->
            <div>
              <label for="professionalId" class="block text-sm font-medium text-gray-700 mb-1">
                Profissional <span class="text-red-500">*</span>
              </label>
              <select
                id="professionalId"
                formControlName="professionalId"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                [class.border-red-500]="procedureForm.get('professionalId')?.invalid && procedureForm.get('professionalId')?.touched"
              >
                <option [ngValue]="''">Selecione um profissional</option>
                <option *ngFor="let dentist of dentists" [value]="dentist.id">
                  {{ dentist.name }}
                </option>
              </select>
              <div
                *ngIf="procedureForm.get('professionalId')?.invalid && procedureForm.get('professionalId')?.touched"
                class="text-red-500 text-xs mt-1"
              >
                Profissional é obrigatório
              </div>
            </div>



            <!-- Observações -->
            <div class="col-span-2">
              <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
                Observações
              </label>
              <textarea
                id="notes"
                formControlName="notes"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Observações sobre o procedimento"
              ></textarea>
            </div>

            <!-- Detalhes da Próxima Visita -->
            <div class="col-span-2">
              <label for="nextVisitDetails" class="block text-sm font-medium text-gray-700 mb-1">
                Detalhes da Próxima Visita
              </label>
              <textarea
                id="nextVisitDetails"
                formControlName="nextVisitDetails"
                rows="2"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Detalhes para a próxima visita"
              ></textarea>
            </div>
          </div>
        </form>
      </div>

      <!-- Footer -->
      <div footer class="flex justify-end space-x-3">
        <button
          type="button"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          (click)="closeModal()"
        >
          Cancelar
        </button>
        <button
          type="button"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          [disabled]="isSubmitting || procedureForm.invalid || !selectedProcedure"
          (click)="onSubmit()"
        >
          <span *ngIf="isSubmitting" class="inline-block animate-spin mr-2">⟳</span>
          Salvar
        </button>
      </div>
</app-modal>
