import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ProcedureService } from '../../../../../core/services/procedure.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { TreatmentPlanService } from '../../../../../core/services/treatment-plan.service';
import { Procedure } from '../../../../../core/models/procedure.model';
import { Dentist } from '../../../../../core/models/dentist.model';
import { TreatmentProcedure } from '../../../../../core/models/treatment-procedure.model';
import { NotificationService } from '../../../../../core/services/notification.service';
import { firstValueFrom } from 'rxjs';
import { ProcedureSearchComponent } from '../procedure-search/procedure-search.component';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

@Component({
  selector: 'app-create-procedure-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ProcedureSearchComponent, ModalComponent],
  templateUrl: './create-procedure-modal.component.html',
  styleUrls: ['./create-procedure-modal.component.scss']
})
export class CreateProcedureModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  @Input() toothNumber: string | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<TreatmentProcedure>();

  procedureForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  dentists: Dentist[] = [];
  procedures: Procedure[] = [];
  selectedProcedure: Procedure | null = null;

  // Valor formatado para exibição
  formattedValue: string = '0,00';

  constructor(
    private fb: FormBuilder,
    private treatmentPlanService: TreatmentPlanService,
    private procedureService: ProcedureService,
    private dentistService: DentistService,
    private notificationService: NotificationService
  ) {
    this.procedureForm = this.fb.group({
      value: [0, [Validators.required, Validators.min(0)]],
      professionalId: ['', [Validators.required]],
      notes: [''],
      nextVisitDetails: ['']
    });
  }

  ngOnInit(): void {
    this.loadDentists();
    this.loadProcedures();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && this.isOpen) {
      this.resetForm();
    }
  }

  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.error = 'Não foi possível carregar a lista de dentistas.';
      }
    });
  }

  loadProcedures(): void {
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.procedures = procedures;
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.error = 'Não foi possível carregar a lista de procedimentos.';
      }
    });
  }

  onProcedureSelected(procedure: any): void {
    if (!procedure) return;

    this.selectedProcedure = procedure as Procedure;
    this.procedureForm.patchValue({
      value: this.selectedProcedure.defaultPrice
    });
    this.formattedValue = this.formatValue(this.selectedProcedure.defaultPrice);
  }

  formatValue(value: number): string {
    return value.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  onValueChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, '');

    // Converter para número com 2 casas decimais
    const numericValue = parseInt(value) / 100;

    // Atualizar o valor no formulário
    this.procedureForm.patchValue({
      value: numericValue
    });

    // Formatar para exibição
    this.formattedValue = this.formatValue(numericValue);
  }

  async onSubmit(): Promise<void> {
    if (this.procedureForm.invalid || !this.patientId || !this.selectedProcedure) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.procedureForm.controls).forEach(key => {
        const control = this.procedureForm.get(key);
        control?.markAsTouched();
      });

      if (!this.patientId) {
        this.error = 'ID do paciente não fornecido';
      }

      if (!this.selectedProcedure) {
        this.error = 'Selecione um procedimento';
      }

      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      const formData = this.procedureForm.value;

      const procedureData = {
        patientId: this.patientId,
        procedureId: this.selectedProcedure.id,
        name: this.selectedProcedure.name,
        value: +formData.value,
        tooth: this.toothNumber || undefined,
        professionalId: +formData.professionalId,
        notes: formData.notes || undefined,
        nextVisitDetails: formData.nextVisitDetails || undefined
      };

      const savedProcedure = await firstValueFrom(
        this.treatmentPlanService.createProcedureWithAutoPlan(procedureData)
      );

      this.notificationService.success(`Procedimento criado com sucesso para o dente ${this.toothNumber}.`);
      this.saved.emit(savedProcedure);
      this.closeModal();
    } catch (err: any) {
      console.error('Erro ao salvar procedimento:', err);

      if (err.error?.message && Array.isArray(err.error.message)) {
        this.error = err.error.message.join(', ');
      } else if (err.error?.message) {
        this.error = err.error.message;
      } else {
        this.error = 'Ocorreu um erro ao salvar o procedimento. Por favor, tente novamente.';
      }
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.procedureForm.reset({
      value: 0,
      professionalId: '',
      notes: '',
      nextVisitDetails: ''
    });
    this.selectedProcedure = null;
    this.error = null;
    this.formattedValue = '0,00';
  }

  closeModal(): void {
    this.resetForm();
    this.close.emit();
  }
}
