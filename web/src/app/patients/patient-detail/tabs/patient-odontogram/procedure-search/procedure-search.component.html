<div class="relative w-full">
  <!-- <PERSON> de busca -->
  <div class="relative">
    <input
      type="text"
      [ngModel]="isFocused ? searchText : displayText"
      (ngModelChange)="searchText = $event; onSearch()"
      (focus)="onInputFocus()"
      (blur)="onInputBlur()"
      placeholder="Digite para buscar procedimento por nome ou tipo..."
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
      [class.bg-blue-50]="selectedProcedure && !isFocused"
    />

    <!-- Botão de limpar seleção -->
    <button
      *ngIf="selectedProcedure && !isFocused"
      type="button"
      class="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
      (click)="clearSelection()"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>

    <!-- Ícone de busca -->
    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
  </div>

  <!-- Resultados da busca -->
  <div
    *ngIf="showResults && filteredProcedures.length > 0"
    class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
  >
    <div class="py-1">
      <div
        *ngFor="let procedure of filteredProcedures"
        (mousedown)="selectProcedure(procedure)"
        class="px-3 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
        [class.bg-blue-50]="selectedProcedure?.id === procedure.id"
      >
        <!-- Nome do procedimento -->
        <div class="font-medium text-gray-800" [innerHTML]="highlightMatch(procedure.name)"></div>

        <!-- Informações adicionais -->
        <div class="flex justify-between items-center mt-1">
          <div class="text-xs text-gray-500">
            <span *ngIf="procedure.type">Tipo: <span [innerHTML]="highlightMatch(procedure.type)"></span></span>
            <span *ngIf="!procedure.type">Tipo: Não especificado</span>
          </div>
          <div class="text-sm font-medium text-blue-600">
            R$ {{ formatPrice(procedure.defaultPrice) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mensagem de nenhum resultado -->
  <div
    *ngIf="showResults && searchText.trim() && filteredProcedures.length === 0"
    class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg py-3 px-4 text-center text-gray-500"
  >
    Nenhum procedimento encontrado para "{{ searchText }}"
  </div>

  <!-- Mensagem de procedimento selecionado -->
  <div *ngIf="selectedProcedure && !isFocused" class="mt-1 text-xs text-blue-600">
    Procedimento selecionado: {{ selectedProcedure.name }} - R$ {{ formatPrice(selectedProcedure.defaultPrice) }}
  </div>
</div>
