import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Procedure } from '../../../../../core/models/procedure.model';

@Component({
  selector: 'app-procedure-search',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './procedure-search.component.html',
  styleUrls: ['./procedure-search.component.scss']
})
export class ProcedureSearchComponent implements OnInit, OnChanges {
  @Input() procedures: Procedure[] = [];
  @Input() selectedProcedure: Procedure | null = null;
  @Output() procedureSelected = new EventEmitter<Procedure>();

  searchText: string = '';
  displayText: string = '';
  filteredProcedures: Procedure[] = [];
  showResults: boolean = false;
  isFocused: boolean = false;

  ngOnInit(): void {
    this.filteredProcedures = [];
    this.updateDisplayFromSelected();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedProcedure']) {
      this.updateDisplayFromSelected();
    }
  }

  updateDisplayFromSelected(): void {
    if (this.selectedProcedure) {
      this.displayText = this.selectedProcedure.name;
    } else {
      this.displayText = '';
    }
  }

  onInputFocus(): void {
    this.isFocused = true;
    this.searchText = '';
    this.onSearch();
  }

  onInputBlur(): void {
    // Pequeno atraso para permitir que o clique no item seja processado antes de fechar
    setTimeout(() => {
      this.isFocused = false;
      this.showResults = false;
      this.updateDisplayFromSelected();
    }, 200);
  }

  onSearch(): void {
    if (!this.searchText.trim() && !this.isFocused) {
      this.filteredProcedures = [];
      this.showResults = false;
      return;
    }

    // Se estiver focado, mostrar todos os procedimentos quando o campo estiver vazio
    if (this.isFocused && !this.searchText.trim()) {
      this.filteredProcedures = this.procedures.slice(0, 10);
      this.showResults = true;
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredProcedures = this.procedures.filter(procedure =>
      procedure.name.toLowerCase().includes(searchLower) ||
      (procedure.type && procedure.type.toLowerCase().includes(searchLower))
    ).slice(0, 10); // Limitar a 10 resultados para melhor desempenho

    this.showResults = true;
  }

  selectProcedure(procedure: Procedure): void {
    this.selectedProcedure = procedure;
    this.displayText = procedure.name;
    this.procedureSelected.emit(procedure);
    this.filteredProcedures = [];
    this.showResults = false;
  }

  highlightMatch(text: string): string {
    if (!this.searchText.trim() || !text) {
      return text;
    }

    const searchRegex = new RegExp(`(${this.escapeRegExp(this.searchText)})`, 'gi');
    return text.replace(searchRegex, '<span class="bg-yellow-200">$1</span>');
  }

  escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  formatPrice(price: any): string {
    // Verifica se o valor é um número válido
    if (price === null || price === undefined || isNaN(Number(price))) {
      return '0,00';
    }

    // Converte para número e formata com 2 casas decimais
    return Number(price).toFixed(2).replace('.', ',');
  }

  clearSelection(): void {
    this.selectedProcedure = null;
    this.displayText = '';
    this.searchText = '';
    this.procedureSelected.emit(null as any);
  }
}
