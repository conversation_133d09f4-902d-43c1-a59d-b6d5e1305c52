import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Patient } from '../../../../core/models/patient.model';
import { PatientExam } from '../../../../core/models/exam.model';
import { ExamService } from '../../../../core/services/exam.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { ExamUploadModalComponent } from './exam-upload-modal/exam-upload-modal.component';
import { ConfirmationDialogComponent } from '../../../../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-patient-exams',
  standalone: true,
  imports: [
    CommonModule,
    ExamUploadModalComponent,
    ConfirmationDialogComponent,
  ],
  templateUrl: './patient-exams.component.html',
  styleUrls: ['./patient-exams.component.scss'],
})
export class PatientExamsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  // Dados
  exams: PatientExam[] = [];

  // Estados
  isLoading = false;
  error: string | null = null;

  // Modais
  isUploadModalOpen = false;

  // Diálogos de confirmação
  isDeleteExamConfirmationOpen = false;
  examToDelete: string | null = null;

  constructor(
    private examService: ExamService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    if (this.patient?.id) {
      this.loadExams();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && this.patient?.id) {
      this.loadExams();
    }
  }

  loadExams(): void {
    if (!this.patient?.id) return;

    this.isLoading = true;
    this.error = null;

    this.examService.getExamsByPatient(this.patient.id).subscribe({
      next: (exams) => {
        this.exams = exams;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar exames:', err);
        this.error =
          'Não foi possível carregar os exames. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar exames');
      },
    });
  }

  openUploadModal(): void {
    this.isUploadModalOpen = true;
  }

  closeUploadModal(): void {
    this.isUploadModalOpen = false;
  }

  onExamUploaded(exam: PatientExam): void {
    this.exams.unshift(exam);
    this.closeUploadModal();
    this.notificationService.success('Exame enviado com sucesso');
  }

  openExam(fileUrl: string): void {
    window.open(fileUrl, '_blank');
  }

  confirmDeleteExam(examId: string): void {
    this.examToDelete = examId;
    this.isDeleteExamConfirmationOpen = true;
  }

  cancelDeleteExam(): void {
    this.examToDelete = null;
    this.isDeleteExamConfirmationOpen = false;
  }

  deleteExam(): void {
    if (!this.examToDelete) return;

    this.isLoading = true;
    this.examService.deleteExam(this.examToDelete).subscribe({
      next: () => {
        this.exams = this.exams.filter((exam) => exam.id !== this.examToDelete);
        this.notificationService.success('Exame removido com sucesso');
        this.isLoading = false;
        this.cancelDeleteExam();
      },
      error: (err) => {
        console.error('Erro ao remover exame:', err);
        this.notificationService.error('Erro ao remover exame');
        this.isLoading = false;
        this.cancelDeleteExam();
      },
    });
  }

  // Formatação de data
  formatDate(date: Date | string): string {
    if (!date) return '';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('pt-BR');
  }
}
