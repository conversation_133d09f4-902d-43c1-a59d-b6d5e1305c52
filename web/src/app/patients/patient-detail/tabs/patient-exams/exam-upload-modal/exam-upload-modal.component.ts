import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ExamService } from '../../../../../core/services/exam.service';
import { NotificationService } from '../../../../../core/services/notification.service';
import { PatientExam } from '../../../../../core/models/exam.model';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

@Component({
  selector: 'app-exam-upload-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent],
  templateUrl: './exam-upload-modal.component.html',
  styleUrls: ['./exam-upload-modal.component.scss'],
})
export class ExamUploadModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;

  @Output() close = new EventEmitter<void>();
  @Output() examUploaded = new EventEmitter<PatientExam>();

  @ViewChild('fileInput') fileInput!: ElementRef;

  examForm: FormGroup;
  isSubmitting = false;
  selectedFile: File | null = null;
  fileError: string | null = null;

  readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB em bytes
  readonly ALLOWED_FILE_TYPE = 'application/pdf';

  constructor(
    private fb: FormBuilder,
    private examService: ExamService,
    private notificationService: NotificationService
  ) {
    this.examForm = this.fb.group({
      name: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file) {
      this.clearFile();
      return;
    }

    // Validar tamanho do arquivo
    if (file.size > this.MAX_FILE_SIZE) {
      this.fileError = 'O arquivo excede o tamanho máximo permitido de 5MB';
      this.clearFile();
      return;
    }

    // Validar tipo do arquivo
    if (file.type !== this.ALLOWED_FILE_TYPE) {
      this.fileError = 'Apenas arquivos PDF são permitidos';
      this.clearFile();
      return;
    }

    this.selectedFile = file;
    this.fileError = null;
  }

  clearFile(): void {
    this.selectedFile = null;
    this.fileError = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  onSubmit(): void {
    if (this.examForm.invalid || !this.selectedFile || !this.patientId) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.examForm.controls).forEach((key) => {
        const control = this.examForm.get(key);
        control?.markAsTouched();
      });

      if (!this.selectedFile) {
        this.fileError = 'Selecione um arquivo PDF';
      }

      return;
    }

    this.isSubmitting = true;
    const formValues = this.examForm.value;

    this.examService
      .uploadExam(this.patientId, formValues.name, this.selectedFile)
      .subscribe({
        next: (exam) => {
          this.examUploaded.emit(exam);
          this.resetForm();
          this.isSubmitting = false;
        },
        error: (err) => {
          console.error('Erro ao enviar exame:', err);
          this.notificationService.error('Erro ao enviar exame');
          this.isSubmitting = false;
        },
      });
  }

  resetForm(): void {
    this.examForm.reset();
    this.clearFile();
  }

  closeModal(): void {
    this.resetForm();
    this.close.emit();
  }
}
