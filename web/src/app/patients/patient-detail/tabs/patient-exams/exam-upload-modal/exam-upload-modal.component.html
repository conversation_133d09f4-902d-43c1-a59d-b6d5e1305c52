<app-modal
  [title]="'Adicionar Exame'"
  [isOpen]="isOpen"
  (close)="closeModal()"
  [showDefaultFooter]="true"
>
  <!-- Modal content -->
  <div class="p-6">
    <form [formGroup]="examForm" (ngSubmit)="onSubmit()" class="space-y-6">
      <!-- Nome do exame -->
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
          >Nome do exame *</label
        >
        <input
          type="text"
          id="name"
          formControlName="name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-500':
              examForm.get('name')?.invalid && examForm.get('name')?.touched
          }"
          placeholder="Ex: Radiografia Panorâmica"
        />
        <div
          *ngIf="examForm.get('name')?.invalid && examForm.get('name')?.touched"
          class="text-red-500 text-sm mt-1"
        >
          <span *ngIf="examForm.get('name')?.errors?.['required']"
            >Nome do exame é obrigatório</span
          >
        </div>
      </div>

      <!-- Upload do arquivo -->
      <div>
        <label
          for="examFile"
          class="block text-sm font-medium text-gray-700 mb-1"
          >Arquivo PDF (máx. 5MB) *</label
        >
        <div class="flex items-center gap-2">
          <input
            #fileInput
            type="file"
            id="examFile"
            (change)="onFileSelected($event)"
            accept=".pdf"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          <!-- Botão para limpar o arquivo -->
          <button
            *ngIf="selectedFile"
            type="button"
            (click)="clearFile()"
            class="px-3 py-2 text-sm text-red-600 hover:text-red-700"
            title="Remover arquivo"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
        <!-- Mostrar nome do arquivo selecionado -->
        <div *ngIf="selectedFile" class="mt-1 text-sm text-gray-500">
          Arquivo selecionado: {{ selectedFile.name }}
        </div>
        <p *ngIf="fileError" class="mt-1 text-sm text-red-600">
          {{ fileError }}
        </p>
      </div>
    </form>
  </div>

  <!-- Modal footer -->
  <div footer class="px-2 flex">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 w-24 flex items-center justify-center"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="onSubmit()"
      [disabled]="examForm.invalid || !selectedFile || isSubmitting"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed w-24 flex items-center justify-center"
    >
      <div
        *ngIf="isSubmitting"
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
      Enviar
    </button>
  </div>
</app-modal>
