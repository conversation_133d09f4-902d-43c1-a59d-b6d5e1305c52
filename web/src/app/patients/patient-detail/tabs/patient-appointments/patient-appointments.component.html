<div class="bg-gray-50 p-4 rounded-lg mb-6">
  <div class="flex justify-between items-center mb-4">
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        Histórico de Agendamentos
      </h2>
      <p class="text-sm text-gray-500 mt-1">
        Visualização do histórico de agendamentos do paciente
      </p>
    </div>
    <button
      *ngIf="patient"
      (click)="openCreateModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center text-sm"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Novo Agendamento
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <div *ngIf="!isLoading" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-100">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer group"
            (click)="sort('date')"
          >
            <div class="flex items-center">
              <span>Data e Hora</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-1 transition-all duration-200"
                [ngClass]="getSortIconClass('date')"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="getSortIcon('date')"
                />
              </svg>
            </div>
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer group"
            (click)="sort('dentistName')"
          >
            <div class="flex items-center">
              <span>Dentista</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-1 transition-all duration-200"
                [ngClass]="getSortIconClass('dentistName')"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="getSortIcon('dentistName')"
                />
              </svg>
            </div>
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer group"
            (click)="sort('motivo')"
          >
            <div class="flex items-center">
              <span>Motivo</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-1 transition-all duration-200"
                [ngClass]="getSortIconClass('motivo')"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="getSortIcon('motivo')"
                />
              </svg>
            </div>
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer group"
            (click)="sort('status')"
          >
            <div class="flex items-center">
              <span>Status</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-1 transition-all duration-200"
                [ngClass]="getSortIconClass('status')"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="getSortIcon('status')"
                />
              </svg>
            </div>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngIf="appointments.length === 0">
          <td colspan="4" class="px-6 py-4 text-center text-gray-500">
            Nenhum agendamento registrado para este paciente.
          </td>
        </tr>
        <tr
          *ngFor="let scheduling of appointments"
          class="hover:bg-gray-50 cursor-pointer"
          (click)="openAppointmentModal(scheduling.id)"
        >
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">
              {{ scheduling.date | date : "dd/MM/yyyy" }}
            </div>
            <div class="text-sm text-gray-500">
              {{ scheduling.time }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ scheduling.dentistName }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ scheduling.motivo || "Não especificado" }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              [ngClass]="getStatusClass(scheduling.status)"
            >
              {{ getStatusText(scheduling.status) }}
            </span>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Paginação -->
    <div
      *ngIf="totalItems > 0"
      class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner mt-4"
    >
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <!-- Informações de paginação e seletor de itens por página -->
        <div class="flex flex-wrap items-center text-sm text-gray-600">
          <div class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md">
            <span>Mostrando</span>
            <span class="font-medium mx-1 text-blue-600">{{
              (currentPage - 1) * itemsPerPage + 1
            }}</span>
            <span>-</span>
            <span class="font-medium mx-1 text-blue-600">{{
              currentPage * itemsPerPage > totalItems
                ? totalItems
                : currentPage * itemsPerPage
            }}</span>
            <span>de</span>
            <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
          </div>
          <div class="ml-4 flex items-center">
            <label for="itemsPerPage" class="mr-2">Itens por página:</label>
            <select
              id="itemsPerPage"
              [(ngModel)]="itemsPerPage"
              (change)="changeItemsPerPage()"
              class="bg-white border border-gray-300 rounded-md px-2 pl-3 pr-8 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option [value]="6">6</option>
              <option [value]="12">12</option>
              <option [value]="24">24</option>
              <option [value]="48">48</option>
            </select>
          </div>
        </div>

        <!-- Controles de paginação -->
        <div class="flex items-center pagination-controls">
          <div class="flex rounded-lg shadow-sm overflow-hidden">
            <!-- Primeira página -->
            <button
              (click)="firstPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Primeira página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
            </button>

            <!-- Página anterior -->
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Página anterior"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Números de página -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page !== '...'">
                <button
                  (click)="goToPage(page)"
                  [disabled]="isLoading"
                  class="relative rounded-sm inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  [ngClass]="
                    currentPage === page
                      ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                      : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  "
                >
                  {{ page }}
                </button>
              </ng-container>
              <div
                *ngIf="page === '...'"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm text-gray-500 border-r border-gray-200 bg-white"
              >
                ...
              </div>
            </ng-container>

            <!-- Próxima página -->
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Próxima página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- Última página -->
            <button
              (click)="lastPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Última página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de detalhes do agendamento -->
<app-appointment-modal
  [isOpen]="isModalOpen"
  [appointmentId]="selectedAppointmentId"
  (close)="closeAppointmentModal()"
></app-appointment-modal>

<!-- Modal de criação de agendamento -->
<app-scheduling-create-modal
  [isOpen]="isCreateModalOpen"
  [patientId]="patient?.id || null"
  (close)="closeCreateModal()"
  (saved)="onSchedulingSaved()"
></app-scheduling-create-modal>
