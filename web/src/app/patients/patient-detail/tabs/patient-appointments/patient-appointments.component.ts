import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Patient } from '../../../../core/models/patient.model';
import { SchedulingService } from '../../../../core/services/scheduling.service';
import { AppointmentModalComponent } from './appointment-modal/appointment-modal.component';
import { SchedulingCreateModalComponent } from './scheduling-create-modal/scheduling-create-modal.component';

interface Appointment {
  id: number;
  date: Date;
  time: string;
  dentistId?: number;
  dentistName: string;
  motivo?: string;
  status:
    | 'confirmed'
    | 'unconfirmed'
    | 'late'
    | 'no-show'
    | 'cancelled'
    | 'rescheduled'
    | 'in-progress'
    | 'completed'
    | 'scheduled-unconfirmed'
    | 'scheduled-confirmed'
    | 'unscheduled'
    | 'scheduled';
}

@Component({
  selector: 'app-patient-appointments',
  standalone: true,
  imports: [
    CommonModule,
    DatePipe,
    FormsModule,
    AppointmentModalComponent,
    SchedulingCreateModalComponent,
  ],
  templateUrl: './patient-appointments.component.html',
  styleUrl: './patient-appointments.component.scss',
})
export class PatientAppointmentsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  appointments: Appointment[] = [];
  isLoading = false;

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;
  allAppointments: Appointment[] = [];

  // Modais
  isModalOpen = false;
  selectedAppointmentId: number | null = null;
  isCreateModalOpen = false;

  // Ordenação
  sortColumn: string = 'date';
  sortDirection: 'asc' | 'desc' = 'desc'; // Padrão: mais recentes primeiro

  constructor(private schedulingService: SchedulingService) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadAppointments();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadAppointments();
    }
  }

  loadAppointments(): void {
    if (!this.patient) return;

    this.isLoading = true;
    // Usar getSchedulingsByPatient para buscar os agendamentos do paciente
    this.schedulingService.getSchedulingsByPatient(this.patient.id).subscribe({
      next: (appointments) => {
        console.log('Agendamentos carregados:', appointments.length);

        // Mapear e ordenar todos os agendamentos
        this.allAppointments = appointments
          .map((scheduling) => ({
            id: scheduling.id,
            date: new Date(scheduling.date),
            time: scheduling.time,
            dentistId: scheduling.dentistId,
            dentistName: scheduling.dentistName || 'Dentista não identificado',

            status: scheduling.status,
          }))
          // Aplicar ordenação inicial
          .sort((a, b) =>
            this.compare(a, b, this.sortColumn, this.sortDirection)
          );

        // Configurar paginação
        this.totalItems = this.allAppointments.length;
        this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);

        // Aplicar paginação
        this.applyPagination();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar agendamentos:', error);
        this.isLoading = false;
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'late':
        return 'bg-orange-100 text-orange-800';
      case 'no-show':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rescheduled':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-emerald-100 text-emerald-800';
      // Manter compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled-confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'unscheduled':
        return 'bg-red-100 text-red-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmado';
      case 'unconfirmed':
        return 'Não confirmado';
      case 'late':
        return 'Atrasado';
      case 'no-show':
        return 'Não compareceu';
      case 'cancelled':
        return 'Desmarcado';
      case 'rescheduled':
        return 'Remarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      // Manter compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'scheduled':
        return 'Agendado';
      default:
        return status;
    }
  }

  /**
   * Aplica a paginação aos agendamentos
   */
  applyPagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.totalItems);
    this.appointments = this.allAppointments.slice(startIndex, endIndex);
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.applyPagination();
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.applyPagination();
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  // Métodos para os modais
  openAppointmentModal(appointmentId: number): void {
    this.selectedAppointmentId = appointmentId;
    this.isModalOpen = true;
  }

  closeAppointmentModal(): void {
    this.isModalOpen = false;
    this.selectedAppointmentId = null;
  }

  openCreateModal(): void {
    this.isCreateModalOpen = true;
  }

  closeCreateModal(): void {
    this.isCreateModalOpen = false;
  }

  onSchedulingSaved(): void {
    this.closeCreateModal();
    this.loadAppointments();
  }

  // Métodos para ordenação
  sort(column: string): void {
    // Se clicar na mesma coluna, inverte a direção
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Se clicar em uma coluna diferente, define a nova coluna e a direção padrão
      this.sortColumn = column;
      // Para data, o padrão é decrescente (mais recentes primeiro)
      this.sortDirection = column === 'date' ? 'desc' : 'asc';
    }

    // Reordenar os dados
    this.allAppointments = [...this.allAppointments].sort((a, b) =>
      this.compare(a, b, this.sortColumn, this.sortDirection)
    );

    // Reaplicar paginação
    this.applyPagination();
  }

  compare(a: any, b: any, column: string, direction: 'asc' | 'desc'): number {
    let result = 0;

    // Comparação baseada na coluna
    if (column === 'date') {
      // Primeiro comparar por data
      const dateComparison = a.date.getTime() - b.date.getTime();

      // Se as datas forem iguais, comparar por hora
      if (dateComparison === 0) {
        const timeA = this.timeToMinutes(a.time);
        const timeB = this.timeToMinutes(b.time);
        result = timeA - timeB;
      } else {
        result = dateComparison;
      }
    } else if (column === 'dentistName') {
      result = a.dentistName.localeCompare(b.dentistName);

    } else if (column === 'status') {
      result = a.status.localeCompare(b.status);
    }

    // Inverter resultado se a direção for decrescente
    return direction === 'asc' ? result : -result;
  }

  timeToMinutes(time: string): number {
    if (!time) return 0;

    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Método para obter a classe do ícone de ordenação
  getSortIconClass(column: string): string {
    if (this.sortColumn !== column) {
      return 'text-gray-400 opacity-0 group-hover:opacity-100';
    }

    return 'text-blue-600';
  }

  // Método para determinar qual ícone de seta mostrar
  getSortIcon(column: string): string {
    if (this.sortColumn !== column) {
      // Ícone neutro para colunas não selecionadas
      return 'M5 15l7-7 7 7';
    }

    // Seta para cima (ascendente) ou para baixo (descendente)
    return this.sortDirection === 'asc'
      ? 'M5 15l7-7 7 7' // Seta para cima
      : 'M19 9l-7 7-7-7'; // Seta para baixo
  }
}
