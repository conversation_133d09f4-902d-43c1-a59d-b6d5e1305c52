import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { SchedulingService } from '../../../../../core/services/scheduling.service';
import { PatientService } from '../../../../../core/services/patient.service';
import { DentistService } from '../../../../../core/services/dentist.service';
import { NotificationService } from '../../../../../core/services/notification.service';
import { Patient } from '../../../../../core/models/patient.model';
import { Dentist } from '../../../../../core/models/dentist.model';

@Component({
  selector: 'app-scheduling-create-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ModalComponent,
  ],
  templateUrl: './scheduling-create-modal.component.html',
  styleUrl: './scheduling-create-modal.component.scss',
})
export class SchedulingCreateModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() patientId: number | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<void>();

  schedulingForm: FormGroup;
  isSubmitting = false;
  isLoading = false;
  
  // Dados para os selects
  dentists: Dentist[] = [];
  patient: Patient | null = null;
  
  // Dropdown de dentistas
  filteredDentists: Dentist[] = [];
  dentistSearchTerm = '';
  showDentistDropdown = false;

  constructor(
    private fb: FormBuilder,
    private schedulingService: SchedulingService,
    private patientService: PatientService,
    private dentistService: DentistService,
    private notificationService: NotificationService
  ) {
    this.schedulingForm = this.fb.group({
      patientId: ['', Validators.required],
      dentistId: ['', Validators.required],
      date: ['', Validators.required],
      time: ['', Validators.required],
      status: ['scheduled-unconfirmed', Validators.required],
      notes: [''],
    });
  }

  ngOnInit(): void {
    this.loadDentists();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && changes['isOpen'].currentValue) {
      this.resetForm();
      
      if (this.patientId) {
        this.loadPatient();
      }
    }
  }

  loadDentists(): void {
    this.isLoading = true;
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
        this.filteredDentists = [...this.dentists];
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar dentistas:', err);
        this.notificationService.error('Erro ao carregar dentistas');
        this.isLoading = false;
      },
    });
  }

  loadPatient(): void {
    if (!this.patientId) return;

    this.isLoading = true;
    this.patientService.getPatient(this.patientId).subscribe({
      next: (patient) => {
        this.patient = patient;
        this.schedulingForm.patchValue({
          patientId: this.patientId,
        });
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Erro ao carregar paciente:', err);
        this.notificationService.error('Erro ao carregar paciente');
        this.isLoading = false;
      },
    });
  }

  resetForm(): void {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    this.schedulingForm.reset({
      patientId: this.patientId || '',
      dentistId: '',
      date: formattedDate,
      time: '',
      status: 'scheduled-unconfirmed',
      notes: '',
    });

    this.dentistSearchTerm = '';
    this.showDentistDropdown = false;
  }

  filterDentists(): void {
    if (!this.dentistSearchTerm.trim()) {
      this.filteredDentists = [...this.dentists];
      return;
    }

    const search = this.dentistSearchTerm.toLowerCase().trim();
    this.filteredDentists = this.dentists.filter(
      (dentist) => dentist.name.toLowerCase().includes(search)
    );
  }

  selectDentist(dentist: Dentist): void {
    this.schedulingForm.patchValue({
      dentistId: dentist.id,
    });
    this.dentistSearchTerm = dentist.name;
    this.showDentistDropdown = false;
  }

  onSubmit(): void {
    if (this.schedulingForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.schedulingForm.controls).forEach((key) => {
        const control = this.schedulingForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValues = this.schedulingForm.value;

    // Preparar os dados para envio
    const schedulingData = {
      ...formValues,
      // Converter campos para número
      patientId: formValues.patientId
        ? Number(formValues.patientId)
        : undefined,
      dentistId: formValues.dentistId
        ? Number(formValues.dentistId)
        : undefined,
    };

    this.schedulingService.createScheduling(schedulingData).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.saved.emit();
      },
      error: (err) => {
        console.error('Erro ao criar agendamento:', err);
        this.notificationService.error('Erro ao criar agendamento');
        this.isSubmitting = false;
      },
    });
  }

  closeModal(): void {
    this.close.emit();
  }
}
