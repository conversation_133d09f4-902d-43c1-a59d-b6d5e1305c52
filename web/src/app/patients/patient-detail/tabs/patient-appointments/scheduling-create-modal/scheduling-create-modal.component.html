<app-modal
  [title]="'Novo Agendamento'"
  [isOpen]="isOpen"
  (close)="closeModal()"
  [showDefaultFooter]="true"
>
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"
    ></div>
    <span class="ml-2 text-gray-600">Carregando...</span>
  </div>

  <!-- Modal content -->
  <div *ngIf="!isLoading" class="p-6">
    <form [formGroup]="schedulingForm" class="space-y-6">
      <!-- Grid de 2 colunas para layout eficiente -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Coluna 1: Paciente, Dentista, Observações -->
        <div class="space-y-4">
          <!-- <PERSON><PERSON><PERSON> (somente leitura) -->
          <div class="space-y-2">
            <label
              for="patientId"
              class="block text-sm font-medium text-gray-700"
              >Paciente *</label
            >
            <input
              type="text"
              [value]="patient?.name || ''"
              class="w-full px-4 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
              readonly
            />
            <input type="hidden" formControlName="patientId" />
          </div>

          <!-- Dentista -->
          <div class="space-y-2">
            <label
              for="dentistSearch"
              class="block text-sm font-medium text-gray-700"
              >Dentista *</label
            >
            <div class="relative">
              <input
                type="text"
                id="dentistSearch"
                [(ngModel)]="dentistSearchTerm"
                [ngModelOptions]="{ standalone: true }"
                (input)="filterDentists()"
                (focus)="showDentistDropdown = true"
                placeholder="Buscar dentista por nome"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  schedulingForm.get('dentistId')?.invalid &&
                  schedulingForm.get('dentistId')?.touched
                "
              />
              <div
                *ngIf="showDentistDropdown && filteredDentists.length > 0"
                class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
              >
                <div
                  *ngFor="let dentist of filteredDentists"
                  (click)="selectDentist(dentist)"
                  class="px-4 py-2 cursor-pointer hover:bg-gray-100"
                >
                  {{ dentist.name }}
                </div>
              </div>
              <input type="hidden" formControlName="dentistId" />
              <div
                *ngIf="
                  schedulingForm.get('dentistId')?.invalid &&
                  schedulingForm.get('dentistId')?.touched
                "
                class="text-red-500 text-sm mt-1"
              >
                Dentista é obrigatório
              </div>
            </div>
          </div>

          <!-- Observações -->
          <div class="space-y-2">
            <label for="notes" class="block text-sm font-medium text-gray-700"
              >Observações</label
            >
            <textarea
              id="notes"
              formControlName="notes"
              rows="3"
              placeholder="Observações adicionais"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>
        </div>

        <!-- Coluna 2: Data, Hora, Status -->
        <div class="space-y-4">
          <!-- Data e Hora -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <label for="date" class="block text-sm font-medium text-gray-700"
                >Data *</label
              >
              <input
                type="date"
                id="date"
                formControlName="date"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  schedulingForm.get('date')?.invalid &&
                  schedulingForm.get('date')?.touched
                "
              />
              <div
                *ngIf="
                  schedulingForm.get('date')?.invalid &&
                  schedulingForm.get('date')?.touched
                "
                class="text-red-500 text-sm mt-1"
              >
                Data é obrigatória
              </div>
            </div>

            <div class="space-y-2">
              <label for="time" class="block text-sm font-medium text-gray-700"
                >Hora *</label
              >
              <input
                type="time"
                id="time"
                formControlName="time"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  schedulingForm.get('time')?.invalid &&
                  schedulingForm.get('time')?.touched
                "
              />
              <div
                *ngIf="
                  schedulingForm.get('time')?.invalid &&
                  schedulingForm.get('time')?.touched
                "
                class="text-red-500 text-sm mt-1"
              >
                Hora é obrigatória
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="space-y-2">
            <label for="status" class="block text-sm font-medium text-gray-700"
              >Status *</label
            >
            <select
              id="status"
              formControlName="status"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="scheduled-unconfirmed">Agendado (não confirmado)</option>
              <option value="scheduled-confirmed">Agendado (confirmado)</option>
              <option value="scheduled">Agendado</option>
              <option value="confirmed">Confirmado</option>
            </select>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer buttons -->
  <div footer class="flex justify-end space-x-3">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="onSubmit()"
      [disabled]="schedulingForm.invalid || isSubmitting"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
    >
      <div
        *ngIf="isSubmitting"
        class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"
      ></div>
      Salvar
    </button>
  </div>
</app-modal>
