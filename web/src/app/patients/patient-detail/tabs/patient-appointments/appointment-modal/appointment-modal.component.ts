import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';
import { SchedulingService } from '../../../../../core/services/scheduling.service';
import { Scheduling } from '../../../../../core/models/scheduling.model';

@Component({
  selector: 'app-appointment-modal',
  standalone: true,
  imports: [CommonModule, ModalComponent, DatePipe],
  templateUrl: './appointment-modal.component.html',
  styleUrl: './appointment-modal.component.scss'
})
export class AppointmentModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() appointmentId: number | null = null;
  @Output() close = new EventEmitter<void>();

  appointment: Scheduling | null = null;
  isLoading = false;
  error = false;

  constructor(private schedulingService: SchedulingService) {}

  ngOnInit(): void {}

  ngOnChanges(): void {
    if (this.isOpen && this.appointmentId) {
      this.loadAppointment();
    }
  }

  loadAppointment(): void {
    if (!this.appointmentId) return;

    this.isLoading = true;
    this.error = false;

    this.schedulingService.getSchedulingById(this.appointmentId).subscribe({
      next: (appointment) => {
        this.appointment = appointment;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar agendamento:', error);
        this.error = true;
        this.isLoading = false;
      }
    });
  }

  closeModal(): void {
    this.appointment = null;
    this.close.emit();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'late':
        return 'bg-orange-100 text-orange-800';
      case 'no-show':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rescheduled':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-emerald-100 text-emerald-800';
      // Manter compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled-confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'unscheduled':
        return 'bg-red-100 text-red-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmado';
      case 'unconfirmed':
        return 'Não confirmado';
      case 'late':
        return 'Atrasado';
      case 'no-show':
        return 'Não compareceu';
      case 'cancelled':
        return 'Desmarcado';
      case 'rescheduled':
        return 'Remarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      // Manter compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'scheduled':
        return 'Agendado';
      default:
        return status;
    }
  }

  formatCurrency(value: number | undefined): string {
    if (value === undefined) return 'R$ 0,00';
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  }
}
