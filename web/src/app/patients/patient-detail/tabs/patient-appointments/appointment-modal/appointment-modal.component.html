<app-modal
  [title]="'Detalhes do Agendamento'"
  [isOpen]="isOpen"
  (close)="closeModal()"
>
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="p-4 bg-red-50 text-red-700 rounded-md">
    <p class="font-medium">Erro ao carregar os detalhes do agendamento.</p>
    <p>Por favor, tente novamente mais tarde.</p>
  </div>

  <!-- Appointment details -->
  <div *ngIf="!isLoading && !error && appointment" class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Informações do Agendamento</h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Status:</span>
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="getStatusClass(appointment.status)">
              {{ getStatusText(appointment.status) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data:</span>
            <span class="text-gray-900">{{ appointment.date | date: 'dd/MM/yyyy' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Hora:</span>
            <span class="text-gray-900">{{ appointment.time }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Criado em:</span>
            <span class="text-gray-900">{{ appointment.createdAt | date: 'dd/MM/yyyy HH:mm' }}</span>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Informações do Paciente</h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Nome:</span>
            <span class="text-gray-900">{{ appointment.patientName }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Dentista:</span>
            <span class="text-gray-900">{{ appointment.dentistName }}</span>
          </div>

        </div>
      </div>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Informações Adicionais</h2>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-500">Valor:</span>
          <span class="text-gray-900">{{ formatCurrency(appointment.cost) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500">Pago:</span>
          <span class="text-gray-900">{{ appointment.paid ? 'Sim' : 'Não' }}</span>
        </div>
        <div>
          <span class="text-gray-500 block mb-2">Observações:</span>
          <div class="bg-white p-3 rounded border border-gray-200 text-gray-900 min-h-[80px]">
            {{ appointment.notes || 'Nenhuma observação registrada.' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</app-modal>
