import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { Patient } from '../../../../core/models/patient.model';
import { TreatmentService } from '../../../../core/services/treatment.service';

interface Treatment {
  id: number;
  name: string;
  date: Date;
  time?: string;
  status: 'completed' | 'in-progress' | 'scheduled' | 'cancelled';
  dentistId: number;
  dentistName: string;
  notes?: string;
}

@Component({
  selector: 'app-patient-treatments',
  standalone: true,
  imports: [CommonModule, RouterLink, DatePipe],
  templateUrl: './patient-treatments.component.html',
  styleUrl: './patient-treatments.component.scss',
})
export class PatientTreatmentsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;
  
  treatments: Treatment[] = [];
  isLoading = false;

  constructor(private treatmentService: TreatmentService) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadTreatments();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadTreatments();
    }
  }

  loadTreatments(): void {
    if (!this.patient) return;
    
    this.isLoading = true;
    this.treatmentService.getTreatments(this.patient.id).subscribe({
      next: (treatments) => {
        this.treatments = treatments.map((treatment) => ({
          id: treatment.id,
          name: treatment.name,
          date: new Date(treatment.createdAt || new Date()),
          time: '08:00', // Valor padrão, já que o campo foi removido
          status: treatment.status as
            | 'completed'
            | 'in-progress'
            | 'scheduled'
            | 'cancelled',
          dentistId: treatment.dentistId,
          dentistName: treatment.dentistName || 'Dentista não identificado',
          notes: treatment.notes,
        }));
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar tratamentos:', error);
        this.isLoading = false;
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled-confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'unscheduled':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'in-progress':
        return 'Em andamento';
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'cancelled':
        return 'Cancelado';
      // Manter compatibilidade com registros antigos
      case 'scheduled':
        return 'Agendado';
      case 'confirmed':
        return 'Confirmado';
      default:
        return status;
    }
  }
}
