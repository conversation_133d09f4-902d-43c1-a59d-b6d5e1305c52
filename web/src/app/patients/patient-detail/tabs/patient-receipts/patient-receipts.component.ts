import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Patient } from '../../../../core/models/patient.model';
import { Receipt } from '../../../../core/models/receipt.model';
import { ReceiptService } from '../../../../core/services/receipt.service';

@Component({
  selector: 'app-patient-receipts',
  standalone: true,
  imports: [CommonModule, DatePipe],
  templateUrl: './patient-receipts.component.html',
  styleUrl: './patient-receipts.component.scss',
})
export class PatientReceiptsComponent implements OnInit, OnChanges {
  @Input() patient: Patient | null = null;

  receipts: Receipt[] = [];
  isLoading = false;

  constructor(private receiptService: ReceiptService) {}

  ngOnInit(): void {
    if (this.patient) {
      this.loadReceipts();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patient'] && changes['patient'].currentValue) {
      this.loadReceipts();
    }
  }

  loadReceipts(): void {
    if (!this.patient) return;

    this.isLoading = true;
    this.receiptService.getReceiptsByPatient(this.patient.id).subscribe({
      next: (receipts) => {
        this.receipts = receipts;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar recibos:', error);
        this.isLoading = false;
      }
    });
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  truncateText(text: string, maxLength: number = 50): string {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
}
