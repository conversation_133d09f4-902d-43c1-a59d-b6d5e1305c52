<div class="bg-gray-50 p-4 rounded-lg mb-6">
  <div class="flex justify-between items-center mb-4">
    <div>
      <h2 class="text-lg font-medium text-gray-900">Recibos</h2>
      <p class="text-sm text-gray-500 mt-1">Visualização dos recibos emitidos para o paciente</p>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
    <span class="ml-2 text-gray-600">Carregando recibos...</span>
  </div>

  <!-- Receipts list -->
  <div *ngIf="!isLoading" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-100">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Número
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Data
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Beneficiário
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            CPF
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Valor
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Profissional
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Observações
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngIf="receipts.length === 0">
          <td colspan="7" class="px-6 py-4 text-center text-gray-500">
            Nenhum recibo registrado para este paciente.
          </td>
        </tr>
        <tr *ngFor="let receipt of receipts" class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">
              {{ receipt.editedNumber || receipt.number }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ receipt.date | date : "dd/MM/yyyy" }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ receipt.beneficiary }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">
              {{ receipt.beneficiaryCpf }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ formatCurrency(receipt.value) }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">
              {{ receipt.professional }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500 max-w-xs" [title]="receipt.observations || ''">
              {{ truncateText(receipt.observations || '', 30) }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
