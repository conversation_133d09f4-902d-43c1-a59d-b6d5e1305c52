<div class="bg-white shadow rounded-lg p-6 min-h-[500px]">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <div *ngIf="!isLoading && patient">
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <a routerLink="/patients" class="mr-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 hover:text-gray-700"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
        </a>
        <div
          class="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mr-4"
        >
          <span class="text-blue-600 font-bold text-xl">
            {{ patient.name.charAt(0) }}
          </span>
        </div>
        <div class="flex flex-col">
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ patient.name }}
          </h1>
          <p class="text-gray-400 text-sm">
            CPF: {{ getDisplayValue(patient.cpf) }}
          </p>
        </div>
      </div>

      <!-- Botões alinhados lado a lado -->
      <div class="flex items-center gap-2">
        <!-- Loading -->
        <div *ngIf="isCheckingMedicalRecord" class="flex items-center">
          <div
            class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"
          ></div>
          <span class="text-gray-600">Verificando...</span>
        </div>

        <!-- Acessar Prontuário -->
        <button
          *ngIf="!isCheckingMedicalRecord && hasMedicalRecord"
          (click)="openMedicalRecordModal()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Acessar Prontuário
        </button>

        <!-- Gerar Prontuário -->
        <button
          *ngIf="!isCheckingMedicalRecord && !hasMedicalRecord"
          (click)="openMedicalRecordModal()"
          class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Gerar Prontuário
        </button>

        <!-- Dropdown Ações -->
        <div class="relative">
          <button
            (click)="toggleActionMenu($event)"
            class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md flex items-center transition-colors border border-gray-300"
          >
            <span>Ações</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
              [ngClass]="{ 'transform rotate-180': isActionMenuOpen }"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>

          <!-- Menu dropdown -->
          <div
            *ngIf="isActionMenuOpen"
            class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50"
          >
            <div class="dropdown-item">
              <app-action-button
                [svgPath]="SVG_PATHS.delete"
                [text]="'Excluir Paciente'"
                [fullWidth]="true"
                [textColor]="'text-gray-700'"
                [iconColor]="'text-red-500'"
                (clicked)="deletePatient(); closeActionMenu()"
              ></app-action-button>
            </div>

            <div class="dropdown-item">
              <app-action-button
                [svgPath]="SVG_PATHS.block"
                [text]="'Bloquear Paciente'"
                [fullWidth]="true"
                [textColor]="'text-gray-700'"
                [iconColor]="'text-orange-500'"
                (clicked)="togglePatientStatus(); closeActionMenu()"
              ></app-action-button>
            </div>

            <div class="dropdown-item">
              <app-action-button
                [svgPath]="SVG_PATHS.print"
                [text]="'Imprimir Paciente'"
                [fullWidth]="true"
                [textColor]="'text-gray-700'"
                [iconColor]="'text-blue-500'"
                (clicked)="printPatientData(); closeActionMenu()"
              ></app-action-button>
            </div>

            <div class="dropdown-item">
              <app-action-button
                [svgPath]="SVG_PATHS.whatsapp"
                [text]="'Enviar WhatsApp'"
                [fullWidth]="true"
                [textColor]="'text-gray-700'"
                [iconColor]="'text-green-500'"
                (clicked)="sendWhatsAppMessage(); closeActionMenu()"
              ></app-action-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs com carrossel horizontal -->
    <div class="mb-6 relative">
      <div class="border-b border-gray-200 overflow-hidden">
        <div class="tabs-carousel px-2">
          <!-- Botão de navegação esquerda -->
          <button
            class="carousel-nav-btn carousel-nav-left"
            (click)="scrollTabsLeft()"
            aria-label="Rolar para a esquerda"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <div class="tabs-container" #tabsContainer>
            <button
              (click)="setActiveTab('cadastro')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'cadastro'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Cadastro
            </button>
            <button
              (click)="setActiveTab('budgets')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'budgets'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Orçamentos
            </button>

            <button
              (click)="setActiveTab('attendances')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'attendances'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Agendamentos
            </button>
            <button
              (click)="setActiveTab('clinical-record')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'clinical-record'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Plano e Ficha Clínica
            </button>
            <button
              (click)="setActiveTab('odontogram')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'odontogram'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Odontograma
            </button>

            <button
              (click)="setActiveTab('receipts')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'receipts'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Recibos
            </button>
            <button
              (click)="setActiveTab('indications')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'indications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Indicações
            </button>
            <button
              (click)="setActiveTab('anamnesis')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'anamnesis'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Anamnese
            </button>

            <button
              (click)="setActiveTab('photos')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'photos'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Fotos
            </button>
            <button
              (click)="setActiveTab('exams')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'exams'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Exames
            </button>
            <button
              (click)="setActiveTab('documents')"
              class="py-4 px-6 text-center border-b-2 font-medium text-sm flex-shrink-0"
              [ngClass]="
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              "
            >
              Documentos
            </button>
          </div>

          <!-- Botão de navegação direita -->
          <button
            class="carousel-nav-btn carousel-nav-right"
            (click)="scrollTabsRight()"
            aria-label="Rolar para a direita"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Tab content -->
    <div>
      <!-- Cadastro tab -->
      <div *ngIf="activeTab === 'cadastro'">
        <app-patient-registration
          [patient]="patient"
        ></app-patient-registration>
      </div>

      <!-- Orçamentos tab -->
      <div *ngIf="activeTab === 'budgets'">
        <app-patient-budgets [patient]="patient"></app-patient-budgets>
      </div>

      <!-- Schedulings tab -->
      <div *ngIf="activeTab === 'attendances'">
        <app-patient-appointments
          [patient]="patient"
        ></app-patient-appointments>
      </div>

      <!-- Clinical Record tab -->
      <div *ngIf="activeTab === 'clinical-record'">
        <app-patient-clinical-record
          [patient]="patient"
        ></app-patient-clinical-record>
      </div>

      <!-- Receipts tab -->
      <div *ngIf="activeTab === 'receipts'">
        <app-patient-receipts [patient]="patient"></app-patient-receipts>
      </div>

      <!-- Indications tab -->
      <div *ngIf="activeTab === 'indications'">
        <app-patient-indications
          [patientId]="patient.id"
        ></app-patient-indications>
      </div>

      <!-- Anamnesis tab -->
      <div *ngIf="activeTab === 'anamnesis'">
        <app-patient-anamnesis [patient]="patient"></app-patient-anamnesis>
      </div>

      <!-- Odontogram tab -->
      <div *ngIf="activeTab === 'odontogram'">
        <app-patient-odontogram [patient]="patient"></app-patient-odontogram>
      </div>

      <!-- Photos tab -->
      <div *ngIf="activeTab === 'photos'">
        <app-patient-photos [patient]="patient"></app-patient-photos>
      </div>

      <!-- Exams tab -->
      <div *ngIf="activeTab === 'exams'">
        <app-patient-exams [patient]="patient"></app-patient-exams>
      </div>

      <!-- Documents tab -->
      <div *ngIf="activeTab === 'documents'">
        <app-patient-documents [patient]="patient"></app-patient-documents>
      </div>
    </div>
  </div>

  <!-- Modal de Prontuário Completo -->
  <app-medical-record-modal
    *ngIf="showMedicalRecordModal && patient"
    [patientId]="patient.id"
    (close)="closeMedicalRecordModal()"
  ></app-medical-record-modal>
</div>
