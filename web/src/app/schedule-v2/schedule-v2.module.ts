import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { ScheduleV2RoutingModule } from './schedule-v2-routing.module';
import { ScheduleV2Component } from './schedule-v2/schedule-v2.component';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    ScheduleV2RoutingModule,
    ScheduleV2Component,
    SharedModule
  ]
})
export class ScheduleV2Module { }
