/* Schedule v2 - Estilos customizados mínimos */

/* Scrollbar customizada */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}
.sidebar-content::-webkit-scrollbar-track {
  background: #f9fafb;
}
.sidebar-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}
.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Animações essenciais */
.sheet-overlay {
  transition: opacity 0.3s ease-out;
}
.sheet-panel {
  transition: transform 0.3s ease-out;
  /* Garantir que o sheet fique no topo da tela */
  max-height: 100vh;
  /* Adicionar sombra inferior para o sheet vindo de cima */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  /* Adicionar bordas arredondadas na parte inferior */
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/* Animação bounce para ícones */
@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Estilos essenciais para a agenda */
.schedule-grid {
  /* Scrollbar horizontal customizada */
  .overflow-auto::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }
  .overflow-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }
  .overflow-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }
  .overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Drag scroll */
  .overflow-auto {
    cursor: grab;
    -webkit-overflow-scrolling: touch;
  }
  .overflow-auto.active {
    cursor: grabbing;
    user-select: none;
  }
}

/* Dropdown de filtros */
.filter-dropdown-container .filter-dropdown {
  animation: slideDown 0.2s ease-out;
  max-height: calc(100vh - 200px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile responsivo */
@media (max-width: 640px) {
  .filter-dropdown-container .filter-dropdown {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
    z-index: 9999;
  }

  .sheet-panel {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    /* Em mobile, o sheet ocupa toda a tela */
    top: 0 !important;
    left: 0 !important;
    transform: translateY(-100%) !important;
  }

  .sheet-panel.translate-y-0 {
    transform: translateY(0) !important;
  }
}
