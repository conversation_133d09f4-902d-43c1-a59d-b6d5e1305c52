<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Agendamentos</h1>
    <a
      routerLink="/schedulings/new"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Novo Agendamento
    </a>
  </div>

  <!-- Filtros -->
  <div class="mb-6 grid grid-cols-1 md:grid-cols-7 gap-4">
    <div class="col-span-1 md:col-span-2">
      <label for="search" class="block text-sm font-medium text-gray-700 mb-1"
        >Buscar</label
      >
      <div class="relative">
        <input
          type="text"
          id="search"
          [(ngModel)]="searchTerm"
          (input)="onSearch()"
          placeholder="Buscar por nome do paciente"
          class="w-full px-4 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
        <button
          *ngIf="searchTerm"
          (click)="searchTerm = ''; onSearch()"
          class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
    <div>
      <label for="status" class="block text-sm font-medium text-gray-700 mb-1"
        >Status</label
      >
      <select
        id="status"
        [(ngModel)]="statusFilter"
        (change)="onStatusFilterChange($event)"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">Todos os status</option>
        <option value="scheduled-unconfirmed">Agendado não confirmado</option>
        <option value="scheduled-confirmed">Agendado confirmado</option>
        <option value="unscheduled">Desmarcado</option>
        <option value="in-progress">Em andamento</option>
        <option value="completed">Concluído</option>
        <option value="cancelled">Cancelado</option>
      </select>
    </div>
    <div>
      <label for="dentist" class="block text-sm font-medium text-gray-700 mb-1"
        >Dentista</label
      >
      <select
        id="dentist"
        [(ngModel)]="dentistFilter"
        (change)="onDentistFilterChange($event)"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">Todos os dentistas</option>
        <option *ngFor="let dentist of dentists" [value]="dentist.id">
          {{ dentist.name }}
        </option>
      </select>
    </div>
    <div>
      <label for="appointmentCategory" class="block text-sm font-medium text-gray-700 mb-1"
        >Categoria</label
      >
      <select
        id="appointmentCategory"
        [(ngModel)]="appointmentCategoryFilter"
        (change)="onAppointmentCategoryFilterChange($event)"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">Todas as categorias</option>
        <option *ngFor="let category of appointmentCategories" [value]="category.id">
          {{ category.name }}
        </option>
      </select>
    </div>
    <div>
      <label
        for="startDate"
        class="block text-sm font-medium text-gray-700 mb-1"
        >Data Inicial</label
      >
      <input
        type="date"
        id="startDate"
        [(ngModel)]="startDateFilter"
        (change)="onStartDateChange($event)"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div>
      <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1"
        >Data Final</label
      >
      <input
        type="date"
        id="endDate"
        [(ngModel)]="endDateFilter"
        (change)="onEndDateChange($event)"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="flex items-end">
      <button
        (click)="clearFilters()"
        class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
            clip-rule="evenodd"
          />
        </svg>
        Limpar
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Tabela de agendamentos -->
  <div *ngIf="!isLoading" class="bg-white rounded-lg shadow-sm overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Paciente
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Data/Hora
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Dentista
          </th>

          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Status
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Categoria
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Ações
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngIf="filteredSchedulings.length === 0">
          <td colspan="6" class="px-6 py-4 text-center text-gray-500">
            Nenhum agendamento encontrado.
          </td>
        </tr>
        <tr
          *ngFor="let scheduling of filteredSchedulings"
          class="hover:bg-gray-50 group"
        >
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">
              {{ scheduling.patient?.name || 'Paciente não encontrado' }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ formatDate(scheduling.date) }}
            </div>
            <div class="text-sm text-gray-500">{{ scheduling.time }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ scheduling.dentist?.name || 'Sem dentista definido' }}
          </td>

          <td class="px-6 py-4 whitespace-nowrap">
            <span
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              [ngClass]="getStatusClass(scheduling.status)"
            >
              {{ getStatusText(scheduling.status) }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span
              *ngIf="scheduling.appointmentCategory"
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full text-white"
              [style.background-color]="scheduling.appointmentCategory.color"
            >
              {{ scheduling.appointmentCategory.name }}
            </span>
            <span
              *ngIf="!scheduling.appointmentCategory"
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"
            >
              Sem categoria
            </span>
          </td>
          <td
            class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
          >
            <!-- Botão de visualizar (sempre visível) -->
            <button
              (click)="viewScheduling(scheduling.id)"
              class="text-blue-600 hover:text-blue-900 mr-3"
              title="Visualizar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </button>

            <!-- Botão de editar (visível para todos os agendamentos) -->
            <button
              (click)="editScheduling(scheduling.id)"
              class="text-indigo-600 hover:text-indigo-900 mr-3"
              title="Editar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Paginação Moderna -->
    <div
      *ngIf="totalItems > 0"
      class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner"
    >
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <!-- Informações de paginação e seletor de itens por página -->
        <div class="flex flex-wrap items-center text-sm text-gray-600">
          <div
            class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md shadow-sm"
          >
            <span>Mostrando</span>
            <span class="font-medium mx-1 text-blue-600">{{
              (currentPage - 1) * itemsPerPage + 1
            }}</span>
            <span>-</span>
            <span class="font-medium mx-1 text-blue-600">{{
              currentPage * itemsPerPage > totalItems
                ? totalItems
                : currentPage * itemsPerPage
            }}</span>
            <span>de</span>
            <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
          </div>

          <!-- Seletor de itens por página -->
          <div class="ml-3 flex items-center">
            <label for="itemsPerPage" class="mr-2 text-sm text-gray-500"
              >Itens por página:</label
            >
            <div class="relative">
              <select
                id="itemsPerPage"
                [(ngModel)]="itemsPerPage"
                (change)="changeItemsPerPage()"
                class="appearance-none bg-white border border-gray-200 rounded-md pl-3 pr-8 py-1.5 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option [value]="6">6</option>
                <option [value]="10">10</option>
                <option [value]="20">20</option>
                <option [value]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Controles de paginação -->
        <div class="flex items-center pagination-controls">
          <div class="flex rounded-lg shadow-sm overflow-hidden">
            <!-- Primeira página -->
            <button
              (click)="firstPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Primeira página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Página anterior -->
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Página anterior"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Números de página -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page !== '...'">
                <button
                  (click)="goToPage(page)"
                  [disabled]="isLoading"
                  class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  [ngClass]="
                    currentPage === page
                      ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                      : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  "
                >
                  {{ page }}
                </button>
              </ng-container>
              <div
                *ngIf="page === '...'"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm border-r border-gray-200 bg-white text-gray-500"
              >
                ...
              </div>
            </ng-container>

            <!-- Próxima página -->
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Próxima página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- Última página -->
            <button
              (click)="lastPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Última página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
