// Estilos específicos para o componente de listagem de agendamentos

// Estilo para a coluna de motivo
.max-w-xs {
  max-width: 200px; // Limita a largura máxima da coluna
}

// Estilos para o texto truncado que expande ao passar o mouse
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
}

.truncate:hover {
  white-space: normal;
  overflow: visible;
  background-color: #f9fafb; // Cor de fundo suave ao passar o mouse
  z-index: 10; // Garante que o texto expandido fique acima de outros elementos
  position: relative;
  border-radius: 4px;
  padding: 2px 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// Estilos para o tooltip personalizado
.tooltip-text {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
  max-width: 250px;
  word-wrap: break-word;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.group:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
