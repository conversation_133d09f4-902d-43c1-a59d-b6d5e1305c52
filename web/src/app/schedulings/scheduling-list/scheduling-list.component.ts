import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SchedulingService } from '../../core/services/scheduling.service';
import { DentistService } from '../../core/services/dentist.service';
import { NotificationService } from '../../core/services/notification.service';
import { AppointmentCategoriesService } from '../../appointment-categories/services/appointment-categories.service';
import { forkJoin } from 'rxjs';
import { PaginatedResponse } from '../../core/models/paginated-response.model';
import { Scheduling, AppointmentCategory } from '../../core/models/scheduling.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-scheduling-list',
  templateUrl: './scheduling-list.component.html',
  styleUrls: ['./scheduling-list.component.scss'],
  standalone: false,
})
export class SchedulingListComponent implements OnInit {
  schedulings: Scheduling[] = [];
  filteredSchedulings: Scheduling[] = [];
  isLoading = true;
  searchTerm = '';
  statusFilter = '';
  dentistFilter = '';
  startDateFilter = '';
  endDateFilter = '';
  appointmentCategoryFilter = '';
  dentists: any[] = [];
  appointmentCategories: AppointmentCategory[] = [];

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;

  constructor(
    private schedulingService: SchedulingService,
    private dentistService: DentistService,
    private appointmentCategoriesService: AppointmentCategoriesService,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    // Verificar se há parâmetros de filtro na URL
    this.route.queryParams.subscribe((params) => {
      if (params['status']) {
        this.statusFilter = params['status'];
      }

      if (params['endDate']) {
        this.endDateFilter = params['endDate'];
      }

      if (params['startDate']) {
        this.startDateFilter = params['startDate'];
      }

      if (params['appointmentCategoryId']) {
        this.appointmentCategoryFilter = params['appointmentCategoryId'];
      }

      if (params['dentistId']) {
        this.dentistFilter = params['dentistId'];
      }

      // Carregar os dados após processar os parâmetros
      this.loadData();
    });
  }

  loadData(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;

    // Preparar os filtros
    const filters: any = {};

    if (this.searchTerm) {
      filters.search = this.searchTerm;
    }

    if (this.statusFilter) {
      filters.status = this.statusFilter;
    }

    if (this.dentistFilter) {
      filters.dentistId = parseInt(this.dentistFilter);
    }

    if (this.startDateFilter) {
      filters.startDate = this.startDateFilter;
    }

    if (this.endDateFilter) {
      filters.endDate = this.endDateFilter;
    }

    if (this.appointmentCategoryFilter) {
      filters.appointmentCategoryId = parseInt(this.appointmentCategoryFilter);
    }

    // Função para carregar os agendamentos
    const loadSchedulings = () => {
      this.schedulingService
        .getSchedulings(page, this.itemsPerPage, filters)
        .pipe(
          finalize(() => {
            this.isLoading = false;
            // Restaura a opacidade com uma pequena animação de fade-in
            setTimeout(() => {
              if (tableContent) {
                tableContent.classList.remove('opacity-60');
              }
            }, 100);
          })
        )
        .subscribe({
          next: (response: PaginatedResponse<Scheduling> | Scheduling[]) => {
            // Verificar se é um objeto paginado ou um array
            if (Array.isArray(response)) {
              this.schedulings = response;
              this.filteredSchedulings = response;
              this.totalItems = response.length;
              this.currentPage = 1;
              this.itemsPerPage = response.length;
              this.totalPages = 1;
            } else if (
              response &&
              typeof response === 'object' &&
              'data' in response
            ) {
              // É um objeto paginado
              const paginatedResponse =
                response as PaginatedResponse<Scheduling>;
              this.schedulings = paginatedResponse.data;
              this.filteredSchedulings = paginatedResponse.data;
              this.totalItems = paginatedResponse.total;
              this.currentPage = paginatedResponse.page;
              this.itemsPerPage = paginatedResponse.limit;
              this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
            }

            // Atualizar a URL com os parâmetros de filtro
            this.updateQueryParams();
          },
          error: (error: any) => {
            console.error('Erro ao carregar agendamentos:', error);
            this.notificationService.error(
              'Erro ao carregar agendamentos. Por favor, tente novamente.'
            );
          },
        });
    };

    // Carregar dentistas e categorias apenas se ainda não estiverem carregados
    if (this.dentists.length === 0 || this.appointmentCategories.length === 0) {
      const requests = [];

      if (this.dentists.length === 0) {
        requests.push(this.dentistService.getAllDentists());
      }

      if (this.appointmentCategories.length === 0) {
        requests.push(this.appointmentCategoriesService.getAllCategories({ isActive: true }));
      }

      if (requests.length > 0) {
        forkJoin(requests).subscribe({
          next: (results) => {
            let resultIndex = 0;
            if (this.dentists.length === 0) {
              this.dentists = results[resultIndex] as any[];
              resultIndex++;
            }
            if (this.appointmentCategories.length === 0) {
              const categoriesResponse = results[resultIndex] as any;
              this.appointmentCategories = categoriesResponse.data;
            }
            loadSchedulings();
          },
          error: (error) => {
            console.error('Erro ao carregar dados:', error);
            this.notificationService.error(
              'Erro ao carregar dados. Por favor, tente novamente.'
            );
            this.isLoading = false;
          },
        });
      } else {
        loadSchedulings();
      }
    } else {
      loadSchedulings();
    }
  }

  onSearch(): void {
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  onStatusFilterChange(event: any): void {
    this.statusFilter = event.target.value;
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  onDentistFilterChange(event: any): void {
    this.dentistFilter = event.target.value;
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  onStartDateChange(event: any): void {
    this.startDateFilter = event.target.value;
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  onEndDateChange(event: any): void {
    this.endDateFilter = event.target.value;
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  onAppointmentCategoryFilterChange(event: any): void {
    this.appointmentCategoryFilter = event.target.value;
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadData(this.currentPage);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.statusFilter = '';
    this.dentistFilter = '';
    this.startDateFilter = '';
    this.endDateFilter = '';
    this.appointmentCategoryFilter = '';
    this.currentPage = 1; // Voltar para a primeira página ao limpar filtros

    // Limpar os parâmetros da URL
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {},
      replaceUrl: true,
    });

    this.loadData(this.currentPage);
  }

  updateQueryParams(): void {
    const queryParams: any = {};

    if (this.searchTerm) {
      queryParams.search = this.searchTerm;
    }

    if (this.statusFilter) {
      queryParams.status = this.statusFilter;
    }

    if (this.dentistFilter) {
      queryParams.dentistId = this.dentistFilter;
    }

    if (this.startDateFilter) {
      queryParams.startDate = this.startDateFilter;
    }

    if (this.endDateFilter) {
      queryParams.endDate = this.endDateFilter;
    }

    if (this.appointmentCategoryFilter) {
      queryParams.appointmentCategoryId = this.appointmentCategoryFilter;
    }

    // Não incluir os parâmetros de paginação na URL
    // queryParams.page = this.currentPage;
    // queryParams.limit = this.itemsPerPage;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      replaceUrl: true, // Substituir a URL atual em vez de adicionar ao histórico
    });
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadData(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadData(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-pink-100 text-pink-800'; // Rosa para Agendado não confirmado
      case 'scheduled-confirmed':
        return 'bg-yellow-100 text-yellow-800'; // Amarelo para Agendado confirmado
      case 'unscheduled':
        return 'bg-purple-100 text-purple-800'; // Roxo para Desmarcado
      case 'in-progress':
        return 'bg-orange-100 text-orange-800'; // Laranja para Em andamento
      case 'completed':
        return 'bg-green-100 text-green-800'; // Verde para Concluído
      case 'cancelled':
        return 'bg-red-100 text-red-800'; // Vermelho para Cancelado
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmado';
      case 'unconfirmed':
        return 'Não confirmado';
      case 'late':
        return 'Atrasado';
      case 'no-show':
        return 'Não compareceu';
      case 'cancelled':
        return 'Desmarcado';
      case 'rescheduled':
        return 'Remarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      // Para compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      default:
        return status;
    }
  }

  formatDate(date: string | Date): string {
    if (!date) return '';

    if (date instanceof Date) {
      return date.toLocaleDateString('pt-BR');
    }

    // Converter para string se não for
    const dateStr = String(date);

    // Criar uma nova data a partir da string, sem ajustes de fuso horário
    const parts = dateStr.split('-');
    if (parts.length === 3) {
      // Se a data está no formato YYYY-MM-DD
      const year = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1; // Meses em JS são 0-indexed
      const day = parseInt(parts[2]);

      // Criar a data com os componentes individuais para evitar problemas de fuso horário
      return new Date(year, month, day).toLocaleDateString('pt-BR');
    }

    // Fallback para o método anterior
    const d = new Date(dateStr);
    return d.toLocaleDateString('pt-BR');
  }

  // Verifica se um agendamento pode ser editado (não pode ser editado se estiver concluído)
  canEditScheduling(scheduling: Scheduling): boolean {
    return scheduling.status !== 'completed';
  }

  viewScheduling(id: number): void {
    this.router.navigate(['/schedulings', id]);
  }

  editScheduling(id: number): void {
    this.router.navigate(['/schedulings', id, 'edit']);
  }

  // O método deleteScheduling foi removido pois agendamentos não podem ser excluídos
}
