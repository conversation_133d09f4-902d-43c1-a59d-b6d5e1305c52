import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SchedulingService } from '../../core/services/scheduling.service';
import { NotificationService } from '../../core/services/notification.service';
import { TreatmentPlanUpdateService } from '../../core/services/treatment-plan-update.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-scheduling-detail',
  templateUrl: './scheduling-detail.component.html',
  styleUrls: ['./scheduling-detail.component.scss'],
  standalone: false
})
export class SchedulingDetailComponent implements OnInit, OnDestroy {
  scheduling: any = null;
  isLoading = true;

  // Subscription para gerenciar a inscrição no serviço de atualização
  private treatmentPlanUpdateSubscription: Subscription | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private schedulingService: SchedulingService,
    private notificationService: NotificationService,
    private treatmentPlanUpdateService: TreatmentPlanUpdateService
  ) { }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      // Inscrever-se para receber atualizações do plano de tratamento
      this.treatmentPlanUpdateSubscription = this.treatmentPlanUpdateService.treatmentPlanUpdated$.subscribe(
        updatedPlan => {
          console.log('Plano de tratamento atualizado recebido no componente de detalhes:', updatedPlan);
          if (updatedPlan && this.scheduling && this.scheduling.treatmentPlan &&
              updatedPlan.id === this.scheduling.treatmentPlan.id) {
            console.log('Atualizando plano de tratamento na interface de detalhes');
            this.scheduling.treatmentPlan = updatedPlan;
          }
        }
      );

      this.loadScheduling(+id);
    } else {
      this.router.navigate(['/schedulings']);
    }
  }

  ngOnDestroy(): void {
    // Cancelar a inscrição para evitar vazamentos de memória
    if (this.treatmentPlanUpdateSubscription) {
      this.treatmentPlanUpdateSubscription.unsubscribe();
    }
  }

  loadScheduling(id: number): void {
    this.isLoading = true;
    this.schedulingService.getSchedulingById(id).subscribe({
      next: (scheduling: any) => {
        this.scheduling = scheduling;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Erro ao carregar agendamento:', error);
        this.notificationService.error('Erro ao carregar agendamento. Por favor, tente novamente.');
        this.isLoading = false;
        this.router.navigate(['/schedulings']);
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled-confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'unscheduled':
        return 'bg-red-100 text-red-800';
      case 'in-progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  }

  formatDate(date: string): string {
    if (!date) return '';

    // Criar uma nova data a partir da string, sem ajustes de fuso horário
    const parts = date.split('-');
    if (parts.length === 3) {
      // Se a data está no formato YYYY-MM-DD
      const year = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1; // Meses em JS são 0-indexed
      const day = parseInt(parts[2]);

      // Criar a data com os componentes individuais para evitar problemas de fuso horário
      return new Date(year, month, day).toLocaleDateString('pt-BR');
    }

    // Fallback para o método anterior
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR');
  }

  formatCurrency(value: number): string {
    if (value === undefined || value === null) return 'R$ 0,00';
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  goBack(): void {
    this.router.navigate(['/schedulings']);
  }

  editScheduling(): void {
    this.router.navigate(['/schedulings', this.scheduling.id, 'edit']);
  }

  executeProcedures(): void {
    this.router.navigate(['/schedulings', this.scheduling.id, 'execute']);
  }

  deleteScheduling(): void {
    if (confirm('Tem certeza que deseja excluir este agendamento?')) {
      this.schedulingService.deleteScheduling(this.scheduling.id).subscribe({
        next: () => {
          this.notificationService.success('Agendamento excluído com sucesso!');
          this.router.navigate(['/schedulings']);
        },
        error: (error) => {
          console.error('Erro ao excluir agendamento:', error);
          this.notificationService.error('Erro ao excluir agendamento. Por favor, tente novamente.');
        }
      });
    }
  }
}
