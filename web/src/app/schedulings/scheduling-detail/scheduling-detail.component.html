<div class="bg-white shadow rounded-lg p-6">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <div *ngIf="!isLoading && scheduling">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-900">Detalhes do Agendamento</h1>
      <div class="flex space-x-2">
        <button (click)="goBack()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Voltar
        </button>
        <button (click)="editScheduling()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Editar
        </button>
        <button *ngIf="scheduling.treatmentPlanId" (click)="executeProcedures()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          Executar Procedimentos
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Informações do Agendamento</h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Status:</span>
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" [ngClass]="getStatusClass(scheduling.status)">
              {{ getStatusText(scheduling.status) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data:</span>
            <span class="text-gray-900">{{ formatDate(scheduling.date) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Hora:</span>
            <span class="text-gray-900">{{ scheduling.time }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Valor:</span>
            <span class="text-gray-900">{{ formatCurrency(scheduling.cost) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Pago:</span>
            <span class="text-gray-900">{{ scheduling.paid ? 'Sim' : 'Não' }}</span>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Informações do Paciente</h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Nome:</span>
            <span class="text-gray-900">{{ scheduling.patientName }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Dentista:</span>
            <span class="text-gray-900">{{ scheduling.dentistName }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Motivo:</span>
            <span class="text-gray-900">{{ scheduling.motivo || 'Não especificado' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Observações</h2>
      <p class="text-gray-700 whitespace-pre-line">{{ scheduling.notes || 'Nenhuma observação registrada.' }}</p>
    </div>

    <!-- Plano de Tratamento -->
    <div *ngIf="scheduling.treatmentPlan" class="bg-blue-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Plano de Tratamento</h2>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-500">ID do Plano:</span>
          <span class="text-gray-900">#{{ scheduling.treatmentPlan.id }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500">Status:</span>
          <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
            [ngClass]="{
              'bg-green-100 text-green-800': scheduling.treatmentPlan.status === 'open',
              'bg-blue-100 text-blue-800': scheduling.treatmentPlan.status === 'completed',
              'bg-red-100 text-red-800': scheduling.treatmentPlan.status === 'cancelled'
            }">
            {{ scheduling.treatmentPlan.status }}
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500">Valor Total:</span>
          <span class="text-gray-900">{{ formatCurrency(scheduling.treatmentPlan.totalValue) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500">Progresso:</span>
          <div class="flex items-center">
            <div class="w-32 bg-gray-200 rounded-full h-2.5 mr-2">
              <div class="bg-blue-600 h-2.5 rounded-full" [style.width]="(scheduling.treatmentPlan.completionPercentage || 0) + '%'"></div>
            </div>
            <span>{{ scheduling.treatmentPlan.completionPercentage || 0 }}%</span>
          </div>
        </div>
        <div class="mt-4">
          <button (click)="executeProcedures()" class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Executar Procedimentos
          </button>
        </div>
      </div>
    </div>

    <!-- Mensagem se não houver plano de tratamento -->
    <div *ngIf="!scheduling.treatmentPlan && scheduling.treatmentPlanId" class="bg-yellow-50 p-4 rounded-lg mb-6">
      <div class="flex items-center">
        <svg class="h-6 w-6 text-yellow-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p class="text-yellow-700">Este agendamento está associado ao plano de tratamento #{{ scheduling.treatmentPlanId }}, mas não foi possível carregar os detalhes do plano.</p>
      </div>
    </div>

    <div class="flex justify-end">
      <button (click)="deleteScheduling()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Excluir Agendamento
      </button>
    </div>
  </div>
</div>
