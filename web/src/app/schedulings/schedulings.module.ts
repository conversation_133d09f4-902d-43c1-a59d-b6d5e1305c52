import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { SchedulingsRoutingModule } from './schedulings-routing.module';
import { SchedulingListComponent } from './scheduling-list/scheduling-list.component';
import { SchedulingDetailComponent } from './scheduling-detail/scheduling-detail.component';
import { SchedulingExecutionComponent } from './scheduling-execution/scheduling-execution.component';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [
    SchedulingListComponent,
    SchedulingDetailComponent,
    SchedulingExecutionComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    SchedulingsRoutingModule,
    SharedModule
  ]
})
export class SchedulingsModule { }
