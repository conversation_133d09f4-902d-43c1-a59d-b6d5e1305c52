import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SchedulingService } from '../../core/services/scheduling.service';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { NotificationService } from '../../core/services/notification.service';
import { TreatmentPlanUpdateService } from '../../core/services/treatment-plan-update.service';
import { Scheduling } from '../../core/models/scheduling.model';
import { TreatmentPlan } from '../../core/models/treatment-plan.model';
import { TreatmentProcedure, TreatmentProcedureStatus } from '../../core/models/treatment-procedure.model';
import { forkJoin, of, Subscription } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-scheduling-execution',
  templateUrl: './scheduling-execution.component.html',
  styleUrls: ['./scheduling-execution.component.scss'],
  standalone: false
})
export class SchedulingExecutionComponent implements OnInit, OnDestroy {
  isLoading = true;
  isSubmitting = false;
  schedulingId: number | null = null;
  scheduling: Scheduling | null = null;
  treatmentPlan: TreatmentPlan | null = null;
  selectedProcedures: number[] = [];

  // Subscription para gerenciar a inscrição no serviço de atualização
  private treatmentPlanUpdateSubscription: Subscription | null = null;

  // Mapeamento de status para exibição
  statusLabels: { [key: string]: string } = {
    'pending': 'Pendente',
    'scheduled': 'Agendado',
    'in_progress': 'Em andamento',
    'completed': 'Concluído',
    'cancelled': 'Cancelado',
    'open': 'Em Aberto'
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private schedulingService: SchedulingService,
    private treatmentPlanService: TreatmentPlanService,
    private notificationService: NotificationService,
    private treatmentPlanUpdateService: TreatmentPlanUpdateService
  ) {}

  ngOnInit(): void {
    this.isLoading = true;

    // Obter o ID do agendamento da URL
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.notificationService.error('ID do agendamento não fornecido');
      this.router.navigate(['/schedulings']);
      return;
    }

    this.schedulingId = +id;

    // Inscrever-se para receber atualizações do plano de tratamento
    this.treatmentPlanUpdateSubscription = this.treatmentPlanUpdateService.treatmentPlanUpdated$.subscribe(
      updatedPlan => {
        console.log('Plano de tratamento atualizado recebido:', updatedPlan);
        if (updatedPlan && this.treatmentPlan && updatedPlan.id === this.treatmentPlan.id) {
          console.log('Atualizando plano de tratamento na interface');
          // Criar uma cópia do plano atualizado para garantir que o Angular detecte as mudanças
          this.treatmentPlan = { ...updatedPlan };

          // Forçar a detecção de mudanças para atualizar a interface
          setTimeout(() => {
            console.log('Status do plano atualizado para:', this.treatmentPlan?.status);
            console.log('Porcentagem de conclusão atualizada para:', this.treatmentPlan?.completionPercentage);
          }, 0);
        }
      }
    );

    // Carregar o agendamento e o plano de tratamento associado
    this.schedulingService.getSchedulingById(this.schedulingId).pipe(
      switchMap(scheduling => {
        this.scheduling = scheduling;

        if (!scheduling.treatmentPlanId) {
          return of(null);
        }

        return this.treatmentPlanService.getTreatmentPlan(scheduling.treatmentPlanId).pipe(
          catchError(error => {
            console.error('Erro ao carregar plano de tratamento:', error);
            this.notificationService.error('Erro ao carregar plano de tratamento');
            return of(null);
          })
        );
      }),
      catchError(error => {
        console.error('Erro ao carregar agendamento:', error);
        this.notificationService.error('Erro ao carregar agendamento');
        return of(null);
      })
    ).subscribe(plan => {
      this.treatmentPlan = plan;
      this.isLoading = false;
    });
  }

  ngOnDestroy(): void {
    // Cancelar a inscrição para evitar vazamentos de memória
    if (this.treatmentPlanUpdateSubscription) {
      this.treatmentPlanUpdateSubscription.unsubscribe();
    }
  }

  // Verificar se um procedimento está selecionado
  isProcedureSelected(procedureId: number | undefined): boolean {
    if (procedureId === undefined) return false;
    return this.selectedProcedures.includes(procedureId);
  }

  // Alternar a seleção de um procedimento
  toggleProcedureSelection(procedureId: number | undefined): void {
    if (procedureId === undefined) return;

    if (this.isProcedureSelected(procedureId)) {
      this.selectedProcedures = this.selectedProcedures.filter(id => id !== procedureId);
    } else {
      this.selectedProcedures.push(procedureId);
    }
  }

  // Verificar se um procedimento pode ser executado (está pendente ou em andamento)
  canExecuteProcedure(procedure: TreatmentProcedure): boolean {
    return procedure.status === TreatmentProcedureStatus.PENDING ||
           procedure.status === TreatmentProcedureStatus.IN_PROGRESS;
  }

  // Marcar os procedimentos selecionados como concluídos
  executeProcedures(): void {
    if (this.selectedProcedures.length === 0) {
      this.notificationService.warning('Selecione pelo menos um procedimento para executar');
      return;
    }

    this.isSubmitting = true;

    // Criar um array de observáveis para cada procedimento a ser atualizado
    const updateObservables = this.selectedProcedures.map(procedureId => {
      return this.treatmentPlanService.updateProcedure(
        procedureId,
        {
          status: TreatmentProcedureStatus.COMPLETED,
          executionDate: new Date(),
          appointmentId: this.schedulingId
        }
      ).pipe(
        catchError(error => {
          console.error(`Erro ao atualizar procedimento ${procedureId}:`, error);
          return of(null);
        })
      );
    });

    // Executar todas as atualizações em paralelo
    forkJoin(updateObservables).subscribe({
      next: results => {
        const successCount = results.filter(result => result !== null).length;

        if (successCount === this.selectedProcedures.length) {
          // Não exibimos a notificação aqui porque já é exibida no serviço
          console.log(`${successCount} procedimento(s) concluído(s) com sucesso`);
        } else {
          this.notificationService.warning(`${successCount} de ${this.selectedProcedures.length} procedimentos foram concluídos`);
        }

        // Limpar a seleção de procedimentos
        this.selectedProcedures = [];
        this.isSubmitting = false;

        // Recarregar o plano de tratamento para garantir que temos os dados mais atualizados
        if (this.treatmentPlan?.id) {
          console.log('Recarregando plano de tratamento após execução de procedimentos');
          this.treatmentPlanService.getTreatmentPlan(this.treatmentPlan.id).subscribe(
            updatedPlan => {
              console.log('Plano de tratamento recarregado:', updatedPlan);
              // Atualizar o plano de tratamento na interface
              this.treatmentPlan = { ...updatedPlan };
            },
            error => {
              console.error('Erro ao recarregar plano de tratamento:', error);
            }
          );
        }
      },
      error: error => {
        console.error('Erro ao executar procedimentos:', error);
        this.notificationService.error('Erro ao executar procedimentos');
        this.isSubmitting = false;
      }
    });
  }

  // Voltar para a página de detalhes do agendamento
  goBack(): void {
    this.router.navigate(['/schedulings', this.schedulingId]);
  }
}
