<div class="container bg-white shadow rounded-lg p-6 mx-auto">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <div *ngIf="!isLoading">
    <div class="flex items-center mb-6 gap-6">
      <button
        (click)="goBack()"
        class="text-blue-600 hover:text-blue-800 mr-2"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
      <h1 class="text-2xl font-semibold text-gray-900">
        Execução de Procedimentos
      </h1>
    </div>

    <!-- Informações do Agendamento -->
    <div class="bg-blue-50 p-4 rounded-lg shadow-sm mb-6">
      <h2 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">
        Informações do Agendamento
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex flex-col">
          <span class="font-medium text-gray-700">Paciente:</span>
          <span class="text-gray-900">{{ scheduling?.patientName }}</span>
        </div>
        <div class="flex flex-col">
          <span class="font-medium text-gray-700">Dentista:</span>
          <span class="text-gray-900">{{ scheduling?.dentistName }}</span>
        </div>
        <div class="flex flex-col">
          <span class="font-medium text-gray-700">Data e Hora:</span>
          <span class="text-gray-900">{{ scheduling?.date | date:'dd/MM/yyyy' }} às {{ scheduling?.time }}</span>
        </div>
      </div>
    </div>

    <!-- Mensagem se não houver plano de tratamento -->
    <div *ngIf="!treatmentPlan" class="bg-yellow-50 p-4 rounded-lg shadow-sm mb-6">
      <div class="flex items-center">
        <svg class="h-6 w-6 text-yellow-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p class="text-yellow-700">Este agendamento não está associado a nenhum plano de tratamento.</p>
      </div>
    </div>

    <!-- Plano de Tratamento e Procedimentos -->
    <div *ngIf="treatmentPlan" class="space-y-6">
      <!-- Mensagem de plano concluído -->
      <div *ngIf="treatmentPlan.status === 'completed'" class="bg-green-100 p-4 rounded-lg shadow-sm mb-4">
        <div class="flex items-center">
          <svg class="h-6 w-6 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <p class="text-green-700 font-medium">Este plano de tratamento foi concluído com sucesso!</p>
        </div>
      </div>

      <!-- Informações do Plano -->
      <div class="bg-green-50 p-4 rounded-lg shadow-sm">
        <h2 class="text-lg font-medium mb-3 text-gray-800 border-b pb-2">
          Plano de Tratamento #{{ treatmentPlan.id }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Status:</span>
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1"
              [ngClass]="{
                'bg-blue-100 text-blue-800': treatmentPlan.status === 'open',
                'bg-green-100 text-green-800': treatmentPlan.status === 'completed',
                'bg-red-100 text-red-800': treatmentPlan.status === 'cancelled'
              }"
            >
              {{ statusLabels[treatmentPlan.status] || treatmentPlan.status }}
            </span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Valor Total:</span>
            <span class="text-gray-900">R$ {{ treatmentPlan.totalValue | number:'1.2-2' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Progresso:</span>
            <div class="flex items-center">
              <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                <div class="bg-blue-600 h-2.5 rounded-full" [style.width]="(treatmentPlan.completionPercentage || 0) + '%'"></div>
              </div>
              <span>{{ treatmentPlan.completionPercentage || 0 }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Lista de Procedimentos -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="p-4 border-b">
          <h2 class="text-lg font-medium text-gray-800">
            Procedimentos do Plano
          </h2>
          <p class="text-sm text-gray-600 mt-1">
            Selecione os procedimentos que foram executados neste atendimento
          </p>
        </div>

        <div class="divide-y divide-gray-200">
          <div *ngFor="let procedure of treatmentPlan.procedures" class="p-4 hover:bg-gray-50">
            <div class="flex items-center">
              <input
                type="checkbox"
                [id]="'procedure-' + (procedure.id || 'unknown')"
                [checked]="isProcedureSelected(procedure.id)"
                (change)="toggleProcedureSelection(procedure.id)"
                [disabled]="!canExecuteProcedure(procedure) || isSubmitting"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label [for]="'procedure-' + (procedure.id || 'unknown')" class="ml-3 flex-1">
                <div class="flex flex-col sm:flex-row sm:justify-between">
                  <div>
                    <span class="text-gray-900 font-medium">{{ procedure.name }}</span>
                    <span *ngIf="procedure.tooth" class="ml-2 text-gray-500">(Dente {{ procedure.tooth }})</span>
                  </div>
                  <div class="mt-1 sm:mt-0">
                    <span class="text-gray-700">R$ {{ procedure.value | number:'1.2-2' }}</span>
                  </div>
                </div>
                <div class="mt-1 flex items-center">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    [ngClass]="{
                      'bg-yellow-100 text-yellow-800': procedure.status === 'pending',
                      'bg-orange-100 text-orange-800': procedure.status === 'in_progress',
                      'bg-green-100 text-green-800': procedure.status === 'completed',
                      'bg-red-100 text-red-800': procedure.status === 'cancelled'
                    }"
                  >
                    {{ statusLabels[procedure.status] }}
                  </span>
                  <span *ngIf="procedure.executionDate" class="ml-2 text-xs text-gray-500">
                    Executado em {{ procedure.executionDate | date:'dd/MM/yyyy' }}
                  </span>
                </div>
              </label>
            </div>
          </div>
        </div>

        <div class="p-4 border-t bg-gray-50 flex justify-end">
          <button
            (click)="executeProcedures()"
            [disabled]="selectedProcedures.length === 0 || isSubmitting"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
            [class.opacity-50]="selectedProcedures.length === 0 || isSubmitting"
          >
            <svg
              *ngIf="isSubmitting"
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <svg *ngIf="!isSubmitting" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Marcar como Concluídos
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
