import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SchedulingListComponent } from './scheduling-list/scheduling-list.component';
import { SchedulingDetailComponent } from './scheduling-detail/scheduling-detail.component';
import { SchedulingExecutionComponent } from './scheduling-execution/scheduling-execution.component';

const routes: Routes = [
  { path: '', component: SchedulingListComponent },
  {
    path: 'new',
    loadComponent: () => import('./scheduling-form/scheduling-form.component').then(m => m.SchedulingFormComponent)
  },
  {
    path: 'board',
    loadComponent: () => import('./scheduling-board/scheduling-board.component').then(m => m.SchedulingBoardComponent)
  },
  { path: ':id', component: SchedulingDetailComponent },
  {
    path: ':id/edit',
    loadComponent: () => import('./scheduling-form/scheduling-form.component').then(m => m.SchedulingFormComponent)
  },
  { path: ':id/execute', component: SchedulingExecutionComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SchedulingsRoutingModule { }
