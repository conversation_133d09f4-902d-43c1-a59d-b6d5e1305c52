<div class="container bg-white shadow rounded-lg p-6 mx-auto">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <div *ngIf="!isLoading">
    <!-- <PERSON><PERSON><PERSON><PERSON> e botão voltar (apenas quando não usado como modal) -->
    <div *ngIf="!useAsModal" class="flex items-center mb-6 gap-6">
      <a
        routerLink="/treatments"
        class="text-blue-600 hover:text-blue-800 mr-2"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </a>
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ isEditMode ? "Editar" : "Novo" }} Agendamento
      </h1>
    </div>

    <!-- Usar o componente puro do formulário -->
    <app-appointment-form
      [appointment]="appointment"
      [isEditMode]="isEditMode"
      [initialDate]="initialDate"
      [initialTime]="initialTime"
      [initialDentistId]="initialDentistId"
      [showSubmitButtons]="!useAsModal"
      (formSubmit)="onFormSubmit($event)"
      (formCancel)="cancel()"
    ></app-appointment-form>
  </div>
</div>

