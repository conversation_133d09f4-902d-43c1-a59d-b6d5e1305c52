.board-container {
  overflow-x: auto;
  padding-bottom: 1rem;
  cursor: grab;

  &.active {
    cursor: grabbing;
  }

  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }
}

.board {
  display: flex;
  min-height: calc(100vh - 200px);
  padding: 0.5rem 0;
}

.board-column {
  min-width: 320px;
  max-width: 320px;
  margin-right: 1.25rem;
  display: flex;
  flex-direction: column;
  min-height: 400px;

  @media (max-width: 768px) {
    min-width: 280px;
    max-width: 280px;
  }

  &:last-child {
    margin-right: 0.5rem;
  }
}

.board-column-header {
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
}

.board-column-content {
  min-height: 150px;
  transition: background-color 0.2s ease;
  flex: 1;

  &.cdk-drop-list-dragging {
    background-color: #f9fafb;
  }

  &.cdk-drop-list-receiving {
    background-color: #ebf4ff;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }
}

.scheduling-card {
  transition: all 0.2s ease;
  cursor: move;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  &.cdk-drag-preview {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    opacity: 0.9;
    z-index: 100;
  }

  &.cdk-drag-placeholder {
    opacity: 0.3;
  }

  &.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
}

.cdk-drop-list-dragging .scheduling-card:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.non-draggable {
  user-select: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
  backdrop-filter: blur(2px);
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: rgba(59, 130, 246, 1);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
