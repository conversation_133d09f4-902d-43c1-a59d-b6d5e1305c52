import { Component, OnInit } from '@angular/core';
import { CommonModule, NgClass, NgFor, NgIf, DecimalPipe, DOCUMENT } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { SchedulingService } from '../../core/services/scheduling.service';
import { DentistService } from '../../core/services/dentist.service';
import { NotificationService } from '../../core/services/notification.service';
import { Scheduling } from '../../core/models/scheduling.model';
import { Dentist } from '../../core/models/dentist.model';
import { SchedulingStatus } from '../../core/enums/scheduling-status.enum';

@Component({
  selector: 'app-scheduling-board',
  templateUrl: './scheduling-board.component.html',
  styleUrls: ['./scheduling-board.component.scss'],
  standalone: true,
  imports: [CommonModule, NgClass, NgFor, NgIf, DecimalPipe, RouterLink, DragDropModule, ModalComponent],
  providers: [
    { provide: DOCUMENT, useFactory: () => document }
  ]
})
export class SchedulingBoardComponent implements OnInit {
  // Dados
  schedulings: Scheduling[] = [];
  dentists: Dentist[] = [];
  selectedDentistId: number | null = null;

  // Estado da UI
  isLoading = true;
  selectedScheduling: Scheduling | null = null;
  isModalOpen = false;
  isDragging = false;
  isDraggingBoard = false;

  // Propriedades para arrastar o board
  private startX = 0;
  private scrollLeft = 0;
  private boardContainer: HTMLElement | null = null;

  // Enums e constantes
  SchedulingStatus = SchedulingStatus;

  // Mapeamento de status para exibição
  statusLabels: { [key: string]: string } = {
    'confirmed': 'Confirmado',
    'unconfirmed': 'Não confirmado',
    'late': 'Atrasado',
    'no-show': 'Não compareceu',
    'cancelled': 'Desmarcado',
    'rescheduled': 'Remarcado',
    'in-progress': 'Em andamento',
    'completed': 'Concluído',
    // Para compatibilidade com registros antigos
    'scheduled-unconfirmed': 'Agendado não confirmado',
    'scheduled-confirmed': 'Agendado confirmado',
    'unscheduled': 'Desmarcado',
    'scheduled': 'Agendado'
  };

  // Mapeamento de status para cores de cards
  statusColors: { [key: string]: string } = {
    'confirmed': 'bg-green-100 border-green-300',
    'unconfirmed': 'bg-yellow-100 border-yellow-300',
    'late': 'bg-orange-100 border-orange-300',
    'no-show': 'bg-red-100 border-red-300',
    'cancelled': 'bg-gray-100 border-gray-300',
    'rescheduled': 'bg-blue-100 border-blue-300',
    'in-progress': 'bg-indigo-100 border-indigo-300',
    'completed': 'bg-emerald-100 border-emerald-300',
    // Para compatibilidade com registros antigos
    'scheduled-unconfirmed': 'bg-pink-100 border-pink-300',
    'scheduled-confirmed': 'bg-yellow-100 border-yellow-300',
    'unscheduled': 'bg-purple-100 border-purple-300',
    'scheduled': 'bg-pink-100 border-pink-300'
  };

  // Mapeamento de status para cores de bordas
  borderColors: { [key: string]: string } = {
    'confirmed': 'border-green-500',
    'unconfirmed': 'border-yellow-500',
    'late': 'border-orange-500',
    'no-show': 'border-red-500',
    'cancelled': 'border-gray-500',
    'rescheduled': 'border-blue-500',
    'in-progress': 'border-indigo-500',
    'completed': 'border-emerald-500',
    // Para compatibilidade com registros antigos
    'scheduled-unconfirmed': 'border-pink-500',
    'scheduled-confirmed': 'border-yellow-500',
    'unscheduled': 'border-purple-500',
    'scheduled': 'border-pink-500'
  };

  // Mapeamento de status para cores de cabeçalhos
  headerColors: { [key: string]: string } = {
    'confirmed': 'bg-green-600',
    'unconfirmed': 'bg-yellow-600',
    'late': 'bg-orange-600',
    'no-show': 'bg-red-600',
    'cancelled': 'bg-gray-600',
    'rescheduled': 'bg-blue-600',
    'in-progress': 'bg-indigo-600',
    'completed': 'bg-emerald-600',
    // Para compatibilidade com registros antigos
    'scheduled-unconfirmed': 'bg-pink-600',
    'scheduled-confirmed': 'bg-yellow-600',
    'unscheduled': 'bg-purple-600',
    'scheduled': 'bg-pink-600'
  };

  // Ordem dos status para exibição no board
  statusOrder: string[] = [
    'unconfirmed',
    'confirmed',
    'late',
    'in-progress',
    'completed',
    'no-show',
    'cancelled',
    'rescheduled'
  ];

  // Configurações de tipos de paciente para exibição
  patientTypeConfigs: { [key: string]: { name: string; color: string; bgColor: string; textColor: string } } = {
    'Particular': {
      name: 'Particular',
      color: '#1E40AF',
      bgColor: 'bg-blue-600',
      textColor: 'text-white'
    },
    'Convênio': {
      name: 'Convênio',
      color: '#059669',
      bgColor: 'bg-emerald-600',
      textColor: 'text-white'
    },
    'SUS': {
      name: 'SUS',
      color: '#7C3AED',
      bgColor: 'bg-violet-600',
      textColor: 'text-white'
    },
    'Ouro': {
      name: 'Ouro',
      color: '#D97706',
      bgColor: 'bg-amber-600',
      textColor: 'text-white'
    },
    'Safira': {
      name: 'Safira',
      color: '#0EA5E9',
      bgColor: 'bg-sky-600',
      textColor: 'text-white'
    },
    'Diamante': {
      name: 'Diamante',
      color: '#8B5CF6',
      bgColor: 'bg-purple-600',
      textColor: 'text-white'
    },
    'default': {
      name: 'Não definido',
      color: '#6B7280',
      bgColor: 'bg-gray-500',
      textColor: 'text-white'
    }
  };

  constructor(
    private schedulingService: SchedulingService,
    private dentistService: DentistService,
    private notificationService: NotificationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadDentists();
    this.loadSchedulings();
  }

  // Carregar todos os dentistas
  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.notificationService.error('Erro ao carregar dentistas. Por favor, tente novamente.');
      }
    });
  }

  // Carregar todos os agendamentos
  loadSchedulings(): void {
    this.isLoading = true;

    // Adicionar parâmetro noPagination=true para garantir que recebemos um array
    this.schedulingService.getAllSchedulings().subscribe({
      next: (response: any) => {
        console.log('Resposta da API:', response);

        // Verificar se a resposta é um array ou um objeto paginado
        let schedulings: Scheduling[] = [];

        if (Array.isArray(response)) {
          schedulings = response;
        } else if (response && typeof response === 'object' && 'data' in response) {
          // É um objeto paginado
          schedulings = response.data;
        }

        // Garantir que todos os agendamentos tenham um status válido e nomes de paciente/dentista
        schedulings = schedulings.map(scheduling => {
          // Se o agendamento não tiver status, definir um status padrão
          if (!scheduling.status) {
            console.log('Agendamento sem status, definindo status padrão:', scheduling);
            return {
              ...scheduling,
              status: 'unconfirmed' // Status padrão
            };
          }

          // Garantir que patientName e dentistName estejam definidos
          return {
            ...scheduling,
            patientName: scheduling.patientName || 'Paciente não encontrado',
            dentistName: scheduling.dentistName || 'Dentista não encontrado'
          };
        });

        // Filtrar apenas agendamentos com plano de tratamento, mas não aplicar o filtro se não houver nenhum
        if (schedulings.length > 0) {
          const withTreatmentPlan = schedulings.filter(scheduling => scheduling.treatmentPlanId);
          // Se não houver nenhum agendamento com plano de tratamento, mostrar todos
          this.schedulings = withTreatmentPlan.length > 0 ? withTreatmentPlan : schedulings;
        } else {
          this.schedulings = schedulings;
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar agendamentos:', error);
        this.notificationService.error('Erro ao carregar agendamentos. Por favor, tente novamente.');
        this.isLoading = false;
      }
    });
  }

  // Filtrar agendamentos por dentista
  filterByDentist(dentistId: number | null): void {
    this.selectedDentistId = dentistId;
    this.loadSchedulings();
  }

  // Obter agendamentos por status
  getSchedulingsByStatus(status: string): Scheduling[] {
    if (!this.schedulings) {
      return [];
    }

    // Verificar se há agendamentos com o status especificado
    const schedulingsWithStatus = this.schedulings.filter(scheduling => {
      // Verificar se o agendamento tem a propriedade status
      if (!scheduling.status) {
        return false;
      }

      // Filtrar por dentista se um dentista estiver selecionado
      const dentistMatch = this.selectedDentistId ? scheduling.dentistId === this.selectedDentistId : true;

      // Filtrar por status
      const statusMatch = scheduling.status === status;

      return statusMatch && dentistMatch;
    });

    return schedulingsWithStatus;
  }

  // Abrir modal de detalhes do agendamento
  openSchedulingDetails(scheduling: Scheduling): void {
    this.isLoading = true;

    // Carregar os detalhes completos do agendamento
    this.schedulingService.getSchedulingById(scheduling.id).subscribe({
      next: (detailedScheduling) => {
        this.selectedScheduling = detailedScheduling;

        // Se o agendamento tem um plano de tratamento, carregar os detalhes do plano
        if (detailedScheduling.treatmentPlanId) {
          this.loadTreatmentPlanDetails(detailedScheduling);
        } else {
          this.isLoading = false;
          this.isModalOpen = true;
        }
      },
      error: (error) => {
        console.error('Erro ao carregar detalhes do agendamento:', error);
        this.notificationService.error('Erro ao carregar detalhes do agendamento. Por favor, tente novamente.');
        this.isLoading = false;
      }
    });
  }

  // Carregar detalhes do plano de tratamento
  loadTreatmentPlanDetails(scheduling: Scheduling): void {
    // Implementar chamada para o serviço que busca os detalhes do plano de tratamento
    // Incluindo os procedimentos associados ao agendamento
    this.schedulingService.getSchedulingProcedures(scheduling.id).subscribe({
      next: (procedures: any[]) => {
        if (this.selectedScheduling) {
          this.selectedScheduling.treatmentProcedures = procedures;
        }
        this.isLoading = false;
        this.isModalOpen = true;
      },
      error: (error: any) => {
        console.error('Erro ao carregar procedimentos do agendamento:', error);
        // Não mostrar erro para o usuário, apenas abrir o modal sem os procedimentos
        this.isLoading = false;
        this.isModalOpen = true;
      }
    });
  }

  // Fechar modal de detalhes
  closeModal(): void {
    this.isModalOpen = false;
    this.selectedScheduling = null;
  }

  // Formatar data para exibição
  formatDate(date: Date | string): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR');
  }

  // Obter classe CSS para o status do card
  getStatusClass(status: string): string {
    return this.statusColors[status] || 'bg-gray-100 border-gray-300';
  }

  // Obter classe CSS para a borda do card
  getBorderClass(status: string): string {
    return this.borderColors[status] || 'border-gray-500';
  }

  // Obter classe CSS para o cabeçalho da coluna
  getHeaderClass(status: string): string {
    return this.headerColors[status] || 'bg-gray-600';
  }

  // Obter texto para o status
  getStatusText(status: string): string {
    return this.statusLabels[status] || status;
  }

  // Obter configuração do tipo de paciente
  getPatientTypeConfig(scheduling: Scheduling): { name: string; color: string; bgColor: string; textColor: string } {
    // Tentar extrair o tipo do paciente dos dados disponíveis
    let patientTypeName = 'default';

    // Verificar se há informação do tipo de paciente no agendamento
    if (scheduling.patient?.patientType?.nome) {
      patientTypeName = scheduling.patient.patientType.nome;
    }

    return this.patientTypeConfigs[patientTypeName] || this.patientTypeConfigs['default'];
  }

  // Obter nome do tipo de paciente
  getPatientTypeName(scheduling: Scheduling): string {
    const config = this.getPatientTypeConfig(scheduling);
    return config.name;
  }

  // Ordenar procedimentos: primeiro em andamento, depois pendentes, por último concluídos
  getSortedProcedures(): any[] {
    if (!this.selectedScheduling || !this.selectedScheduling.treatmentPlan || !this.selectedScheduling.treatmentPlan.procedures) {
      return [];
    }

    // Criar uma cópia dos procedimentos para não modificar o original
    const procedures = [...this.selectedScheduling.treatmentPlan.procedures];

    // Definir a ordem de prioridade dos status
    const statusOrder = {
      'in_progress': 1,
      'pending': 2,
      'completed': 3,
      'cancelled': 4
    };

    // Ordenar os procedimentos com base na ordem de prioridade dos status
    return procedures.sort((a, b) => {
      return (statusOrder[a.status] || 999) - (statusOrder[b.status] || 999);
    });
  }

  // Método para lidar com o drop de um card
  onDrop(event: CdkDragDrop<Scheduling[]>) {
    // Resetar o estado de arraste
    this.isDragging = false;

    if (event.previousContainer === event.container) {
      // Se o card foi movido dentro da mesma coluna, apenas reordenar
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Obter o agendamento que foi movido
      const scheduling = event.previousContainer.data[event.previousIndex];

      // Determinar o novo status baseado na lista de destino
      const containerId = event.container.id;
      const newStatus = containerId.replace('list-', '');

      // Verificar se o status é diferente
      if (scheduling.status !== newStatus) {
        // Atualizar o status do agendamento
        this.updateSchedulingStatus(scheduling, newStatus);
      }

      // Transferir o item entre as listas
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }

  // Método para atualizar o status de um agendamento
  updateSchedulingStatus(scheduling: Scheduling, newStatus: string) {
    this.isLoading = true;

    // Converter o status para o tipo correto (SchedulingStatus)
    const statusValue = newStatus as SchedulingStatus;

    // Criar um objeto com apenas o status para atualizar
    const update = { status: statusValue };

    this.schedulingService.updateScheduling(scheduling.id, update).subscribe({
      next: (updatedScheduling) => {
        // Atualizar o status do agendamento na lista local
        scheduling.status = statusValue;
        this.isLoading = false;
        this.notificationService.success(`Status do agendamento atualizado para ${this.getStatusText(newStatus)}`);
      },
      error: (error) => {
        console.error('Erro ao atualizar status do agendamento:', error);
        this.notificationService.error('Erro ao atualizar status do agendamento. Por favor, tente novamente.');
        this.isLoading = false;

        // Recarregar os agendamentos para restaurar o estado anterior
        this.loadSchedulings();
      }
    });
  }

  // Método para navegar para a página do paciente
  navigateToPatient(patientId: number) {
    this.router.navigate(['/patients', patientId]);
  }

  // Método para obter a lista de IDs dos drop lists conectados
  getConnectedLists(): string[] {
    return this.statusOrder.map(s => 'list-' + s);
  }

  // Métodos para arrastar o board com o mouse
  startDragging(event: MouseEvent): void {
    // Verificar se o clique foi em um elemento que não deve iniciar o arraste
    const target = event.target as HTMLElement;
    if (target.closest('.scheduling-card') || target.closest('button') || target.closest('a') || target.closest('input') || target.closest('select')) {
      return;
    }

    this.isDraggingBoard = true;
    this.boardContainer = event.currentTarget as HTMLElement;
    this.startX = event.pageX - this.boardContainer.offsetLeft;
    this.scrollLeft = this.boardContainer.scrollLeft;
  }

  onDrag(event: MouseEvent): void {
    if (!this.isDraggingBoard || !this.boardContainer) return;

    event.preventDefault();
    const x = event.pageX - this.boardContainer.offsetLeft;
    const walk = (x - this.startX) * 2; // Multiplicador para ajustar a velocidade do arraste
    this.boardContainer.scrollLeft = this.scrollLeft - walk;
  }

  stopDragging(): void {
    this.isDraggingBoard = false;
  }
}
