<div class="bg-white shadow rounded-lg p-6 ">
  <div class="relative">
    <!-- Loading overlay -->
    <div *ngIf="isLoading" class="loading-overlay">
      <div class="spinner"></div>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON>ho -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-4">Board de Atendimentos</h1>

      <!-- Filt<PERSON> de dentista -->
      <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <div class="w-full sm:w-64">
          <label for="dentist-filter" class="block text-sm font-medium text-gray-700 mb-1">Filtrar por Dentista</label>
          <select
            id="dentist-filter"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            (change)="filterByDentist($any($event.target).value ? +$any($event.target).value : null)"
          >
            <option value="">Todos os Dentistas</option>
            <option *ngFor="let dentist of dentists" [value]="dentist.id">{{ dentist.name }}</option>
          </select>
        </div>

        <button
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mt-4 sm:mt-6"
          (click)="loadSchedulings()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          Atualizar
        </button>
      </div>
    </div>

    <!-- Board Kanban -->
    <div class="board-container"
         (mousedown)="startDragging($event)"
         (mousemove)="onDrag($event)"
         (mouseup)="stopDragging()"
         (mouseleave)="stopDragging()"
         [class.active]="isDraggingBoard">
      <div class="board non-draggable">
        <!-- Colunas para cada status -->
        <div *ngFor="let status of statusOrder" class="board-column">
          <!-- Cabeçalho da coluna -->
          <div class="board-column-header p-3 rounded-t-lg shadow-sm" [ngClass]="getHeaderClass(status)">
            <h2 class="font-semibold text-white">
              {{ getStatusText(status) }}
              <span class="ml-2 bg-white text-gray-700 rounded-full px-2 py-0.5 text-xs font-bold">
                {{ getSchedulingsByStatus(status).length }}
              </span>
            </h2>
          </div>

          <!-- Conteúdo da coluna -->
          <div
            class="board-column-content p-3 space-y-3 bg-white border border-gray-200 border-t-0 rounded-b-lg shadow-sm min-h-[200px] h-[calc(100vh-220px)] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
            cdkDropList
            [cdkDropListData]="getSchedulingsByStatus(status)"
            [id]="'list-' + status"
            [cdkDropListConnectedTo]="getConnectedLists()"
            (cdkDropListDropped)="onDrop($event)"
          >
            <!-- Mensagem quando não há agendamentos -->
            <div *ngIf="getSchedulingsByStatus(status).length === 0" class="p-3 bg-gray-50 rounded-md border border-gray-200 text-gray-500 text-center text-sm">
              Nenhum atendimento
            </div>

            <!-- Cards de agendamentos -->
            <div
              *ngFor="let scheduling of getSchedulingsByStatus(status)"
              class="scheduling-card p-3 rounded-md border-l-4 bg-gray-50 cursor-pointer shadow-sm hover:shadow-md transition-all"
              [ngClass]="getBorderClass(scheduling.status)"
              cdkDrag
              [cdkDragData]="scheduling"
              (cdkDragStarted)="isDragging = true"
              (click)="openSchedulingDetails(scheduling)"
            >
              <!-- Cabeçalho do card -->
              <div class="flex justify-between items-start mb-2">
                <div class="flex-1 min-w-0">
                  <!-- Tipo de paciente com destaque -->
                  <div class="mb-1">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold shadow-sm"
                      [ngClass]="[getPatientTypeConfig(scheduling).bgColor, getPatientTypeConfig(scheduling).textColor]"
                    >
                      {{ getPatientTypeName(scheduling) }}
                    </span>
                  </div>
                  <h3 class="font-medium text-gray-900 truncate">{{ scheduling.patientName }}</h3>
                  
                </div>
                <div class="flex space-x-1 ml-2">
                  <button
                    class="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50"
                    (click)="openSchedulingDetails(scheduling); $event.stopPropagation();"
                    title="Ver detalhes"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Informações de data e hora -->
              <p class="text-sm text-gray-600">
                <span class="inline-block mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ formatDate(scheduling.date) }}
                </span>
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                  </svg>
                  {{ scheduling.time }}
                </span>
              </p>

              <!-- Informações do dentista -->
              <div class="flex items-center text-sm text-gray-700 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
                <span class="truncate">{{ scheduling.dentistName }}</span>
              </div>

              <!-- Informações do plano de tratamento -->
              <div *ngIf="scheduling.treatmentPlanId" class="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-600">
                <span class="inline-flex items-center px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                  Plano #{{ scheduling.treatmentPlanId }}
                </span>
                <span *ngIf="scheduling.paid" class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                  Pago
                </span>
                <span *ngIf="!scheduling.paid" class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full bg-red-100 text-red-800">
                  Não pago
                </span>
              </div>

              <!-- Preview ao arrastar -->
              <div *cdkDragPreview class="scheduling-card p-3 rounded-md border-l-4 bg-gray-50 shadow-lg" [ngClass]="getBorderClass(scheduling.status)">
                <h3 class="font-medium text-gray-900">{{ scheduling.patientName }}</h3>
                <!-- Tipo de paciente no preview -->
                <div class="mt-1 mb-2">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold shadow-sm"
                    [ngClass]="[getPatientTypeConfig(scheduling).bgColor, getPatientTypeConfig(scheduling).textColor]"
                  >
                    {{ getPatientTypeName(scheduling) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600">{{ formatDate(scheduling.date) }} às {{ scheduling.time }}</p>
                <div class="text-xs text-gray-500 mt-1">{{ scheduling.dentistName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de detalhes do agendamento -->
  <app-modal
    [title]="'Detalhes do Atendimento'"
    [isOpen]="isModalOpen"
    (close)="closeModal()"
  >
    <!-- Conteúdo do modal -->
    <div *ngIf="selectedScheduling" class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
        <!-- Informações do agendamento -->
        <div class="flex flex-col h-full">
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-2">
              <h3 class="text-lg font-medium text-gray-900">Informações do Agendamento</h3>
              <a
                [routerLink]="['/schedulings', selectedScheduling.id]"
                class="text-gray-600 hover:text-gray-800 p-2 rounded-full hover:bg-gray-50 transition-colors"
                title="Ver detalhes completos do agendamento"
                target="_blank"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 shadow-sm flex-grow">
            <div class="space-y-3">
              <div class="flex items-end gap-4 justify-between">
                <div>
                  <span class="block text-sm font-medium text-gray-700">Paciente</span>
                <span class="block text-base text-gray-900 font-semibold">{{ selectedScheduling.patientName }}</span>
                </div>
                <a
                  [routerLink]="['/patients', selectedScheduling.patientId]"
                  class="text-blue-600 hover:text-blue-800 p-2 rounded-full hover:bg-blue-50 transition-colors"
                  title="Ver perfil do paciente"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>
              <div>
                <span class="block text-sm font-medium text-gray-700">Dentista</span>
                <span class="block text-base text-gray-900">{{ selectedScheduling.dentistName }}</span>
              </div>
              <div>
                <span class="block text-sm font-medium text-gray-700">Data e Hora</span>
                <span class="block text-base text-gray-900">
                  {{ formatDate(selectedScheduling.date) }} às {{ selectedScheduling.time }}
                </span>
              </div>
              <div *ngIf="selectedScheduling.notes">
                <span class="block text-sm font-medium text-gray-700">Observações</span>
                <span class="block text-base text-gray-900">{{ selectedScheduling.notes }}</span>
              </div>
              <div>
                <span class="block text-sm font-medium text-gray-700">Valor</span>
                <div class="flex items-center">
                  <span class="text-base text-gray-900 font-semibold">
                    R$ {{ selectedScheduling.cost | number:'1.2-2' }}
                  </span>
                  <span *ngIf="selectedScheduling.paid" class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800 text-xs">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    Pago
                  </span>
                  <span *ngIf="!selectedScheduling.paid" class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full bg-red-100 text-red-800 text-xs">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                    Não pago
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Informações do plano de tratamento -->
        <div *ngIf="selectedScheduling.treatmentPlan" class="flex flex-col h-full">
          <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-900">Plano de Tratamento #{{ selectedScheduling.treatmentPlanId }}</h3>
          </div>
          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 shadow-sm flex-grow flex flex-col justify-between">
            <div class="space-y-4">
              <div>
                <span class="block text-sm font-medium text-gray-700">Status do Plano</span>
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1"
                  [ngClass]="{
                    'bg-blue-100 text-blue-800': selectedScheduling.treatmentPlan.status === 'open',
                    'bg-green-100 text-green-800': selectedScheduling.treatmentPlan.status === 'completed',
                    'bg-red-100 text-red-800': selectedScheduling.treatmentPlan.status === 'cancelled'
                  }"
                >
                  {{ selectedScheduling.treatmentPlan.status === 'open' ? 'Em Aberto' :
                     selectedScheduling.treatmentPlan.status === 'completed' ? 'Concluído' :
                     selectedScheduling.treatmentPlan.status === 'cancelled' ? 'Cancelado' :
                     selectedScheduling.treatmentPlan.status }}
                </span>
              </div>
              <div>
                <span class="block text-sm font-medium text-gray-700">Valor Total do Plano</span>
                <span class="block text-base text-gray-900 font-semibold">
                  R$ {{ selectedScheduling.treatmentPlan.totalValue | number:'1.2-2' }}
                </span>
              </div>
              <div class="mb-4">
                <span class="block text-sm font-medium text-gray-700">Progresso</span>
                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                  <div
                    class="bg-blue-600 h-2.5 rounded-full"
                    [style.width]="(selectedScheduling.treatmentPlan.completionPercentage || 0) + '%'"
                  ></div>
                </div>
                <span class="text-sm text-gray-600 mt-1 block">
                  {{ selectedScheduling.treatmentPlan.completionPercentage || 0 }}% concluído
                </span>
              </div>
              <!-- Espaço adicional para equilibrar a altura -->
              <div class="py-2">
                <span class="block text-sm font-medium text-gray-700">Procedimentos</span>
                <span class="block text-base text-gray-900">
                  {{ getSortedProcedures().length }} procedimento(s) no plano
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de procedimentos -->
          <div class="mt-4 md:col-span-2">
            <h4 class="text-md font-medium text-gray-900 mb-2">Procedimentos</h4>
            <div class="bg-white border rounded-lg overflow-hidden shadow-sm">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedimento</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr *ngFor="let procedure of getSortedProcedures()">
                    <td class="px-3 py-2 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ procedure.name }}</div>
                      <div *ngIf="procedure.tooth" class="text-sm text-gray-500">Dente {{ procedure.tooth }}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        [ngClass]="{
                          'bg-yellow-100 text-yellow-800': procedure.status === 'pending',
                          'bg-orange-100 text-orange-800': procedure.status === 'in_progress',
                          'bg-green-100 text-green-800': procedure.status === 'completed',
                          'bg-red-100 text-red-800': procedure.status === 'cancelled'
                        }"
                      >
                        {{ procedure.status === 'pending' ? 'Pendente' :
                           procedure.status === 'in_progress' ? 'Em andamento' :
                           procedure.status === 'completed' ? 'Concluído' :
                           procedure.status === 'cancelled' ? 'Cancelado' : procedure.status }}
                      </span>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      R$ {{ procedure.value | number:'1.2-2' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
      </div>
    </div>

    <!-- Footer com botões de ação -->
    <div footer class="flex justify-end space-x-3">
      <!-- Botão para confirmar agendamento -->
      <button
        *ngIf="selectedScheduling?.status === 'scheduled-unconfirmed'"
        (click)="selectedScheduling && updateSchedulingStatus(selectedScheduling, 'scheduled-confirmed'); closeModal()"
        class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        Confirmar Agendamento
      </button>

      <!-- Botão para iniciar atendimento -->
      <button
        *ngIf="selectedScheduling?.status === 'scheduled-confirmed'"
        (click)="selectedScheduling && updateSchedulingStatus(selectedScheduling, 'in-progress'); closeModal()"
        class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
        </svg>
        Iniciar Atendimento
      </button>

      <!-- Botão para concluir atendimento -->
      <button
        *ngIf="selectedScheduling?.status === 'in-progress'"
        (click)="selectedScheduling && updateSchedulingStatus(selectedScheduling, 'completed'); closeModal()"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        Concluir Atendimento
      </button>

      <!-- Botão para cancelar atendimento -->
      <button
        *ngIf="selectedScheduling?.status !== 'cancelled' && selectedScheduling?.status !== 'completed'"
        (click)="selectedScheduling && updateSchedulingStatus(selectedScheduling, 'cancelled'); closeModal()"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        Cancelar Atendimento
      </button>

      <!-- Botão para fechar modal -->
      <button
        (click)="closeModal()"
        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
      >
        Fechar
      </button>
    </div>
  </app-modal>
</div>
