<div class="bg-white shadow rounded-lg p-6">
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6"
    role="alert"
  >
    <p>{{ error }}</p>
    <a
      routerLink="/employees"
      class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors inline-block"
    >
      Voltar para a lista
    </a>
  </div>

  <!-- Employee details -->
  <div *ngIf="!isLoading && !error && employee">
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <a routerLink="/employees" class="mr-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500 hover:text-gray-700"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
        </a>
        <div
          class="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mr-4"
        >
          <span class="text-blue-600 font-bold text-xl">{{
            employee.name.charAt(0)
          }}</span>
        </div>
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ employee.name }}
          </h1>
          <div class="flex items-center text-sm text-gray-600 mt-1">
            <span class="mr-4">{{ getEmployeeTypeLabel(employee.type) }}</span>
            <span
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              [ngClass]="{
                'bg-green-100 text-green-800': employee.isActive,
                'bg-red-100 text-red-800': !employee.isActive
              }"
            >
              {{ employee.isActive ? "Ativo" : "Inativo" }}
            </span>
          </div>
        </div>
      </div>
      <div class="flex space-x-2">
        <a
          [routerLink]="['/employees', employee.id, 'edit']"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          Editar
        </a>
        <button
          (click)="confirmDelete()"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          Excluir
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Informações Pessoais
        </h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Nome:</span>
            <span class="text-gray-900">{{ employee.name }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Email:</span>
            <span class="text-gray-900">{{ employee.email }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Telefone:</span>
            <span class="text-gray-900">{{ formatPhone(employee.phone) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">CPF:</span>
            <span class="text-gray-900">{{ formatCpf(employee.cpf) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data de Nascimento:</span>
            <span class="text-gray-900">{{
              employee.birthDate
                ? formatDate(employee.birthDate)
                : "Não informado"
            }}</span>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-medium text-gray-900 mb-4">
          Informações Profissionais
        </h2>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">Cargo:</span>
            <span class="text-gray-900">{{
              getEmployeeTypeLabel(employee.type)
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data de Admissão:</span>
            <span class="text-gray-900">{{
              formatDate(employee.admissionDate)
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Status:</span>
            <span class="text-gray-900">{{
              employee.isActive ? "Ativo" : "Inativo"
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Data de Cadastro:</span>
            <span class="text-gray-900">{{
              formatDate(employee.createdAt)
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">Última Atualização:</span>
            <span class="text-gray-900">{{
              formatDate(employee.updatedAt)
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="employee.notes" class="bg-gray-50 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Observações</h2>
      <p class="text-gray-700 whitespace-pre-line">{{ employee.notes }}</p>
    </div>
  </div>
</div>

<!-- Modal de confirmação de exclusão -->
<app-modal
  [isOpen]="isDeleteModalOpen"
  title="Confirmar exclusão"
  (close)="closeDeleteModal()"
>
  <div class="mb-4">
    <p class="text-gray-700">
      Tem certeza que deseja excluir o funcionário
      <span class="font-semibold">{{ employee?.name }}</span
      >?
    </p>
    <p class="text-gray-500 text-sm mt-2">Esta ação não pode ser desfeita.</p>
  </div>

  <div footer class="flex justify-end space-x-3">
    <button
      type="button"
      class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none"
      (click)="closeDeleteModal()"
    >
      Cancelar
    </button>
    <button
      type="button"
      class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none flex items-center"
      (click)="deleteEmployee()"
      [disabled]="isDeleting"
    >
      <svg
        *ngIf="isDeleting"
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {{ isDeleting ? "Excluindo..." : "Excluir" }}
    </button>
  </div>
</app-modal>
