import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { EmployeeService } from '../../core/services/employee.service';
import {
  Employee,
  EmployeeType,
  employeeTypeLabels,
} from '../../core/models/employee.model';
import { NotificationService } from '../../core/services/notification.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';

@Component({
  selector: 'app-employee-detail',
  standalone: true,
  imports: [CommonModule, RouterLink, ModalComponent],
  templateUrl: './employee-detail.component.html',
  styles: [],
})
export class EmployeeDetailComponent implements OnInit {
  employee: Employee | null = null;
  isLoading = true;
  error: string | null = null;
  isDeleteModalOpen = false;
  isDeleting = false;
  employeeTypeLabels = employeeTypeLabels;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private employeeService: EmployeeService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadEmployee(id);
    } else {
      this.error = 'ID do funcionário não fornecido';
      this.isLoading = false;
    }
  }

  loadEmployee(id: string): void {
    this.isLoading = true;
    this.error = null;

    this.employeeService.getEmployee(id).subscribe({
      next: (employee) => {
        this.employee = employee;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading employee:', err);
        this.error =
          'Não foi possível carregar os dados do funcionário. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      },
    });
  }

  getEmployeeTypeLabel(type: EmployeeType): string {
    return this.employeeTypeLabels[type] || type;
  }

  formatDate(date: Date | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('pt-BR');
  }

  formatCpf(cpf: string | undefined): string {
    if (!cpf) return 'N/A';
    // Remove non-numeric characters
    const numericCpf = cpf.replace(/\D/g, '');
    // Format as CPF (000.000.000-00)
    if (numericCpf.length === 11) {
      return numericCpf.replace(
        /^(\d{3})(\d{3})(\d{3})(\d{2})$/,
        '$1.$2.$3-$4'
      );
    }
    return cpf;
  }

  formatPhone(phone: string | undefined): string {
    if (!phone) return 'Não informado';
    // Remove non-numeric characters
    const numericPhone = phone.replace(/\D/g, '');
    // Format as phone number
    if (numericPhone.length === 11) {
      return numericPhone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
    } else if (numericPhone.length === 10) {
      return numericPhone.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
    }
    return phone;
  }

  confirmDelete(): void {
    this.isDeleteModalOpen = true;
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
  }

  deleteEmployee(): void {
    if (!this.employee || this.isDeleting) return;

    this.isDeleting = true;
    this.employeeService.deleteEmployee(this.employee.id).subscribe({
      next: () => {
        this.notificationService.success('Funcionário excluído com sucesso');
        this.isDeleting = false;
        this.router.navigate(['/employees']);
      },
      error: (err) => {
        console.error('Error deleting employee:', err);
        this.notificationService.error(
          'Erro ao excluir funcionário. Por favor, tente novamente.'
        );
        this.isDeleting = false;
        this.closeDeleteModal();
      },
    });
  }
}
