import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { EmployeeService } from '../../core/services/employee.service';
import {
  Employee,
  EmployeeType,
  employeeTypeLabels,
} from '../../core/models/employee.model';
import { NotificationService } from '../../core/services/notification.service';
import { maskCPF, maskPhone } from '../../core/utils/input-masks';

@Component({
  selector: 'app-employee-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './employee-form.component.html',
  styles: [],
})
export class EmployeeFormComponent implements OnInit {
  employeeForm: FormGroup;
  isEditMode = false;
  employeeId: string | null = null;
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;

  // Enum and labels for employee type
  employeeTypes = Object.values(EmployeeType);
  employeeTypeLabels = employeeTypeLabels;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    public router: Router,
    private employeeService: EmployeeService,
    private notificationService: NotificationService
  ) {
    this.employeeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [this.phoneValidator]],
      type: ['', [Validators.required]],
      birthDate: [''],
      cpf: ['', [Validators.required, this.cpfValidator]],
      admissionDate: ['', [Validators.required]],
      notes: [''],
      isActive: [true],
    });
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.employeeId = id;
      this.loadEmployeeData(id);
    } else {
      // Set default admission date to today
      const today = new Date();
      this.employeeForm.patchValue({
        admissionDate: this.formatDateForInput(today),
      });
    }
  }

  loadEmployeeData(id: string): void {
    this.isLoading = true;
    this.error = null;

    this.employeeService.getEmployee(id).subscribe({
      next: (employee) => {
        // Format dates for input type="date"
        const birthDate = employee.birthDate
          ? this.formatDateForInput(employee.birthDate)
          : '';
        const admissionDate = this.formatDateForInput(employee.admissionDate);

        this.employeeForm.patchValue({
          name: employee.name,
          email: employee.email,
          phone: employee.phone || '',
          type: employee.type,
          birthDate: birthDate,
          cpf: employee.cpf,
          admissionDate: admissionDate,
          notes: employee.notes || '',
          isActive: employee.isActive,
        });

        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading employee:', err);
        this.error =
          'Não foi possível carregar os dados do funcionário. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.employeeForm.invalid) {
      // Mark all fields as touched to show errors
      Object.keys(this.employeeForm.controls).forEach((key) => {
        const control = this.employeeForm.get(key);
        control?.markAsTouched();
      });

      this.notificationService.error(
        'Por favor, corrija os erros no formulário antes de salvar.'
      );
      return;
    }

    this.isSubmitting = true;

    const formValues = this.employeeForm.value;

    // Convert dates from string to Date
    const employeeData: any = {
      ...formValues,
      birthDate: formValues.birthDate
        ? new Date(formValues.birthDate)
        : undefined,
      admissionDate: new Date(formValues.admissionDate),
    };

    if (this.isEditMode && this.employeeId) {
      // Edit mode
      this.employeeService
        .updateEmployee(this.employeeId, employeeData)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.router.navigate(['/employees', this.employeeId]);
          },
          error: (err) => {
            console.error('Error updating employee:', err);
            this.error =
              'Não foi possível atualizar o funcionário. Por favor, tente novamente mais tarde.';
            this.isSubmitting = false;
          },
        });
    } else {
      // Create mode
      this.employeeService.createEmployee(employeeData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.router.navigate(['/employees']);
        },
        error: (err) => {
          console.error('Error creating employee:', err);
          this.error =
            'Não foi possível criar o funcionário. Por favor, tente novamente mais tarde.';
          this.isSubmitting = false;
        },
      });
    }
  }

  // Format date for input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Check if a field has an error and was touched
  hasError(controlName: string, errorName: string): boolean {
    const control = this.employeeForm.get(controlName);
    return !!control && control.hasError(errorName) && control.touched;
  }

  // Get the label for employee type
  getEmployeeTypeLabel(type: EmployeeType): string {
    return this.employeeTypeLabels[type] || type;
  }

  // Custom validator for CPF
  cpfValidator(control: { value: string }) {
    if (!control.value) {
      return null;
    }

    // Remove non-numeric characters
    const cpf = control.value.replace(/\D/g, '');

    // Check if CPF has 11 digits
    if (cpf.length !== 11) {
      return { invalidCpf: true };
    }

    // Check if all digits are the same (invalid CPF)
    if (/^(\d)\1+$/.test(cpf)) {
      return { invalidCpf: true };
    }

    return null;
  }

  // Custom validator for phone
  phoneValidator(control: { value: string }) {
    if (!control.value) {
      return null;
    }

    // Remove non-numeric characters
    const phone = control.value.replace(/\D/g, '');

    // Check if phone has 10 or 11 digits (with area code)
    if (phone.length < 10 || phone.length > 11) {
      return { invalidPhone: true };
    }

    return null;
  }

  // Métodos para aplicar máscaras
  onCpfInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskCPF(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.employeeForm.get('cpf')?.setValue(maskedValue, { emitEvent: false });
  }

  onPhoneInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const maskedValue = maskPhone(value);

    // Atualiza o valor no input
    input.value = maskedValue;

    // Atualiza o valor no formulário
    this.employeeForm.get('phone')?.setValue(maskedValue, { emitEvent: false });
  }
}
