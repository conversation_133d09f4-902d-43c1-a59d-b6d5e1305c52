<div class="container bg-white shadow rounded-lg p-6 mx-auto">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-6">
      <a routerLink="/employees" class="text-blue-600 hover:text-blue-800 mr-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </a>
      <h1 class="text-2xl font-bold text-gray-800">
        {{ isEditMode ? "Editar Funcionário" : "Novo Funcionário" }}
      </h1>
    </div>
  </div>

  <!-- Loading state -->
  <div
    *ngIf="isLoading"
    class="bg-white rounded-lg shadow-md p-6 flex justify-center items-center"
  >
    <svg
      class="animate-spin h-8 w-8 text-blue-600"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  </div>

  <!-- Error state -->
  <div
    *ngIf="!isLoading && error"
    class="bg-white rounded-lg shadow-md p-6 text-center mb-6"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-16 w-16 text-red-500 mx-auto mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
    <h2 class="text-xl font-semibold text-gray-700 mb-2">Erro</h2>
    <p class="text-gray-500 mb-4">{{ error }}</p>
    <button
      (click)="
        isEditMode
          ? loadEmployeeData(employeeId!)
          : router.navigate(['/employees'])
      "
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      {{ isEditMode ? "Tentar novamente" : "Voltar para a lista" }}
    </button>
  </div>

  <!-- Form -->
  <form
    *ngIf="!isLoading && !error"
    [formGroup]="employeeForm"
    (ngSubmit)="onSubmit()"
    class="bg-white rounded-lg"
  >
    <!-- Informações Básicas -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-gray-700 mb-4 pb-2 border-b">
        Informações Básicas
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Nome -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
            >Nome <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="name"
            formControlName="name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500':
                hasError('name', 'required') || hasError('name', 'minlength')
            }"
          />
          <div
            *ngIf="hasError('name', 'required')"
            class="text-red-500 text-sm mt-1"
          >
            Nome é obrigatório
          </div>
          <div
            *ngIf="hasError('name', 'minlength')"
            class="text-red-500 text-sm mt-1"
          >
            Nome deve ter pelo menos 3 caracteres
          </div>
        </div>

        <!-- Email -->
        <div>
          <label
            for="email"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Email <span class="text-red-500">*</span></label
          >
          <input
            type="email"
            id="email"
            formControlName="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500':
                hasError('email', 'required') || hasError('email', 'email')
            }"
          />
          <div
            *ngIf="hasError('email', 'required')"
            class="text-red-500 text-sm mt-1"
          >
            Email é obrigatório
          </div>
          <div
            *ngIf="hasError('email', 'email')"
            class="text-red-500 text-sm mt-1"
          >
            Email inválido
          </div>
        </div>

        <!-- Telefone -->
        <div>
          <label
            for="phone"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Telefone</label
          >
          <input
            type="tel"
            id="phone"
            formControlName="phone"
            (input)="onPhoneInput($event)"
            placeholder="(00) 00000-0000"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{ 'border-red-500': hasError('phone', 'invalidPhone') }"
          />
          <div
            *ngIf="hasError('phone', 'invalidPhone')"
            class="text-red-500 text-sm mt-1"
          >
            Telefone inválido. Use o formato (00) 00000-0000
          </div>
        </div>

        <!-- CPF -->
        <div>
          <label for="cpf" class="block text-sm font-medium text-gray-700 mb-1"
            >CPF <span class="text-red-500">*</span></label
          >
          <input
            type="text"
            id="cpf"
            formControlName="cpf"
            (input)="onCpfInput($event)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500':
                hasError('cpf', 'required') || hasError('cpf', 'invalidCpf')
            }"
            placeholder="000.000.000-00"
          />
          <div
            *ngIf="hasError('cpf', 'required')"
            class="text-red-500 text-sm mt-1"
          >
            CPF é obrigatório
          </div>
          <div
            *ngIf="hasError('cpf', 'invalidCpf')"
            class="text-red-500 text-sm mt-1"
          >
            CPF inválido. Use o formato 000.000.000-00
          </div>
        </div>
      </div>
    </div>

    <!-- Informações Profissionais -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold text-gray-700 mb-4 pb-2 border-b">
        Informações Profissionais
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Tipo -->
        <div>
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1"
            >Tipo <span class="text-red-500">*</span></label
          >
          <select
            id="type"
            formControlName="type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{ 'border-red-500': hasError('type', 'required') }"
          >
            <option value="" disabled>Selecione um tipo</option>
            <option *ngFor="let type of employeeTypes" [value]="type">
              {{ employeeTypeLabels[type] }}
            </option>
          </select>
          <div
            *ngIf="hasError('type', 'required')"
            class="text-red-500 text-sm mt-1"
          >
            Tipo é obrigatório
          </div>
        </div>

        <!-- Data de Admissão -->
        <div>
          <label
            for="admissionDate"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Data de Admissão <span class="text-red-500">*</span></label
          >
          <input
            type="date"
            id="admissionDate"
            formControlName="admissionDate"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-500': hasError('admissionDate', 'required')
            }"
          />
          <div
            *ngIf="hasError('admissionDate', 'required')"
            class="text-red-500 text-sm mt-1"
          >
            Data de admissão é obrigatória
          </div>
        </div>

        <!-- Data de Nascimento -->
        <div>
          <label
            for="birthDate"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Data de Nascimento</label
          >
          <input
            type="date"
            id="birthDate"
            formControlName="birthDate"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Status -->
        <div class="flex items-center">
          <div class="flex h-5 items-center">
            <input
              id="isActive"
              type="checkbox"
              formControlName="isActive"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>
          <div class="ml-3 text-sm">
            <label for="isActive" class="font-medium text-gray-700"
              >Funcionário Ativo</label
            >
            <p class="text-gray-500">Desmarque para inativar o funcionário</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Observações -->
    <div class="mb-6">
      <label for="notes" class="block text-sm font-medium text-gray-700 mb-1"
        >Observações</label
      >
      <textarea
        id="notes"
        formControlName="notes"
        rows="4"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-y"
      ></textarea>
    </div>

    <!-- Botões -->
    <div class="flex justify-end space-x-3">
      <a
        routerLink="/employees"
        class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none"
      >
        Cancelar
      </a>
      <button
        type="submit"
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none flex items-center"
        [disabled]="isSubmitting"
      >
        <svg
          *ngIf="isSubmitting"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isSubmitting ? "Salvando..." : "Salvar" }}
      </button>
    </div>
  </form>
</div>
