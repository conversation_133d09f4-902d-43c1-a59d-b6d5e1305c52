/* Animações personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* Estilização da paginação */
.pagination-controls {
  button {
    transition: all 0.2s ease-in-out;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }
}

/* Estilização da tabela */
.overflow-x-auto {
  transition: opacity 0.3s ease-in-out;
}

/* Estilização do seletor de itens por página */
select {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    border-color: #3b82f6;
  }
}

/* Estilização do overlay de carregamento */
.backdrop-blur-\[1px\] {
  backdrop-filter: blur(1px);
}
