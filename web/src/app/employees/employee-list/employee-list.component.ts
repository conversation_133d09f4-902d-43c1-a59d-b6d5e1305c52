import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { EmployeeService } from '../../core/services/employee.service';
import {
  Employee,
  EmployeeType,
  employeeTypeLabels,
} from '../../core/models/employee.model';
import { NotificationService } from '../../core/services/notification.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-employee-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule, ModalComponent],
  templateUrl: './employee-list.component.html',
  styleUrl: './employee-list.component.scss',
})
export class EmployeeListComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  isLoading = true;
  error: string | null = null;

  // Filters
  searchTerm = '';
  typeFilter: EmployeeType | '' = '';

  // Confirmation modal
  isDeleteModalOpen = false;
  employeeToDelete: Employee | null = null;
  isDeleting = false;

  // Enum and labels for employee type
  employeeTypes = Object.values(EmployeeType);
  employeeTypeLabels = employeeTypeLabels;

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalItems: number = 0;
  totalPages: number = 0;

  constructor(
    private employeeService: EmployeeService,
    private notificationService: NotificationService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Verificar se há parâmetros de filtro na URL
    this.route.queryParams.subscribe((params) => {
      if (params['search']) {
        this.searchTerm = params['search'];
      }

      if (params['type']) {
        this.typeFilter = params['type'];
      }

      // Não usar os parâmetros de paginação da URL
      // if (params['page']) {
      //   this.currentPage = parseInt(params['page']);
      // }
      //
      // if (params['limit']) {
      //   this.itemsPerPage = parseInt(params['limit']);
      // }

      this.loadEmployees(this.currentPage);
    });
  }

  loadEmployees(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.error = null;

    this.employeeService
      .getEmployees(
        page,
        this.itemsPerPage,
        this.searchTerm,
        this.typeFilter as EmployeeType
      )
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response: PaginatedResponse<Employee>) => {
          this.employees = response.data;
          this.filteredEmployees = response.data;
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);

          // Atualizar a URL com os parâmetros de filtro e paginação
          this.updateQueryParams();
        },
        error: (err) => {
          console.error('Error loading employees:', err);
          this.error =
            'Não foi possível carregar a lista de funcionários. Por favor, tente novamente mais tarde.';
        },
      });
  }

  onSearchChange(): void {
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadEmployees(this.currentPage);
  }

  onTypeFilterChange(): void {
    this.currentPage = 1; // Voltar para a primeira página ao aplicar filtros
    this.loadEmployees(this.currentPage);
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar em torno da página atual
      const leftBound = Math.max(2, this.currentPage - 1);
      const rightBound = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Adicionar elipse à esquerda se necessário
      if (leftBound > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = leftBound; i <= rightBound; i++) {
        pages.push(i);
      }

      // Adicionar elipse à direita se necessário
      if (rightBound < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadEmployees(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadEmployees(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  confirmDelete(employee: Employee): void {
    this.employeeToDelete = employee;
    this.isDeleteModalOpen = true;
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
    this.employeeToDelete = null;
  }

  deleteEmployee(): void {
    if (!this.employeeToDelete || this.isDeleting) return;

    this.isDeleting = true;
    const id = this.employeeToDelete.id;

    this.employeeService.deleteEmployee(id).subscribe({
      next: () => {
        this.employees = this.employees.filter((e) => e.id !== id);
        this.filteredEmployees = this.employees;
        this.isDeleting = false;
        this.closeDeleteModal();
        // Recarregar a lista para atualizar a paginação
        this.loadEmployees(this.currentPage);
      },
      error: (err) => {
        console.error('Error deleting employee:', err);
        this.notificationService.error(
          'Erro ao excluir funcionário. Por favor, tente novamente.'
        );
        this.isDeleting = false;
        this.closeDeleteModal();
      },
    });
  }

  getEmployeeTypeLabel(type: EmployeeType): string {
    return this.employeeTypeLabels[type] || type;
  }

  formatPhone(phone: string | undefined): string {
    if (!phone) return 'Sem telefone';
    // Remove non-numeric characters
    const numericPhone = phone.replace(/\D/g, '');
    // Format as phone number
    if (numericPhone.length === 11) {
      return numericPhone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
    } else if (numericPhone.length === 10) {
      return numericPhone.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
    }
    return phone;
  }

  updateQueryParams(): void {
    const queryParams: any = {};

    if (this.searchTerm) {
      queryParams.search = this.searchTerm;
    }

    if (this.typeFilter) {
      queryParams.type = this.typeFilter;
    }

    // Não incluir os parâmetros de paginação na URL
    // queryParams.page = this.currentPage;
    // queryParams.limit = this.itemsPerPage;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      replaceUrl: true, // Substituir a URL atual em vez de adicionar ao histórico
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.typeFilter = '';
    this.currentPage = 1; // Voltar para a primeira página ao limpar filtros

    // Limpar os parâmetros da URL
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {},
      replaceUrl: true,
    });

    this.loadEmployees(this.currentPage);
  }
}
