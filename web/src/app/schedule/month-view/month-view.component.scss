/* Estilos para a visualização mensal */
.calendar-month {
  .max-h-90px {
    max-height: 90px;
    overflow-x: hidden;
  }
}

/* Estilo para o tooltip */
.tooltip-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;

  &:hover .tooltip-content {
    display: block;
  }
}

.tooltip-content {
  display: none;
  position: fixed;
  left: auto;
  top: auto;
  margin-top: 8px;
  width: 16rem; /* w-64 */
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  border-radius: 0.375rem; /* rounded-md */
  padding: 0.75rem; /* p-3 */
  font-size: 0.875rem; /* text-sm */
  z-index: 99999;
  overflow: visible;

  &:before {
    content: '';
    position: absolute;
    left: 12px; /* left-3 */
    top: -6px;
    width: 12px; /* w-3 */
    height: 12px; /* h-3 */
    background-color: white;
    transform: rotate(45deg);
  }
}
