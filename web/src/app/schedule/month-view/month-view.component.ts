import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  ElementRef,
  HostListener,
  AfterViewInit,
  Renderer2
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ModalService } from '../../shared/services/modal.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  events: any[];
  isToday: boolean;
}

@Component({
  selector: 'app-month-view',
  templateUrl: './month-view.component.html',
  styleUrls: ['./month-view.component.scss'],
  standalone: true,
  imports: [CommonModule, ModalComponent],
})
export class MonthViewComponent implements OnChanges, AfterViewInit {
  // Modal properties
  modalTitle: string = '';
  modalContent: any = null;
  isModalOpen: boolean = false;
  @Input() currentDate: Date = new Date();
  @Input() attendances: any[] = []; // Renomeado para schedulings na interface, mantido para compatibilidade
  @Output() createScheduling = new EventEmitter<{ date: Date; time: string }>();

  calendarDays: CalendarDay[] = [];
  weekDays: string[] = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  constructor(
    private modalService: ModalService,
    private router: Router,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['currentDate'] ||
      changes['attendances'] ||
      changes['treatments']
    ) {
      this.generateCalendarDays();
    }
  }

  generateCalendarDays(): void {
    this.calendarDays = [];

    // Obter o primeiro dia do mês
    const firstDayOfMonth = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth(),
      1
    );

    // Obter o último dia do mês
    const lastDayOfMonth = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() + 1,
      0
    );

    // Obter o dia da semana do primeiro dia do mês (0 = Domingo, 6 = Sábado)
    const firstDayOfWeek = firstDayOfMonth.getDay();

    // Adicionar dias do mês anterior para completar a primeira semana
    const prevMonthLastDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth(),
      0
    ).getDate();

    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() - 1,
        prevMonthLastDay - i
      );
      this.calendarDays.push({
        date,
        isCurrentMonth: false,
        events: this.getEventsForDate(date),
        isToday: this.isToday(date),
      });
    }

    // Adicionar dias do mês atual
    for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
      const date = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth(),
        i
      );
      this.calendarDays.push({
        date,
        isCurrentMonth: true,
        events: this.getEventsForDate(date),
        isToday: this.isToday(date),
      });
    }

    // Adicionar dias do próximo mês para completar a última semana
    const remainingDays = 42 - this.calendarDays.length; // 6 semanas * 7 dias = 42

    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() + 1,
        i
      );
      this.calendarDays.push({
        date,
        isCurrentMonth: false,
        events: this.getEventsForDate(date),
        isToday: this.isToday(date),
      });
    }
  }

  getEventsForDate(date: Date): any[] {
    const events: any[] = [];

    // Filtrar agendamentos para a data
    const filteredSchedulings = this.attendances.filter((scheduling) => {
      // Extrair os componentes da data do agendamento para evitar problemas de fuso horário
      const schedulingDateStr = scheduling.date.toString();
      const parts = schedulingDateStr.split('-');
      if (parts.length === 3) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // Meses em JS são 0-indexed
        const day = parseInt(parts[2]);

        // Comparar os componentes individuais da data
        return (
          day === date.getDate() &&
          month === date.getMonth() &&
          year === date.getFullYear()
        );
      }

      // Fallback para o método anterior
      const schedulingDate = new Date(scheduling.date);
      return (
        schedulingDate.getDate() === date.getDate() &&
        schedulingDate.getMonth() === date.getMonth() &&
        schedulingDate.getFullYear() === date.getFullYear()
      );
    });

    // Adicionar agendamentos aos eventos
    filteredSchedulings.forEach((scheduling) => {
      events.push({
        id: scheduling.id,
        type: 'scheduling',
        title: `${scheduling.patientName || 'Paciente'} - ${scheduling.time}`,
        status: scheduling.status,
        data: scheduling,
      });
    });

    return events;
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }

  // Método para obter a cor de fundo com base no status
  getStatusColor(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 border-yellow-300';
      case 'scheduled-confirmed':
        return 'bg-blue-100 border-blue-300';
      case 'unscheduled':
        return 'bg-red-100 border-red-300';
      case 'in-progress':
        return 'bg-indigo-100 border-indigo-300';
      case 'completed':
        return 'bg-green-100 border-green-300';
      case 'cancelled':
        return 'bg-red-100 border-red-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  }

  // Método para obter a cor do texto com base no status
  getStatusTextColor(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'text-yellow-800';
      case 'scheduled-confirmed':
        return 'text-blue-800';
      case 'unscheduled':
        return 'text-red-800';
      case 'in-progress':
        return 'text-indigo-800';
      case 'completed':
        return 'text-green-800';
      case 'cancelled':
        return 'text-red-800';
      default:
        return 'text-gray-800';
    }
  }

  // Método para formatar a data
  formatDate(date: Date): string {
    return date.getDate().toString().padStart(2, '0');
  }

  // Método para traduzir o status
  translateStatus(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  }

  // Método para gerar o texto do tooltip
  generateTooltipText(event: any): string {
    let tooltipText = '';

    // Apenas agendamentos são considerados agora
    tooltipText = `Paciente: ${event.data.patientName}\n`;
    tooltipText += `Data: ${new Date(event.data.date).toLocaleDateString(
      'pt-BR'
    )}\n`;
    tooltipText += `Hora: ${event.data.time}\n`;
    tooltipText += `Dentista: ${event.data.dentistName || 'Não informado'}\n`;
    tooltipText += `Status: ${this.translateStatus(event.data.status)}\n`;

    if (event.data.treatment) {
      tooltipText += `Tratamento: ${event.data.treatment}\n`;
    }

    if (event.data.notes) {
      tooltipText += `Observações: ${event.data.notes}`;
    }

    return tooltipText;
  }

  // Método para abrir o modal com as informações do evento
  openEventModal(event: any) {
    // Apenas agendamentos são considerados agora
    const title = `Agendamento: ${event.data.patientName}`;
    const content = {
      type: 'scheduling',
      id: event.data.id,
      patientId: event.data.patientId,
      patientName: event.data.patientName,
      date: new Date(event.data.date).toLocaleDateString('pt-BR'),
      time: event.data.time,
      dentistId: event.data.dentistId,
      dentistName: event.data.dentistName || 'Não informado',
      status: this.translateStatus(event.data.status),
      statusOriginal: event.data.status,
      motivo: event.data.motivo,
      notes: event.data.notes,
      cost: event.data.cost,
      paid: event.data.paid,
      treatmentId: event.data.treatmentId
    };

    this.modalTitle = title;
    this.modalContent = content;
    this.isModalOpen = true;
  }

  // Método para navegar para a página de detalhes do paciente
  navigateToPatientDetails() {
    if (this.modalContent && this.modalContent.patientId) {
      this.router.navigate(['/patients', this.modalContent.patientId]);
    }
  }

  // Método para editar o agendamento
  editScheduling() {
    if (this.modalContent && this.modalContent.id) {
      this.router.navigate(['/schedule/edit', this.modalContent.id]);
    }
  }

  closeModal() {
    this.isModalOpen = false;
  }

  // Método para criar um novo agendamento ao clicar em uma data
  createNewScheduling(date: Date) {
    // Garantir que estamos trabalhando com um objeto Date válido
    const selectedDate = new Date(date);

    console.log(
      'Criando novo agendamento para a data:',
      selectedDate,
      'Dia:',
      selectedDate.getDate(),
      'Mês:',
      selectedDate.getMonth() + 1,
      'Ano:',
      selectedDate.getFullYear()
    );

    // Emitir evento para o componente pai abrir o modal com a data selecionada
    this.createScheduling.emit({
      date: selectedDate,
      time: '',
    });
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Implementação de AfterViewInit para configurar os tooltips
  ngAfterViewInit(): void {
    this.setupTooltips();
  }

  // Configurar tooltips após a renderização da view
  setupTooltips(): void {
    // Selecionar todos os eventos do calendário
    const eventElements = this.elementRef.nativeElement.querySelectorAll('.calendar-month .cursor-pointer');

    // Adicionar listeners para cada evento
    eventElements.forEach((element: HTMLElement) => {
      // Adicionar listener para mouseover
      this.renderer.listen(element, 'mouseover', (event) => {
        this.showTooltip(element, event);
      });

      // Adicionar listener para mouseout
      this.renderer.listen(element, 'mouseout', () => {
        this.hideTooltips();
      });
    });
  }

  // Mostrar tooltip para um elemento específico
  showTooltip(element: HTMLElement, event: MouseEvent): void {
    // Encontrar o tooltip associado ao elemento
    const tooltipContent = element.querySelector('.tooltip-content') as HTMLElement;

    if (tooltipContent) {
      // Posicionar o tooltip
      const rect = element.getBoundingClientRect();

      // Definir posição do tooltip
      tooltipContent.style.left = `${rect.left + window.scrollX}px`;
      tooltipContent.style.top = `${rect.bottom + window.scrollY}px`;

      // Mostrar o tooltip
      tooltipContent.style.display = 'block';
    }
  }

  // Esconder todos os tooltips
  hideTooltips(): void {
    const tooltips = this.elementRef.nativeElement.querySelectorAll('.tooltip-content');
    tooltips.forEach((tooltip: HTMLElement) => {
      tooltip.style.display = 'none';
    });
  }

  // Reposicionar tooltips quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize(): void {
    this.hideTooltips();
  }

  // Reposicionar tooltips quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.hideTooltips();
  }
}
