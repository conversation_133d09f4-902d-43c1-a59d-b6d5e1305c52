import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  ElementRef,
  HostListener,
  AfterViewInit,
  Renderer2
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ModalService } from '../../shared/services/modal.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';

interface TimeSlot {
  time: string;         // Formato HH:MM
  hour: number;         // Hora (0-23)
  minute: number;       // Minuto (0-55, incrementos de 5)
  events: any[];
  displayLabel: string; // Rótulo a ser exibido (HH:MM)
}

@Component({
  selector: 'app-day-view',
  templateUrl: './day-view.component.html',
  styleUrls: ['./day-view.component.scss'],
  standalone: true,
  imports: [CommonModule, ModalComponent],
})
export class DayViewComponent implements OnChanges, AfterViewInit {
  // Modal properties
  modalTitle: string = '';
  modalContent: any = null;
  isModalOpen: boolean = false;
  @Input() currentDate: Date = new Date();
  @Input() attendances: any[] = []; // Renomeado para schedulings na interface, mantido para compatibilidade
  @Output() createScheduling = new EventEmitter<{ date: Date; time: string }>();

  timeSlots: TimeSlot[] = [];
  currentTimeHour: number = new Date().getHours();
  currentTimeMinute: number = Math.floor(new Date().getMinutes() / 5) * 5; // Arredondar para o intervalo de 5 minutos mais próximo

  constructor(
    private modalService: ModalService,
    private router: Router,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['currentDate'] ||
      changes['attendances'] ||
      changes['treatments']
    ) {
      // Atualizar a hora atual
      const now = new Date();
      this.currentTimeHour = now.getHours();
      this.currentTimeMinute = Math.floor(now.getMinutes() / 5) * 5;

      this.generateTimeSlots();
    }
  }

  // Método para verificar se um slot de tempo é o slot atual
  isCurrentTimeSlot(hour: number, minute: number): boolean {
    // Verificar se a data atual é hoje
    const today = new Date();
    const isToday =
      today.getDate() === this.currentDate.getDate() &&
      today.getMonth() === this.currentDate.getMonth() &&
      today.getFullYear() === this.currentDate.getFullYear();

    // Se não for hoje, retornar false
    if (!isToday) {
      return false;
    }

    // Verificar se o slot corresponde à hora atual
    return hour === this.currentTimeHour && minute === this.currentTimeMinute;
  }

  generateTimeSlots(): void {
    this.timeSlots = [];

    // Gerar slots de 5 em 5 minutos das 8h às 18h
    for (let hour = 8; hour <= 18; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        // Não incluir 18:05, 18:10, etc. (apenas 18:00)
        if (hour === 18 && minute > 0) {
          continue;
        }

        const hourStr = hour.toString().padStart(2, '0');
        const minuteStr = minute.toString().padStart(2, '0');
        const time = `${hourStr}:${minuteStr}`;

        this.timeSlots.push({
          time,
          hour,
          minute,
          displayLabel: time,
          events: this.getEventsForTimeSlot(hour, minute),
        });
      }
    }
  }

  // Método para compatibilidade com código existente
  getEventsForHour(hour: string): any[] {
    const hourNum = parseInt(hour.substring(0, 2));
    return this.getEventsForTimeSlot(hourNum, 0);
  }

  // Novo método para obter eventos para um slot de tempo específico (hora e minuto)
  getEventsForTimeSlot(hour: number, minute: number): any[] {
    const events: any[] = [];
    const currentSlotTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

    // Filtrar agendamentos para a data atual
    const filteredSchedulings = this.attendances.filter((scheduling) => {
      // Extrair os componentes da data do agendamento para evitar problemas de fuso horário
      const schedulingDateStr = scheduling.date.toString();
      const parts = schedulingDateStr.split('-');
      let dateMatches = false;

      if (parts.length === 3) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // Meses em JS são 0-indexed
        const day = parseInt(parts[2]);

        // Comparar os componentes individuais da data
        dateMatches =
          day === this.currentDate.getDate() &&
          month === this.currentDate.getMonth() &&
          year === this.currentDate.getFullYear();
      } else {
        // Fallback para o método anterior
        const schedulingDate = new Date(scheduling.date);
        dateMatches =
          schedulingDate.getDate() === this.currentDate.getDate() &&
          schedulingDate.getMonth() === this.currentDate.getMonth() &&
          schedulingDate.getFullYear() === this.currentDate.getFullYear();
      }

      if (!dateMatches) {
        return false;
      }

      // Verificar se o agendamento está no slot de tempo atual
      // Formato do tempo: "HH:MM"
      const [schedulingHourStr, schedulingMinuteStr] = scheduling.time.split(':');
      const schedulingHour = parseInt(schedulingHourStr);
      const schedulingMinute = parseInt(schedulingMinuteStr);

      // Calcular a duração do agendamento (padrão: 30 minutos se não especificado)
      const durationMinutes = scheduling.duration || 30;

      // Calcular o horário de término do agendamento
      let endHour = schedulingHour;
      let endMinute = schedulingMinute + durationMinutes;

      // Ajustar se ultrapassar 60 minutos
      if (endMinute >= 60) {
        endHour += Math.floor(endMinute / 60);
        endMinute = endMinute % 60;
      }

      // Converter horário atual e horário do agendamento para minutos desde o início do dia
      const currentTimeInMinutes = hour * 60 + minute;
      const schedulingStartInMinutes = schedulingHour * 60 + schedulingMinute;
      const schedulingEndInMinutes = endHour * 60 + endMinute;

      // Um agendamento pertence a este slot se o slot atual está dentro do período do agendamento
      return dateMatches &&
             currentTimeInMinutes >= schedulingStartInMinutes &&
             currentTimeInMinutes < schedulingEndInMinutes;
    });

    // Adicionar agendamentos aos eventos
    filteredSchedulings.forEach((scheduling) => {
      events.push({
        id: scheduling.id,
        type: 'scheduling',
        title: `${scheduling.patientName || 'Paciente'} - ${scheduling.time}`,
        time: scheduling.time,
        status: scheduling.status,
        data: scheduling,
      });
    });

    // Ordenar eventos por hora
    return events.sort((a, b) => {
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }
      return 0;
    });
  }

  // Método para obter a cor de fundo com base no status
  getStatusColor(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 border-yellow-300';
      case 'scheduled-confirmed':
        return 'bg-blue-100 border-blue-300';
      case 'unscheduled':
        return 'bg-red-100 border-red-300';
      case 'in-progress':
        return 'bg-indigo-100 border-indigo-300';
      case 'completed':
        return 'bg-green-100 border-green-300';
      case 'cancelled':
        return 'bg-red-100 border-red-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  }

  // Método para obter a cor do texto com base no status
  getStatusTextColor(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'text-green-800';
      case 'unconfirmed':
        return 'text-yellow-800';
      case 'late':
        return 'text-orange-800';
      case 'no-show':
        return 'text-red-800';
      case 'cancelled':
        return 'text-gray-800';
      case 'rescheduled':
        return 'text-blue-800';
      case 'in-progress':
        return 'text-indigo-800';
      case 'completed':
        return 'text-emerald-800';
      // Para compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'text-yellow-800';
      case 'scheduled-confirmed':
        return 'text-blue-800';
      case 'unscheduled':
        return 'text-red-800';
      default:
        return 'text-gray-800';
    }
  }

  // Método para formatar a data
  formatDate(date: Date): string {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  }

  // Método para traduzir o status
  translateStatus(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmado';
      case 'unconfirmed':
        return 'Não confirmado';
      case 'late':
        return 'Atrasado';
      case 'no-show':
        return 'Não compareceu';
      case 'cancelled':
        return 'Desmarcado';
      case 'rescheduled':
        return 'Remarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      // Para compatibilidade com registros antigos
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      default:
        return status;
    }
  }

  // Método para gerar o texto do tooltip
  generateTooltipText(event: any): string {
    let tooltipText = '';

    // Apenas agendamentos são considerados agora
    tooltipText = `Paciente: ${event.data.patientName}\n`;
    tooltipText += `Data: ${new Date(event.data.date).toLocaleDateString(
      'pt-BR'
    )}\n`;
    tooltipText += `Hora: ${event.data.time}\n`;
    tooltipText += `Dentista: ${event.data.dentistName || 'Não informado'}\n`;
    tooltipText += `Status: ${this.translateStatus(event.data.status)}\n`;

    if (event.data.treatment) {
      tooltipText += `Tratamento: ${event.data.treatment}\n`;
    }

    if (event.data.notes) {
      tooltipText += `Observações: ${event.data.notes}`;
    }

    return tooltipText;
  }

  // Método para abrir o modal com as informações do evento
  openEventModal(event: any) {
    // Apenas agendamentos são considerados agora
    const title = `Agendamento: ${event.data.patientName}`;
    const content = {
      type: 'scheduling',
      id: event.data.id,
      patientId: event.data.patientId,
      patientName: event.data.patientName,
      date: new Date(event.data.date).toLocaleDateString('pt-BR'),
      time: event.data.time,
      dentistId: event.data.dentistId,
      dentistName: event.data.dentistName || 'Não informado',
      status: this.translateStatus(event.data.status),
      statusOriginal: event.data.status,
      motivo: event.data.motivo,
      notes: event.data.notes,
      cost: event.data.cost,
      paid: event.data.paid,
      treatmentId: event.data.treatmentId
    };

    this.modalTitle = title;
    this.modalContent = content;
    this.isModalOpen = true;
  }

  // Método para navegar para a página de detalhes do paciente
  navigateToPatientDetails() {
    if (this.modalContent && this.modalContent.patientId) {
      this.router.navigate(['/patients', this.modalContent.patientId]);
    }
  }

  // Método para editar o agendamento
  editScheduling() {
    if (this.modalContent && this.modalContent.id) {
      this.router.navigate(['/schedule/edit', this.modalContent.id]);
    }
  }

  closeModal() {
    this.isModalOpen = false;
  }

  // Método para criar um novo agendamento ao clicar em um slot de tempo
  createNewScheduling(time: string) {
    // Emitir evento para o componente pai abrir o modal
    this.createScheduling.emit({
      date: this.currentDate,
      time: time,
    });
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Implementação de AfterViewInit para configurar os tooltips
  ngAfterViewInit(): void {
    this.setupTooltips();
  }

  // Configurar tooltips após a renderização da view
  setupTooltips(): void {
    // Selecionar todos os eventos do calendário
    const eventElements = this.elementRef.nativeElement.querySelectorAll('.calendar-day .cursor-pointer');

    // Adicionar listeners para cada evento
    eventElements.forEach((element: HTMLElement) => {
      // Adicionar listener para mouseover
      this.renderer.listen(element, 'mouseover', (event) => {
        this.showTooltip(element, event);
      });

      // Adicionar listener para mouseout
      this.renderer.listen(element, 'mouseout', () => {
        this.hideTooltips();
      });
    });
  }

  // Mostrar tooltip para um elemento específico
  showTooltip(element: HTMLElement, event: MouseEvent): void {
    // Encontrar o tooltip associado ao elemento
    const tooltipContent = element.querySelector('.tooltip-content') as HTMLElement;

    if (tooltipContent) {
      // Posicionar o tooltip
      const rect = element.getBoundingClientRect();

      // Definir posição do tooltip
      tooltipContent.style.left = `${rect.left + window.scrollX}px`;
      tooltipContent.style.top = `${rect.bottom + window.scrollY}px`;

      // Mostrar o tooltip
      tooltipContent.style.display = 'block';
    }
  }

  // Esconder todos os tooltips
  hideTooltips(): void {
    const tooltips = this.elementRef.nativeElement.querySelectorAll('.tooltip-content');
    tooltips.forEach((tooltip: HTMLElement) => {
      tooltip.style.display = 'none';
    });
  }

  // Reposicionar tooltips quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize(): void {
    this.hideTooltips();
  }

  // Reposicionar tooltips quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.hideTooltips();
  }
}
