<div class="calendar-day overflow-visible">
  <!-- <PERSON>abe<PERSON>lho do dia -->
  <div class="mb-4 text-center overflow-visible">
    <h3 class="text-xl font-medium text-gray-800">
      {{ formatDate(currentDate) }}
    </h3>
  </div>

  <!-- Slots de tempo (5 em 5 minutos) -->
  <div class="overflow-y-auto max-h-[calc(100vh-200px)]">
    <!-- Agrupamento por hora para melhor organização -->
    <ng-container *ngFor="let hour of [8,9,10,11,12,13,14,15,16,17,18]">
      <!-- Cabe<PERSON><PERSON><PERSON> da hora -->
      <div class="sticky top-0 p-2 bg-gray-100 border-b z-10 mb-1">
        <div class="font-medium text-gray-700">
          {{ hour.toString().padStart(2, '0') }}:00
        </div>
      </div>

      <!-- Slots de 5 minutos para esta hora -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-1 mb-4">
        <ng-container *ngFor="let slot of timeSlots | slice:(hour-8)*12:((hour-8)+1)*12">
          <div [ngClass]="{'border-blue-500 border-2 bg-blue-50': isCurrentTimeSlot(slot.hour, slot.minute), 'border bg-white': !isCurrentTimeSlot(slot.hour, slot.minute)}" class="rounded-md overflow-hidden">
            <!-- Cabeçalho do slot de 5 minutos -->
            <div class="p-1 bg-gray-50 border-b flex justify-between items-center">
              <span class="text-sm">{{ slot.displayLabel }}</span>
              <button
                (click)="createNewScheduling(slot.time)"
                class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50 transition-colors"
                title="Novo agendamento para {{ slot.time }}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>

            <!-- Eventos do slot -->
            <div class="p-1 min-h-[30px]">
              <div *ngIf="slot.events.length === 0" class="text-center text-gray-400 text-xs">
                <!-- Espaço vazio -->
              </div>

              <div *ngFor="let event of slot.events"
                  [ngClass]="getStatusColor(event.status)"
                  class="p-1 mb-1 rounded border text-xs cursor-pointer transition-colors hover:opacity-80 relative"
                  (click)="openEventModal(event)">

                <!-- Conteúdo do evento -->
                <div [ngClass]="getStatusTextColor(event.status)" class="flex flex-col">
                  <div class="flex items-center justify-between">
                    <div *ngIf="event.type === 'scheduling'" class="font-medium text-xs">{{ event.data.time }}</div>
                    <div *ngIf="event.data.duration" class="text-xs text-gray-500">{{ event.data.duration }}min</div>
                  </div>
                  <div class="flex items-center">
                    <div class="truncate">{{ event.data.patientName }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-container>
  </div>
</div>

<!-- Modal -->
<app-modal [title]="modalTitle" [isOpen]="isModalOpen" (close)="closeModal()">
  <div *ngIf="modalContent" class="max-h-[80vh] overflow-y-auto">
    <div *ngIf="modalContent.type === 'scheduling'" class="p-6">
      <!-- Cabeçalho com informações principais -->
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <!-- Status do agendamento -->
        <div class="flex items-center">
          <div
            [ngClass]="{
              'bg-pink-100 text-pink-800 border-pink-300': modalContent.status.includes('não confirmado'),
              'bg-yellow-100 text-yellow-800 border-yellow-300': modalContent.status.includes('confirmado'),
              'bg-purple-100 text-purple-800 border-purple-300': modalContent.status.includes('Desmarcado'),
              'bg-blue-100 text-blue-800 border-blue-300': modalContent.status.includes('andamento'),
              'bg-green-100 text-green-800 border-green-300': modalContent.status.includes('Concluído'),
              'bg-red-100 text-red-800 border-red-300': modalContent.status.includes('Cancelado')
            }"
            class="px-3 py-1 rounded-full text-sm font-medium border"
          >
            {{ modalContent.status }}
          </div>
        </div>

        <!-- Botões de ação -->
        <div class="flex space-x-2">
          <button
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            (click)="navigateToPatientDetails()"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Ver Paciente
          </button>
          <button
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            (click)="editScheduling()"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Editar
          </button>
        </div>
      </div>

      <!-- Informações do agendamento em cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Card de informações do paciente -->
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-5">
          <div class="flex items-center mb-4">
            <div class="bg-blue-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Paciente</h3>
          </div>

          <div class="space-y-3">
            <div class="flex items-center">
              <span class="text-gray-500 w-24">Nome:</span>
              <span class="font-medium text-gray-900">{{ modalContent.patientName }}</span>
            </div>
          </div>
        </div>

        <!-- Card de informações do profissional -->
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-5">
          <div class="flex items-center mb-4">
            <div class="bg-green-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Profissional</h3>
          </div>

          <div class="space-y-3">
            <div class="flex items-center">
              <span class="text-gray-500 w-24">Dentista:</span>
              <span class="font-medium text-gray-900">{{ modalContent.dentistName }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de detalhes do agendamento -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-5 mb-6">
        <div class="flex items-center mb-4">
          <div class="bg-purple-100 p-2 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900">Detalhes do Agendamento</h3>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="text-gray-500 w-24">Data:</span>
            <span class="font-medium text-gray-900">{{ modalContent.date }}</span>
          </div>

          <div class="flex items-center">
            <span class="text-gray-500 w-24">Hora:</span>
            <span class="font-medium text-gray-900">{{ modalContent.time }}</span>
          </div>

          <div *ngIf="modalContent.motivo" class="flex items-start sm:col-span-2">
            <span class="text-gray-500 w-24">Motivo:</span>
            <span class="font-medium text-gray-900">{{ modalContent.motivo }}</span>
          </div>
        </div>
      </div>

      <!-- Observações (se houver) -->
      <div *ngIf="modalContent.notes" class="bg-white border border-gray-200 rounded-lg shadow-sm p-5">
        <div class="flex items-center mb-4">
          <div class="bg-yellow-100 p-2 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900">Observações</h3>
        </div>

        <div class="bg-gray-50 p-4 rounded-md border border-gray-100">
          <p class="text-gray-700 whitespace-pre-line">{{ modalContent.notes }}</p>
        </div>
      </div>
    </div>
  </div>
</app-modal>
