/* Estilos para a visualização diária */
.calendar-day {
  .min-h-80px {
    min-height: 80px;
  }
}

/* Estilo para o tooltip */
.tooltip-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;

  &:hover .tooltip-content {
    display: block;
  }
}

.tooltip-content {
  display: none;
  position: fixed; /* Alterado para fixed para evitar sobreposição */
  left: auto; /* Removido o left: 0 para permitir posicionamento dinâmico */
  top: auto; /* Removido o top: 100% para permitir posicionamento dinâmico */
  margin-top: 8px;
  width: 16rem; /* w-64 */
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  border-radius: 0.375rem; /* rounded-md */
  padding: 0.75rem; /* p-3 */
  font-size: 0.875rem; /* text-sm */
  z-index: 99999; /* Aumentado o z-index para garantir que fique acima de tudo */
  overflow: visible;

  &:before {
    content: '';
    position: absolute;
    left: 12px; /* left-3 */
    top: -6px;
    width: 12px; /* w-3 */
    height: 12px; /* h-3 */
    background-color: white;
    transform: rotate(45deg);
  }
}
