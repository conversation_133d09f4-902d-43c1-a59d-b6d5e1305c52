import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ElementRef,
  HostListener
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import { Patient } from '../../core/models/patient.model';
import { Dentist } from '../../core/models/dentist.model';
import { PatientService } from '../../core/services/patient.service';
import { DentistService } from '../../core/services/dentist.service';

@Component({
  selector: 'app-schedule-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, ModalComponent],
  templateUrl: './schedule-modal.component.html',
  styleUrl: './schedule-modal.component.scss',
})
export class ScheduleModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() initialDate: string = '';
  @Input() initialTime: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<any>();

  schedulingForm: FormGroup;
  isSubmitting = false;
  patients: Patient[] = [];
  dentists: Dentist[] = [];
  filteredPatients: Patient[] = [];
  patientSearchTerm = '';
  showPatientDropdown = false;

  constructor(
    private fb: FormBuilder,
    private patientService: PatientService,
    private dentistService: DentistService,
    private elementRef: ElementRef
  ) {
    this.schedulingForm = this.fb.group({
      patientId: ['', Validators.required],
      dentistId: ['', Validators.required],
      date: ['', Validators.required],
      time: ['', Validators.required],
      status: ['scheduled-unconfirmed', Validators.required],
      notes: [''],
    });
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  // Usar ngOnChanges para reagir às mudanças nas propriedades de entrada
  ngOnChanges(changes: SimpleChanges): void {
    // Se o modal foi aberto, atualizar o formulário com os valores iniciais
    if (changes['isOpen'] && changes['isOpen'].currentValue === true) {
      this.updateFormWithInitialValues();
    }
  }

  // Método para atualizar o formulário com os valores iniciais
  private updateFormWithInitialValues(): void {
    // Se houver data inicial, preencher o formulário com ela
    if (this.initialDate) {
      console.log('Usando data inicial fornecida:', this.initialDate);
      this.schedulingForm.patchValue({ date: this.initialDate });
    } else {
      // Definir a data padrão como hoje
      const today = new Date();
      const formattedDate = this.formatDateForInput(today);
      console.log('Usando data padrão (hoje):', formattedDate);
      this.schedulingForm.patchValue({ date: formattedDate });
    }

    // Se houver hora inicial, preencher o formulário com ela
    if (this.initialTime) {
      console.log('Usando hora inicial fornecida:', this.initialTime);
      this.schedulingForm.patchValue({ time: this.initialTime });
    } else {
      // Definir um horário padrão
      console.log('Usando hora padrão: 09:00');
      this.schedulingForm.patchValue({ time: '09:00' });
    }
  }

  loadInitialData(): void {
    // Carregar pacientes
    this.patientService.getAllPatients().subscribe({
      next: (patients) => {
        this.patients = patients;
        this.filteredPatients = [...this.patients];
      },
      error: (error) => {
        console.error('Erro ao carregar pacientes:', error);
      },
    });

    // Carregar dentistas
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists;
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
      },
    });
  }

  // Reposicionar o dropdown quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize() {
    if (this.showPatientDropdown) {
      this.positionPatientDropdown();
    }
  }

  // Reposicionar o dropdown quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll() {
    if (this.showPatientDropdown) {
      this.positionPatientDropdown();
    }
  }

  filterPatients(): void {
    if (!this.patientSearchTerm.trim()) {
      this.filteredPatients = [...this.patients];
      return;
    }

    const searchTerm = this.patientSearchTerm.toLowerCase().trim();
    this.filteredPatients = this.patients.filter(
      (patient) =>
        patient.name.toLowerCase().includes(searchTerm) ||
        (patient.cpf && patient.cpf.includes(searchTerm))
    );

    // Posicionar o dropdown após filtrar
    setTimeout(() => this.positionPatientDropdown(), 0);
  }

  // Método para posicionar o dropdown corretamente
  positionPatientDropdown(): void {
    const inputElement = this.elementRef.nativeElement.querySelector('#patientSearch');
    const dropdownElement = this.elementRef.nativeElement.querySelector('.patient-dropdown');

    if (inputElement && dropdownElement) {
      const rect = inputElement.getBoundingClientRect();

      // Definir a largura do dropdown igual à largura do elemento input
      dropdownElement.style.width = `${rect.width}px`;

      // Posicionar o dropdown abaixo do elemento input
      dropdownElement.style.top = `${rect.bottom + window.scrollY}px`;
      dropdownElement.style.left = `${rect.left + window.scrollX}px`;
    }
  }

  selectPatient(patient: Patient): void {
    this.patientSearchTerm = patient.name;
    this.schedulingForm.patchValue({ patientId: patient.id });
    this.showPatientDropdown = false;
  }

  onSubmit(): void {
    if (this.schedulingForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.schedulingForm.controls).forEach((key) => {
        const control = this.schedulingForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formValues = this.schedulingForm.value;

    // Preparar os dados para envio
    const schedulingData = {
      ...formValues,
      // Converter campos para número
      patientId: formValues.patientId
        ? Number(formValues.patientId)
        : undefined,
      dentistId: formValues.dentistId
        ? Number(formValues.dentistId)
        : undefined,
    };

    // Emitir os dados do formulário
    this.save.emit(schedulingData);
    this.isSubmitting = false;
  }

  closeModal(): void {
    this.schedulingForm.reset({
      status: 'scheduled-unconfirmed',
    });
    this.patientSearchTerm = '';
    this.close.emit();
  }

  // Método para esconder o dropdown de pacientes com delay
  hidePatientDropdown(): void {
    setTimeout(() => {
      this.showPatientDropdown = false;
    }, 200);
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get dateControl() {
    return this.schedulingForm.get('date');
  }
  get timeControl() {
    return this.schedulingForm.get('time');
  }
  get patientIdControl() {
    return this.schedulingForm.get('patientId');
  }
  get dentistIdControl() {
    return this.schedulingForm.get('dentistId');
  }
}
