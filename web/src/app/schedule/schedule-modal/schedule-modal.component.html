<app-modal
  [title]="'Novo Agendamento'"
  [isOpen]="isOpen"
  (close)="closeModal()"
  [showDefaultFooter]="true"
>
  <!-- Conteúdo do modal -->
  <div class="p-6">
    <form
      [formGroup]="schedulingForm"
      (ngSubmit)="onSubmit()"
      class="space-y-6"
    >
      <!-- Grid de 2 colunas para layout eficiente -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Coluna 1: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Observações -->
        <div class="space-y-4">
          <!-- Paciente -->
          <div class="space-y-2">
            <label
              for="patientSearch"
              class="block text-sm font-medium text-gray-700"
              >Paciente *</label
            >
            <div class="relative">
              <input
                type="text"
                id="patientSearch"
                [(ngModel)]="patientSearchTerm"
                [ngModelOptions]="{ standalone: true }"
                (input)="filterPatients()"
                (focus)="showPatientDropdown = true"
                (blur)="hidePatientDropdown()"
                placeholder="Buscar paciente por nome ou CPF"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  patientIdControl?.invalid && patientIdControl?.touched
                "
              />
              <!-- Dropdown de pacientes -->
              <div
                *ngIf="showPatientDropdown && filteredPatients.length > 0"
                class="fixed z-50 mt-1 bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm patient-dropdown"
              >
                <div
                  *ngFor="let patient of filteredPatients"
                  (click)="selectPatient(patient)"
                  class="cursor-pointer hover:bg-gray-100 px-4 py-2"
                >
                  <div class="font-medium">{{ patient.name }}</div>
                  <div class="text-sm text-gray-500" *ngIf="patient.cpf">
                    CPF: {{ patient.cpf }}
                  </div>
                </div>
              </div>
              <div
                *ngIf="patientIdControl?.invalid && patientIdControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                Paciente é obrigatório
              </div>
            </div>
          </div>

          <!-- Dentista -->
          <div class="space-y-2">
            <label
              for="dentistId"
              class="block text-sm font-medium text-gray-700"
              >Dentista *</label
            >
            <select
              id="dentistId"
              formControlName="dentistId"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              [class.border-red-500]="
                dentistIdControl?.invalid && dentistIdControl?.touched
              "
            >
              <option value="">Selecione um dentista</option>
              <option *ngFor="let dentist of dentists" [value]="dentist.id">
                {{ dentist.name }}
              </option>
            </select>
            <div
              *ngIf="dentistIdControl?.invalid && dentistIdControl?.touched"
              class="text-red-500 text-sm mt-1"
            >
              Dentista é obrigatório
            </div>
          </div>

          <!-- Observações -->
          <div class="space-y-2">
            <label for="notes" class="block text-sm font-medium text-gray-700"
              >Observações</label
            >
            <textarea
              id="notes"
              formControlName="notes"
              rows="3"
              placeholder="Observações adicionais"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>
        </div>

        <!-- Coluna 2: Data, Hora, Status -->
        <div class="space-y-4">
          <!-- Data e Hora -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <label for="date" class="block text-sm font-medium text-gray-700"
                >Data *</label
              >
              <input
                type="date"
                id="date"
                formControlName="date"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  dateControl?.invalid && dateControl?.touched
                "
              />
              <div
                *ngIf="dateControl?.invalid && dateControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                Data é obrigatória
              </div>
            </div>

            <div class="space-y-2">
              <label for="time" class="block text-sm font-medium text-gray-700"
                >Hora *</label
              >
              <input
                type="time"
                id="time"
                formControlName="time"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-red-500]="
                  timeControl?.invalid && timeControl?.touched
                "
              />
              <div
                *ngIf="timeControl?.invalid && timeControl?.touched"
                class="text-red-500 text-sm mt-1"
              >
                Hora é obrigatória
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="space-y-2">
            <label for="status" class="block text-sm font-medium text-gray-700"
              >Status *</label
            >
            <select
              id="status"
              formControlName="status"
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="scheduled-unconfirmed">
                Agendado não confirmado
              </option>
              <option value="scheduled-confirmed">Agendado confirmado</option>
              <option value="unscheduled">Desmarcado</option>
              <option value="in-progress">Em andamento</option>
              <option value="completed">Concluído</option>
              <option value="cancelled">Cancelado</option>
            </select>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer com botões de ação -->
  <div footer class="flex justify-end space-x-3">
    <button
      type="button"
      (click)="closeModal()"
      class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
    >
      Cancelar
    </button>
    <button
      type="button"
      (click)="onSubmit()"
      [disabled]="isSubmitting"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
      [class.opacity-50]="isSubmitting"
    >
      <svg
        *ngIf="isSubmitting"
        class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <svg
        *ngIf="!isSubmitting"
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-1"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
          clip-rule="evenodd"
        />
      </svg>
      {{ isSubmitting ? "Salvando..." : "Salvar" }}
    </button>
  </div>
</app-modal>
