import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  ElementRef,
  HostListener,
  AfterViewInit,
  Renderer2
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ModalService } from '../../shared/services/modal.service';
import { ModalComponent } from '../../shared/components/modal/modal.component';

interface WeekDay {
  date: Date;
  dayName: string;
  dayNumber: string;
  isToday: boolean;
  events: any[];
}

@Component({
  selector: 'app-week-view',
  templateUrl: './week-view.component.html',
  styleUrls: ['./week-view.component.scss'],
  standalone: true,
  imports: [CommonModule, ModalComponent],
})
export class WeekViewComponent implements OnChanges, AfterViewInit {
  // Modal properties
  modalTitle: string = '';
  modalContent: any = null;
  isModalOpen: boolean = false;
  @Input() currentDate: Date = new Date();
  @Input() attendances: any[] = []; // Renomeado para schedulings na interface, mantido para compatibilidade
  @Output() createScheduling = new EventEmitter<{ date: Date; time: string }>();

  weekDays: WeekDay[] = [];

  constructor(
    private modalService: ModalService,
    private router: Router,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['currentDate'] ||
      changes['attendances'] ||
      changes['treatments']
    ) {
      this.generateWeekDays();
    }
  }

  generateWeekDays(): void {
    this.weekDays = [];

    // Encontrar o primeiro dia da semana (domingo)
    const firstDayOfWeek = new Date(this.currentDate);
    const day = this.currentDate.getDay();
    firstDayOfWeek.setDate(this.currentDate.getDate() - day);

    // Gerar os 7 dias da semana
    for (let i = 0; i < 7; i++) {
      const date = new Date(firstDayOfWeek);
      date.setDate(firstDayOfWeek.getDate() + i);

      const dayNames = [
        'Domingo',
        'Segunda',
        'Terça',
        'Quarta',
        'Quinta',
        'Sexta',
        'Sábado',
      ];

      this.weekDays.push({
        date,
        dayName: dayNames[i],
        dayNumber: date.getDate().toString().padStart(2, '0'),
        isToday: this.isToday(date),
        events: this.getEventsForDate(date),
      });
    }
  }

  getEventsForDate(date: Date): any[] {
    const events: any[] = [];

    // Filtrar agendamentos para a data
    const filteredSchedulings = this.attendances.filter((scheduling) => {
      // Extrair os componentes da data do agendamento para evitar problemas de fuso horário
      const schedulingDateStr = scheduling.date.toString();
      const parts = schedulingDateStr.split('-');
      if (parts.length === 3) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // Meses em JS são 0-indexed
        const day = parseInt(parts[2]);

        // Comparar os componentes individuais da data
        return (
          day === date.getDate() &&
          month === date.getMonth() &&
          year === date.getFullYear()
        );
      }

      // Fallback para o método anterior
      const schedulingDate = new Date(scheduling.date);
      return (
        schedulingDate.getDate() === date.getDate() &&
        schedulingDate.getMonth() === date.getMonth() &&
        schedulingDate.getFullYear() === date.getFullYear()
      );
    });

    // Adicionar agendamentos aos eventos
    filteredSchedulings.forEach((scheduling) => {
      events.push({
        id: scheduling.id,
        type: 'scheduling',
        title: `${scheduling.patientName || 'Paciente'} - ${scheduling.time}`,
        time: scheduling.time,
        status: scheduling.status,
        data: scheduling,
      });
    });

    // Ordenar eventos por hora
    return events.sort((a, b) => a.time.localeCompare(b.time));
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }

  // Método para obter a cor de fundo com base no status
  getStatusColor(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'bg-yellow-100 border-yellow-300';
      case 'scheduled-confirmed':
        return 'bg-blue-100 border-blue-300';
      case 'unscheduled':
        return 'bg-red-100 border-red-300';
      case 'in-progress':
        return 'bg-indigo-100 border-indigo-300';
      case 'completed':
        return 'bg-green-100 border-green-300';
      case 'cancelled':
        return 'bg-red-100 border-red-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  }

  // Método para obter a cor do texto com base no status
  getStatusTextColor(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'text-yellow-800';
      case 'scheduled-confirmed':
        return 'text-blue-800';
      case 'unscheduled':
        return 'text-red-800';
      case 'in-progress':
        return 'text-indigo-800';
      case 'completed':
        return 'text-green-800';
      case 'cancelled':
        return 'text-red-800';
      default:
        return 'text-gray-800';
    }
  }

  // Método para formatar a data
  formatDate(date: Date): string {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
    });
  }

  // Método para traduzir o status
  translateStatus(status: string): string {
    switch (status) {
      case 'scheduled-unconfirmed':
        return 'Agendado não confirmado';
      case 'scheduled-confirmed':
        return 'Agendado confirmado';
      case 'unscheduled':
        return 'Desmarcado';
      case 'in-progress':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  }

  // Método para abrir o modal com as informações do evento
  openEventModal(event: any) {
    // Apenas agendamentos são considerados agora
    const title = `Agendamento: ${event.data.patientName}`;
    const content = {
      type: 'scheduling',
      id: event.data.id,
      patientId: event.data.patientId,
      patientName: event.data.patientName,
      date: new Date(event.data.date).toLocaleDateString('pt-BR'),
      time: event.data.time,
      dentistId: event.data.dentistId,
      dentistName: event.data.dentistName || 'Não informado',
      status: this.translateStatus(event.data.status),
      statusOriginal: event.data.status,
      motivo: event.data.motivo,
      notes: event.data.notes,
      cost: event.data.cost,
      paid: event.data.paid,
      treatmentId: event.data.treatmentId
    };

    this.modalTitle = title;
    this.modalContent = content;
    this.isModalOpen = true;
  }

  // Método para navegar para a página de detalhes do paciente
  navigateToPatientDetails() {
    if (this.modalContent && this.modalContent.patientId) {
      this.router.navigate(['/patients', this.modalContent.patientId]);
    }
  }

  // Método para editar o agendamento
  editScheduling() {
    if (this.modalContent && this.modalContent.id) {
      this.router.navigate(['/schedule/edit', this.modalContent.id]);
    }
  }

  closeModal() {
    this.isModalOpen = false;
  }

  // Método para criar um novo agendamento ao clicar em uma data
  createNewScheduling(date: Date) {
    // Emitir evento para o componente pai abrir o modal
    this.createScheduling.emit({
      date: date,
      time: '',
    });
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Implementação de AfterViewInit para configurar os tooltips
  ngAfterViewInit(): void {
    this.setupTooltips();
  }

  // Configurar tooltips após a renderização da view
  setupTooltips(): void {
    // Selecionar todos os eventos do calendário
    const eventElements = this.elementRef.nativeElement.querySelectorAll('.calendar-week .cursor-pointer');

    // Adicionar listeners para cada evento
    eventElements.forEach((element: HTMLElement) => {
      // Adicionar listener para mouseover
      this.renderer.listen(element, 'mouseover', (event) => {
        this.showTooltip(element, event);
      });

      // Adicionar listener para mouseout
      this.renderer.listen(element, 'mouseout', () => {
        this.hideTooltips();
      });
    });
  }

  // Mostrar tooltip para um elemento específico
  showTooltip(element: HTMLElement, event: MouseEvent): void {
    // Encontrar o tooltip associado ao elemento
    const tooltipContent = element.querySelector('.tooltip-content') as HTMLElement;

    if (tooltipContent) {
      // Posicionar o tooltip
      const rect = element.getBoundingClientRect();

      // Definir posição do tooltip
      tooltipContent.style.left = `${rect.left + window.scrollX}px`;
      tooltipContent.style.top = `${rect.bottom + window.scrollY}px`;

      // Mostrar o tooltip
      tooltipContent.style.display = 'block';
    }
  }

  // Esconder todos os tooltips
  hideTooltips(): void {
    const tooltips = this.elementRef.nativeElement.querySelectorAll('.tooltip-content');
    tooltips.forEach((tooltip: HTMLElement) => {
      tooltip.style.display = 'none';
    });
  }

  // Reposicionar tooltips quando a janela for redimensionada
  @HostListener('window:resize')
  onWindowResize(): void {
    this.hideTooltips();
  }

  // Reposicionar tooltips quando a página for rolada
  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.hideTooltips();
  }
}
