<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Agenda</h1>
      <p class="text-gray-600">Visualize e gerencie os agendamentos</p>
    </div>
    <button
      (click)="openModal()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Novo Agendamento
    </button>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Mensagem de erro -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
  </div>

  <!-- Conteúdo principal -->
  <div
    *ngIf="!isLoading && !error"
    class="bg-white rounded-lg shadow-sm overflow-hidden"
  >
    <!-- Cabeçalho do calendário -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex flex-col sm:flex-row justify-between items-center mb-4">
        <!-- Título do período atual -->
        <h2 class="text-xl font-semibold mb-2 sm:mb-0">
          {{ formatPeriodTitle() }}
        </h2>

        <!-- Botões de navegação -->
        <div class="flex space-x-2">
          <button
            (click)="today()"
            class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium transition-colors"
          >
            Hoje
          </button>
          <button
            (click)="previousPeriod()"
            class="p-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <button
            (click)="nextPeriod()"
            class="p-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Seletor de visualização -->
      <div class="flex justify-center">
        <div class="inline-flex rounded-md shadow-sm" role="group">
          <button
            (click)="setView('month')"
            [ngClass]="{
              'bg-blue-600 text-white': currentView === 'month',
              'bg-white text-gray-700 hover:bg-gray-50': currentView !== 'month'
            }"
            class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-l-lg focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700 transition-colors"
          >
            Mês
          </button>
          <button
            (click)="setView('week')"
            [ngClass]="{
              'bg-blue-600 text-white': currentView === 'week',
              'bg-white text-gray-700 hover:bg-gray-50': currentView !== 'week'
            }"
            class="px-4 py-2 text-sm font-medium border-t border-b border-gray-200 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700 transition-colors"
          >
            Semana
          </button>
          <button
            (click)="setView('day')"
            [ngClass]="{
              'bg-blue-600 text-white': currentView === 'day',
              'bg-white text-gray-700 hover:bg-gray-50': currentView !== 'day'
            }"
            class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-r-md focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700 transition-colors"
          >
            Dia
          </button>
        </div>
      </div>
    </div>

    <!-- Conteúdo do calendário -->
    <div class="p-4">
      <!-- Visualização mensal -->
      <app-month-view
        *ngIf="currentView === 'month'"
        [currentDate]="currentDate"
        [attendances]="schedulings"
        (createScheduling)="openModalFromView($event)"
      >
      </app-month-view>

      <!-- Visualização semanal -->
      <app-week-view
        *ngIf="currentView === 'week'"
        [currentDate]="currentDate"
        [attendances]="schedulings"
        (createScheduling)="openModalFromView($event)"
      >
      </app-week-view>

      <!-- Visualização diária -->
      <app-day-view
        *ngIf="currentView === 'day'"
        [currentDate]="currentDate"
        [attendances]="schedulings"
        (createScheduling)="openModalFromView($event)"
      >
      </app-day-view>
    </div>
  </div>
</div>

<!-- Modal de Agendamento -->
<app-schedule-modal
  [isOpen]="isModalOpen"
  [initialDate]="initialDate"
  [initialTime]="initialTime"
  (close)="closeModal()"
  (save)="saveScheduling($event)"
></app-schedule-modal>
