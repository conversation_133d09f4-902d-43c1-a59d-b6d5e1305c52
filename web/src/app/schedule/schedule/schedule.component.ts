import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SchedulingService } from '../../core/services/scheduling.service';
import { NotificationService } from '../../core/services/notification.service';
import { MonthViewComponent } from '../month-view/month-view.component';
import { WeekViewComponent } from '../week-view/week-view.component';
import { DayViewComponent } from '../day-view/day-view.component';
import { ScheduleModalComponent } from '../schedule-modal/schedule-modal.component';
import { Scheduling } from '../../core/models/scheduling.model';
import { PaginatedResponse } from '../../core/models/pagination.model';

@Component({
  selector: 'app-schedule',
  templateUrl: './schedule.component.html',
  styleUrls: ['./schedule.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MonthViewComponent,
    WeekViewComponent,
    DayViewComponent,
    ScheduleModalComponent,
  ],
})
export class ScheduleComponent implements OnInit {
  currentView: 'month' | 'week' | 'day' = 'month';
  isLoading = false;
  error: string | null = null;

  // Dados para o calendário
  schedulings: Scheduling[] = [];

  // Data atual selecionada
  currentDate = new Date();

  // Modal de agendamento
  isModalOpen = false;
  initialDate = '';
  initialTime = '';
  selectedDate: Date | null = null;

  constructor(
    private schedulingService: SchedulingService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;
    this.error = null;

    // Carregar agendamentos sem paginação usando o parâmetro noPagination=true
    this.schedulingService.getAllSchedulings().subscribe({
      next: (response) => {
        console.log('Resposta da API de agendamentos:', response);

        // Verificar se é um objeto paginado ou um array
        if (Array.isArray(response)) {
          this.schedulings = response;
          console.log(
            'Agendamentos carregados como array:',
            this.schedulings.length
          );
        } else if (
          response &&
          typeof response === 'object' &&
          'data' in response
        ) {
          this.schedulings = (response as PaginatedResponse<Scheduling>).data;
          console.log(
            'Agendamentos carregados como objeto paginado:',
            this.schedulings.length
          );
        } else {
          console.error('Formato de resposta inesperado:', response);
          this.schedulings = [];
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar agendamentos:', error);
        this.error =
          'Não foi possível carregar os agendamentos. Por favor, tente novamente mais tarde.';
        this.isLoading = false;
        this.notificationService.error('Erro ao carregar agendamentos');
      },
    });
  }

  // Métodos para alternar entre as visualizações
  setView(view: 'month' | 'week' | 'day'): void {
    this.currentView = view;
  }

  // Métodos para navegação no calendário
  previousPeriod(): void {
    switch (this.currentView) {
      case 'month':
        this.currentDate = new Date(
          this.currentDate.getFullYear(),
          this.currentDate.getMonth() - 1,
          1
        );
        break;
      case 'week':
        this.currentDate = new Date(
          this.currentDate.getTime() - 7 * 24 * 60 * 60 * 1000
        );
        break;
      case 'day':
        this.currentDate = new Date(
          this.currentDate.getTime() - 24 * 60 * 60 * 1000
        );
        break;
    }
  }

  nextPeriod(): void {
    switch (this.currentView) {
      case 'month':
        this.currentDate = new Date(
          this.currentDate.getFullYear(),
          this.currentDate.getMonth() + 1,
          1
        );
        break;
      case 'week':
        this.currentDate = new Date(
          this.currentDate.getTime() + 7 * 24 * 60 * 60 * 1000
        );
        break;
      case 'day':
        this.currentDate = new Date(
          this.currentDate.getTime() + 24 * 60 * 60 * 1000
        );
        break;
    }
  }

  today(): void {
    this.currentDate = new Date();
  }

  // Formatação de data para exibição
  formatPeriodTitle(): string {
    const options: Intl.DateTimeFormatOptions = {};

    switch (this.currentView) {
      case 'month':
        options.month = 'long';
        options.year = 'numeric';
        break;
      case 'week':
        // Encontrar o primeiro dia da semana (domingo)
        const firstDayOfWeek = new Date(this.currentDate);
        const day = this.currentDate.getDay();
        firstDayOfWeek.setDate(this.currentDate.getDate() - day);

        // Encontrar o último dia da semana (sábado)
        const lastDayOfWeek = new Date(firstDayOfWeek);
        lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);

        // Formatar como "DD/MM - DD/MM/YYYY"
        return `${firstDayOfWeek.getDate().toString().padStart(2, '0')}/${(
          firstDayOfWeek.getMonth() + 1
        )
          .toString()
          .padStart(2, '0')} - ${lastDayOfWeek
          .getDate()
          .toString()
          .padStart(2, '0')}/${(lastDayOfWeek.getMonth() + 1)
          .toString()
          .padStart(2, '0')}/${lastDayOfWeek.getFullYear()}`;

      case 'day':
        options.day = 'numeric';
        options.month = 'long';
        options.year = 'numeric';
        break;
    }

    return this.currentDate.toLocaleDateString('pt-BR', options);
  }

  // Métodos para o modal de agendamento
  openModal(date?: Date, time?: string): void {
    // Se uma data específica for fornecida, use-a e armazene-a
    if (date) {
      this.selectedDate = new Date(date);
      console.log('Abrindo modal com data específica:', this.selectedDate);
      this.initialDate = this.formatDateForInput(this.selectedDate);
    } else {
      // Caso contrário, use a data atual
      this.selectedDate = new Date();
      console.log('Abrindo modal com a data atual:', this.selectedDate);
      this.initialDate = this.formatDateForInput(this.selectedDate);
    }

    if (time) {
      this.initialTime = time;
    } else {
      this.initialTime = '';
    }

    this.isModalOpen = true;
  }

  // Método para abrir o modal a partir dos componentes de visualização
  openModalFromView(event: { date: Date; time: string }): void {
    // Garantir que a data seja um objeto Date válido
    this.selectedDate =
      event.date instanceof Date ? new Date(event.date) : new Date(event.date);

    console.log(
      'Abrindo modal com data selecionada do calendário:',
      this.selectedDate,
      'Dia:',
      this.selectedDate.getDate(),
      'Mês:',
      this.selectedDate.getMonth() + 1,
      'Ano:',
      this.selectedDate.getFullYear()
    );

    // Formatar a data para o input
    this.initialDate = this.formatDateForInput(this.selectedDate);
    this.initialTime = event.time || '';
    this.isModalOpen = true;
  }

  closeModal(): void {
    // Fechar o modal, mas manter a data selecionada para uso futuro
    this.isModalOpen = false;

    // Não limpar selectedDate aqui, pois queremos manter a data selecionada
    // para o caso de o usuário abrir o modal novamente
  }

  saveScheduling(schedulingData: any): void {
    this.isLoading = true;

    this.schedulingService.createScheduling(schedulingData).subscribe({
      next: (_) => {
        this.notificationService.success('Agendamento criado com sucesso!');
        this.loadData(); // Recarregar os dados para atualizar a agenda
        this.isLoading = false;
        this.closeModal();
      },
      error: (error) => {
        console.error('Erro ao criar agendamento:', error);
        this.notificationService.error(
          'Erro ao criar agendamento. Por favor, tente novamente.'
        );
        this.isLoading = false;
      },
    });
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
