.board-page {
  min-height: calc(100vh - 64px);
}

.board-container {
  cursor: grab;
  
  &.active {
    cursor: grabbing;
  }
}

.non-draggable {
  user-select: none;
}

/* Estilo para a barra de rolagem */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

/* Estilo para os cards durante o arraste */
.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
