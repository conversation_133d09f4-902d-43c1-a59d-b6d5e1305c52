import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { ProcedureService } from '../../core/services/procedure.service';
import { PatientService } from '../../core/services/patient.service';
import { DentistService } from '../../core/services/dentist.service';
import { Patient } from '../../core/models/patient.model';
import { Dentist } from '../../core/models/dentist.model';
import { Procedure } from '../../core/models/procedure.model';
import { TreatmentProcedure } from '../../core/models/treatment-procedure.model';
import { NotificationService } from '../../core/services/notification.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-procedure-form-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './procedure-form-modal.component.html',
  styleUrls: ['./procedure-form-modal.component.scss']
})
export class ProcedureFormModalComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() saved = new EventEmitter<TreatmentProcedure>();

  procedureForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  patients: Patient[] = [];
  dentists: Dentist[] = [];
  procedures: Procedure[] = [];
  selectedProcedure: Procedure | null = null;

  constructor(
    private fb: FormBuilder,
    private treatmentPlanService: TreatmentPlanService,
    private procedureService: ProcedureService,
    private patientService: PatientService,
    private dentistService: DentistService,
    private notificationService: NotificationService
  ) {
    this.procedureForm = this.fb.group({
      patientId: ['', [Validators.required]],
      procedureId: ['', [Validators.required]],
      name: ['', [Validators.required]],
      value: [0, [Validators.required, Validators.min(0)]],
      tooth: [''],
      professionalId: ['', [Validators.required]],
      notes: [''],
      nextVisitDetails: ['']
    });
  }

  ngOnInit(): void {
    this.loadPatients();
    this.loadDentists();
    this.loadProcedures();
  }

  async loadPatients(): Promise<void> {
    try {
      const response = await firstValueFrom(this.patientService.getPatients(1, 1000));
      this.patients = response.data;
    } catch (error) {
      console.error('Erro ao carregar pacientes:', error);
      this.notificationService.error('Erro ao carregar pacientes');
    }
  }

  async loadDentists(): Promise<void> {
    try {
      const response = await firstValueFrom(this.dentistService.getDentists(1, 1000));
      this.dentists = response.data;
    } catch (error) {
      console.error('Erro ao carregar dentistas:', error);
      this.notificationService.error('Erro ao carregar dentistas');
    }
  }

  async loadProcedures(): Promise<void> {
    try {
      const response = await firstValueFrom(this.procedureService.getProcedures(1, 1000));
      this.procedures = response.data;
    } catch (error) {
      console.error('Erro ao carregar procedimentos:', error);
      this.notificationService.error('Erro ao carregar procedimentos');
    }
  }

  onProcedureChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const procedureId = +select.value;
    
    if (procedureId) {
      this.selectedProcedure = this.procedures.find(p => p.id === procedureId) || null;
      
      if (this.selectedProcedure) {
        this.procedureForm.patchValue({
          name: this.selectedProcedure.name,
          value: this.selectedProcedure.defaultPrice
        });
      }
    } else {
      this.selectedProcedure = null;
      this.procedureForm.patchValue({
        name: '',
        value: 0
      });
    }
  }

  async onSubmit(): Promise<void> {
    if (this.procedureForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.procedureForm.controls).forEach(key => {
        const control = this.procedureForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      const formData = this.procedureForm.value;
      
      const procedureData = {
        patientId: +formData.patientId,
        procedureId: +formData.procedureId,
        name: formData.name,
        value: +formData.value,
        tooth: formData.tooth || undefined,
        professionalId: +formData.professionalId,
        notes: formData.notes || undefined,
        nextVisitDetails: formData.nextVisitDetails || undefined
      };

      const savedProcedure = await firstValueFrom(
        this.treatmentPlanService.createProcedureWithAutoPlan(procedureData)
      );

      this.saved.emit(savedProcedure);
      this.resetForm();
    } catch (err: any) {
      console.error('Erro ao salvar procedimento:', err);

      if (err.error?.message && Array.isArray(err.error.message)) {
        this.error = err.error.message.join(', ');
      } else if (err.error?.message) {
        this.error = err.error.message;
      } else {
        this.error = 'Ocorreu um erro ao salvar o procedimento. Por favor, tente novamente.';
      }
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.procedureForm.reset({
      patientId: '',
      procedureId: '',
      name: '',
      value: 0,
      tooth: '',
      professionalId: '',
      notes: '',
      nextVisitDetails: ''
    });
    this.selectedProcedure = null;
    this.error = null;
  }

  onCancel(): void {
    this.close.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get patientIdControl() { return this.procedureForm.get('patientId'); }
  get procedureIdControl() { return this.procedureForm.get('procedureId'); }
  get nameControl() { return this.procedureForm.get('name'); }
  get valueControl() { return this.procedureForm.get('value'); }
  get toothControl() { return this.procedureForm.get('tooth'); }
  get professionalIdControl() { return this.procedureForm.get('professionalId'); }
  get notesControl() { return this.procedureForm.get('notes'); }
  get nextVisitDetailsControl() { return this.procedureForm.get('nextVisitDetails'); }

  // Formatação de valor para exibição
  formatCurrency(value: number): string {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }
}
