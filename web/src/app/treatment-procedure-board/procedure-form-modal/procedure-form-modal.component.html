<div class="p-6">
  <form [formGroup]="procedureForm" (ngSubmit)="onSubmit()" #formElement="ngForm">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Paciente -->
      <div class="col-span-1">
        <label for="patientId" class="block text-sm font-medium text-gray-700 mb-1">
          Paciente <span class="text-red-500">*</span>
        </label>
        <select
          id="patientId"
          formControlName="patientId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              patientIdControl?.invalid && patientIdControl?.touched
          }"
        >
          <option value="">Selecione um paciente</option>
          <option *ngFor="let patient of patients" [value]="patient.id">
            {{ patient.name }}
          </option>
        </select>
        <div
          *ngIf="patientIdControl?.invalid && patientIdControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          Selecione um paciente
        </div>
      </div>

      <!-- Procedimento -->
      <div class="col-span-1">
        <label for="procedureId" class="block text-sm font-medium text-gray-700 mb-1">
          Procedimento <span class="text-red-500">*</span>
        </label>
        <select
          id="procedureId"
          formControlName="procedureId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              procedureIdControl?.invalid && procedureIdControl?.touched
          }"
          (change)="onProcedureChange($event)"
        >
          <option value="">Selecione um procedimento</option>
          <option *ngFor="let procedure of procedures" [value]="procedure.id">
            {{ procedure.name }}
          </option>
        </select>
        <div
          *ngIf="procedureIdControl?.invalid && procedureIdControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          Selecione um procedimento
        </div>
      </div>

      <!-- Nome do procedimento -->
      <div class="col-span-1">
        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
          Nome <span class="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="name"
          formControlName="name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              nameControl?.invalid && nameControl?.touched
          }"
        />
        <div
          *ngIf="nameControl?.invalid && nameControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          O nome é obrigatório
        </div>
      </div>

      <!-- Valor -->
      <div class="col-span-1">
        <label for="value" class="block text-sm font-medium text-gray-700 mb-1">
          Valor <span class="text-red-500">*</span>
        </label>
        <input
          type="number"
          id="value"
          formControlName="value"
          min="0"
          step="0.01"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              valueControl?.invalid && valueControl?.touched
          }"
        />
        <div
          *ngIf="valueControl?.invalid && valueControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          <div *ngIf="valueControl?.errors?.['required']">O valor é obrigatório</div>
          <div *ngIf="valueControl?.errors?.['min']">O valor não pode ser negativo</div>
        </div>
      </div>

      <!-- Dente -->
      <div class="col-span-1">
        <label for="tooth" class="block text-sm font-medium text-gray-700 mb-1">
          Dente
        </label>
        <input
          type="text"
          id="tooth"
          formControlName="tooth"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- Profissional -->
      <div class="col-span-1">
        <label for="professionalId" class="block text-sm font-medium text-gray-700 mb-1">
          Profissional <span class="text-red-500">*</span>
        </label>
        <select
          id="professionalId"
          formControlName="professionalId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              professionalIdControl?.invalid && professionalIdControl?.touched
          }"
        >
          <option value="">Selecione um profissional</option>
          <option *ngFor="let dentist of dentists" [value]="dentist.id">
            {{ dentist.name }}
          </option>
        </select>
        <div
          *ngIf="professionalIdControl?.invalid && professionalIdControl?.touched"
          class="text-red-500 text-xs mt-1"
        >
          Selecione um profissional
        </div>
      </div>

      <!-- Observações -->
      <div class="col-span-2">
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
          Observações
        </label>
        <textarea
          id="notes"
          formControlName="notes"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <!-- Detalhes para próxima consulta -->
      <div class="col-span-2">
        <label for="nextVisitDetails" class="block text-sm font-medium text-gray-700 mb-1">
          Detalhes para próxima consulta
        </label>
        <textarea
          id="nextVisitDetails"
          formControlName="nextVisitDetails"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>
    </div>

    <!-- Mensagem de erro -->
    <div *ngIf="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
      {{ error }}
    </div>

    <!-- Botões de ação -->
    <div class="flex justify-end gap-3 mt-6">
      <button
        type="button"
        (click)="onCancel()"
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Cancelar
      </button>
      <button
        type="submit"
        [disabled]="isSubmitting"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
      >
        <span *ngIf="isSubmitting" class="mr-2">
          <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        Salvar
      </button>
    </div>
  </form>
</div>
