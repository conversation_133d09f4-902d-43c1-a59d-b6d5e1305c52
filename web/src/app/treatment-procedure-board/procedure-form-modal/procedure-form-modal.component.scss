/* Estilos específicos para o formulário de procedimento */
:host {
  display: block;
  width: 100%;
}

/* Estilo para o campo de valor com formatação de moeda */
input[type="number"] {
  text-align: right;
}

/* Estilo para os campos inválidos */
.ng-invalid.ng-touched {
  border-color: #f56565;
}

/* Estilo para o botão de submit quando desabilitado */
button[type="submit"]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
