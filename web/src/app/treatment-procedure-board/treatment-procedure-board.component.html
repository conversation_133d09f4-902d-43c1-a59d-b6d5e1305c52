<div class="py-6 board-page">
  <div class="mb-6 flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Board de Procedimentos</h1>
      <p class="text-gray-600">Visão geral dos procedimentos de tratamento por status</p>
    </div>
    <button
      (click)="openProcedureFormModal()"
      class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
          clip-rule="evenodd"
        />
      </svg>
      Adicionar procedimento avulso
    </button>
  </div>

  <!-- Mensagem de carregamento -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Mensagem de erro -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
  </div>

  <!-- Board de procedimentos -->
  <div class="overflow-x-auto board-container">
    <div
      *ngIf="!isLoading && !error"
      class="grid grid-cols-4 gap-6 non-draggable"
      style="min-width: 1200px; width: max-content"
    >
      <!-- Coluna: Pendentes (azul) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-blue-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Pendentes
          </h2>
          <span
            class="bg-white text-blue-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ pendingProcedures.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="pendingProcedures"
          [cdkDropListData]="pendingProcedures"
          [cdkDropListConnectedTo]="[
            'inProgressProcedures',
            'completedProcedures',
            'cancelledProcedures'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-220px)] overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          <!-- Cartões de Procedimentos -->
          <div
            *ngFor="let procedure of pendingProcedures"
            cdkDrag
            (cdkDragStarted)="isDragging = true"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-blue-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openProcedureModal(procedure.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ procedure.name }}</h3>
              <span
                [ngClass]="getCategoryClass(procedure.patientCategory)"
                class="px-2 py-0.5 rounded-full text-xs font-medium"
              >
                {{ procedure.patientCategory }}
              </span>
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Paciente: {{ procedure.patientName }}
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Dentista: {{ procedure.professionalName }}
            </div>
            <div class="text-sm text-gray-600 mb-2" *ngIf="procedure.tooth">
              Dente: {{ procedure.tooth }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Valor: R$ {{ formatValue(procedure.value) }}
            </div>
          </div>

          <!-- Mensagem quando não há procedimentos -->
          <div
            *ngIf="pendingProcedures.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum procedimento pendente</p>
          </div>
        </div>
      </div>



      <!-- Coluna: Em andamento (laranja) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-orange-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Em andamento
          </h2>
          <span
            class="bg-white text-orange-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ inProgressProcedures.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="inProgressProcedures"
          [cdkDropListData]="inProgressProcedures"
          [cdkDropListConnectedTo]="[
            'pendingProcedures',
            'completedProcedures',
            'cancelledProcedures'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-220px)] overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          <!-- Cartões de Procedimentos -->
          <div
            *ngFor="let procedure of inProgressProcedures"
            cdkDrag
            (cdkDragStarted)="isDragging = true"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-orange-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openProcedureModal(procedure.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ procedure.name }}</h3>
              <span
                [ngClass]="getCategoryClass(procedure.patientCategory)"
                class="px-2 py-0.5 rounded-full text-xs font-medium"
              >
                {{ procedure.patientCategory }}
              </span>
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Paciente: {{ procedure.patientName }}
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Dentista: {{ procedure.professionalName }}
            </div>
            <div class="text-sm text-gray-600 mb-2" *ngIf="procedure.tooth">
              Dente: {{ procedure.tooth }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Valor: R$ {{ formatValue(procedure.value) }}
            </div>
          </div>

          <!-- Mensagem quando não há procedimentos -->
          <div
            *ngIf="inProgressProcedures.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum procedimento em andamento</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Concluídos (verde) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-green-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Concluídos
          </h2>
          <span
            class="bg-white text-green-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ completedProcedures.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="completedProcedures"
          [cdkDropListData]="completedProcedures"
          [cdkDropListConnectedTo]="[
            'pendingProcedures',
            'inProgressProcedures',
            'cancelledProcedures'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-220px)] overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          <!-- Cartões de Procedimentos -->
          <div
            *ngFor="let procedure of completedProcedures"
            cdkDrag
            (cdkDragStarted)="isDragging = true"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-green-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openProcedureModal(procedure.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ procedure.name }}</h3>
              <span
                [ngClass]="getCategoryClass(procedure.patientCategory)"
                class="px-2 py-0.5 rounded-full text-xs font-medium"
              >
                {{ procedure.patientCategory }}
              </span>
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Paciente: {{ procedure.patientName }}
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Dentista: {{ procedure.professionalName }}
            </div>
            <div class="text-sm text-gray-600 mb-2" *ngIf="procedure.tooth">
              Dente: {{ procedure.tooth }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Valor: R$ {{ formatValue(procedure.value) }}
            </div>
          </div>

          <!-- Mensagem quando não há procedimentos -->
          <div
            *ngIf="completedProcedures.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum procedimento concluído</p>
          </div>
        </div>
      </div>

      <!-- Coluna: Cancelados (vermelho) -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div
          class="bg-red-600 text-white px-4 py-3 flex justify-between items-center"
        >
          <h2 class="font-semibold text-gray-50 text-base">
            Cancelados
          </h2>
          <span
            class="bg-white text-red-600 rounded-full px-2 py-1 text-xs font-bold"
            >{{ cancelledProcedures.length }}</span
          >
        </div>

        <div
          cdkDropList
          id="cancelledProcedures"
          [cdkDropListData]="cancelledProcedures"
          [cdkDropListConnectedTo]="[
            'pendingProcedures',
            'inProgressProcedures',
            'completedProcedures'
          ]"
          (cdkDropListDropped)="onDrop($event)"
          class="p-4 h-[calc(100vh-220px)] overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          <!-- Cartões de Procedimentos -->
          <div
            *ngFor="let procedure of cancelledProcedures"
            cdkDrag
            (cdkDragStarted)="isDragging = true"
            class="bg-gray-50 rounded-md p-3 shadow-sm border-l-4 border-red-500 cursor-pointer hover:shadow-md transition-shadow"
            (click)="openProcedureModal(procedure.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-900">{{ procedure.name }}</h3>
              <span
                [ngClass]="getCategoryClass(procedure.patientCategory)"
                class="px-2 py-0.5 rounded-full text-xs font-medium"
              >
                {{ procedure.patientCategory }}
              </span>
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Paciente: {{ procedure.patientName }}
            </div>
            <div class="text-sm text-gray-600 mb-2">
              Dentista: {{ procedure.professionalName }}
            </div>
            <div class="text-sm text-gray-600 mb-2" *ngIf="procedure.tooth">
              Dente: {{ procedure.tooth }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Valor: R$ {{ formatValue(procedure.value) }}
            </div>
          </div>

          <!-- Mensagem quando não há procedimentos -->
          <div
            *ngIf="cancelledProcedures.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <p>Nenhum procedimento cancelado</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para detalhes do procedimento -->
<app-modal [isOpen]="isModalOpen" [title]="modalTitle" (close)="closeModal()" [showDefaultFooter]="true">
  <div
    *ngIf="modalContent"
    class="p-6 md:p-8 max-h-[70vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
  >
    <!-- Layout de duas colunas para desktop, uma coluna para celular -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10 w-full lg:w-[900px]">

      <!-- Informações do Plano de Tratamento -->
      <div class="bg-blue-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-blue-100">
        <h3 class="text-lg font-semibold mb-4 text-blue-800 border-b border-blue-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Plano de Tratamento
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Identificação:</span>
            <span class="text-gray-900">{{ modalContent.plan.id ? 'Plano #' + modalContent.plan.id : 'Não informado' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Status:</span>
            <span class="text-gray-900">{{ modalContent.plan.status || 'Não informado' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Valor Total:</span>
            <span class="text-gray-900">R$ {{ formatValue(modalContent.plan.totalValue) }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Progresso:</span>
            <div class="flex items-center">
              <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                <div class="bg-blue-600 h-2.5 rounded-full" [style.width]="(modalContent.plan.completionPercentage || 0) + '%'"></div>
              </div>
              <span>{{ modalContent.plan.completionPercentage || 0 }}%</span>
            </div>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Criado em:</span>
            <span class="text-gray-900">{{ modalContent.plan.createdAt | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>
      </div>

      <!-- Informações do Procedimento -->
      <div class="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100">
        <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          Procedimento
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Nome:</span>
            <span class="text-gray-900">{{ modalContent.procedure.name }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Status:</span>
            <span class="text-gray-900">{{ modalContent.statusLabels[modalContent.procedure.status] }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Valor:</span>
            <span class="text-gray-900">R$ {{ formatValue(modalContent.procedure.value) }}</span>
          </div>
          <div class="flex flex-col" *ngIf="modalContent.procedure.tooth">
            <span class="font-medium text-gray-700">Dente:</span>
            <span class="text-gray-900">{{ modalContent.procedure.tooth }}</span>
          </div>
          <div class="flex flex-col" *ngIf="modalContent.procedure.executionDate">
            <span class="font-medium text-gray-700">Data de execução:</span>
            <span class="text-gray-900">{{ modalContent.procedure.executionDate | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Criado em:</span>
            <span class="text-gray-900">{{ modalContent.procedure.createdAt | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="flex flex-col" *ngIf="modalContent.procedure.updatedAt">
            <span class="font-medium text-gray-700">Atualizado em:</span>
            <span class="text-gray-900">{{ modalContent.procedure.updatedAt | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>
      </div>

      <!-- Informações do Agendamento (se houver) -->
      <div class="bg-green-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-green-100" *ngIf="modalContent.appointment">
        <h3 class="text-lg font-semibold mb-4 text-green-800 border-b border-green-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          Agendamento
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Data:</span>
            <span class="text-gray-900">{{ modalContent.appointment.date | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Horário:</span>
            <span class="text-gray-900">{{ modalContent.appointment.time }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Status:</span>
            <span class="text-gray-900">{{ modalContent.appointment.status }}</span>
          </div>
          <div class="flex flex-col" *ngIf="modalContent.appointment.notes">
            <span class="font-medium text-gray-700">Observações:</span>
            <span class="text-gray-900">{{ modalContent.appointment.notes }}</span>
          </div>
        </div>
      </div>


      <!-- Informações do Paciente -->
      <div class="bg-yellow-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-yellow-100">
        <h3 class="text-lg font-semibold mb-4 text-yellow-800 border-b border-yellow-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          Paciente
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Nome:</span>
            <span class="text-gray-900">{{ modalContent.patient.name }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Categoria:</span>
            <span class="text-gray-900">{{ modalContent.patient.category || 'Rotina' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Telefone:</span>
            <span class="text-gray-900">{{ modalContent.patient.phone || 'Não informado' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Email:</span>
            <span class="text-gray-900">{{ modalContent.patient.email || 'Não informado' }}</span>
          </div>
        </div>
      </div>

      <!-- Informações do Dentista -->
      <div class="bg-purple-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-purple-100">
        <h3 class="text-lg font-semibold mb-4 text-purple-800 border-b border-purple-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Dentista
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Nome:</span>
            <span class="text-gray-900">{{ modalContent.dentist.name }}</span>
          </div>
          <div class="flex flex-col">
            <span class="font-medium text-gray-700">Especialidade:</span>
            <span class="text-gray-900">{{ modalContent.dentist.specialty || 'Não informado' }}</span>
          </div>
        </div>
      </div>

      <!-- Observações do Procedimento -->
      <div class="bg-orange-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-orange-100" *ngIf="modalContent.procedure.notes || modalContent.procedure.nextVisitDetails">
        <h3 class="text-lg font-semibold mb-4 text-orange-800 border-b border-orange-200 pb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Observações
        </h3>
        <div class="space-y-4">
          <div class="flex flex-col" *ngIf="modalContent.procedure.notes">
            <span class="font-medium text-gray-700">Notas:</span>
            <span class="text-gray-900 whitespace-pre-line">{{ modalContent.procedure.notes }}</span>
          </div>
          <div class="flex flex-col" *ngIf="modalContent.procedure.nextVisitDetails">
            <span class="font-medium text-gray-700">Detalhes para próxima visita:</span>
            <span class="text-gray-900 whitespace-pre-line">{{ modalContent.procedure.nextVisitDetails }}</span>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Footer com botões de ação -->
  <div footer class="flex justify-end space-x-5 w-full">
    <button
      *ngIf="modalContent && modalContent.procedure.status === 'pending'"
      class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm hover:shadow-md flex items-center text-base"
      (click)="scheduleAppointment(modalContent.procedure.id)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      Agendar
    </button>
    <button
      *ngIf="modalContent && modalContent.procedure.status === 'scheduled'"
      class="px-6 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors shadow-sm hover:shadow-md flex items-center text-base"
      (click)="startProcedure(modalContent.procedure.id)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      Iniciar
    </button>
    <button
      *ngIf="modalContent && modalContent.procedure.status === 'in_progress'"
      class="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center text-base"
      (click)="completeProcedure(modalContent.procedure.id)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
      Concluir
    </button>
    <button
      *ngIf="modalContent && modalContent.procedure.status !== 'cancelled' && modalContent.procedure.status !== 'completed'"
      class="px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors shadow-sm hover:shadow-md flex items-center text-base"
      (click)="cancelProcedure(modalContent.procedure.id)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
      Cancelar
    </button>
  </div>
</app-modal>

<!-- Modal para criação de procedimento avulso -->
<app-modal
  [isOpen]="isProcedureFormModalOpen"
  title="Adicionar procedimento avulso"
  (close)="closeProcedureFormModal()"
  [showDefaultFooter]="false"
>
  <app-procedure-form-modal
    (close)="closeProcedureFormModal()"
    (saved)="onProcedureSaved($event)"
  ></app-procedure-form-modal>
</app-modal>
