import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
  DragDropModule,
} from '@angular/cdk/drag-drop';
import { PatientService } from '../core/services/patient.service';
import { SchedulingService } from '../core/services/scheduling.service';
import { TreatmentPlanService } from '../core/services/treatment-plan.service';
import { NotificationService } from '../core/services/notification.service';
import { DentistService } from '../core/services/dentist.service';
import { ModalComponent } from '../shared/components/modal/modal.component';
import { TreatmentProcedure, TreatmentProcedureStatus, TreatmentProcedureStatusLabels } from '../core/models/treatment-procedure.model';
import { ProcedureFormModalComponent } from './procedure-form-modal/procedure-form-modal.component';

interface ProcedureCard {
  id: number;
  name: string;
  patientId: number;
  patientName: string;
  patientCategory: string;
  professionalId: number;
  professionalName: string;
  treatmentPlanId: number;
  treatmentPlanName?: string;
  tooth?: string;
  executionDate?: Date;
  status: TreatmentProcedureStatus;
  priority: 'low' | 'medium' | 'high';
  createdAt?: Date;
  updatedAt?: Date;
  value: number;
}

@Component({
  selector: 'app-treatment-procedure-board',
  standalone: true,
  imports: [CommonModule, DragDropModule, ModalComponent, ProcedureFormModalComponent],
  templateUrl: './treatment-procedure-board.component.html',
  styleUrls: ['./treatment-procedure-board.component.scss']
})
export class TreatmentProcedureBoardComponent implements OnInit, OnDestroy {
  pendingProcedures: ProcedureCard[] = []; // Pendente
  inProgressProcedures: ProcedureCard[] = []; // Em andamento
  completedProcedures: ProcedureCard[] = []; // Concluído
  cancelledProcedures: ProcedureCard[] = []; // Cancelado

  isLoading = true;
  error = '';

  // Modal properties
  modalTitle: string = '';
  modalContent: any = null;
  isModalOpen: boolean = false;
  selectedProcedure: TreatmentProcedure | null = null;

  // Modal para criação de procedimento avulso
  isProcedureFormModalOpen: boolean = false;

  // Propriedade para controlar o arraste
  isDragging: boolean = false;

  // Propriedades para controlar o arraste horizontal do board
  isScrolling: boolean = false;
  startX: number = 0;
  scrollLeft: number = 0;
  boardContainer: HTMLElement | null = null;

  // Status labels para exibição
  statusLabels = TreatmentProcedureStatusLabels;

  constructor(
    private patientService: PatientService,
    private schedulingService: SchedulingService,
    private treatmentPlanService: TreatmentPlanService,
    private notificationService: NotificationService,
    private dentistService: DentistService
  ) {}

  ngOnInit(): void {
    this.loadProcedures();
    // Inicializar o container do board após o carregamento da view
    setTimeout(() => {
      this.initDragScroll();
    }, 500);
  }

  // Inicializar o arraste horizontal
  initDragScroll(): void {
    this.boardContainer = document.querySelector('.board-container');
    if (!this.boardContainer) return;

    let isMouseDown = false;
    let startX: number;
    let scrollLeft: number;

    // Mouse events
    this.boardContainer.addEventListener('mousedown', (e) => {
      // Ignorar se o clique foi em um card ou elemento arrastável
      const target = e.target as HTMLElement;
      if (target.closest('[cdkDrag]') || target.closest('.cdk-drag-handle')) {
        return;
      }

      isMouseDown = true;
      this.isScrolling = true;
      this.boardContainer!.classList.add('active');
      startX = e.pageX - this.boardContainer!.offsetLeft;
      scrollLeft = this.boardContainer!.scrollLeft;
      e.preventDefault();
    });

    document.addEventListener('mouseup', () => {
      isMouseDown = false;
      this.isScrolling = false;
      if (this.boardContainer) {
        this.boardContainer.classList.remove('active');
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!isMouseDown || !this.boardContainer) return;
      e.preventDefault();
      const x = e.pageX - this.boardContainer.offsetLeft;
      const walk = (x - startX) * 2; // Velocidade do scroll
      this.boardContainer.scrollLeft = scrollLeft - walk;
    });

    // Touch events para dispositivos móveis
    this.boardContainer.addEventListener('touchstart', (e) => {
      // Ignorar se o toque foi em um card ou elemento arrastável
      const target = e.target as HTMLElement;
      if (target.closest('[cdkDrag]') || target.closest('.cdk-drag-handle')) {
        return;
      }

      isMouseDown = true;
      this.isScrolling = true;
      this.boardContainer!.classList.add('active');
      startX = e.touches[0].pageX - this.boardContainer!.offsetLeft;
      scrollLeft = this.boardContainer!.scrollLeft;
    }, { passive: false });

    document.addEventListener('touchend', () => {
      isMouseDown = false;
      this.isScrolling = false;
      if (this.boardContainer) {
        this.boardContainer.classList.remove('active');
      }
    });

    document.addEventListener('touchmove', (e) => {
      if (!isMouseDown || !this.boardContainer) return;
      e.preventDefault();
      const x = e.touches[0].pageX - this.boardContainer.offsetLeft;
      const walk = (x - startX) * 2;
      this.boardContainer.scrollLeft = scrollLeft - walk;
    }, { passive: false });
  }

  // Limpar os event listeners quando o componente for destruído
  ngOnDestroy(): void {
    // Remover event listeners do documento
    document.removeEventListener('mouseup', () => {});
    document.removeEventListener('mousemove', () => {});
    document.removeEventListener('touchend', () => {});
    document.removeEventListener('touchmove', () => {});
  }

  loadProcedures(): void {
    this.isLoading = true;
    this.pendingProcedures = [];
    this.inProgressProcedures = [];
    this.completedProcedures = [];
    this.cancelledProcedures = [];

    console.log('Iniciando carregamento de procedimentos...');

    // Buscar todos os planos de tratamento ativos
    this.treatmentPlanService.getAllTreatmentPlans().subscribe({
      next: (plans) => {
        console.log(`Recebidos ${plans.length} planos de tratamento`);

        // Criar um array para armazenar todas as promessas de carregamento de procedimentos
        const procedurePromises: Promise<void>[] = [];
        let totalProcedures = 0;

        // Para cada plano, buscar os procedimentos
        plans.forEach(plan => {
          console.log(`Processando plano #${plan.id} - patientId: ${plan.patientId}`);

          // Verificar se o plano tem procedimentos
          if (plan.procedures && plan.procedures.length > 0) {
            console.log(`Plano #${plan.id} tem ${plan.procedures.length} procedimentos`);
            totalProcedures += plan.procedures.length;

            // Para cada procedimento, criar um card
            plan.procedures.forEach(procedure => {
              console.log(`Processando procedimento #${procedure.id} - status: ${procedure.status}, professionalId: ${procedure.professionalId}`);

              // Criar uma promessa para carregar os dados do paciente e do dentista
              const promise = new Promise<void>((resolve) => {
                console.log(`Processando procedimento detalhado:`, {
                  id: procedure.id,
                  name: procedure.name,
                  status: procedure.status,
                  professionalId: procedure.professionalId,
                  treatmentPlanId: plan.id,
                  patientId: plan.patientId
                });

                // Verificar se o plano tem um ID de paciente válido
                if (!plan.patientId || isNaN(plan.patientId)) {
                  console.error(`Plano ${plan.id} não tem um ID de paciente válido:`, plan.patientId);

                  // Tentar usar o ID do paciente do objeto patient se disponível
                  if (plan.patient && plan.patient.id) {
                    console.log(`Usando ID do paciente do objeto patient: ${plan.patient.id}`);
                    plan.patientId = plan.patient.id;
                  } else {
                    console.error(`Não foi possível obter um ID de paciente válido para o plano ${plan.id}`);
                    resolve();
                    return;
                  }
                }

                // Buscar dados do paciente
                this.patientService.getPatient(plan.patientId).subscribe({
                  next: (patient) => {
                    console.log(`Dados do paciente #${plan.patientId} carregados com sucesso: ${patient.name}`);

                    // Verificar se o procedimento tem um ID de profissional válido
                    if (!procedure.professionalId || isNaN(procedure.professionalId)) {
                      console.error(`Procedimento ${procedure.id} não tem um ID de profissional válido:`, procedure.professionalId);

                      // Tentar usar o ID do profissional do objeto professional se disponível
                      if (procedure.professional && procedure.professional.id) {
                        console.log(`Usando ID do profissional do objeto professional: ${procedure.professional.id}`);
                        procedure.professionalId = procedure.professional.id;
                      } else {
                        console.error(`Não foi possível obter um ID de profissional válido para o procedimento ${procedure.id}`);
                        resolve();
                        return;
                      }
                    }

                    // Buscar dados do dentista
                    this.dentistService.getDentist(procedure.professionalId).subscribe({
                      next: (dentist) => {
                        console.log(`Dados do dentista #${procedure.professionalId} carregados com sucesso: ${dentist.name}`);

                        // Determinar prioridade baseada no status
                        let priority: 'low' | 'medium' | 'high' = 'low';
                        if (procedure.status === TreatmentProcedureStatus.IN_PROGRESS) {
                          priority = 'high';
                        } else if (procedure.status === TreatmentProcedureStatus.PENDING) {
                          priority = 'medium';
                        }

                        // Converter o valor para número se for uma string
                        let procedureValue = procedure.value;

                        // Usar uma abordagem mais segura para converter o valor
                        if (procedureValue !== null && procedureValue !== undefined) {
                          if (typeof procedureValue === 'string') {
                            // Converter string para número
                            const strValue = procedureValue as string;
                            const cleanValue = strValue.replace(/[^0-9.]/g, '');
                            const numValue = parseFloat(cleanValue);
                            if (!isNaN(numValue)) {
                              procedureValue = numValue;
                            }
                          } else if (typeof procedureValue !== 'number') {
                            // Tentar converter outros tipos para número
                            try {
                              const numValue = Number(procedureValue);
                              if (!isNaN(numValue)) {
                                procedureValue = numValue;
                              }
                            } catch (error) {
                              console.error('Erro ao converter valor para número:', error);
                              procedureValue = 0;
                            }
                          }
                        } else {
                          // Se o valor for null ou undefined, usar 0
                          procedureValue = 0;
                        }

                        // Criar o card do procedimento
                        const procedureCard: ProcedureCard = {
                          id: procedure.id!,
                          name: procedure.name,
                          patientId: plan.patientId,
                          patientName: patient.name,
                          patientCategory: patient.category || 'Rotina',
                          professionalId: procedure.professionalId,
                          professionalName: dentist.name,
                          treatmentPlanId: plan.id!,
                          treatmentPlanName: `Plano #${plan.id}`,
                          tooth: procedure.tooth,
                          executionDate: procedure.executionDate,
                          status: procedure.status,
                          priority: priority,
                          createdAt: procedure.createdAt,
                          updatedAt: procedure.updatedAt,
                          value: procedureValue
                        };

                        console.log(`Adicionando procedimento #${procedure.id} à lista de status ${procedure.status}`);

                        // Adicionar o card à lista correta baseada no status
                        this.addProcedureToCorrectList(procedureCard);
                        resolve();
                      },
                      error: (error) => {
                        console.error(`Erro ao carregar dentista ${procedure.professionalId}:`, error);

                        // Tentar criar o card mesmo sem os dados do dentista
                        console.log(`Tentando criar card sem dados do dentista para o procedimento #${procedure.id}`);

                        let priority: 'low' | 'medium' | 'high' = 'low';
                        if (procedure.status === TreatmentProcedureStatus.IN_PROGRESS) {
                          priority = 'high';
                        } else if (procedure.status === TreatmentProcedureStatus.PENDING) {
                          priority = 'medium';
                        }

                        // Converter o valor para número se for uma string
                        let procedureValue = procedure.value;

                        // Usar uma abordagem mais segura para converter o valor
                        if (procedureValue !== null && procedureValue !== undefined) {
                          if (typeof procedureValue === 'string') {
                            // Converter string para número
                            const strValue = procedureValue as string;
                            const cleanValue = strValue.replace(/[^0-9.]/g, '');
                            const numValue = parseFloat(cleanValue);
                            if (!isNaN(numValue)) {
                              procedureValue = numValue;
                            }
                          } else if (typeof procedureValue !== 'number') {
                            // Tentar converter outros tipos para número
                            try {
                              const numValue = Number(procedureValue);
                              if (!isNaN(numValue)) {
                                procedureValue = numValue;
                              }
                            } catch (error) {
                              console.error('Erro ao converter valor para número:', error);
                              procedureValue = 0;
                            }
                          }
                        } else {
                          // Se o valor for null ou undefined, usar 0
                          procedureValue = 0;
                        }

                        const procedureCard: ProcedureCard = {
                          id: procedure.id!,
                          name: procedure.name,
                          patientId: plan.patientId,
                          patientName: patient.name,
                          patientCategory: patient.category || 'Rotina',
                          professionalId: procedure.professionalId,
                          professionalName: 'Dentista não encontrado',
                          treatmentPlanId: plan.id!,
                          treatmentPlanName: `Plano #${plan.id}`,
                          tooth: procedure.tooth,
                          executionDate: procedure.executionDate,
                          status: procedure.status,
                          priority: priority,
                          createdAt: procedure.createdAt,
                          updatedAt: procedure.updatedAt,
                          value: procedureValue
                        };

                        this.addProcedureToCorrectList(procedureCard);
                        resolve();
                      }
                    });
                  },
                  error: (error) => {
                    console.error(`Erro ao carregar paciente ${plan.patientId}:`, error);
                    resolve();
                  }
                });
              });

              procedurePromises.push(promise);
            });
          } else {
            console.warn(`Plano #${plan.id} não tem procedimentos ou procedures não é um array`);
            console.log('Valor de plan.procedures:', plan.procedures);
          }
        });

        console.log(`Total de ${totalProcedures} procedimentos encontrados em ${plans.length} planos`);

        // Quando todas as promessas forem resolvidas
        Promise.all(procedurePromises)
          .then(() => {
            // Ordenar os procedimentos por prioridade e data
            this.sortProcedures();
            console.log(`Carregamento concluído. Procedimentos por status:
              Pendentes: ${this.pendingProcedures.length}
              Em andamento: ${this.inProgressProcedures.length}
              Concluídos: ${this.completedProcedures.length}
              Cancelados: ${this.cancelledProcedures.length}
            `);
            this.isLoading = false;
          })
          .catch((error) => {
            console.error('Erro ao carregar procedimentos:', error);
            this.error = 'Erro ao carregar procedimentos. Tente novamente mais tarde.';
            this.isLoading = false;
          });
      },
      error: (error) => {
        console.error('Erro ao carregar planos de tratamento:', error);
        this.error = 'Erro ao carregar planos de tratamento. Tente novamente mais tarde.';
        this.isLoading = false;
      }
    });
  }

  // Método para adicionar o procedimento à lista correta baseada no status
  addProcedureToCorrectList(procedureCard: ProcedureCard): void {
    console.log(`Adicionando procedimento #${procedureCard.id} (${procedureCard.name}) à lista de status ${procedureCard.status}`);

    // Verificar se o status é uma string ou um enum
    let status = procedureCard.status;

    // Se o status for undefined ou null, definir como PENDING
    if (!status) {
      console.warn(`Procedimento #${procedureCard.id} com status indefinido. Definindo como PENDING.`);
      status = TreatmentProcedureStatus.PENDING;
      procedureCard.status = status;
    }

    // Converter string para enum se necessário
    if (typeof status === 'string') {
      console.log(`Convertendo status string "${status}" para enum`);
      switch (status.toLowerCase()) {
        case 'pending':
          status = TreatmentProcedureStatus.PENDING;
          break;

        case 'in_progress':
          status = TreatmentProcedureStatus.IN_PROGRESS;
          break;
        case 'completed':
          status = TreatmentProcedureStatus.COMPLETED;
          break;
        case 'cancelled':
          status = TreatmentProcedureStatus.CANCELLED;
          break;
        default:
          console.warn(`Status string desconhecido: ${status}. Definindo como PENDING.`);
          status = TreatmentProcedureStatus.PENDING;
          break;
      }
      procedureCard.status = status;
    }

    console.log(`Status final do procedimento #${procedureCard.id}: ${procedureCard.status}`);

    switch (procedureCard.status) {
      case TreatmentProcedureStatus.PENDING:
        this.pendingProcedures.push(procedureCard);
        console.log(`Procedimento adicionado à lista de pendentes. Total: ${this.pendingProcedures.length}`);
        break;

      case TreatmentProcedureStatus.IN_PROGRESS:
        this.inProgressProcedures.push(procedureCard);
        console.log(`Procedimento adicionado à lista de em andamento. Total: ${this.inProgressProcedures.length}`);
        break;
      case TreatmentProcedureStatus.COMPLETED:
        this.completedProcedures.push(procedureCard);
        console.log(`Procedimento adicionado à lista de concluídos. Total: ${this.completedProcedures.length}`);
        break;
      case TreatmentProcedureStatus.CANCELLED:
        this.cancelledProcedures.push(procedureCard);
        console.log(`Procedimento adicionado à lista de cancelados. Total: ${this.cancelledProcedures.length}`);
        break;
      default:
        console.warn(`Status desconhecido após conversão: ${procedureCard.status}. Adicionando à lista de pendentes.`);
        this.pendingProcedures.push(procedureCard);
        console.log(`Procedimento adicionado à lista de pendentes. Total: ${this.pendingProcedures.length}`);
        break;
    }
  }

  // Método para ordenar os procedimentos por prioridade e data
  sortProcedures(): void {
    // Função para ordenar procedimentos por prioridade (alta -> média -> baixa) e depois por data
    const sortByPriorityAndDate = (a: ProcedureCard, b: ProcedureCard) => {
      // Primeiro por prioridade
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Depois por data de criação
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateA - dateB;
    };

    this.pendingProcedures.sort(sortByPriorityAndDate);
    this.inProgressProcedures.sort(sortByPriorityAndDate);
    this.completedProcedures.sort(sortByPriorityAndDate);
    this.cancelledProcedures.sort(sortByPriorityAndDate);
  }

  // Método para lidar com o evento de drop (arrastar e soltar)
  onDrop(event: CdkDragDrop<ProcedureCard[]>): void {
    if (event.previousContainer === event.container) {
      // Se o procedimento foi movido dentro da mesma lista, apenas reordenar
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Se o procedimento foi movido para outra lista, transferir e atualizar o status
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // Obter o procedimento que foi movido
      const procedure = event.container.data[event.currentIndex];

      // Determinar o novo status baseado na lista de destino
      let newStatus: TreatmentProcedureStatus;

      if (event.container.id === 'pendingProcedures') {
        newStatus = TreatmentProcedureStatus.PENDING;
      } else if (event.container.id === 'inProgressProcedures') {
        newStatus = TreatmentProcedureStatus.IN_PROGRESS;
      } else if (event.container.id === 'completedProcedures') {
        newStatus = TreatmentProcedureStatus.COMPLETED;
      } else if (event.container.id === 'cancelledProcedures') {
        newStatus = TreatmentProcedureStatus.CANCELLED;
      } else {
        // Se não for uma lista válida, não fazer nada
        return;
      }

      // Atualizar o status do procedimento no objeto
      procedure.status = newStatus;

      // Atualizar o status do procedimento na API
      this.treatmentPlanService.updateProcedure(procedure.id, { status: newStatus }).subscribe({
        next: () => {
          this.notificationService.success(`Status do procedimento atualizado para ${this.statusLabels[newStatus]}`);
        },
        error: (error) => {
          console.error('Erro ao atualizar status do procedimento:', error);
          this.notificationService.error('Erro ao atualizar status do procedimento');

          // Recarregar os procedimentos para garantir que o estado esteja correto
          this.loadProcedures();
        }
      });
    }
  }

  // Método para abrir o modal de detalhes do procedimento
  openProcedureModal(procedureId: number): void {
    // Se estiver arrastando, não abrir o modal
    if (this.isDragging) {
      // Resetar a flag após um curto período
      setTimeout(() => {
        this.isDragging = false;
      }, 300);
      return;
    }

    // Buscar os detalhes do procedimento
    this.treatmentPlanService.getProcedure(procedureId).subscribe({
      next: (procedure) => {
        this.selectedProcedure = procedure;

        // Verificar se o procedimento tem um ID de plano de tratamento válido
        if (!procedure.treatmentPlanId || isNaN(procedure.treatmentPlanId)) {
          console.error(`Procedimento ${procedure.id} não tem um ID de plano de tratamento válido:`, procedure.treatmentPlanId);
          this.notificationService.error('Erro ao carregar detalhes do procedimento: ID de plano inválido');
          return;
        }

        // Buscar dados do plano de tratamento
        this.treatmentPlanService.getTreatmentPlan(procedure.treatmentPlanId).subscribe({
          next: (plan) => {
            // Verificar se o plano tem um ID de paciente válido
            if (!plan.patientId || isNaN(plan.patientId)) {
              console.error(`Plano ${plan.id} não tem um ID de paciente válido:`, plan.patientId);
              this.notificationService.error('Erro ao carregar detalhes do paciente: ID inválido');
              return;
            }

            // Buscar dados do paciente
            this.patientService.getPatient(plan.patientId).subscribe({
              next: (patient) => {
                // Verificar se o procedimento tem um ID de profissional válido
                if (!procedure.professionalId || isNaN(procedure.professionalId)) {
                  console.error(`Procedimento ${procedure.id} não tem um ID de profissional válido:`, procedure.professionalId);
                  this.notificationService.error('Erro ao carregar detalhes do dentista: ID inválido');
                  return;
                }

                // Buscar dados do dentista
                this.dentistService.getDentist(procedure.professionalId).subscribe({
                  next: (dentist) => {
                    // Buscar dados do agendamento (se houver)
                    if (procedure.appointmentId) {
                      this.schedulingService.getScheduling(procedure.appointmentId).subscribe({
                        next: (appointment) => {
                          this.modalTitle = `Procedimento: ${procedure.name}`;
                          this.modalContent = {
                            procedure: procedure,
                            plan: plan,
                            patient: patient,
                            dentist: dentist,
                            appointment: appointment,
                            statusLabels: this.statusLabels
                          };
                          this.isModalOpen = true;
                        },
                        error: (error) => {
                          console.error('Erro ao carregar agendamento:', error);
                          // Continuar mesmo sem o agendamento
                          this.modalTitle = `Procedimento: ${procedure.name}`;
                          this.modalContent = {
                            procedure: procedure,
                            plan: plan,
                            patient: patient,
                            dentist: dentist,
                            appointment: null,
                            statusLabels: this.statusLabels
                          };
                          this.isModalOpen = true;
                        }
                      });
                    } else {
                      // Se não houver agendamento
                      this.modalTitle = `Procedimento: ${procedure.name}`;
                      this.modalContent = {
                        procedure: procedure,
                        plan: plan,
                        patient: patient,
                        dentist: dentist,
                        appointment: null,
                        statusLabels: this.statusLabels
                      };
                      this.isModalOpen = true;
                    }
                  },
                  error: (error) => {
                    console.error('Erro ao carregar dentista:', error);
                    this.notificationService.error('Erro ao carregar detalhes do dentista');
                  }
                });
              },
              error: (error) => {
                console.error('Erro ao carregar paciente:', error);
                this.notificationService.error('Erro ao carregar detalhes do paciente');
              }
            });
          },
          error: (error) => {
            console.error('Erro ao carregar plano de tratamento:', error);
            this.notificationService.error('Erro ao carregar detalhes do plano de tratamento');
          }
        });
      },
      error: (error) => {
        console.error('Erro ao carregar procedimento:', error);
        this.notificationService.error('Erro ao carregar detalhes do procedimento');
      }
    });
  }

  // Método para fechar o modal
  closeModal(): void {
    this.isModalOpen = false;
    this.selectedProcedure = null;
    this.modalContent = null;
  }

  // Método para agendar um procedimento
  scheduleAppointment(procedureId: number): void {
    // Aqui você pode implementar a lógica para abrir um modal de agendamento
    // ou redirecionar para a página de agendamento
    // Por enquanto, vamos apenas atualizar o status para "em andamento"
    this.treatmentPlanService.updateProcedure(procedureId, {
      status: TreatmentProcedureStatus.IN_PROGRESS
    }).subscribe({
      next: () => {
        this.notificationService.success('Procedimento marcado como em andamento');
        this.closeModal();
        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao atualizar procedimento:', error);
        this.notificationService.error('Erro ao atualizar procedimento');
      }
    });
  }

  // Método para iniciar um procedimento
  startProcedure(procedureId: number): void {
    this.treatmentPlanService.updateProcedure(procedureId, {
      status: TreatmentProcedureStatus.IN_PROGRESS,
      executionDate: new Date()
    }).subscribe({
      next: () => {
        this.notificationService.success('Procedimento iniciado');
        this.closeModal();
        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao iniciar procedimento:', error);
        this.notificationService.error('Erro ao iniciar procedimento');
      }
    });
  }

  // Método para concluir um procedimento
  completeProcedure(procedureId: number): void {
    this.treatmentPlanService.updateProcedure(procedureId, {
      status: TreatmentProcedureStatus.COMPLETED
    }).subscribe({
      next: () => {
        this.notificationService.success('Procedimento concluído');
        this.closeModal();
        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao concluir procedimento:', error);
        this.notificationService.error('Erro ao concluir procedimento');
      }
    });
  }

  // Método para cancelar um procedimento
  cancelProcedure(procedureId: number): void {
    this.treatmentPlanService.updateProcedure(procedureId, {
      status: TreatmentProcedureStatus.CANCELLED
    }).subscribe({
      next: () => {
        this.notificationService.success('Procedimento cancelado');
        this.closeModal();
        this.loadProcedures();
      },
      error: (error) => {
        console.error('Erro ao cancelar procedimento:', error);
        this.notificationService.error('Erro ao cancelar procedimento');
      }
    });
  }

  // Método para obter a classe CSS baseada na categoria do paciente
  getCategoryClass(category: string): string {
    switch (category) {
      case 'Urgente':
        return 'bg-red-100 text-red-800';
      case 'Follow-up':
        return 'bg-blue-100 text-blue-800';
      case 'Rotina':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Método para formatar o valor como número com 2 casas decimais
  formatValue(value: any): string {
    // Se o valor for undefined ou null, retornar '0.00'
    if (value === undefined || value === null) {
      console.log('Valor indefinido ou nulo, retornando 0.00');
      return '0.00';
    }

    try {
      // Se o valor for uma string, tentar converter para número
      if (typeof value === 'string') {
        console.log(`Convertendo valor string "${value}" para número`);
        // Remover caracteres não numéricos, exceto ponto decimal
        const strValue = value as string;
        const cleanValue = strValue.replace(/[^0-9.]/g, '');

        // Tentar converter para número
        const numValue = parseFloat(cleanValue);

        // Se a conversão falhar, retornar '0.00'
        if (isNaN(numValue)) {
          console.warn(`Não foi possível converter "${value}" para número, retornando 0.00`);
          return '0.00';
        }

        // Retornar o valor formatado com 2 casas decimais
        return numValue.toFixed(2);
      }

      // Se o valor já for um número, apenas formatar com 2 casas decimais
      if (typeof value === 'number') {
        return value.toFixed(2);
      }

      // Para qualquer outro tipo, tentar converter para número
      console.warn(`Tipo de valor inesperado: ${typeof value}, valor: ${String(value)}`);

      // Tentar converter para número usando Number()
      const numValue = Number(value);

      // Se a conversão falhar, retornar '0.00'
      if (isNaN(numValue)) {
        console.warn(`Não foi possível converter "${String(value)}" para número, retornando 0.00`);
        return '0.00';
      }

      // Retornar o valor formatado com 2 casas decimais
      return numValue.toFixed(2);
    } catch (error) {
      console.error(`Erro ao converter valor para número: ${error}`);
      return '0.00';
    }
  }

  // Método para obter a classe CSS baseada no status do procedimento
  getStatusClass(status: TreatmentProcedureStatus): string {
    switch (status) {
      case TreatmentProcedureStatus.PENDING:
        return 'bg-blue-100 text-blue-800';
      case TreatmentProcedureStatus.IN_PROGRESS:
        return 'bg-orange-100 text-orange-800';
      case TreatmentProcedureStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TreatmentProcedureStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Método para abrir o modal de criação de procedimento avulso
  openProcedureFormModal(): void {
    this.isProcedureFormModalOpen = true;
  }

  // Método para fechar o modal de criação de procedimento avulso
  closeProcedureFormModal(): void {
    this.isProcedureFormModalOpen = false;
  }

  // Método chamado quando um procedimento é salvo no modal
  onProcedureSaved(procedure: TreatmentProcedure): void {
    this.closeProcedureFormModal();
    this.notificationService.success('Procedimento criado com sucesso!');
    this.loadProcedures();
  }
}
