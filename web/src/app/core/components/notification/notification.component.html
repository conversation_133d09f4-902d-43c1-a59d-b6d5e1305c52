<div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col space-y-2 max-w-md">
  <div
    *ngFor="let item of notifications"
    [@fadeInOut]
    [ngClass]="getBackgroundClass(item.notification.type)"
    class="p-4 rounded-lg shadow-md border flex items-start"
  >
    <div [ngClass]="getIconClass(item.notification.type)" class="flex-shrink-0 mr-3" [innerHTML]="getIcon(item.notification.type)"></div>
    <div class="flex-grow">
      <p class="text-sm font-medium">{{ item.notification.message }}</p>
    </div>
    <button
      (click)="removeNotification(item.id)"
      class="ml-4 text-gray-400 hover:text-gray-600 focus:outline-none"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>
</div>
