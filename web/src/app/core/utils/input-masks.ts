/**
 * Funções para aplicar máscaras em campos de input
 */

/**
 * Aplica máscara de CPF (000.000.000-00)
 * @param value Valor a ser formatado
 * @returns Valor formatado
 */
export function maskCPF(value: string): string {
  if (!value) return '';

  // Remove caracteres não numéricos
  value = value.replace(/\D/g, '');

  // Limita a 11 dígitos
  value = value.slice(0, 11);

  // Aplica a máscara
  return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
    .replace(/(\d{3})(\d{3})(\d{3})(\d)$/, '$1.$2.$3-$4')
    .replace(/(\d{3})(\d{3})(\d{1,2})$/, '$1.$2.$3')
    .replace(/(\d{3})(\d{1,3})$/, '$1.$2')
    .replace(/(\d{1,3})$/, '$1');
}

/**
 * Aplica máscara de telefone ((00) 00000-0000)
 * @param value Valor a ser formatado
 * @returns Valor formatado
 */
export function maskPhone(value: string): string {
  if (!value) return '';

  // Remove caracteres não numéricos
  value = value.replace(/\D/g, '');

  // Limita a 11 dígitos
  value = value.slice(0, 11);

  // Aplica a máscara de acordo com o número de dígitos
  if (value.length === 0) {
    return '';
  } else if (value.length <= 2) {
    return `(${value}`;
  } else if (value.length <= 6) {
    return `(${value.substring(0, 2)}) ${value.substring(2)}`;
  } else if (value.length <= 10) {
    return `(${value.substring(0, 2)}) ${value.substring(2, 6)}-${value.substring(6)}`;
  } else {
    return `(${value.substring(0, 2)}) ${value.substring(2, 7)}-${value.substring(7)}`;
  }
}

/**
 * Aplica máscara de CEP (00000-000)
 * @param value Valor a ser formatado
 * @returns Valor formatado
 */
export function maskCEP(value: string): string {
  if (!value) return '';

  // Remove caracteres não numéricos
  value = value.replace(/\D/g, '');

  // Limita a 8 dígitos
  value = value.slice(0, 8);

  // Aplica a máscara
  return value.replace(/(\d{5})(\d{3})/, '$1-$2')
    .replace(/(\d{5})(\d{1,2})$/, '$1-$2')
    .replace(/(\d{1,5})$/, '$1');
}
