import { Patient } from './patient.model';
import { Employee } from './employee.model';

export enum AnamnesisQuestionType {
  TEXT = 'text',
  BOOLEAN = 'boolean',
  OPTIONS = 'options',
}

export interface AnamnesisQuestion {
  id: number;
  title: string;
  type: AnamnesisQuestionType;
  order: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AnamnesisAnswer {
  id: number;
  anamnesisId: string;
  questionId: number;
  question?: AnamnesisQuestion;
  answer: string;
}

export interface Anamnesis {
  id: string;
  patientId: number;
  patient?: Patient;
  employeeId?: string;
  employee?: Employee;
  answers: AnamnesisAnswer[];
  createdAt: Date;
}
