export interface MedicalRecord {
  id: number;
  patientId: number;
  patientName?: string;
  patientCpf?: string;
  patientPhone?: string;

  // Anamnese
  preExistingDiseases?: string;
  allergies?: string;
  medications?: string;
  medicalHistory?: string;
  habits?: string;

  // Exame Clínico e Diagnóstico
  clinicalExamination?: string;
  diagnosis?: string;

  // Plano de Tratamento
  treatmentPlan?: string;
  risks?: string;
  patientConsent?: boolean;

  // Evolução do Caso
  evolution?: string;
  fileExam?: File;
  // Exames complementares
  complementaryExams?: string;
  examFilePath?: string;

  createdAt?: Date;
}
