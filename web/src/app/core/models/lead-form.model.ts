import { Patient } from './patient.model';

export enum LastProcedureTime {
  LESS_THAN_6_MONTHS = '<6meses',
  ONE_YEAR = '1ano',
  MORE_THAN_2_YEARS = '>2anos',
}

export interface LeadForm {
  id: string;

  // Dados pessoais
  fullName: string;
  cpf: string;
  phone: string;
  email: string;

  // Endereço
  cep: string;
  street: string;
  number: string;
  neighborhood: string;
  city: string;
  state: string;

  // Histórico odontológico
  pastTreatments: string[];
  lastProcedureTime: LastProcedureTime;

  // Interesse atual
  interestedTreatment: string[];
  wantsFreeEvaluation: boolean;
  wantsPromotions: boolean;

  // Personalização
  bestContactTime: string;
  referralSource: string;

  // Campos de sistema
  createdAt: Date;
  updatedAt: Date;

  // Campos de relacionamento com paciente existente
  isExistingPatient: boolean;
  patientId?: number;
  patient?: Patient;

  // Campos para atualização de cadastro
  hasUpdatesAvailable: boolean;
  fieldsToUpdate?: string[];
}
