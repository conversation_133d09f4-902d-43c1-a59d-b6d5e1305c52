import { Patient } from './patient.model';
import { Dentist } from './dentist.model';
import { Procedure } from './procedure.model';

export enum BudgetStatus {
  OPEN = 'open',
  APPROVED = 'approved',
  CANCELLED = 'cancelled',
}

export enum DiscountType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  NONE = 'none',
}

export enum PaymentMethod {
  TO_DEFINE = 'to_define',
  CASH = 'cash',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_SLIP = 'bank_slip',
  TRANSFER = 'transfer',
  PIX = 'pix',
  CHECK = 'check',
  MULTIPLE = 'multiple',
}

export interface BudgetItem {
  id?: number;
  budgetId?: number;
  procedureId: number;
  procedure?: Procedure;
  tooth?: string;
  executingDentistId: number;
  executingDentist?: Dentist;
  value: number;
}

export interface Budget {
  id?: number;
  patientId: number;
  patient?: Patient;
  dentistId: number;
  dentist?: Dentist;
  notes?: string;
  status: BudgetStatus;
  totalValue: number;
  amountPaid: number;
  installments: number;
  discount: number;
  discountType: DiscountType;
  paymentMethod: PaymentMethod;
  items: BudgetItem[];
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Mapeamento para exibição em português
export const BudgetStatusLabels = {
  [BudgetStatus.OPEN]: 'Em aberto',
  [BudgetStatus.APPROVED]: 'Aprovado',
  [BudgetStatus.CANCELLED]: 'Cancelado',
};

export const PaymentMethodLabels = {
  [PaymentMethod.TO_DEFINE]: 'A Definir',
  [PaymentMethod.CASH]: 'Dinheiro',
  [PaymentMethod.CREDIT_CARD]: 'Cartão de Crédito',
  [PaymentMethod.DEBIT_CARD]: 'Cartão de Débito',
  [PaymentMethod.BANK_SLIP]: 'Boleto',
  [PaymentMethod.TRANSFER]: 'Transferência',
  [PaymentMethod.PIX]: 'Pix',
  [PaymentMethod.CHECK]: 'Cheque',
  [PaymentMethod.MULTIPLE]: 'Múltiplas Formas',
};
