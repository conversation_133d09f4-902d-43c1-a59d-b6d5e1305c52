import { Procedure } from './procedure.model';
import { Dentist } from './dentist.model';
import { Scheduling } from './scheduling.model';

export enum TreatmentProcedureStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export interface TreatmentProcedure {
  id?: number;
  treatmentPlanId: number;
  procedureId: number;
  procedure?: Procedure;
  name: string;
  value: number;
  tooth?: string;
  executionDate?: Date;
  professionalId: number;
  professional?: Dentist;
  appointmentId?: number;
  appointment?: Scheduling;
  status: TreatmentProcedureStatus;
  notes?: string;
  nextVisitDetails?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Mapeamento para exibição em português
export const TreatmentProcedureStatusLabels = {
  [TreatmentProcedureStatus.PENDING]: 'Pendente',
  [TreatmentProcedureStatus.IN_PROGRESS]: 'Em andamento',
  [TreatmentProcedureStatus.COMPLETED]: 'Concluído',
  [TreatmentProcedureStatus.CANCELLED]: 'Cancelado',
};
