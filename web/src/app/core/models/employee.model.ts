export enum EmployeeType {
  AUXILIAR_SAUDE_BUCAL = 'AUXILIAR_SAUDE_BUCAL',
  TECNICO_SAUDE_BUCAL = 'TECNICO_SAUDE_BUCAL',
  RECEPCIONISTA = 'RECEPCIONISTA',
  SECRETARIA_ADMINISTRATIVA = 'SECRETARIA_ADMINISTRATIVA',
  GERENTE_CLINICA = 'GERENTE_CLINICA',
  FINANCEIRO = 'FINANCEIRO',
  SERVICOS_GERAIS = 'SERVICOS_GERAIS',
  SUPORTE_TECNICO = 'SUPORTE_TECNICO',
  MARKETING = 'MARKETING',
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  type: EmployeeType;
  birthDate?: Date;
  cpf: string;
  admissionDate: Date;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const employeeTypeLabels: Record<EmployeeType, string> = {
  [EmployeeType.AUXILIAR_SAUDE_BUCAL]: 'Auxiliar de Saúde Bucal',
  [EmployeeType.TECNICO_SAUDE_BUCAL]: 'Técnico de Saúde Bucal',
  [EmployeeType.RECEPCIONISTA]: 'Recepcionista',
  [EmployeeType.SECRETARIA_ADMINISTRATIVA]: 'Secretária Administrativa',
  [EmployeeType.GERENTE_CLINICA]: 'Gerente de Clínica',
  [EmployeeType.FINANCEIRO]: 'Financeiro',
  [EmployeeType.SERVICOS_GERAIS]: 'Serviços Gerais',
  [EmployeeType.SUPORTE_TECNICO]: 'Suporte Técnico',
  [EmployeeType.MARKETING]: 'Marketing',
};
