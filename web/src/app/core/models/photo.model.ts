import { Patient } from './patient.model';

export interface PhotoFolder {
  id: string;
  name: string;
  patientId: number;
  patient?: Patient;
  photos?: PatientPhoto[];
  createdAt: Date;
}

export interface PatientPhoto {
  id: string;
  url: string;
  filename: string;
  caption?: string | null;
  order: number;
  uploadedAt: Date;
  patientId: number;
  patient?: Patient;
  folderId: string | null;
  folder?: PhotoFolder | null;
}
