export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  WAITING = 'WAITING',
  DONE = 'DONE',
  CANCELLED = 'CANCELLED',
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum Sector {
  ATTENDANCE = 'ATTENDANCE',
  CLINICAL = 'CLINICAL',
  BIOSECURITY = 'BIOSECURITY',
  RADIOLOGY = 'RADIOLOGY',
  FINANCIAL = 'FINANCIAL',
  COMMERCIAL = 'COMMERCIAL',
  MARKETING = 'MARKETING',
  HUMAN_RESOURCES = 'HUMAN_RESOURCES',
  ADMINISTRATION = 'ADMINISTRATION',
  IT_SUPPORT = 'IT_SUPPORT',
  SUPPLIES = 'SUPPLIES',
}

import { Employee } from './employee.model';

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: string;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  employee?: Employee;
  employeeId?: string;
  tags?: string[];
  sector?: Sector; // Garantir que o campo sector esteja definido no modelo
}

export const taskStatusLabels: Record<TaskStatus, string> = {
  [TaskStatus.TODO]: 'A Fazer',
  [TaskStatus.IN_PROGRESS]: 'Em Andamento',
  [TaskStatus.WAITING]: 'Aguardando',
  [TaskStatus.DONE]: 'Concluída',
  [TaskStatus.CANCELLED]: 'Cancelada',
};

export const taskPriorityLabels: Record<TaskPriority, string> = {
  [TaskPriority.LOW]: 'Baixa',
  [TaskPriority.MEDIUM]: 'Média',
  [TaskPriority.HIGH]: 'Alta',
  [TaskPriority.URGENT]: 'Urgente',
};

export const taskPriorityColors: Record<TaskPriority, string> = {
  [TaskPriority.LOW]: 'bg-blue-100 text-blue-800',
  [TaskPriority.MEDIUM]: 'bg-green-100 text-green-800',
  [TaskPriority.HIGH]: 'bg-orange-100 text-orange-800',
  [TaskPriority.URGENT]: 'bg-red-100 text-red-800',
};

export const sectorLabels: Record<Sector, string> = {
  [Sector.ATTENDANCE]: 'Atendimento',
  [Sector.CLINICAL]: 'Clínico',
  [Sector.BIOSECURITY]: 'Biossegurança',
  [Sector.RADIOLOGY]: 'Radiologia',
  [Sector.FINANCIAL]: 'Financeiro',
  [Sector.COMMERCIAL]: 'Comercial',
  [Sector.MARKETING]: 'Marketing',
  [Sector.HUMAN_RESOURCES]: 'Recursos Humanos',
  [Sector.ADMINISTRATION]: 'Administração',
  [Sector.IT_SUPPORT]: 'Suporte de TI',
  [Sector.SUPPLIES]: 'Suprimentos',
};
