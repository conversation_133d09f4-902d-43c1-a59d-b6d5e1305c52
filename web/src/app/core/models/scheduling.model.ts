import { TreatmentPlan } from './treatment-plan.model';
import { TreatmentProcedure } from './treatment-procedure.model';
import { Patient } from './patient.model';
import { Dentist } from './dentist.model';

// Interface local para categoria de agendamento
export interface AppointmentCategory {
  id: number;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Scheduling {
  id: number;
  patientId: number;
  patientName?: string;
  patient?: Patient; // Relacionamento com o paciente
  date: Date;
  time: string;
  duration?: number; // Duração do agendamento em minutos
  endTime?: string; // Hora de término do agendamento (HH:MM)
  dentistId?: number;
  dentistName?: string;
  dentist?: Dentist; // Relacionamento com o dentista

  status:
    | 'confirmed'
    | 'unconfirmed'
    | 'late'
    | 'no-show'
    | 'cancelled'
    | 'rescheduled'
    | 'in-progress'
    | 'completed'
    | 'scheduled-unconfirmed'
    | 'scheduled-confirmed'
    | 'unscheduled'
    | 'scheduled';
  notes?: string;
  cost?: number;
  paid?: boolean;
  createdAt?: Date;
  treatmentId?: number; // ID do tratamento associado (compatibilidade com modelo antigo)
  treatmentPlanId?: number; // ID do plano de tratamento associado
  treatmentPlan?: TreatmentPlan; // Plano de tratamento associado
  treatmentProcedures?: TreatmentProcedure[]; // Procedimentos associados ao agendamento

  // Novos campos para procedimentos
  procedureIds?: number[]; // IDs dos procedimentos selecionados

  // Campos de contato específicos do agendamento
  email?: string; // Email do paciente para este agendamento
  phone?: string; // Celular do paciente para este agendamento
  isFirstConsultation?: boolean; // Indica se é a primeira consulta do paciente

  // Categoria do agendamento
  appointmentCategory?: AppointmentCategory;
}
