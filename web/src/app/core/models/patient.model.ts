export type PatientCategory = 'Urgente' | 'Rotina' | 'Follow-up';
export type PatientGender = 'male' | 'female' | 'other';

import { PatientType } from './patient-type.model';

export interface Patient {
  id: number;

  // <PERSON><PERSON>
  name: string;
  birthDate?: Date;
  gender?: PatientGender;
  cpf: string;
  howDidYouFindUs?: string;
  registrationDate?: Date;
  notes?: string;

  // Contato
  email: string;
  phone: string;
  whatsapp?: string;
  addressZipCode?: string;
  addressStreet?: string;
  addressNumber?: string;
  addressNeighborhood?: string;
  addressCity?: string;
  addressState?: string;
  addressComplement?: string;

  // Dados Complementares
  profession?: string;
  medicalRecordNumber?: string;
  category?: PatientCategory;
  patientTypeId?: number;
  patientType?: PatientType;
  referredById?: number;

  // Campos de sistema
  isIncomplete?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
