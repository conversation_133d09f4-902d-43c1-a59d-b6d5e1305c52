import { Patient } from './patient.model';
import { Budget } from './budget.model';
import { TreatmentProcedure } from './treatment-procedure.model';
import { Dentist } from './dentist.model';

export enum TreatmentPlanStatus {
  OPEN = 'open',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export interface TreatmentPlan {
  id?: number;
  patientId: number;
  patient?: Patient;
  budgetId?: number;
  budget?: Budget;
  dentistId?: number;
  dentist?: Dentist;
  totalValue: number;
  completionPercentage: number;
  status: TreatmentPlanStatus;
  procedures: TreatmentProcedure[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Mapeamento para exibição em português
export const TreatmentPlanStatusLabels = {
  [TreatmentPlanStatus.OPEN]: 'Em aberto',
  [TreatmentPlanStatus.COMPLETED]: 'Concluído',
  [TreatmentPlanStatus.CANCELLED]: 'Cancelado',
};
