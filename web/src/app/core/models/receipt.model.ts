import { Patient } from './patient.model';

export interface Receipt {
  id: number;
  date: Date;
  number: string;
  editedNumber?: string;
  beneficiary: string;
  beneficiaryCpf: string;
  responsible: string;
  responsibleCpf: string;
  observations?: string;
  professional: string;
  professionalCpf: string;
  description: string;
  value: number;
  patientId?: number;
  patient?: Patient;
  createdAt?: Date;
  updatedAt?: Date;
}
