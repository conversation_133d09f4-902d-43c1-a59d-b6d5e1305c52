export enum ProcedureType {
  CLINICAL = 'CLINIC<PERSON>',
  SURGICAL = 'SURGICAL',
  AESTHETIC = 'AESTHETIC',
  ORTHODONTIC = 'ORTHODONTIC',
  ENDODONTIC = 'ENDODONTIC',
  PERIODONTIC = 'PERIODONTIC',
  PEDIATRIC = 'PEDIATRIC',
  RADIOLOGY = 'RADIOLOGY',
  PROSTHODONTIC = 'PROSTHODONTIC',
}

export enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface Procedure {
  id: number;
  name: string;
  description?: string;
  defaultPrice: number;
  estimatedDuration: number;
  type: ProcedureType;
  status: Status;
  createdAt: string;
  updatedAt: string;
}

// Mapeamento para exibição em português
export const ProcedureTypeLabels = {
  [ProcedureType.CLINICAL]: 'Clínico',
  [ProcedureType.SURGICAL]: 'Cirúrgico',
  [ProcedureType.AESTHETIC]: 'Estético',
  [ProcedureType.ORTHODONTIC]: 'Ortodôntico',
  [ProcedureType.ENDODONTIC]: 'Endodôntico',
  [ProcedureType.PERIODONTIC]: 'Periodôntico',
  [ProcedureType.PEDIATRIC]: 'Pediátrico',
  [ProcedureType.RADIOLOGY]: 'Radiologia',
  [ProcedureType.PROSTHODONTIC]: 'Prótese',
};

export const StatusLabels = {
  [Status.ACTIVE]: 'Ativo',
  [Status.INACTIVE]: 'Inativo',
};
