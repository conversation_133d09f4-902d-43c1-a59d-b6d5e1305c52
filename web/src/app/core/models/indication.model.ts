import { Patient } from './patient.model';

export enum IndicationStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
}

export const IndicationStatusLabels = {
  [IndicationStatus.PENDING]: 'Pendente',
  [IndicationStatus.CONFIRMED]: 'Confirmada',
  [IndicationStatus.REJECTED]: 'Rejeitada',
};

export interface Indication {
  id: number;
  date: Date;
  status: IndicationStatus;
  observation?: string;
  tags?: string[];
  indicatedPatientId: number;
  indicatedPatient?: Patient;
  referredById?: number;
  referredBy?: Patient;
  createdAt?: Date;
  updatedAt?: Date;
}
