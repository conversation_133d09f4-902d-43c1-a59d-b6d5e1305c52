export interface TimeSlot {
  start: string;
  end: string;
}

export interface WeeklySchedule {
  monday?: TimeSlot[];
  tuesday?: TimeSlot[];
  wednesday?: TimeSlot[];
  thursday?: TimeSlot[];
  friday?: TimeSlot[];
  saturday?: TimeSlot[];
  sunday?: TimeSlot[];
}

export interface DentistSchedule {
  id: number;
  dentistId: number;
  dentist?: {
    id: number;
    name: string;
    specialty: string;
    active: boolean;
  };
  weeklySchedule: WeeklySchedule;
  createdAt: string;
  updatedAt: string;
}

export interface DentistException {
  id: number;
  dentistId: number;
  dentist?: {
    id: number;
    name: string;
    specialty: string;
  };
  date: string;
  type: 'day-off' | 'custom-hours';
  customHours?: TimeSlot[];
  reason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DentistAvailability {
  dentistId: number;
  dentistName: string;
  date: string;
  dayOfWeek: string;
  isWorkingDay: boolean;
  availableSlots: {
    start: string;
    end: string;
    available: boolean;
  }[];
  exception?: {
    type: string;
    reason?: string;
    customHours?: TimeSlot[];
  };
}

export interface DentistScheduleOverview {
  dentistId: number;
  dentistName: string;
  dentistSpecialty: string;
  dentistActive: boolean;
  hasSchedule: boolean;
  weeklySchedule?: WeeklySchedule;
  upcomingExceptions: number;
}

export interface AvailableTimes {
  availableTimes: string[];
}
