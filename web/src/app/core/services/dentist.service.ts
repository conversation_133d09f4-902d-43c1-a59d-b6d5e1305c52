import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, map, tap } from 'rxjs';
import { Dentist } from '../models/dentist.model';
import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root',
})
export class DentistService {
  private apiUrl = `${environment.apiUrl}/dentists`;

  constructor(private http: HttpClient) {}

  getDentists(
    page: number = 1,
    limit: number = 10,
    search?: string,
    active?: boolean,
    specialties?: string[]
  ): Observable<PaginatedResponse<Dentist>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (active !== undefined) {
      params = params.set('active', active.toString());
    }

    if (specialties && specialties.length > 0) {
      params = params.set('specialties', specialties.join(','));
    }

    return this.http
      .get<PaginatedResponse<Dentist>>(this.apiUrl, { params })
      .pipe(
        tap((response) => {
          console.log(
            `Dentistas carregados: ${response.data.length} de ${response.total}`
          );
          console.log(
            `Página ${response.page} de ${Math.ceil(
              response.total / response.limit
            )}`
          );
        })
      );
  }

  // Método para compatibilidade com código existente que espera um array de dentistas
  getAllDentists(): Observable<Dentist[]> {
    return this.http
      .get<PaginatedResponse<Dentist>>(this.apiUrl, {
        params: new HttpParams().set('limit', '1000'), // Limite alto para pegar todos
      })
      .pipe(
        tap((response) => {
          console.log(`Todos os dentistas carregados: ${response.data.length}`);
        }),
        map((response) => response.data)
      );
  }

  getActiveDentists(): Observable<Dentist[]> {
    return this.http.get<any>(`${this.apiUrl}?active=true`).pipe(
      map((response) => {
        // Verificar se a resposta é um objeto paginado ou um array
        if (response && response.data && Array.isArray(response.data)) {
          console.log(
            `Dentistas ativos carregados (paginado): ${response.data.length}`
          );
          return response.data;
        } else if (Array.isArray(response)) {
          console.log(
            `Dentistas ativos carregados (array): ${response.length}`
          );
          return response;
        } else {
          console.error(
            'Resposta inesperada ao carregar dentistas ativos:',
            response
          );
          return [];
        }
      })
    );
  }

  getDentist(id: number): Observable<Dentist> {
    return this.http.get<Dentist>(`${this.apiUrl}/${id}`);
  }

  createDentist(
    dentist: Omit<Dentist, 'id' | 'createdAt' | 'updatedAt'>
  ): Observable<Dentist> {
    return this.http.post<Dentist>(this.apiUrl, dentist);
  }

  updateDentist(id: number, dentist: Partial<Dentist>): Observable<Dentist> {
    return this.http.patch<Dentist>(`${this.apiUrl}/${id}`, dentist);
  }

  deleteDentist(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getAvailableSpecialties(): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/specialties/available`);
  }
}
