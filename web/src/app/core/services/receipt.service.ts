import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Receipt } from '../models/receipt.model';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root'
})
export class ReceiptService {
  private apiUrl = `${environment.apiUrl}/receipts`;

  constructor(private http: HttpClient) {}

  getReceipts(
    page: number = 1,
    limit: number = 10,
    search?: string,
    patientId?: number
  ): Observable<PaginatedResponse<Receipt>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (patientId) {
      params = params.set('patientId', patientId.toString());
    }

    return this.http.get<PaginatedResponse<Receipt>>(this.apiUrl, { params });
  }

  getReceiptsByPatient(patientId: number): Observable<Receipt[]> {
    return this.http.get<Receipt[]>(`${this.apiUrl}/patient/${patientId}`);
  }

  getReceipt(id: number): Observable<Receipt> {
    return this.http.get<Receipt>(`${this.apiUrl}/${id}`);
  }

  createReceipt(receipt: Omit<Receipt, 'id' | 'createdAt' | 'updatedAt'>): Observable<Receipt> {
    return this.http.post<Receipt>(this.apiUrl, receipt);
  }

  updateReceipt(id: number, receipt: Partial<Receipt>): Observable<Receipt> {
    return this.http.patch<Receipt>(`${this.apiUrl}/${id}`, receipt);
  }

  deleteReceipt(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
