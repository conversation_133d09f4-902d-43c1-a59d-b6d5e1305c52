import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { PatientExam } from '../models/exam.model';

@Injectable({
  providedIn: 'root',
})
export class ExamService {
  private apiUrl = `${environment.apiUrl}/exams`;

  constructor(private http: HttpClient) {}

  uploadExam(
    patientId: number,
    name: string,
    file: File
  ): Observable<PatientExam> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('patientId', patientId.toString());
    formData.append('name', name);

    return this.http.post<PatientExam>(`${this.apiUrl}/upload`, formData);
  }

  getExamsByPatient(patientId: number): Observable<PatientExam[]> {
    return this.http.get<PatientExam[]>(`${this.apiUrl}/patients/${patientId}/exams`);
  }

  getExam(examId: string): Observable<PatientExam> {
    return this.http.get<PatientExam>(`${this.apiUrl}/${examId}`);
  }

  deleteExam(examId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${examId}`);
  }
}
