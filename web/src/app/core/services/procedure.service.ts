import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Procedure, ProcedureType, Status } from '../models/procedure.model';
import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root',
})
export class ProcedureService {
  private apiUrl = `${environment.apiUrl}/procedures`;

  constructor(private http: HttpClient) {}

  getProcedures(
    page: number = 1,
    limit: number = 10,
    search?: string,
    type?: ProcedureType,
    status?: Status
  ): Observable<PaginatedResponse<Procedure>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (type) {
      params = params.set('type', type);
    }

    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<PaginatedResponse<Procedure>>(this.apiUrl, { params });
  }

  getAllProcedures(): Observable<Procedure[]> {
    const params = new HttpParams().set('noPagination', 'true');
    return this.http.get<Procedure[]>(this.apiUrl, { params });
  }

  getProcedure(id: number): Observable<Procedure> {
    return this.http.get<Procedure>(`${this.apiUrl}/${id}`);
  }

  createProcedure(procedure: Partial<Procedure>): Observable<Procedure> {
    return this.http.post<Procedure>(this.apiUrl, procedure);
  }

  updateProcedure(id: number, procedure: Partial<Procedure>): Observable<Procedure> {
    return this.http.patch<Procedure>(`${this.apiUrl}/${id}`, procedure);
  }

  deleteProcedure(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
