import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError, of, switchMap, tap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { TreatmentPlan } from '../models/treatment-plan.model';
import { TreatmentProcedure, TreatmentProcedureStatus } from '../models/treatment-procedure.model';
import { NotificationService } from './notification.service';
import { TreatmentPlanUpdateService } from './treatment-plan-update.service';

@Injectable({
  providedIn: 'root'
})
export class TreatmentPlanService {
  private apiUrl = `${environment.apiUrl}/treatment-plans`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService,
    private treatmentPlanUpdateService: TreatmentPlanUpdateService
  ) { }

  getTreatmentPlans(page: number = 1, limit: number = 10, search?: string, status?: string): Observable<TreatmentPlan[]> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<any>(`${this.apiUrl}`, { params }).pipe(
      map(response => response.data),
      catchError(error => {
        console.error('Erro ao buscar planos de tratamento:', error);
        return throwError(() => error);
      })
    );
  }

  getAllTreatmentPlans(): Observable<TreatmentPlan[]> {
    // Buscar todos os planos de tratamento sem paginação
    let params = new HttpParams()
      .set('noPagination', 'true');

    return this.http.get<any>(`${this.apiUrl}`, { params }).pipe(
      map(response => {
        // Verificar se a resposta é um objeto com propriedade data (paginado) ou um array direto
        const plans = Array.isArray(response) ? response : (response.data || []);

        // Processar os planos para garantir que os procedimentos estejam corretamente formatados
        return plans.map((plan: any) => {
          // Verificar se o plano tem um ID válido
          if (!plan.id) {
            console.error('Plano sem ID válido:', plan);
          }

          // Extrair patientId do objeto patient se disponível
          if (!plan.patientId && plan.patient?.id) {
            console.log(`Extraindo patientId do objeto patient para o plano ${plan.id}: ${plan.patient.id}`);
            plan.patientId = plan.patient.id;
          } else if (!plan.patientId && !plan.patient?.id) {
            console.error('Plano sem patientId válido e sem objeto patient:', plan);
          }

          // Corrigir o nome da propriedade __procedures__ para procedures
          if (plan.__procedures__) {
            plan.procedures = plan.__procedures__;
            delete plan.__procedures__;
          }

          // Garantir que os procedimentos tenham o professionalId definido
          if (plan.procedures && Array.isArray(plan.procedures)) {
            plan.procedures = plan.procedures.map((proc: any) => {
              // Verificar se o procedimento tem um ID válido
              if (!proc.id) {
                console.error('Procedimento sem ID válido:', proc);
              }

              // Extrair professionalId do objeto professional se disponível
              let professionalId = proc.professionalId;
              if (!professionalId && proc.professional?.id) {
                console.log(`Extraindo professionalId do objeto professional para o procedimento ${proc.id}: ${proc.professional.id}`);
                professionalId = proc.professional.id;
              } else if (!professionalId && !proc.professional?.id) {
                console.error('Procedimento sem professionalId válido e sem objeto professional:', proc);
              }

              return {
                ...proc,
                professionalId: professionalId || 0, // Usar 0 como fallback para evitar undefined
                treatmentPlanId: plan.id || 0 // Usar 0 como fallback para evitar undefined
              };
            });
          } else {
            plan.procedures = [];
          }

          // Extrair patientId do objeto patient novamente para garantir
          const patientId = plan.patientId || plan.patient?.id || 0;

          return {
            ...plan,
            patientId: patientId // Usar o ID extraído ou 0 como fallback
          } as TreatmentPlan;
        });
      }),
      catchError(error => {
        console.error('Erro ao buscar todos os planos de tratamento:', error);
        return throwError(() => error);
      })
    );
  }

  getTreatmentPlansByPatient(patientId: number): Observable<TreatmentPlan[]> {
    // Adicionar parâmetro para expandir os procedimentos
    let params = new HttpParams().set('expand', 'procedures');

    return this.http.get<any[]>(`${this.apiUrl}/patient/${patientId}`, { params }).pipe(
      map(plans => {
        console.log('Planos de tratamento recebidos da API:', plans);

        // Corrigir o nome da propriedade __procedures__ para procedures e garantir que professionalId esteja definido
        return plans.map((plan: any) => {
          // Verificar se o plano tem um ID válido
          if (!plan.id) {
            console.error('Plano sem ID válido:', plan);
          }

          // Extrair patientId do objeto patient se disponível
          if (!plan.patientId && plan.patient?.id) {
            console.log(`Extraindo patientId do objeto patient para o plano ${plan.id}: ${plan.patient.id}`);
            plan.patientId = plan.patient.id;
          } else if (!plan.patientId) {
            // Como estamos buscando por patientId, vamos garantir que o plano tenha o patientId correto
            console.log(`Definindo patientId para o plano ${plan.id} com o valor fornecido: ${patientId}`);
            plan.patientId = patientId;
          }

          // Verificar se os procedimentos estão na propriedade __procedures__ ou procedures
          if (plan.__procedures__) {
            console.log(`Plano ${plan.id} tem procedimentos na propriedade __procedures__`);
            // Mapear os procedimentos garantindo que professionalId esteja definido
            plan.procedures = plan.__procedures__.map((proc: any) => {
              // Verificar se o procedimento tem um ID válido
              if (!proc.id) {
                console.error('Procedimento sem ID válido:', proc);
              }

              // Extrair professionalId do objeto professional se disponível
              let professionalId = proc.professionalId;
              if (!professionalId && proc.professional?.id) {
                console.log(`Extraindo professionalId do objeto professional para o procedimento ${proc.id}: ${proc.professional.id}`);
                professionalId = proc.professional.id;
              } else if (!professionalId && !proc.professional?.id) {
                console.error('Procedimento sem professionalId válido e sem objeto professional:', proc);
              }

              return {
                ...proc,
                professionalId: professionalId || 0, // Usar 0 como fallback para evitar undefined
                treatmentPlanId: plan.id || 0 // Usar 0 como fallback para evitar undefined
              };
            });
            delete plan.__procedures__;
          } else if (plan.procedures && Array.isArray(plan.procedures)) {
            console.log(`Plano ${plan.id} tem procedimentos na propriedade procedures`);
            // Mapear os procedimentos garantindo que professionalId esteja definido
            plan.procedures = plan.procedures.map((proc: any) => {
              // Verificar se o procedimento tem um ID válido
              if (!proc.id) {
                console.error('Procedimento sem ID válido:', proc);
              }

              // Extrair professionalId do objeto professional se disponível
              let professionalId = proc.professionalId;
              if (!professionalId && proc.professional?.id) {
                console.log(`Extraindo professionalId do objeto professional para o procedimento ${proc.id}: ${proc.professional.id}`);
                professionalId = proc.professional.id;
              } else if (!professionalId && !proc.professional?.id) {
                console.error('Procedimento sem professionalId válido e sem objeto professional:', proc);
              }

              return {
                ...proc,
                professionalId: professionalId || 0, // Usar 0 como fallback para evitar undefined
                treatmentPlanId: plan.id || 0 // Usar 0 como fallback para evitar undefined
              };
            });
          } else {
            console.warn(`Plano ${plan.id} não tem procedimentos`);
            plan.procedures = [];
          }

          // Extrair patientId do objeto patient novamente para garantir
          const finalPatientId = plan.patientId || plan.patient?.id || patientId;

          // Extrair dentistId do objeto dentist se disponível
          let dentistId = plan.dentistId;
          if (!dentistId && plan.dentist?.id) {
            console.log(`Extraindo dentistId do objeto dentist para o plano ${plan.id}: ${plan.dentist.id}`);
            dentistId = plan.dentist.id;
          }

          // Criar o objeto TreatmentPlan com todos os campos necessários
          const treatmentPlan: TreatmentPlan = {
            ...plan,
            id: plan.id,
            patientId: finalPatientId,
            dentistId: dentistId,
            dentist: plan.dentist,
            totalValue: plan.totalValue || 0,
            completionPercentage: plan.completionPercentage || 0,
            status: plan.status || 'open',
            procedures: plan.procedures || []
          };

          return treatmentPlan;
        });
      }),
      catchError(error => {
        console.error(`Erro ao buscar planos de tratamento do paciente ${patientId}:`, error);
        return throwError(() => error);
      })
    );
  }

  getTreatmentPlan(id: number): Observable<TreatmentPlan> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map((plan: any) => {
        // Verificar se o plano tem um ID válido
        if (!plan.id) {
          console.error('Plano sem ID válido:', plan);
        }

        // Extrair patientId do objeto patient se disponível
        if (!plan.patientId && plan.patient?.id) {
          console.log(`Extraindo patientId do objeto patient para o plano ${plan.id}: ${plan.patient.id}`);
          plan.patientId = plan.patient.id;
        } else if (!plan.patientId && !plan.patient?.id) {
          console.error('Plano sem patientId válido e sem objeto patient:', plan);
        }

        // Corrigir o nome da propriedade __procedures__ para procedures
        if (plan.__procedures__) {
          plan.procedures = plan.__procedures__;
          delete plan.__procedures__;
        }

        // Garantir que os procedimentos tenham o professionalId definido
        if (plan.procedures && Array.isArray(plan.procedures)) {
          plan.procedures = plan.procedures.map((proc: any) => {
            // Verificar se o procedimento tem um ID válido
            if (!proc.id) {
              console.error('Procedimento sem ID válido:', proc);
            }

            // Extrair professionalId do objeto professional se disponível
            let professionalId = proc.professionalId;
            if (!professionalId && proc.professional?.id) {
              console.log(`Extraindo professionalId do objeto professional para o procedimento ${proc.id}: ${proc.professional.id}`);
              professionalId = proc.professional.id;
            } else if (!professionalId && !proc.professional?.id) {
              console.error('Procedimento sem professionalId válido e sem objeto professional:', proc);
            }

            return {
              ...proc,
              professionalId: professionalId || 0, // Usar 0 como fallback para evitar undefined
              treatmentPlanId: plan.id || 0 // Usar 0 como fallback para evitar undefined
            };
          });
        } else {
          plan.procedures = [];
        }

        // Extrair patientId do objeto patient novamente para garantir
        const patientId = plan.patientId || plan.patient?.id || 0;

        // Extrair dentistId do objeto dentist se disponível
        let dentistId = plan.dentistId;
        if (!dentistId && plan.dentist?.id) {
          console.log(`Extraindo dentistId do objeto dentist para o plano ${plan.id}: ${plan.dentist.id}`);
          dentistId = plan.dentist.id;
        }

        return {
          ...plan,
          patientId: patientId, // Usar o ID extraído ou 0 como fallback
          dentistId: dentistId,
          dentist: plan.dentist
        } as TreatmentPlan;
      }),
      catchError(error => {
        console.error(`Erro ao buscar plano de tratamento ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  createTreatmentPlan(treatmentPlan: any): Observable<TreatmentPlan> {
    return this.http.post<TreatmentPlan>(`${this.apiUrl}`, treatmentPlan).pipe(
      map(response => {
        this.notificationService.success('Plano de tratamento criado com sucesso!');
        return response;
      }),
      catchError(error => {
        console.error('Erro ao criar plano de tratamento:', error);
        this.notificationService.error('Erro ao criar plano de tratamento');
        return throwError(() => error);
      })
    );
  }

  updateTreatmentPlan(id: number, treatmentPlan: any): Observable<TreatmentPlan> {
    return this.http.patch<TreatmentPlan>(`${this.apiUrl}/${id}`, treatmentPlan).pipe(
      map(response => {
        this.notificationService.success('Plano de tratamento atualizado com sucesso!');
        return response;
      }),
      catchError(error => {
        console.error(`Erro ao atualizar plano de tratamento ${id}:`, error);
        this.notificationService.error('Erro ao atualizar plano de tratamento');
        return throwError(() => error);
      })
    );
  }

  deleteTreatmentPlan(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      map(() => {
        this.notificationService.success('Plano de tratamento removido com sucesso!');
      }),
      catchError(error => {
        console.error(`Erro ao remover plano de tratamento ${id}:`, error);
        this.notificationService.error('Erro ao remover plano de tratamento');
        return throwError(() => error);
      })
    );
  }

  // Métodos para procedimentos
  getProcedure(id: number): Observable<TreatmentProcedure> {
    // Buscar todos os planos de tratamento e encontrar o procedimento pelo ID
    return this.getAllTreatmentPlans().pipe(
      map(plans => {
        console.log(`Buscando procedimento ${id} em ${plans.length} planos de tratamento`);

        // Procurar o procedimento em todos os planos
        for (const plan of plans) {
          if (plan.procedures && Array.isArray(plan.procedures)) {
            const procedure = plan.procedures.find(proc => proc.id === id);
            if (procedure) {
              console.log(`Procedimento ${id} encontrado no plano ${plan.id}`);

              // Garantir que o professionalId esteja definido
              const professionalId = procedure.professional?.id || procedure.professionalId || 0;

              // Garantir que o plano tenha um ID válido
              if (!plan.id) {
                console.error('Plano sem ID válido:', plan);
                throw new Error(`Plano sem ID válido para o procedimento ${id}`);
              }

              // Criar um novo objeto TreatmentProcedure com todos os campos necessários
              const result: TreatmentProcedure = {
                ...procedure,
                id: procedure.id || id, // Usar o ID fornecido se o procedimento não tiver ID
                professionalId: professionalId,
                treatmentPlanId: plan.id,
                // Garantir que todos os campos obrigatórios estejam presentes
                procedureId: procedure.procedureId || 0,
                name: procedure.name || '',
                value: procedure.value || 0,
                status: procedure.status || TreatmentProcedureStatus.PENDING
              };

              return result;
            }
          }
        }

        throw new Error(`Procedimento com ID ${id} não encontrado em nenhum plano de tratamento`);
      }),
      catchError(error => {
        console.error(`Erro ao buscar procedimento ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  updateProcedure(id: number, procedure: any): Observable<TreatmentProcedure> {
    return this.http.patch<TreatmentProcedure>(`${this.apiUrl}/procedures/${id}`, procedure).pipe(
      switchMap(response => {
        // Após atualizar o procedimento, buscar o plano de tratamento atualizado
        if (response && response.treatmentPlanId) {
          return this.getTreatmentPlan(response.treatmentPlanId!).pipe(
            tap(updatedPlan => {
              // Notificar outros componentes sobre a atualização do plano
              console.log('Notificando atualização do plano:', updatedPlan);
              this.treatmentPlanUpdateService.notifyTreatmentPlanUpdated(updatedPlan);
              this.notificationService.success('Procedimento atualizado com sucesso!');
            }),
            map(() => response) // Retornar o procedimento original após a notificação
          );
        } else {
          this.notificationService.success('Procedimento atualizado com sucesso!');
          return of(response);
        }
      }),
      catchError(error => {
        console.error(`Erro ao atualizar procedimento ${id}:`, error);
        this.notificationService.error('Erro ao atualizar procedimento');
        return throwError(() => error);
      })
    );
  }

  deleteProcedure(id: number): Observable<void> {
    // Primeiro, obter o procedimento para saber a qual plano ele pertence
    return this.getProcedure(id).pipe(
      switchMap(procedure => {
        const treatmentPlanId = procedure.treatmentPlanId;

        // Depois, excluir o procedimento
        return this.http.delete<void>(`${this.apiUrl}/procedures/${id}`).pipe(
          switchMap(() => {
            // Após excluir, buscar o plano atualizado e notificar
            if (treatmentPlanId) {
              return this.getTreatmentPlan(treatmentPlanId).pipe(
                tap(updatedPlan => {
                  console.log('Notificando atualização do plano após exclusão:', updatedPlan);
                  this.treatmentPlanUpdateService.notifyTreatmentPlanUpdated(updatedPlan);
                  this.notificationService.success('Procedimento removido com sucesso!');
                }),
                map(() => undefined) // Retornar void
              );
            } else {
              this.notificationService.success('Procedimento removido com sucesso!');
              return of(undefined);
            }
          })
        );
      }),
      catchError(error => {
        console.error(`Erro ao remover procedimento ${id}:`, error);
        this.notificationService.error('Erro ao remover procedimento');
        return throwError(() => error);
      })
    );
  }

  // Método para buscar planos de tratamento de um paciente criados na data atual
  getTodayTreatmentPlansByPatient(patientId: number): Observable<TreatmentPlan[]> {
    // Obter a data atual no formato YYYY-MM-DD
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    // Buscar todos os planos do paciente
    return this.getTreatmentPlansByPatient(patientId).pipe(
      map(plans => {
        console.log('Todos os planos do paciente:', plans);

        // Filtrar apenas os planos criados hoje e com status OPEN
        const todayPlans = plans.filter(plan => {
          // Verificar se createdAt existe antes de criar uma nova data
          if (!plan.createdAt) return false;

          const planDate = new Date(plan.createdAt).toISOString().split('T')[0];
          return planDate === formattedDate && plan.status === 'open';
        });

        console.log('Planos criados hoje com status OPEN:', todayPlans);

        // Ordenar por data de criação (mais recente primeiro)
        return todayPlans.sort((a, b) => {
          if (!a.createdAt || !b.createdAt) return 0;
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
      })
    );
  }

  createProcedureWithAutoPlan(procedureData: any): Observable<TreatmentProcedure> {
    // Verificar se existe um plano de tratamento criado hoje para o paciente
    return this.getTodayTreatmentPlansByPatient(procedureData.patientId).pipe(
      switchMap(todayPlans => {
        // Se existir um plano criado hoje, adicionar o procedimento a esse plano
        if (todayPlans.length > 0) {
          const existingPlan = todayPlans[0];
          console.log('Encontrado plano de tratamento criado hoje:', existingPlan);

          if (!existingPlan.id) {
            console.error('Plano encontrado não tem ID válido:', existingPlan);
            return this.createNewPlanWithProcedure(procedureData);
          }

          // Criar uma cópia do plano existente com o novo procedimento
          return this.getTreatmentPlan(existingPlan.id).pipe(
            switchMap(fullPlan => {
              console.log('Plano completo obtido:', fullPlan);

              if (!fullPlan || !fullPlan.procedures) {
                console.error('Não foi possível obter o plano completo ou o plano não tem procedimentos:', fullPlan);
                return this.createNewPlanWithProcedure(procedureData);
              }

              // Preparar o procedimento para adicionar ao plano
              // Garantir que o valor seja um número válido
              let value = 0;
              if (typeof procedureData.value === 'number') {
                value = procedureData.value;
              } else if (typeof procedureData.value === 'string') {
                // Tentar converter string para número
                const valueStr = String(procedureData.value);
                const parsedValue = parseFloat(valueStr.replace(/[^\d.,]/g, '').replace(',', '.'));
                value = isNaN(parsedValue) ? 0 : parsedValue;
              }

              // Criar uma cópia do procedimento com o valor corrigido
              const procedureWithCorrectValue = {
                ...procedureData,
                value: value,
                procedureId: Number(procedureData.procedureId),
                professionalId: Number(procedureData.professionalId),
                appointmentId: procedureData.appointmentId ? Number(procedureData.appointmentId) : undefined
              };

              const { patientId, ...procedureWithoutPatientId } = procedureWithCorrectValue;

              // Criar uma cópia dos procedimentos existentes com o formato correto para o backend
              const existingProcedures = fullPlan.procedures.map(proc => {
                // Garantir que o valor seja um número válido
                let value = 0;
                if (typeof proc.value === 'number') {
                  value = proc.value;
                } else if (typeof proc.value === 'string') {
                  // Tentar converter string para número
                  const valueStr = String(proc.value);
                  const parsedValue = parseFloat(valueStr.replace(/[^\d.,]/g, '').replace(',', '.'));
                  value = isNaN(parsedValue) ? 0 : parsedValue;
                }

                // Garantir que o ID do procedimento seja um número
                const procedureId = typeof proc.procedureId === 'number'
                  ? proc.procedureId
                  : (proc.procedure?.id ? Number(proc.procedure.id) : 0);

                // Garantir que o ID do profissional seja um número
                const professionalId = typeof proc.professionalId === 'number'
                  ? proc.professionalId
                  : (proc.professional?.id ? Number(proc.professional.id) : 0);

                // Extrair apenas as propriedades que o backend espera
                return {
                  procedureId: procedureId,
                  name: proc.name || '',
                  value: value,
                  tooth: proc.tooth || undefined,
                  executionDate: proc.executionDate || undefined,
                  professionalId: professionalId,
                  appointmentId: proc.appointmentId ? Number(proc.appointmentId) : undefined,
                  status: proc.status || TreatmentProcedureStatus.PENDING,
                  notes: proc.notes || undefined,
                  nextVisitDetails: proc.nextVisitDetails || undefined
                };
              });

              // Adicionar o novo procedimento à lista
              const allProcedures = [...existingProcedures, procedureWithoutPatientId];

              console.log('Atualizando plano com todos os procedimentos:', {
                planId: existingPlan.id,
                existingProceduresCount: existingProcedures.length,
                newProcedure: procedureWithoutPatientId,
                totalProcedures: allProcedures.length
              });

              // Log detalhado para depuração
              console.log('Formato dos procedimentos enviados para o backend:', JSON.stringify(allProcedures, null, 2));

              // Verificar se todos os procedimentos têm valores válidos
              const validProcedures = allProcedures.map(proc => {
                // Garantir que o valor seja um número válido
                if (typeof proc.value !== 'number') {
                  console.warn(`Valor inválido encontrado: ${proc.value}, convertendo para 0`);
                  return { ...proc, value: 0 };
                }
                return proc;
              });

              console.log('Procedimentos validados antes de enviar:', validProcedures);

              // Atualizar o plano com todos os procedimentos
              return this.http.patch<TreatmentPlan>(`${this.apiUrl}/${existingPlan.id}`, {
                procedures: validProcedures
              }).pipe(
                tap(() => {
                  this.notificationService.success('Procedimento adicionado ao plano de tratamento existente.');

                  // Atualizar o plano de tratamento na interface
                  if (existingPlan.id) {
                    this.getTreatmentPlan(existingPlan.id).subscribe(updatedPlan => {
                      this.treatmentPlanUpdateService.notifyTreatmentPlanUpdated(updatedPlan);
                    });
                  }
                }),
                // Retornar o último procedimento adicionado (simulando o retorno de um procedimento)
                map(() => {
                  return {
                    ...procedureWithoutPatientId,
                    id: -1, // ID temporário, será substituído pelo real quando o plano for atualizado
                    treatmentPlanId: existingPlan.id || 0
                  } as TreatmentProcedure;
                })
              );
            })
          );
        } else {
          // Se não existir um plano criado hoje, criar um novo plano com o procedimento
          return this.createNewPlanWithProcedure(procedureData);
        }
      }),
      catchError(error => {
        console.error('Erro ao criar procedimento com plano automático:', error);
        this.notificationService.error('Erro ao criar procedimento');
        return throwError(() => error);
      })
    );
  }

  // Método auxiliar para criar um novo plano com o procedimento
  private createNewPlanWithProcedure(procedureData: any): Observable<TreatmentProcedure> {
    console.log('Criando novo plano com procedimento (dados originais):', procedureData);

    // Garantir que o valor seja um número válido
    let value = 0;
    if (typeof procedureData.value === 'number') {
      value = procedureData.value;
    } else if (typeof procedureData.value === 'string') {
      // Tentar converter string para número
      const valueStr = String(procedureData.value);
      const parsedValue = parseFloat(valueStr.replace(/[^\d.,]/g, '').replace(',', '.'));
      value = isNaN(parsedValue) ? 0 : parsedValue;
    }

    // Criar uma cópia do procedimento com valores corrigidos
    const correctedProcedureData = {
      ...procedureData,
      value: value,
      procedureId: Number(procedureData.procedureId),
      professionalId: Number(procedureData.professionalId),
      patientId: Number(procedureData.patientId),
      appointmentId: procedureData.appointmentId ? Number(procedureData.appointmentId) : undefined
      // Não enviar dentistId, pois o backend não espera esse campo
    };

    console.log('Criando novo plano com procedimento (dados corrigidos):', correctedProcedureData);

    // Verificação final para garantir que o valor seja um número
    if (typeof correctedProcedureData.value !== 'number') {
      console.warn(`Valor final ainda não é um número: ${correctedProcedureData.value}, forçando para 0`);
      correctedProcedureData.value = 0;
    }

    // Verificação final para garantir que todos os IDs sejam números
    if (typeof correctedProcedureData.procedureId !== 'number') {
      correctedProcedureData.procedureId = Number(correctedProcedureData.procedureId) || 0;
    }
    if (typeof correctedProcedureData.professionalId !== 'number') {
      correctedProcedureData.professionalId = Number(correctedProcedureData.professionalId) || 0;
    }
    if (typeof correctedProcedureData.patientId !== 'number') {
      correctedProcedureData.patientId = Number(correctedProcedureData.patientId) || 0;
    }

    console.log('Dados finais do procedimento:', correctedProcedureData);

    return this.http.post<TreatmentProcedure>(`${this.apiUrl}/procedures/with-plan`, correctedProcedureData).pipe(
      switchMap(response => {
        // Após criar o procedimento, buscar o plano de tratamento criado
        if (response && response.treatmentPlanId) {
          return this.getTreatmentPlan(response.treatmentPlanId).pipe(
            tap(newPlan => {
              // Notificar outros componentes sobre a criação do plano
              console.log('Notificando criação do plano:', newPlan);
              this.treatmentPlanUpdateService.notifyTreatmentPlanUpdated(newPlan);
              this.notificationService.success('Procedimento criado com novo plano de tratamento.');
            }),
            map(() => response) // Retornar o procedimento original após a notificação
          );
        } else {
          this.notificationService.success('Procedimento criado com novo plano de tratamento.');
          return of(response);
        }
      })
    );
  }

  // Método para buscar todos os procedimentos de um paciente (para a ficha clínica)
  getAllProceduresByPatient(patientId: number): Observable<TreatmentProcedure[]> {
    return this.getTreatmentPlansByPatient(patientId).pipe(
      map((plans: TreatmentPlan[]) => {
        // Extrair todos os procedimentos de todos os planos
        const allProcedures: TreatmentProcedure[] = [];
        plans.forEach((plan: TreatmentPlan) => {
          console.log(`Processando plano ${plan.id} para extrair procedimentos`);

          // Verificar se o plano tem procedimentos
          if (plan.procedures && Array.isArray(plan.procedures)) {
            console.log(`Plano ${plan.id} tem ${plan.procedures.length} procedimentos`);

            plan.procedures.forEach((procedure: TreatmentProcedure) => {
              // Garantir que o professionalId seja definido a partir do objeto professional
              const professionalId = procedure.professional?.id || procedure.professionalId;

              // Adicionar o ID do plano ao procedimento para referência
              allProcedures.push({
                ...procedure,
                treatmentPlanId: plan.id!,
                professionalId: professionalId
              });
            });
          } else {
            console.log(`Plano ${plan.id} não tem procedimentos ou não é um array`);
            console.log(`Valor da propriedade procedures:`, plan.procedures);

            // Verificar se há procedimentos na propriedade __procedures__
            if ((plan as any).__procedures__ && Array.isArray((plan as any).__procedures__)) {
              console.log(`Plano ${plan.id} tem ${(plan as any).__procedures__.length} procedimentos na propriedade __procedures__`);

              (plan as any).__procedures__.forEach((procedure: any) => {
                // Garantir que o professionalId seja definido a partir do objeto professional
                const professionalId = procedure.professional?.id || procedure.professionalId;

                // Adicionar o ID do plano ao procedimento para referência
                allProcedures.push({
                  ...procedure,
                  treatmentPlanId: plan.id!,
                  professionalId: professionalId
                });
              });
            }
          }
        });

        console.log(`Total de procedimentos extraídos: ${allProcedures.length}`);
        return allProcedures;
      }),
      catchError(error => {
        console.error(`Erro ao buscar procedimentos do paciente ${patientId}:`, error);
        return throwError(() => error);
      })
    );
  }

  // Alias para getAllProceduresByPatient para compatibilidade com o componente de odontograma
  getAllPatientProcedures(patientId: number): Observable<TreatmentProcedure[]> {
    return this.getAllProceduresByPatient(patientId);
  }
}
