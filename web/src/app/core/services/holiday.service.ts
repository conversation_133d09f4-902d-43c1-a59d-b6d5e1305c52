import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Holiday, CreateHolidayDto } from '../models/holiday.model';

@Injectable({
  providedIn: 'root',
})
export class HolidayService {
  private apiUrl = `${environment.apiUrl}/holidays`;

  constructor(private http: HttpClient) {}

  getHolidays(year?: number): Observable<Holiday[]> {
    let params = new HttpParams();
    if (year) {
      params = params.set('year', year.toString());
    }
    return this.http.get<Holiday[]>(this.apiUrl, { params });
  }

  createHoliday(holiday: CreateHolidayDto): Observable<Holiday> {
    return this.http.post<Holiday>(this.apiUrl, holiday);
  }

  updateHoliday(id: number, holiday: Partial<CreateHolidayDto>): Observable<Holiday> {
    return this.http.put<Holiday>(`${this.apiUrl}/${id}`, holiday);
  }

  deleteHoliday(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  isHoliday(date: string): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/check/${date}`);
  }
}
