import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Indication } from '../models/indication.model';
import { PaginatedResponse } from '../models/pagination.model';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class IndicationService {
  private apiUrl = `${environment.apiUrl}/indications`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) { }

  /**
   * Obtém todas as indicações com paginação
   */
  getIndications(
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
    indicatedPatientId?: number,
    referredById?: number,
    tag?: string
  ): Observable<PaginatedResponse<Indication>> {
    let url = `${this.apiUrl}?page=${page}&limit=${limit}`;
    
    if (search) url += `&search=${search}`;
    if (status) url += `&status=${status}`;
    if (indicatedPatientId) url += `&indicatedPatientId=${indicatedPatientId}`;
    if (referredById) url += `&referredById=${referredById}`;
    if (tag) url += `&tag=${tag}`;
    
    return this.http.get<PaginatedResponse<Indication>>(url);
  }

  /**
   * Obtém as indicações de um paciente específico
   */
  getPatientIndications(patientId: number): Observable<Indication[]> {
    return this.http.get<Indication[]>(`${this.apiUrl}/patient/${patientId}`);
  }

  /**
   * Obtém uma indicação pelo ID
   */
  getIndication(id: number): Observable<Indication> {
    return this.http.get<Indication>(`${this.apiUrl}/${id}`);
  }

  /**
   * Cria uma nova indicação
   */
  createIndication(indication: Partial<Indication>): Observable<Indication> {
    return this.http.post<Indication>(this.apiUrl, indication);
  }

  /**
   * Atualiza uma indicação existente
   */
  updateIndication(id: number, indication: Partial<Indication>): Observable<Indication> {
    return this.http.patch<Indication>(`${this.apiUrl}/${id}`, indication);
  }
}
