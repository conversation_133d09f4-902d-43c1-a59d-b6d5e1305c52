import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface AppointmentObservation {
  id: string;
  note: string;
  createdAt: Date;
  appointment?: {
    id: number;
    date: Date;
    time: string;
    status: string;
  };
  patient?: {
    id: number;
    name: string;
  };
}

@Injectable({
  providedIn: 'root',
})
export class AppointmentObservationService {
  private apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) {}

  getObservationsByPatient(patientId: number): Observable<AppointmentObservation[]> {
    return this.http.get<AppointmentObservation[]>(`${this.apiUrl}/patients/${patientId}/appointment-observations`);
  }

  createObservation(observation: {
    appointmentId: number;
    patientId: number;
    note: string;
  }): Observable<AppointmentObservation> {
    return this.http.post<AppointmentObservation>(`${this.apiUrl}/appointment-observations`, observation);
  }
}
