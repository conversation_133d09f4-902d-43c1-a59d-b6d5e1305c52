import { Injectable } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { Observable, catchError, map, tap, throwError } from 'rxjs';
import { Patient } from '../models/patient.model';
import { environment } from '../../../environments/environment';
import { NotificationService } from './notification.service';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root',
})
export class PatientService {
  private apiUrl = `${environment.apiUrl}/patients`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) {}

  getPatients(
    page: number = 1,
    limit: number = 10,
    search?: string,
    category?: string
  ): Observable<PaginatedResponse<Patient>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (category) {
      params = params.set('category', category);
    }

    return this.http
      .get<PaginatedResponse<Patient>>(this.apiUrl, { params })
      .pipe(
        tap((response) => {
          console.log(
            `Pacientes carregados: ${response.data.length} de ${response.total}`
          );
          console.log(
            `Página ${response.page} de ${Math.ceil(
              response.total / response.limit
            )}`
          );
        })
      );
  }

  // Método para compatibilidade com código existente que espera um array de pacientes
  getAllPatients(): Observable<Patient[]> {
    return this.http
      .get<PaginatedResponse<Patient>>(this.apiUrl, {
        params: new HttpParams().set('limit', '1000'), // Limite alto para pegar todos
      })
      .pipe(
        tap((response) => {
          console.log(`Todos os pacientes carregados: ${response.data.length}`);
        }),
        // Extrair apenas o array de dados
        map((response) => response.data)
      );
  }

  getPatient(id: number): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}`).pipe(
      tap((patient) => {
        console.log('Paciente carregado:', patient.id);
      })
    );
  }

  createPatient(
    patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>
  ): Observable<Patient> {
    // Converte a data para o formato ISO string para garantir compatibilidade com a API
    const patientData = {
      ...patient,
      birthDate:
        patient.birthDate instanceof Date
          ? patient.birthDate.toISOString()
          : patient.birthDate,
    };
    console.log('Enviando para API:', patientData);
    return this.http.post<Patient>(this.apiUrl, patientData).pipe(
      tap((newPatient) => {
        this.notificationService.success('Paciente cadastrado com sucesso!');
        console.log('Paciente criado:', newPatient.id);
      })
    );
  }

  createExternalPatient(patientData: { name: string; phone: string }): Observable<Patient> {
    return this.http.post<Patient>(`${this.apiUrl}/external`, patientData).pipe(
      tap((newPatient) => {
        console.log('Paciente externo criado:', newPatient.id);
      })
    );
  }

  updatePatient(id: number, patient: Partial<Patient>): Observable<Patient> {
    // Converte a data para o formato ISO string para garantir compatibilidade com a API
    const patientData = {
      ...patient,
      birthDate:
        patient.birthDate instanceof Date
          ? patient.birthDate.toISOString()
          : patient.birthDate,
    };

    // Log detalhado para depuração
    console.log('Atualizando na API:', patientData);
    console.log(
      'Tipo de paciente sendo enviado (ID):',
      patientData.patientTypeId
    );

    return this.http.patch<Patient>(`${this.apiUrl}/${id}`, patientData).pipe(
      tap((updatedPatient) => {
        this.notificationService.success('Paciente atualizado com sucesso!');
        console.log('Paciente atualizado:', updatedPatient.id);
        console.log('Resposta da API (paciente atualizado):', updatedPatient);
        console.log(
          'Tipo de paciente retornado (ID):',
          updatedPatient.patientTypeId
        );
      }),
      catchError((error) => {
        console.error('Erro ao atualizar paciente:', error);
        this.notificationService.error(
          'Erro ao atualizar paciente. Por favor, tente novamente.'
        );
        return throwError(() => error);
      })
    );
  }

  deletePatient(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      tap(() => {
        this.notificationService.success('Paciente removido com sucesso!');
        console.log('Paciente removido:', id);
      })
    );
  }
}
