import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { TreatmentPlan } from '../models/treatment-plan.model';

@Injectable({
  providedIn: 'root'
})
export class TreatmentPlanUpdateService {
  // Subject para notificar sobre atualizações de planos de tratamento
  private treatmentPlanUpdatedSource = new Subject<TreatmentPlan>();
  
  // Observable que os componentes podem assinar para receber atualizações
  treatmentPlanUpdated$ = this.treatmentPlanUpdatedSource.asObservable();

  constructor() { }

  // Método para notificar que um plano de tratamento foi atualizado
  notifyTreatmentPlanUpdated(treatmentPlan: TreatmentPlan): void {
    console.log('Notificando atualização do plano de tratamento:', treatmentPlan);
    this.treatmentPlanUpdatedSource.next(treatmentPlan);
  }
}
