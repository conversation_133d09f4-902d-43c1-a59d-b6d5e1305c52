import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Budget } from '../models/budget.model';
import { environment } from '../../../environments/environment';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root',
})
export class BudgetService {
  private apiUrl = `${environment.apiUrl}/budgets`;

  constructor(private http: HttpClient) {}

  getBudgets(
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
    patientId?: number,
    dentistId?: number
  ): Observable<PaginatedResponse<Budget>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (status) {
      params = params.set('status', status);
    }

    if (patientId) {
      params = params.set('patientId', patientId.toString());
    }

    if (dentistId) {
      params = params.set('dentistId', dentistId.toString());
    }

    return this.http.get<PaginatedResponse<Budget>>(this.apiUrl, { params });
  }

  getBudgetsByPatient(patientId: number): Observable<Budget[]> {
    return this.http.get<Budget[]>(`${this.apiUrl}/patient/${patientId}`);
  }

  getBudget(id: number): Observable<Budget> {
    return this.http.get<Budget>(`${this.apiUrl}/${id}`);
  }

  createBudget(budget: Partial<Budget>): Observable<Budget> {
    return this.http.post<Budget>(this.apiUrl, budget);
  }

  updateBudget(id: number, budget: Partial<Budget>): Observable<Budget> {
    return this.http.patch<Budget>(`${this.apiUrl}/${id}`, budget);
  }

  deleteBudget(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
