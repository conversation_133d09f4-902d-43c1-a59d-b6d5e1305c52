import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SidebarService {
  private isOpenSubject = new BehaviorSubject<boolean>(true);
  private isCollapsedSubject = new BehaviorSubject<boolean>(false);

  public isOpen$: Observable<boolean> = this.isOpenSubject.asObservable();
  public isCollapsed$: Observable<boolean> = this.isCollapsedSubject.asObservable();

  constructor() {
    // Inicializa o estado do sidebar com base no tamanho da tela
    if (typeof window !== 'undefined') {
      this.checkScreenSize();
      // Adiciona um listener para atualizar o estado quando a tela for redimensionada
      window.addEventListener('resize', () => this.checkScreenSize());
    }
  }

  private checkScreenSize(): void {
    // Em telas menores que 1024px (lg no Tailwind), o sidebar começa fechado
    if (typeof window !== 'undefined' && window.innerWidth < 1024) {
      this.isOpenSubject.next(false);
      this.isCollapsedSubject.next(false); // Reset collapsed state on mobile
    } else {
      this.isOpenSubject.next(true);
    }
  }

  toggle(): void {
    this.isOpenSubject.next(!this.isOpenSubject.value);
  }

  open(): void {
    this.isOpenSubject.next(true);
  }

  close(): void {
    this.isOpenSubject.next(false);
  }

  toggleCollapse(): void {
    // Only allow collapse on desktop (large screens)
    if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
      this.isCollapsedSubject.next(!this.isCollapsedSubject.value);
    }
  }

  collapse(): void {
    if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
      this.isCollapsedSubject.next(true);
    }
  }

  expand(): void {
    if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
      this.isCollapsedSubject.next(false);
    }
  }

  get isCollapsed(): boolean {
    return this.isCollapsedSubject.value;
  }

  get isOpen(): boolean {
    return this.isOpenSubject.value;
  }
}
