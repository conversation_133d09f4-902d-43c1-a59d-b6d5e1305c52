import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface Notification {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationSubject = new Subject<Notification>();
  notifications$ = this.notificationSubject.asObservable();

  constructor() { }

  /**
   * Exibe uma notificação de sucesso
   * @param message Mensagem a ser exibida
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  success(message: string, duration = 5000): void {
    this.notify({ type: 'success', message, duration });
  }

  /**
   * Exibe uma notificação de erro
   * @param message Mensagem a ser exibida
   * @param duration Duração em milissegundos (padrão: 8000ms)
   */
  error(message: string, duration = 8000): void {
    this.notify({ type: 'error', message, duration });
  }

  /**
   * Exibe uma notificação informativa
   * @param message Mensagem a ser exibida
   * @param duration Duração em milissegundos (padrão: 5000ms)
   */
  info(message: string, duration = 5000): void {
    this.notify({ type: 'info', message, duration });
  }

  /**
   * Exibe uma notificação de aviso
   * @param message Mensagem a ser exibida
   * @param duration Duração em milissegundos (padrão: 7000ms)
   */
  warning(message: string, duration = 7000): void {
    this.notify({ type: 'warning', message, duration });
  }

  /**
   * Método privado para emitir notificações
   * @param notification Objeto de notificação
   */
  private notify(notification: Notification): void {
    this.notificationSubject.next(notification);
  }
}
