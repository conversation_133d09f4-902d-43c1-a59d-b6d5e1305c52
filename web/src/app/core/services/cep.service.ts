import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface CepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
  erro?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CepService {
  constructor(private http: HttpClient) {}

  consultarCep(cep: string): Observable<CepResponse | null> {
    // Remover caracteres não numéricos
    cep = cep.replace(/\D/g, '');
    
    if (cep === '') {
      return of(null);
    }

    // Validar o CEP
    const validacep = /^[0-9]{8}$/;
    
    if (!validacep.test(cep)) {
      return of(null);
    }

    const url = `https://viacep.com.br/ws/${cep}/json/`;
    
    return this.http.get<CepResponse>(url).pipe(
      map(response => {
        if (response.erro) {
          return null;
        }
        return response;
      }),
      catchError(() => of(null))
    );
  }
}
