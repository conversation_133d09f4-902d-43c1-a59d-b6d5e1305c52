import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  DentistSchedule,
  DentistException,
  DentistAvailability,
  DentistScheduleOverview,
  WeeklySchedule,
  AvailableTimes
} from '../models/dentist-schedule.model';

@Injectable({
  providedIn: 'root',
})
export class DentistScheduleService {
  private apiUrl = `${environment.apiUrl}/dentists`;

  constructor(private http: HttpClient) {}

  // Escala semanal
  getSchedule(dentistId: number): Observable<DentistSchedule> {
    return this.http.get<DentistSchedule>(`${this.apiUrl}/${dentistId}/schedule`);
  }

  updateSchedule(dentistId: number, weeklySchedule: WeeklySchedule): Observable<DentistSchedule> {
    return this.http.put<DentistSchedule>(`${this.apiUrl}/${dentistId}/schedule`, {
      weeklySchedule
    });
  }

  // Exceções
  createException(dentistId: number, exception: {
    date: string;
    type: 'day-off' | 'custom-hours';
    customHours?: { start: string; end: string }[];
    reason?: string;
  }): Observable<DentistException> {
    return this.http.post<DentistException>(`${this.apiUrl}/${dentistId}/exceptions`, exception);
  }

  getExceptions(
    dentistId: number,
    startDate?: string,
    endDate?: string
  ): Observable<DentistException[]> {
    let params = new HttpParams();

    if (startDate) {
      params = params.set('startDate', startDate);
    }

    if (endDate) {
      params = params.set('endDate', endDate);
    }

    return this.http.get<DentistException[]>(`${this.apiUrl}/${dentistId}/exceptions`, { params });
  }

  updateException(dentistId: number, exceptionId: number, exception: {
    date: string;
    type: 'day-off' | 'custom-hours';
    customHours?: { start: string; end: string }[];
    reason?: string;
  }): Observable<DentistException> {
    return this.http.put<DentistException>(`${this.apiUrl}/${dentistId}/exceptions/${exceptionId}`, exception);
  }

  deleteException(dentistId: number, exceptionId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${dentistId}/exceptions/${exceptionId}`);
  }

  // Disponibilidade
  getAvailability(dentistId: number, date: string): Observable<DentistAvailability> {
    const params = new HttpParams().set('date', date);
    return this.http.get<DentistAvailability>(`${this.apiUrl}/${dentistId}/availability`, { params });
  }

  // Horários disponíveis para agendamento
  getAvailableTimes(dentistId: number, date: string, duration: number = 30, excludeAppointmentId?: number): Observable<AvailableTimes> {
    let params = new HttpParams()
      .set('date', date)
      .set('duration', duration.toString());

    if (excludeAppointmentId) {
      params = params.set('excludeAppointmentId', excludeAppointmentId.toString());
    }

    return this.http.get<AvailableTimes>(`${this.apiUrl}/${dentistId}/available-times`, { params });
  }

  // Detalhes completos da agenda do dentista
  getScheduleDetails(dentistId: number, date: string, duration: number = 5): Observable<any> {
    const params = new HttpParams()
      .set('date', date)
      .set('duration', duration.toString());

    return this.http.get<any>(`${environment.apiUrl}/dentists/${dentistId}/schedule-details`, { params });
  }

  // Overview
  getScheduleOverview(): Observable<DentistScheduleOverview[]> {
    return this.http.get<DentistScheduleOverview[]>(`${this.apiUrl}/schedule/overview`);
  }

  // Métodos utilitários
  getDayName(dayKey: string): string {
    const dayNames: { [key: string]: string } = {
      monday: 'Segunda-feira',
      tuesday: 'Terça-feira',
      wednesday: 'Quarta-feira',
      thursday: 'Quinta-feira',
      friday: 'Sexta-feira',
      saturday: 'Sábado',
      sunday: 'Domingo'
    };
    return dayNames[dayKey] || dayKey;
  }

  getShortDayName(dayKey: string): string {
    const shortDayNames: { [key: string]: string } = {
      monday: 'Seg',
      tuesday: 'Ter',
      wednesday: 'Qua',
      thursday: 'Qui',
      friday: 'Sex',
      saturday: 'Sáb',
      sunday: 'Dom'
    };
    return shortDayNames[dayKey] || dayKey;
  }

  formatTimeSlots(timeSlots: { start: string; end: string }[]): string {
    if (!timeSlots || timeSlots.length === 0) {
      return 'Folga';
    }
    
    return timeSlots.map(slot => `${slot.start}-${slot.end}`).join(', ');
  }

  isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  isValidDateFormat(date: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    return dateRegex.test(date);
  }
}
