import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { LeadForm } from '../models/lead-form.model';
import { NotificationService } from './notification.service';
import { tap } from 'rxjs/operators';

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface LeadPaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  isExistingPatient?: boolean;
  hasUpdatesAvailable?: boolean;
  createdAt?: string;
}

@Injectable({
  providedIn: 'root',
})
export class LeadService {
  private apiUrl = `${environment.apiUrl}/leads`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) {}

  getLeads(): Observable<LeadForm[]> {
    return this.http.get<LeadForm[]>(this.apiUrl);
  }

  getLeadsPaginated(params: LeadPaginationParams): Observable<PaginatedResponse<LeadForm>> {
    let httpParams = new HttpParams();

    if (params.page !== undefined) {
      httpParams = httpParams.set('page', params.page.toString());
    }

    if (params.limit !== undefined) {
      httpParams = httpParams.set('limit', params.limit.toString());
    }

    if (params.search) {
      httpParams = httpParams.set('search', params.search);
    }

    if (params.isExistingPatient !== undefined) {
      httpParams = httpParams.set('isExistingPatient', params.isExistingPatient.toString());
    }

    if (params.hasUpdatesAvailable !== undefined) {
      httpParams = httpParams.set('hasUpdatesAvailable', params.hasUpdatesAvailable.toString());
    }

    if (params.createdAt) {
      httpParams = httpParams.set('createdAt', params.createdAt);
    }

    return this.http.get<PaginatedResponse<LeadForm>>(`${this.apiUrl}/paginated`, { params: httpParams });
  }

  getLead(id: string): Observable<LeadForm> {
    return this.http.get<LeadForm>(`${this.apiUrl}/${id}`);
  }

  createLead(leadForm: Partial<LeadForm>): Observable<LeadForm> {
    return this.http.post<LeadForm>(this.apiUrl, leadForm).pipe(
      tap(() => {
        this.notificationService.success('Formulário enviado com sucesso!');
      })
    );
  }

  updateLead(id: string, leadForm: Partial<LeadForm>): Observable<LeadForm> {
    return this.http.patch<LeadForm>(`${this.apiUrl}/${id}`, leadForm).pipe(
      tap(() => {
        this.notificationService.success('Lead atualizado com sucesso!');
      })
    );
  }

  deleteLead(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      tap(() => {
        this.notificationService.success('Lead removido com sucesso!');
      })
    );
  }

  updatePatientField(leadFormId: string, fieldName: string): Observable<LeadForm> {
    return this.http.post<LeadForm>(`${this.apiUrl}/update-patient-field`, {
      leadFormId,
      fieldName,
    }).pipe(
      tap(() => {
        this.notificationService.success('Campo do paciente atualizado com sucesso!');
      })
    );
  }

  skipPatientField(leadFormId: string, fieldName: string): Observable<LeadForm> {
    return this.http.post<LeadForm>(`${this.apiUrl}/skip-patient-field`, {
      leadFormId,
      fieldName,
    }).pipe(
      tap(() => {
        this.notificationService.success('Campo ignorado com sucesso!');
      })
    );
  }

  comparePatientFields(leadFormId: string): Observable<LeadForm> {
    return this.http.post<LeadForm>(`${this.apiUrl}/compare-patient-fields`, {
      leadFormId,
    }).pipe(
      tap(() => {
        this.notificationService.success('Comparação de campos realizada com sucesso!');
      })
    );
  }
}
