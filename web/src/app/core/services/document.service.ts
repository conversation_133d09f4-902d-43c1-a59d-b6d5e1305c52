import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { PatientDocument } from '../models/document.model';

@Injectable({
  providedIn: 'root',
})
export class DocumentService {
  private apiUrl = `${environment.apiUrl}/documents`;

  constructor(private http: HttpClient) {}

  uploadDocument(
    patientId: number,
    name: string,
    file: File
  ): Observable<PatientDocument> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('patientId', patientId.toString());
    formData.append('name', name);

    return this.http.post<PatientDocument>(`${this.apiUrl}/upload`, formData);
  }

  getDocumentsByPatient(patientId: number): Observable<PatientDocument[]> {
    return this.http.get<PatientDocument[]>(`${this.apiUrl}/patients/${patientId}/documents`);
  }

  getDocument(documentId: string): Observable<PatientDocument> {
    return this.http.get<PatientDocument>(`${this.apiUrl}/${documentId}`);
  }

  deleteDocument(documentId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${documentId}`);
  }
}
