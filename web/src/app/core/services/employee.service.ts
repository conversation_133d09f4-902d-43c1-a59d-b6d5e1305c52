import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Employee, EmployeeType } from '../models/employee.model';
import { environment } from '../../../environments/environment';
import { NotificationService } from './notification.service';
import { PaginatedResponse } from '../models/pagination.model';

@Injectable({
  providedIn: 'root',
})
export class EmployeeService {
  private apiUrl = `${environment.apiUrl}/employees`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) {}

  getEmployees(
    page: number = 1,
    limit: number = 10,
    search?: string,
    type?: EmployeeType
  ): Observable<PaginatedResponse<Employee>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    if (type) {
      params = params.set('type', type);
    }

    return this.http
      .get<PaginatedResponse<Employee>>(this.apiUrl, { params })
      .pipe(
        tap((response) => {
          console.log(
            `Funcionários carregados: ${response.data.length} de ${response.total}`
          );
          console.log(
            `Página ${response.page} de ${Math.ceil(
              response.total / response.limit
            )}`
          );
        }),
        map((response) => ({
          ...response,
          data: response.data.map((employee) => ({
            ...employee,
            birthDate: employee.birthDate
              ? new Date(employee.birthDate)
              : undefined,
            admissionDate: new Date(employee.admissionDate),
            createdAt: new Date(employee.createdAt),
            updatedAt: new Date(employee.updatedAt),
          })),
        }))
      );
  }

  // Método para compatibilidade com código existente que espera um array de funcionários
  getAllEmployees(
    search?: string,
    type?: EmployeeType
  ): Observable<Employee[]> {
    let params = new HttpParams().set('limit', '1000'); // Limite alto para pegar todos

    if (search) {
      params = params.set('search', search);
    }

    if (type) {
      params = params.set('type', type);
    }

    return this.http
      .get<PaginatedResponse<Employee>>(this.apiUrl, { params })
      .pipe(
        tap((response) => {
          console.log(
            `Todos os funcionários carregados: ${response.data.length}`
          );
        }),
        map((response) =>
          response.data.map((employee) => ({
            ...employee,
            birthDate: employee.birthDate
              ? new Date(employee.birthDate)
              : undefined,
            admissionDate: new Date(employee.admissionDate),
            createdAt: new Date(employee.createdAt),
            updatedAt: new Date(employee.updatedAt),
          }))
        )
      );
  }

  getEmployee(id: string): Observable<Employee> {
    return this.http.get<Employee>(`${this.apiUrl}/${id}`).pipe(
      map((employee) => ({
        ...employee,
        birthDate: employee.birthDate
          ? new Date(employee.birthDate)
          : undefined,
        admissionDate: new Date(employee.admissionDate),
        createdAt: new Date(employee.createdAt),
        updatedAt: new Date(employee.updatedAt),
      }))
    );
  }

  createEmployee(
    employee: Omit<Employee, 'id' | 'createdAt' | 'updatedAt'>
  ): Observable<Employee> {
    // Converter datas para formato ISO string para a API
    const employeeData = {
      ...employee,
      birthDate: employee.birthDate
        ? employee.birthDate.toISOString().split('T')[0]
        : undefined,
      admissionDate: employee.admissionDate.toISOString().split('T')[0],
    };

    return this.http.post<Employee>(this.apiUrl, employeeData).pipe(
      map((newEmployee) => {
        this.notificationService.success('Funcionário cadastrado com sucesso!');
        return {
          ...newEmployee,
          birthDate: newEmployee.birthDate
            ? new Date(newEmployee.birthDate)
            : undefined,
          admissionDate: new Date(newEmployee.admissionDate),
          createdAt: new Date(newEmployee.createdAt),
          updatedAt: new Date(newEmployee.updatedAt),
        };
      })
    );
  }

  updateEmployee(
    id: string,
    employee: Partial<Employee>
  ): Observable<Employee> {
    // Converter datas para formato ISO string para a API
    const employeeData: any = { ...employee };

    if (employeeData.birthDate) {
      employeeData.birthDate = employeeData.birthDate
        .toISOString()
        .split('T')[0];
    }

    if (employeeData.admissionDate) {
      employeeData.admissionDate = employeeData.admissionDate
        .toISOString()
        .split('T')[0];
    }

    return this.http.patch<Employee>(`${this.apiUrl}/${id}`, employeeData).pipe(
      map((updatedEmployee) => {
        this.notificationService.success('Funcionário atualizado com sucesso!');
        return {
          ...updatedEmployee,
          birthDate: updatedEmployee.birthDate
            ? new Date(updatedEmployee.birthDate)
            : undefined,
          admissionDate: new Date(updatedEmployee.admissionDate),
          createdAt: new Date(updatedEmployee.createdAt),
          updatedAt: new Date(updatedEmployee.updatedAt),
        };
      })
    );
  }

  deleteEmployee(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      map(() => {
        this.notificationService.success('Funcionário removido com sucesso!');
      })
    );
  }
}
