import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError, of, forkJoin } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Scheduling } from '../models/scheduling.model';
import { Patient } from '../models/patient.model';
import { Dentist } from '../models/dentist.model';
import { PaginatedResponse } from '../models/paginated-response.model';
import { NotificationService } from './notification.service';
import { PatientService } from './patient.service';
import { DentistService } from './dentist.service';
import { TreatmentPlanService } from './treatment-plan.service';

@Injectable({
  providedIn: 'root',
})
export class SchedulingService {
  private apiUrl = `${environment.apiUrl}/schedulings`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService,
    private patientService: PatientService,
    private dentistService: DentistService,
    private treatmentPlanService: TreatmentPlanService
  ) {}

  getAllSchedulings(): Observable<Scheduling[] | PaginatedResponse<Scheduling>> {
    // Adicionar parâmetro noPagination=true para garantir que recebemos um array
    const params = new HttpParams().set('noPagination', 'true');

    return this.http.get<Scheduling[] | PaginatedResponse<Scheduling>>(this.apiUrl, { params }).pipe(
      switchMap((response: any) => {
        // Verificar se a resposta é um array ou um objeto paginado
        let schedulings: Scheduling[] = [];

        if (Array.isArray(response)) {
          schedulings = response;
        } else if (response && typeof response === 'object' && 'data' in response) {
          // É um objeto paginado
          schedulings = response.data;
        }

        if (schedulings.length === 0) {
          return of(response); // Retornar a resposta original se não houver agendamentos
        }

        // Obter informações de pacientes e dentistas
        const patientIds = [...new Set(schedulings.map((s) => s.patientId))];
        const dentistIds = [...new Set(schedulings.map((s) => s.dentistId))];

        // Buscar todos os pacientes e dentistas e filtrar localmente
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
        }).pipe(
          map(({ patients, dentists }) => {
            const enrichedSchedulings = schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              );

              return {
                ...scheduling,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || 'Dentista não encontrado',
              };
            });

            // Se a resposta original era um objeto paginado, retornar um objeto paginado
            if (!Array.isArray(response) && response && typeof response === 'object' && 'data' in response) {
              return {
                ...response,
                data: enrichedSchedulings
              };
            }

            // Caso contrário, retornar o array enriquecido
            return enrichedSchedulings;
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  // Método para obter um agendamento por ID
  getSchedulingById(id: number): Observable<Scheduling> {
    return this.http.get<Scheduling>(`${this.apiUrl}/${id}`).pipe(
      switchMap((scheduling) => {
        // Criar um array de observables para buscar informações relacionadas
        const observables: any = {
          patient: this.patientService.getPatient(scheduling.patientId),
        };

        // Só buscar dentista se dentistId estiver presente
        if (scheduling.dentistId) {
          observables.dentist = this.dentistService.getDentist(scheduling.dentistId);
        }

        // Se o agendamento tem um plano de tratamento, buscar os detalhes do plano
        if (scheduling.treatmentPlanId) {
          observables.treatmentPlan = this.treatmentPlanService.getTreatmentPlan(scheduling.treatmentPlanId);
        }

        return forkJoin(observables).pipe(
          map((results: any) => {
            const { patient, dentist, treatmentPlan } = results;

            return {
              ...scheduling,
              patientName: patient?.name || 'Paciente não encontrado',
              dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
              treatmentPlan: treatmentPlan || undefined
            };
          })
        );
      }),
      catchError((error) => this.handleError(error))
    );
  }

  // Método para obter os procedimentos de um agendamento
  getSchedulingProcedures(schedulingId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/${schedulingId}/procedures`).pipe(
      catchError((error) => {
        // Se o endpoint não existir, retornar um array vazio
        if (error.status === 404) {
          return of([]);
        }
        return this.handleError(error);
      })
    );
  }

  // Método para obter agendamentos paginados com filtros
  getSchedulings(page: number, limit: number, filters?: any): Observable<PaginatedResponse<Scheduling> | Scheduling[]> {
    // Construir os parâmetros de consulta
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    // Adicionar filtros se fornecidos
    if (filters) {
      if (filters.search) params = params.set('search', filters.search);
      if (filters.status) params = params.set('status', filters.status);
      if (filters.dentistId) params = params.set('dentistId', filters.dentistId);
      if (filters.startDate) params = params.set('startDate', filters.startDate);
      if (filters.endDate) params = params.set('endDate', filters.endDate);
      if (filters.paid !== undefined) params = params.set('paid', filters.paid.toString());
    }

    return this.http.get<PaginatedResponse<Scheduling> | Scheduling[]>(this.apiUrl, { params }).pipe(
      switchMap((response: any) => {
        // Verificar se a resposta é um array ou um objeto paginado
        let schedulings: Scheduling[] = [];

        if (Array.isArray(response)) {
          schedulings = response;
        } else if (response && typeof response === 'object' && 'data' in response) {
          // É um objeto paginado
          schedulings = response.data;
        }

        if (schedulings.length === 0) {
          return of(response); // Retornar a resposta original se não houver agendamentos
        }

        // Obter informações de pacientes e dentistas
        const patientIds = [...new Set(schedulings.map((s) => s.patientId))];
        const dentistIds = [...new Set(schedulings.map((s) => s.dentistId).filter(id => id !== undefined))];

        // Buscar todos os pacientes e dentistas e filtrar localmente
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
        }).pipe(
          map(({ patients, dentists }) => {
            const enrichedSchedulings = schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = scheduling.dentistId ? dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              ) : null;

              return {
                ...scheduling,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
              };
            });

            // Se a resposta original era um objeto paginado, retornar um objeto paginado
            if (!Array.isArray(response) && response && typeof response === 'object' && 'data' in response) {
              return {
                ...response,
                data: enrichedSchedulings
              };
            }

            // Caso contrário, retornar o array enriquecido
            return enrichedSchedulings;
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  // Método legado para compatibilidade
  getScheduling(id: number): Observable<Scheduling> {
    return this.getSchedulingById(id);
  }

  getSchedulingsByPatient(patientId?: number): Observable<Scheduling[]> {
    let url = this.apiUrl;
    if (patientId) {
      url += `?patientId=${patientId}`;
    }

    return this.http.get<Scheduling[]>(url).pipe(
      switchMap((schedulings) => {
        if (schedulings.length === 0) {
          return of([]);
        }

        // Obter informações de pacientes e dentistas
        const patientIds = [...new Set(schedulings.map((s) => s.patientId))];
        const dentistIds = [...new Set(schedulings.map((s) => s.dentistId).filter(id => id !== undefined))];

        // Se a API já retorna os relacionamentos (patient, dentist), usar esses dados
        // Caso contrário, buscar separadamente
        if (schedulings.length > 0 && schedulings[0].patient) {
          // A API já retorna os relacionamentos, apenas adicionar campos de compatibilidade
          return of(schedulings.map((scheduling) => ({
            ...scheduling,
            patientName: scheduling.patient?.name || 'Paciente não encontrado',
            dentistName: scheduling.dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
          })));
        }

        // Fallback: buscar todos os pacientes e dentistas e filtrar localmente
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
        }).pipe(
          map(({ patients, dentists }) => {
            return schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = scheduling.dentistId ? dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              ) : null;

              return {
                ...scheduling,
                patient: patient,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
              };
            });
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  findByPatient(patientId: number): Observable<Scheduling[]> {
    return this.getSchedulingsByPatient(patientId);
  }

  findByTreatment(treatmentId: number): Observable<Scheduling[]> {
    return this.getSchedulingsByTreatmentId(treatmentId);
  }

  findByTreatmentPlan(treatmentPlanId: number): Observable<Scheduling[]> {
    return this.getSchedulingsByTreatmentPlanId(treatmentPlanId);
  }

  findByDateRange(startDate: string, endDate: string): Observable<Scheduling[]> {
    return this.getSchedulingsByDateRange(new Date(startDate), new Date(endDate));
  }

  createScheduling(scheduling: Partial<Scheduling>): Observable<Scheduling> {
    return this.http.post<Scheduling>(this.apiUrl, scheduling).pipe(
      tap(() => {
        this.notificationService.success('Agendamento criado com sucesso!');
      }),
      catchError(this.handleError)
    );
  }

  updateScheduling(
    id: number,
    scheduling: Partial<Scheduling>
  ): Observable<Scheduling> {
    return this.http.patch<Scheduling>(`${this.apiUrl}/${id}`, scheduling).pipe(
      tap(() => {
        this.notificationService.success('Agendamento atualizado com sucesso!');
      }),
      catchError(this.handleError)
    );
  }

  deleteScheduling(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      tap(() => {
        this.notificationService.success('Agendamento excluído com sucesso!');
      }),
      catchError(this.handleError)
    );
  }

  getSchedulingsByTreatmentId(treatmentId: number): Observable<Scheduling[]> {
    const url = `${this.apiUrl}?treatmentId=${treatmentId}`;

    return this.http.get<Scheduling[]>(url).pipe(
      switchMap((schedulings) => {
        if (schedulings.length === 0) {
          return of([]);
        }

        // Obter informações de pacientes e dentistas
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
        }).pipe(
          map(({ patients, dentists }) => {
            return schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = scheduling.dentistId ? dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              ) : null;

              return {
                ...scheduling,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
              };
            });
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  getSchedulingsByTreatmentPlanId(treatmentPlanId: number): Observable<Scheduling[]> {
    const url = `${this.apiUrl}?treatmentPlanId=${treatmentPlanId}`;

    return this.http.get<Scheduling[]>(url).pipe(
      switchMap((schedulings) => {
        if (schedulings.length === 0) {
          return of([]);
        }

        // Obter informações de pacientes, dentistas e plano de tratamento
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
          treatmentPlan: this.treatmentPlanService.getTreatmentPlan(treatmentPlanId)
        }).pipe(
          map(({ patients, dentists, treatmentPlan }) => {
            return schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = scheduling.dentistId ? dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              ) : null;

              return {
                ...scheduling,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
                treatmentPlan: treatmentPlan
              };
            });
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  getSchedulingsByDateRange(
    startDate: Date,
    endDate: Date
  ): Observable<Scheduling[]> {
    // Formatar datas para o formato ISO (YYYY-MM-DD)
    const start = this.formatDateForApi(startDate);
    const end = this.formatDateForApi(endDate);

    // Construir URL com parâmetros de consulta
    const url = `${this.apiUrl}?startDate=${start}&endDate=${end}`;

    console.log('🔍 Buscando agendamentos:', { url, start, end });

    return this.http.get<any>(url).pipe(
      tap((response) => {
        console.log('📡 Resposta da API:', response);
      }),
      switchMap((response: any) => {
        // Verificar se a resposta é um array ou um objeto paginado
        let schedulings: Scheduling[] = [];

        if (Array.isArray(response)) {
          schedulings = response;
        } else if (response && typeof response === 'object' && 'data' in response) {
          // É um objeto paginado
          schedulings = response.data;
        }

        console.log('📋 Agendamentos extraídos:', schedulings.length);

        if (schedulings.length === 0) {
          return of([]);
        }

        // Obter informações de pacientes e dentistas
        const patientIds = [...new Set(schedulings.map((s) => s.patientId))];
        const dentistIds = [...new Set(schedulings.map((s) => s.dentistId).filter(id => id !== undefined))];

        // Buscar todos os pacientes e dentistas e filtrar localmente
        return forkJoin({
          patients: this.patientService.getAllPatients(),
          dentists: this.dentistService.getAllDentists(),
        }).pipe(
          map(({ patients, dentists }) => {
            const enrichedSchedulings = schedulings.map((scheduling) => {
              const patient = patients.find(
                (p: Patient) => p.id === scheduling.patientId
              );
              const dentist = scheduling.dentistId ? dentists.find(
                (d: Dentist) => d.id === scheduling.dentistId
              ) : null;

              return {
                ...scheduling,
                patientName: patient?.name || 'Paciente não encontrado',
                dentistName: dentist?.name || (scheduling.dentistId ? 'Dentista não encontrado' : 'Sem dentista definido'),
              };
            });

            console.log('✅ Agendamentos enriquecidos:', enrichedSchedulings.length);
            return enrichedSchedulings;
          })
        );
      }),
      catchError(this.handleError)
    );
  }

  private formatDateForApi(date: Date): string {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Ocorreu um erro ao processar a solicitação.';

    if (error.error instanceof ErrorEvent) {
      // Erro do lado do cliente
      errorMessage = `Erro: ${error.error.message}`;
    } else {
      // Erro retornado pelo backend
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      } else if (error.status) {
        errorMessage = `Código de erro: ${error.status}, Mensagem: ${error.message}`;
      }
    }

    this.notificationService.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
