import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Anamnesis,
  AnamnesisQuestion,
} from '../models/anamnesis.model';

@Injectable({
  providedIn: 'root',
})
export class AnamnesisService {
  private apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) {}

  // Métodos para AnamnesisQuestion
  getQuestions(): Observable<AnamnesisQuestion[]> {
    return this.http.get<AnamnesisQuestion[]>(`${this.apiUrl}/anamnesis-questions`);
  }

  getQuestion(id: number): Observable<AnamnesisQuestion> {
    return this.http.get<AnamnesisQuestion>(`${this.apiUrl}/anamnesis-questions/${id}`);
  }

  createQuestion(question: Partial<AnamnesisQuestion>): Observable<AnamnesisQuestion> {
    return this.http.post<AnamnesisQuestion>(`${this.apiUrl}/anamnesis-questions`, question);
  }

  updateQuestion(id: number, question: Partial<AnamnesisQuestion>): Observable<AnamnesisQuestion> {
    return this.http.patch<AnamnesisQuestion>(`${this.apiUrl}/anamnesis-questions/${id}`, question);
  }

  deleteQuestion(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/anamnesis-questions/${id}`);
  }

  // Métodos para Anamnesis
  getPatientAnamnesis(patientId: number): Observable<Anamnesis[]> {
    return this.http.get<Anamnesis[]>(`${this.apiUrl}/anamnesis/patients/${patientId}`);
  }

  getAnamnesis(id: string): Observable<Anamnesis> {
    return this.http.get<Anamnesis>(`${this.apiUrl}/anamnesis/${id}`);
  }

  createAnamnesis(anamnesis: {
    patientId: number;
    employeeId?: string;
    answers: { questionId: number; answer: string }[];
  }): Observable<Anamnesis> {
    return this.http.post<Anamnesis>(`${this.apiUrl}/anamnesis`, anamnesis);
  }

  updateAnamnesis(
    id: string,
    anamnesis: Partial<{
      patientId: number;
      employeeId?: string;
      answers: { questionId: number; answer: string }[];
    }>,
  ): Observable<Anamnesis> {
    return this.http.patch<Anamnesis>(`${this.apiUrl}/anamnesis/${id}`, anamnesis);
  }

  deleteAnamnesis(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/anamnesis/${id}`);
  }
}
