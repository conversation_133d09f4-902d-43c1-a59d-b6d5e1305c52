import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { TreatmentFlowStep } from '../models/treatment-flow-step.model';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class TreatmentFlowService {
  private apiUrl = `${environment.apiUrl}/treatment-flow`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) { }

  getAllFlowSteps(): Observable<TreatmentFlowStep[]> {
    return this.http.get<TreatmentFlowStep[]>(this.apiUrl);
  }

  getFlowStep(id: number): Observable<TreatmentFlowStep> {
    return this.http.get<TreatmentFlowStep>(`${this.apiUrl}/${id}`);
  }

  createFlowStep(flowStep: Partial<TreatmentFlowStep>): Observable<TreatmentFlowStep> {
    return this.http.post<TreatmentFlowStep>(this.apiUrl, flowStep);
  }

  updateFlowStep(id: number, flowStep: Partial<TreatmentFlowStep>): Observable<TreatmentFlowStep> {
    return this.http.patch<TreatmentFlowStep>(`${this.apiUrl}/${id}`, flowStep);
  }

  deleteFlowStep(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
