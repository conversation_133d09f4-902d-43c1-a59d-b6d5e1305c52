import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { PatientPhoto, PhotoFolder } from '../models/photo.model';

@Injectable({
  providedIn: 'root',
})
export class PhotoService {
  private apiUrl = `${environment.apiUrl}/photos`;
  private folderApiUrl = `${environment.apiUrl}/photo-folders`;

  constructor(private http: HttpClient) {}

  // Métodos para fotos
  uploadPhoto(
    patientId: number,
    file: File,
    folderId?: string,
    caption?: string,
    order?: number
  ): Observable<PatientPhoto> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('patientId', patientId.toString());

    if (folderId) {
      formData.append('folderId', folderId);
    }
    if (caption) {
      formData.append('caption', caption);
    }
    if (order !== undefined) {
      formData.append('order', order.toString());
    }

    return this.http.post<PatientPhoto>(`${this.apiUrl}/upload`, formData);
  }

  uploadMultiplePhotos(
    patientId: number,
    files: File[],
    folderId?: string,
    photosInfo?: Array<{ caption?: string; order?: number }>
  ): Observable<PatientPhoto[]> {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    formData.append('patientId', patientId.toString());

    if (folderId) {
      formData.append('folderId', folderId);
    }
    if (photosInfo) {
      formData.append('photosInfo', JSON.stringify(photosInfo));
    }

    return this.http.post<PatientPhoto[]>(`${this.apiUrl}/upload-multiple`, formData);
  }

  capturePhoto(
    patientId: number,
    imageBase64: string,
    folderId?: string,
    caption?: string,
    order?: number
  ): Observable<PatientPhoto> {
    return this.http.post<PatientPhoto>(`${this.apiUrl}/camera`, {
      patientId,
      imageBase64,
      folderId,
      caption,
      order,
    });
  }

  captureMultiplePhotos(
    patientId: number,
    photos: Array<{
      imageBase64: string;
      caption?: string;
      order?: number;
    }>,
    folderId?: string
  ): Observable<PatientPhoto[]> {
    return this.http.post<PatientPhoto[]>(`${this.apiUrl}/camera-multiple`, {
      patientId,
      folderId,
      photos,
    });
  }

  getPhotosByPatient(
    patientId: number
  ): Observable<{ folders: PhotoFolder[]; unfiled: PatientPhoto[] }> {
    return this.http.get<{ folders: PhotoFolder[]; unfiled: PatientPhoto[] }>(
      `${this.apiUrl}/patients/${patientId}`
    );
  }

  getPhotosByFolder(folderId: string): Observable<PatientPhoto[]> {
    return this.http.get<PatientPhoto[]>(
      `${this.folderApiUrl}/${folderId}/photos`
    );
  }

  getPhoto(photoId: string): Observable<PatientPhoto> {
    return this.http.get<PatientPhoto>(`${this.apiUrl}/${photoId}`);
  }

  movePhoto(photoId: string, folderId: string | null): Observable<PatientPhoto> {
    return this.http.patch<PatientPhoto>(`${this.apiUrl}/${photoId}/move`, {
      folderId,
    });
  }

  updatePhoto(
    photoId: string,
    updates: { caption?: string; order?: number }
  ): Observable<PatientPhoto> {
    return this.http.patch<PatientPhoto>(`${this.apiUrl}/${photoId}`, updates);
  }

  reorderPhotos(
    photos: Array<{ photoId: string; order: number }>
  ): Observable<PatientPhoto[]> {
    return this.http.post<PatientPhoto[]>(`${this.apiUrl}/reorder`, { photos });
  }

  deletePhoto(photoId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${photoId}`);
  }

  // Métodos para pastas
  createFolder(
    patientId: number,
    name: string
  ): Observable<PhotoFolder> {
    return this.http.post<PhotoFolder>(this.folderApiUrl, {
      patientId,
      name,
    });
  }

  getFoldersByPatient(patientId: number): Observable<PhotoFolder[]> {
    return this.http.get<PhotoFolder[]>(
      `${this.folderApiUrl}/patients/${patientId}`
    );
  }

  getFolder(folderId: string): Observable<PhotoFolder> {
    return this.http.get<PhotoFolder>(`${this.folderApiUrl}/${folderId}`);
  }

  updateFolder(
    folderId: string,
    name: string
  ): Observable<PhotoFolder> {
    return this.http.patch<PhotoFolder>(`${this.folderApiUrl}/${folderId}`, {
      name,
    });
  }

  deleteFolder(folderId: string): Observable<void> {
    return this.http.delete<void>(`${this.folderApiUrl}/${folderId}`);
  }
}
