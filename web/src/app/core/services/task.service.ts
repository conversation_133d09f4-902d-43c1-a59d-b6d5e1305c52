import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Task, TaskStatus, TaskPriority, Sector } from '../models/task.model';
import { environment } from '../../../environments/environment';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root',
})
export class TaskService {
  private apiUrl = `${environment.apiUrl}/tasks`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) {}

  getTasks(
    status?: TaskStatus,
    priority?: TaskPriority,
    employeeId?: string,
    search?: string,
    tags?: string,
    sector?: Sector
  ): Observable<Task[]> {
    let params = new HttpParams();

    if (status) {
      params = params.set('status', status);
    }

    if (priority) {
      params = params.set('priority', priority);
    }

    if (employeeId) {
      params = params.set('employeeId', employeeId);
    }

    if (search) {
      params = params.set('search', search);
    }

    if (tags) {
      params = params.set('tags', tags);
    }

    if (sector) {
      params = params.set('sector', sector);
    }

    return this.http.get<Task[]>(this.apiUrl, { params }).pipe(
      map((tasks) =>
        tasks.map((task) => ({
          ...task,
          // Manter dueDate como string para evitar problemas de fuso horário
          completedAt: task.completedAt
            ? new Date(task.completedAt)
            : undefined,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
        }))
      )
    );
  }

  getTask(id: string): Observable<Task> {
    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(
      map((task) => ({
        ...task,
        // Manter dueDate como string para evitar problemas de fuso horário
        completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt),
      }))
    );
  }

  createTask(
    task: Omit<Task, 'id' | 'createdAt' | 'updatedAt' | 'completedAt'>
  ): Observable<Task> {
    // Criar uma cópia para não modificar o objeto original
    const taskCopy = { ...task };

    // Remover a propriedade dueDate da cópia para evitar problemas de tipagem
    if ('dueDate' in taskCopy) {
      delete taskCopy.dueDate;
    }

    // Criar um objeto para enviar à API
    const taskData: any = { ...taskCopy };

    // Formatar a data corretamente
    if ('dueDate' in task) {
      if (task.dueDate) {
        // Se já for uma string no formato YYYY-MM-DD, manter como está
        if (
          typeof task.dueDate === 'string' &&
          task.dueDate.match(/^\d{4}-\d{2}-\d{2}$/)
        ) {
          taskData.dueDate = task.dueDate;
          console.log(
            'Data enviada para API (string YYYY-MM-DD):',
            taskData.dueDate
          );
        }
        // Se for uma string ISO ou um objeto Date, converter para YYYY-MM-DD
        else {
          const dateObj = new Date(task.dueDate);
          if (!isNaN(dateObj.getTime())) {
            // Formatar a data como YYYY-MM-DD (sem hora, sem fuso)
            taskData.dueDate = dateObj.toISOString().split('T')[0];
            console.log('Data enviada para API (formatada):', taskData.dueDate);
          } else {
            console.error('Data inválida:', task.dueDate);
          }
        }
      } else {
        // Se dueDate for null ou undefined, enviar null explicitamente
        taskData.dueDate = null;
        console.log('Data enviada para API (null)');
      }
    }

    return this.http.post<Task>(this.apiUrl, taskData).pipe(
      map((newTask) => {
        this.notificationService.success('Tarefa criada com sucesso!');
        return {
          ...newTask,
          // Manter dueDate como string para evitar problemas de fuso horário
          completedAt: newTask.completedAt
            ? new Date(newTask.completedAt)
            : undefined,
          createdAt: new Date(newTask.createdAt),
          updatedAt: new Date(newTask.updatedAt),
        };
      })
    );
  }

  updateTask(id: string, task: Partial<Task>): Observable<Task> {
    // Criar uma cópia para não modificar o objeto original
    const taskCopy = { ...task };

    // Remover a propriedade dueDate da cópia para evitar problemas de tipagem
    if ('dueDate' in taskCopy) {
      delete taskCopy.dueDate;
    }

    // Criar um objeto para enviar à API
    const taskData: any = { ...taskCopy };

    // Verificar se dueDate está presente no objeto task original
    if ('dueDate' in task) {
      if (task.dueDate) {
        // Se já for uma string no formato YYYY-MM-DD, manter como está
        if (
          typeof task.dueDate === 'string' &&
          task.dueDate.match(/^\d{4}-\d{2}-\d{2}$/)
        ) {
          taskData.dueDate = task.dueDate;
          console.log(
            'Data enviada para API (update/string YYYY-MM-DD):',
            taskData.dueDate
          );
        }
        // Se for uma string ISO ou um objeto Date, converter para YYYY-MM-DD
        else {
          const dateObj = new Date(task.dueDate);
          if (!isNaN(dateObj.getTime())) {
            // Formatar a data como YYYY-MM-DD (sem hora, sem fuso)
            taskData.dueDate = dateObj.toISOString().split('T')[0];
            console.log(
              'Data enviada para API (update/formatada):',
              taskData.dueDate
            );
          } else {
            console.error('Data inválida:', task.dueDate);
          }
        }
      } else {
        // Se dueDate for null ou undefined, enviar null explicitamente
        taskData.dueDate = null;
        console.log('Data enviada para API (update/null)');
      }
    }

    return this.http.patch<Task>(`${this.apiUrl}/${id}`, taskData).pipe(
      map((updatedTask) => {
        this.notificationService.success('Tarefa atualizada com sucesso!');
        return {
          ...updatedTask,
          // Manter dueDate como string para evitar problemas de fuso horário
          completedAt: updatedTask.completedAt
            ? new Date(updatedTask.completedAt)
            : undefined,
          createdAt: new Date(updatedTask.createdAt),
          updatedAt: new Date(updatedTask.updatedAt),
        };
      })
    );
  }

  deleteTask(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      map(() => {
        this.notificationService.success('Tarefa removida com sucesso!');
      })
    );
  }
}
