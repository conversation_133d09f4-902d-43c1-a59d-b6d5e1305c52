import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface UserNotification {
  id: string;
  title: string;
  message: string;
  type: string;
  read: boolean;
  createdAt: Date;
  task?: any;
}

@Injectable({
  providedIn: 'root',
})
export class UserNotificationService {
  private apiUrl = `${environment.apiUrl}/notifications`;

  private notificationsSubject = new BehaviorSubject<UserNotification[]>([]);
  public notifications$ = this.notificationsSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Obter notificações do funcionário
  getNotifications(
    unreadOnly: boolean = false
  ): Observable<UserNotification[]> {
    const url = `${this.apiUrl}`;

    const request = this.http.get<UserNotification[]>(url);

    // Atualizar o subject quando obtivermos novas notificações
    request.subscribe({
      next: (notifications) => this.notificationsSubject.next(notifications),
      error: (error) => console.error('Erro ao buscar notificações:', error),
    });

    return request;
  }

  // Marcar uma notificação como lida
  markAsRead(notificationId: string): Observable<{ count: number }> {
    return this.http.patch<{ count: number }>(
      `${this.apiUrl}/${notificationId}/read`,
      {}
    );
  }

  // Marcar todas as notificações como lidas
  markAllAsRead(): Observable<{ count: number }> {
    return this.http.patch<{ count: number }>(`${this.apiUrl}/read-all`, {});
  }
}
