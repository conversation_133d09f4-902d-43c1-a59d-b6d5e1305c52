import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface FinancialReport {
  // Tratamentos
  totalTreatments: number;
  completedTreatments: number;
  inProgressTreatments: number;
  scheduledTreatments: number;
  cancelledTreatments: number;
  treatmentRevenue: number;
  treatmentPendingRevenue: number;
  treatmentReceivedRevenue: number;

  // Agendamentos
  totalSchedulings: number;
  completedSchedulings: number;
  confirmedSchedulings: number;
  scheduledSchedulings: number;
  cancelledSchedulings: number;
  schedulingRevenue: number;
  schedulingPendingRevenue: number;
  schedulingReceivedRevenue: number;

  // Total
  totalRevenue: number;
  totalPendingRevenue: number;
  totalReceivedRevenue: number;
}

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  private apiUrl = `${environment.apiUrl}/reports`;

  constructor(private http: HttpClient) { }

  getFinancialReport(
    startDate?: string,
    endDate?: string,
    patientId?: number,
    status?: string,
    paymentStatus?: string,
    type?: string
  ): Observable<FinancialReport> {
    let url = `${this.apiUrl}/financial`;

    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    if (patientId) params.patientId = patientId;
    if (status) params.status = status;
    if (paymentStatus) params.paymentStatus = paymentStatus;
    if (type) params.type = type;

    // Adicionar parâmetros à URL
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      queryParams.append(key, params[key]);
    });

    if (Object.keys(params).length > 0) {
      url += `?${queryParams.toString()}`;
    }

    return this.http.get<FinancialReport>(url);
  }
}
