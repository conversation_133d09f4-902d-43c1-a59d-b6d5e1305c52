import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, firstValueFrom, map, tap, of, throwError, switchMap } from 'rxjs';
import { catchError, retry } from 'rxjs/operators';
import { MedicalRecord } from '../models/medical-record.model';
import { CompleteMedicalRecord } from '../models/complete-medical-record.model';
import { environment } from '../../../environments/environment';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { PatientService } from './patient.service';

@Injectable({
  providedIn: 'root',
})
export class MedicalRecordService {
  private apiUrl = `${environment.apiUrl}/medical-records`;

  constructor(
    private http: HttpClient,
    private patientService: PatientService
  ) {}

  getMedicalRecords(): Observable<MedicalRecord[]> {
    return this.http.get<any[]>(this.apiUrl).pipe(
      map((records) =>
        records.map((record) => ({
          ...record,
          patientName: record.patient?.name,
          patientCpf: record.patient?.cpf,
          patientPhone: record.patient?.phone,
        }))
      )
    );
  }

  getMedicalRecord(id: number): Observable<MedicalRecord> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map((record) => ({
        ...record,
        patientName: record.patient?.name,
        patientCpf: record.patient?.cpf,
        patientPhone: record.patient?.phone,
      }))
    );
  }

  getMedicalRecordsByPatient(patientId: number): Observable<MedicalRecord[]> {
    return this.http.get<any>(`${this.apiUrl}/patient/${patientId}`).pipe(
      map((response) => {
        // Verificar se a resposta é um array ou um objeto único
        const records = Array.isArray(response) ? response : [response];
        return records.map((record) => ({
          ...record,
          patientName: record.patient?.name,
          patientCpf: record.patient?.cpf,
          patientPhone: record.patient?.phone,
        }));
      }),
      catchError(error => {
        console.error('Erro ao obter prontuários do paciente:', error);
        return of([]);
      })
    );
  }

  async getExamFileUrl(filePath: string): Promise<string> {
    const response = await firstValueFrom(
      this.http.get<{ url: string }>(
        `${this.apiUrl}/exam-file/${encodeURIComponent(filePath)}`
      )
    );
    return response?.url ?? '';
  }

  createMedicalRecord(formData: FormData): Observable<MedicalRecord> {
    return this.http.post<MedicalRecord>(this.apiUrl, formData);
  }

  updateMedicalRecord(
    id: number,
    formData: FormData
  ): Observable<MedicalRecord> {
    return this.http.patch<MedicalRecord>(`${this.apiUrl}/${id}`, formData);
  }

  deleteMedicalRecord(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  /**
   * Obtém o prontuário completo de um paciente
   * @param patientId ID do paciente
   * @returns Observable com o prontuário completo
   */
  getCompleteMedicalRecord(patientId: number): Observable<CompleteMedicalRecord> {
    return this.http.get<CompleteMedicalRecord>(`${this.apiUrl}/complete/${patientId}`);
  }

  /**
   * Obtém o prontuário completo de um paciente através do endpoint de pacientes
   * @param patientId ID do paciente
   * @returns Observable com o prontuário completo
   */
  getPatientMedicalRecord(patientId: number): Observable<CompleteMedicalRecord> {
    return this.http.get<CompleteMedicalRecord>(`${environment.apiUrl}/patients/${patientId}/medical-record`)
      .pipe(
        catchError(error => {
          // Se o erro for 404 (Not Found), significa que o paciente não tem prontuário
          // Nesse caso, não exibimos o erro, apenas retornamos um Observable vazio
          console.log('Paciente não possui prontuário ainda, isso é normal:', error);
          return of(null as any);
        })
      );
  }

  /**
   * Verifica se um paciente já possui prontuário
   * @param patientId ID do paciente
   * @returns Observable com true se o paciente tem prontuário, false caso contrário
   */
  checkPatientHasMedicalRecord(patientId: number): Observable<boolean> {
    return this.getMedicalRecordsByPatient(patientId).pipe(
      map(records => records && records.length > 0),
      catchError(() => of(false))
    );
  }

  /**
   * Cria um prontuário para um paciente e atualiza o número do prontuário no cadastro do paciente
   * @param patientId ID do paciente
   * @returns Observable com o prontuário criado
   */
  createPatientMedicalRecord(patientId: number): Observable<MedicalRecord> {
    // Enviar como JSON em vez de FormData
    return this.http.post<MedicalRecord>(this.apiUrl, { patientId }).pipe(
      // Após criar o prontuário, atualizar o número do prontuário no paciente
      switchMap(medicalRecord => {
        console.log('Prontuário criado:', medicalRecord);

        // Gerar um número de prontuário formatado (MRN + ID com zeros à esquerda)
        const medicalRecordNumber = `MRN${medicalRecord.id.toString().padStart(5, '0')}`;

        // Atualizar o paciente com o número do prontuário
        return this.patientService.updatePatient(patientId, {
          medicalRecordNumber: medicalRecordNumber
        }).pipe(
          // Retornar o prontuário original após a atualização do paciente
          map(() => {
            console.log(`Número do prontuário atualizado para ${medicalRecordNumber}`);
            return medicalRecord;
          }),
          catchError(error => {
            console.error('Erro ao atualizar número do prontuário:', error);
            // Mesmo que falhe a atualização do número, retornamos o prontuário criado
            return of(medicalRecord);
          })
        );
      })
    );
  }

  /**
   * Gera um PDF do prontuário no frontend
   * @param medicalRecord Prontuário completo
   * @returns Promise com o blob do PDF
   */
  async generatePDF(medicalRecord: CompleteMedicalRecord): Promise<Blob> {
    return new Promise<Blob>((resolve, reject) => {
      try {
        console.log('Iniciando geração de PDF para:', medicalRecord.patient.name);
        console.log('Dados do prontuário:', {
          patient: !!medicalRecord.patient,
          anamneses: medicalRecord.anamneses?.length || 0,
          treatmentPlans: medicalRecord.treatmentPlans?.length || 0,
          completedProcedures: medicalRecord.completedProcedures?.length || 0,
          exams: medicalRecord.exams?.length || 0,
          budgets: medicalRecord.budgets?.length || 0
        });

        // Verificar se os dados essenciais estão presentes
        if (!medicalRecord || !medicalRecord.patient) {
          throw new Error('Dados do prontuário ou paciente não encontrados');
        }

        // Verificar se jsPDF está funcionando
        console.log('Testando jsPDF...');
        const doc = new jsPDF();
        console.log('jsPDF criado com sucesso');

        // Verificar se autoTable está disponível
        console.log('Verificando autoTable:', typeof (doc as any).autoTable);

        const pageWidth = doc.internal.pageSize.getWidth();

        // Adicionar cabeçalho
        console.log('Adicionando cabeçalho...');
        try {
          doc.setFontSize(20);
          doc.setTextColor(0, 51, 153);
          doc.text('PRONTUÁRIO ODONTOLÓGICO', pageWidth / 2, 20, { align: 'center' });

          // Adicionar data de geração
          doc.setFontSize(10);
          doc.setTextColor(100, 100, 100);
          const today = new Date().toLocaleDateString('pt-BR');
          doc.text(`Gerado em: ${today}`, pageWidth - 20, 10, { align: 'right' });
          console.log('Cabeçalho adicionado com sucesso');
        } catch (error) {
          console.error('Erro ao adicionar cabeçalho:', error);
          throw error;
        }

        // Dados do paciente
        console.log('Adicionando dados do paciente...');
        try {
          doc.setFontSize(16);
          doc.setTextColor(0, 0, 0);
          doc.text('Dados do Paciente', 14, 30);

        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(`Nome: ${medicalRecord.patient.name || 'N/A'}`, 14, 40);
        doc.text(`CPF: ${medicalRecord.patient.cpf || 'N/A'}`, 14, 45);

        // Validar data de nascimento
        let birthDateText = 'N/A';
        if (medicalRecord.patient.birthDate) {
          try {
            birthDateText = new Date(medicalRecord.patient.birthDate).toLocaleDateString('pt-BR');
          } catch (e) {
            console.warn('Erro ao formatar data de nascimento:', e);
            birthDateText = 'Data inválida';
          }
        }
          doc.text(`Data de Nascimento: ${birthDateText}`, 14, 50);

          doc.text(`Telefone: ${medicalRecord.patient.phone || 'N/A'}`, 14, 55);
          doc.text(`Email: ${medicalRecord.patient.email || 'N/A'}`, 14, 60);
          console.log('Dados do paciente adicionados com sucesso');
        } catch (error) {
          console.error('Erro ao adicionar dados do paciente:', error);
          throw error;
        }

        let yPosition = 70;

        // Anamneses
        console.log('Processando anamneses...');
        if (medicalRecord.anamneses && medicalRecord.anamneses.length > 0) {
          try {
            console.log('Adicionando seção de anamnese');
            yPosition = this.addSectionTitle(doc, 'Anamnese', yPosition);

            const anamnesis = medicalRecord.anamneses[0]; // Pegar a mais recente
            doc.setFontSize(10);

            // Validar data da anamnese
            let anamnesisDateText = 'Data não disponível';
            if (anamnesis.createdAt) {
              try {
                anamnesisDateText = new Date(anamnesis.createdAt).toLocaleDateString('pt-BR');
              } catch (e) {
                console.warn('Erro ao formatar data da anamnese:', e);
              }
            }
            doc.text(`Data: ${anamnesisDateText}`, 14, yPosition);
            yPosition += 5;

            if (anamnesis.answers && anamnesis.answers.length > 0) {
              for (const answer of anamnesis.answers) {
                // Verificar se precisa adicionar nova página
                if (yPosition > 270) {
                  doc.addPage();
                  yPosition = 20;
                }

                doc.setFontSize(10);
                doc.setFont('helvetica', 'bold');

                // Validar texto da pergunta
                const questionText = answer.questionText || 'Pergunta não disponível';
                doc.text(questionText, 14, yPosition);
                yPosition += 5;

                doc.setFont('helvetica', 'normal');

                // Validar resposta
                const answerText = answer.answer || 'Resposta não disponível';
                doc.text(answerText, 20, yPosition);
                yPosition += 10;
              }
            }
          } catch (error) {
            console.warn('Erro ao processar anamneses:', error);
            yPosition += 10; // Pular seção em caso de erro
          }
        }

        // Procedimentos realizados (temporariamente desabilitado para teste)
        console.log('Pulando procedimentos realizados para teste...');
        if (false && medicalRecord.completedProcedures && medicalRecord.completedProcedures.length > 0) {
          try {
            console.log('Adicionando seção de procedimentos realizados');
            // Adicionar nova página se necessário
            if (yPosition > 230) {
              doc.addPage();
              yPosition = 20;
            }

            yPosition = this.addSectionTitle(doc, 'Procedimentos Realizados', yPosition);

            // Criar tabela de procedimentos com validações
            const proceduresTableData = medicalRecord.completedProcedures.map(proc => {
              let executionDateText = '-';
              if (proc.executionDate) {
                try {
                  executionDateText = new Date(proc.executionDate).toLocaleDateString('pt-BR');
                } catch (e) {
                  console.warn('Erro ao formatar data de execução:', e);
                  executionDateText = 'Data inválida';
                }
              }

              return [
                proc.name || 'Procedimento não especificado',
                proc.tooth || '-',
                executionDateText,
                proc.professionalName || '-',
                this.formatCurrency(proc.value)
              ];
            });

            // Verificar se autoTable está disponível
            if (typeof (doc as any).autoTable === 'function') {
              (doc as any).autoTable({
                startY: yPosition,
                head: [['Procedimento', 'Dente', 'Data', 'Profissional', 'Valor']],
                body: proceduresTableData,
                theme: 'grid',
                headStyles: { fillColor: [0, 102, 204], textColor: 255 },
                margin: { top: 10 },
              });

              yPosition = (doc as any).lastAutoTable.finalY + 10;
            } else {
              // Fallback: criar tabela simples sem autoTable
              console.warn('autoTable não disponível, usando fallback');
              doc.setFontSize(10);
              doc.setFont('helvetica', 'bold');
              doc.text('Procedimento | Dente | Data | Profissional | Valor', 14, yPosition);
              yPosition += 5;

              doc.setFont('helvetica', 'normal');
              for (const row of proceduresTableData) {
                if (yPosition > 270) {
                  doc.addPage();
                  yPosition = 20;
                }
                doc.text(row.join(' | '), 14, yPosition);
                yPosition += 5;
              }
              yPosition += 10;
            }
          } catch (error) {
            console.warn('Erro ao processar procedimentos realizados:', error);
            yPosition += 10; // Pular seção em caso de erro
          }
        }

        // Planos de tratamento (temporariamente desabilitado para teste)
        console.log('Pulando planos de tratamento para teste...');
        if (false && medicalRecord.treatmentPlans && medicalRecord.treatmentPlans.length > 0) {
          try {
            console.log('Adicionando seção de planos de tratamento');
            // Adicionar nova página
            doc.addPage();
            yPosition = 20;

            yPosition = this.addSectionTitle(doc, 'Planos de Tratamento', yPosition);

          for (const plan of medicalRecord.treatmentPlans) {
            doc.setFontSize(12);
            doc.setFont('helvetica', 'bold');
            doc.text(`Plano #${plan.id} - ${this.formatCurrency(plan.totalValue)}`, 14, yPosition);
            yPosition += 5;

            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
            doc.text(`Criado em: ${new Date(plan.createdAt).toLocaleDateString('pt-BR')}`, 14, yPosition);
            doc.text(`Status: ${this.getStatusLabel(plan.status)}`, 100, yPosition);
            yPosition += 10;

            if (plan.procedures && plan.procedures.length > 0) {
              const proceduresTableData = plan.procedures.map(proc => [
                proc.name,
                proc.tooth || '-',
                this.getStatusLabel(proc.status),
                this.formatCurrency(proc.value)
              ]);

              (doc as any).autoTable({
                startY: yPosition,
                head: [['Procedimento', 'Dente', 'Status', 'Valor']],
                body: proceduresTableData,
                theme: 'striped',
                headStyles: { fillColor: [0, 102, 204], textColor: 255 },
                margin: { top: 10 },
              });

              yPosition = (doc as any).lastAutoTable.finalY + 15;
            }

            // Verificar se precisa adicionar nova página
            if (yPosition > 250 && medicalRecord.treatmentPlans.indexOf(plan) < medicalRecord.treatmentPlans.length - 1) {
              doc.addPage();
              yPosition = 20;
            }
          }
          } catch (error) {
            console.warn('Erro ao processar planos de tratamento:', error);
            yPosition += 10; // Pular seção em caso de erro
          }
        }

        // Exames (temporariamente desabilitado para teste)
        console.log('Pulando exames para teste...');
        if (false && medicalRecord.exams && medicalRecord.exams.length > 0) {
          try {
            console.log('Adicionando seção de exames');
          // Adicionar nova página se necessário
          if (yPosition > 230) {
            doc.addPage();
            yPosition = 20;
          }

          yPosition = this.addSectionTitle(doc, 'Exames', yPosition);

          const examsTableData = medicalRecord.exams.map(exam => {
            let uploadDateText = 'Data não disponível';
            if (exam.uploadedAt) {
              try {
                uploadDateText = new Date(exam.uploadedAt).toLocaleDateString('pt-BR');
              } catch (e) {
                console.warn('Erro ao formatar data de upload do exame:', e);
              }
            }
            return [
              exam.name || 'Exame não especificado',
              uploadDateText
            ];
          });

          // Verificar se autoTable está disponível
          if (typeof (doc as any).autoTable === 'function') {
            (doc as any).autoTable({
              startY: yPosition,
              head: [['Nome do Exame', 'Data de Upload']],
              body: examsTableData,
              theme: 'grid',
              headStyles: { fillColor: [0, 102, 204], textColor: 255 },
              margin: { top: 10 },
            });

            yPosition = (doc as any).lastAutoTable.finalY + 10;
          } else {
            // Fallback: criar tabela simples sem autoTable
            console.warn('autoTable não disponível para exames, usando fallback');
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            doc.text('Nome do Exame | Data de Upload', 14, yPosition);
            yPosition += 5;

            doc.setFont('helvetica', 'normal');
            for (const row of examsTableData) {
              if (yPosition > 270) {
                doc.addPage();
                yPosition = 20;
              }
              doc.text(row.join(' | '), 14, yPosition);
              yPosition += 5;
            }
            yPosition += 10;
          }
          } catch (error) {
            console.warn('Erro ao processar exames:', error);
            yPosition += 10; // Pular seção em caso de erro
          }
        }

        // Rodapé
        console.log('Adicionando rodapé...');
        try {
          const totalPages = doc.getNumberOfPages();
          for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setTextColor(100, 100, 100);
            doc.text(`Página ${i} de ${totalPages}`, pageWidth / 2, 290, { align: 'center' });
            doc.text(`Prontuário de ${medicalRecord.patient.name} - ${medicalRecord.patient.cpf}`, 14, 290);
          }
          console.log('Rodapé adicionado com sucesso');
        } catch (error) {
          console.warn('Erro ao adicionar rodapé:', error);
        }

        // Gerar blob e resolver a promise
        const pdfBlob = doc.output('blob');
        console.log('PDF gerado com sucesso, tamanho:', pdfBlob.size, 'bytes');
        resolve(pdfBlob);
      } catch (error) {
        console.error('Erro ao gerar PDF completo:', error);

        try {
          // Em caso de erro, gerar um PDF básico com informações mínimas
          console.log('Tentando gerar PDF básico...');
          const basicDoc = new jsPDF();

          // Cabeçalho básico
          basicDoc.setFontSize(20);
          basicDoc.setTextColor(0, 51, 153);
          basicDoc.text('PRONTUÁRIO ODONTOLÓGICO', basicDoc.internal.pageSize.getWidth() / 2, 20, { align: 'center' });

          // Dados básicos do paciente
          basicDoc.setFontSize(16);
          basicDoc.setTextColor(0, 0, 0);
          basicDoc.text('Dados do Paciente', 14, 40);

          basicDoc.setFontSize(12);
          if (medicalRecord?.patient) {
            basicDoc.text(`Nome: ${medicalRecord.patient.name || 'N/A'}`, 14, 50);
            basicDoc.text(`CPF: ${medicalRecord.patient.cpf || 'N/A'}`, 14, 55);
            basicDoc.text(`Telefone: ${medicalRecord.patient.phone || 'N/A'}`, 14, 60);
            basicDoc.text(`Email: ${medicalRecord.patient.email || 'N/A'}`, 14, 65);
          }

          // Mensagem de erro
          basicDoc.setFontSize(10);
          basicDoc.setTextColor(255, 0, 0);
          basicDoc.text('Nota: Ocorreu um erro ao gerar o prontuário completo.', 14, 80);
          basicDoc.text('Este PDF contém apenas as informações básicas do paciente.', 14, 85);

          // Data de geração
          basicDoc.setFontSize(8);
          basicDoc.setTextColor(100, 100, 100);
          const today = new Date().toLocaleDateString('pt-BR');
          basicDoc.text(`Gerado em: ${today}`, 14, 270);

          const basicPdfBlob = basicDoc.output('blob');
          console.log('PDF básico gerado com sucesso, tamanho:', basicPdfBlob.size, 'bytes');
          resolve(basicPdfBlob);
        } catch (basicError) {
          console.error('Erro ao gerar PDF básico:', basicError);
          reject(new Error('Não foi possível gerar o PDF do prontuário'));
        }
      }
    });
  }

  /**
   * Adiciona um título de seção ao PDF
   * @param doc Documento PDF
   * @param title Título da seção
   * @param yPosition Posição Y atual
   * @returns Nova posição Y após adicionar o título
   */
  private addSectionTitle(doc: jsPDF, title: string, yPosition: number): number {
    doc.setFillColor(240, 240, 240);
    doc.rect(10, yPosition - 5, doc.internal.pageSize.getWidth() - 20, 10, 'F');

    doc.setFontSize(14);
    doc.setTextColor(0, 51, 153);
    doc.setFont('helvetica', 'bold');
    doc.text(title, 14, yPosition);

    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0);

    return yPosition + 10;
  }

  /**
   * Formata um valor monetário
   * @param value Valor a ser formatado
   * @returns String formatada
   */
  private formatCurrency(value: number | undefined): string {
    if (value === undefined) return 'N/A';
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  /**
   * Obtém o rótulo de um status
   * @param status Status a ser traduzido
   * @returns Rótulo em português
   */
  private getStatusLabel(status: string): string {
    const statusMap: Record<string, string> = {
      'COMPLETED': 'Concluído',
      'IN_PROGRESS': 'Em Andamento',
      'PENDING': 'Pendente',
      'SCHEDULED': 'Agendado',
      'CANCELLED': 'Cancelado',
      'APPROVED': 'Aprovado',
      'REJECTED': 'Rejeitado'
    };

    return statusMap[status] || status;
  }
}
