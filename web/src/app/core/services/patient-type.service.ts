import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { PatientType } from '../models/patient-type.model';
import { PaginatedResponse } from '../models/pagination.model';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root',
})
export class PatientTypeService {
  private apiUrl = `${environment.apiUrl}/patient-types`;

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService
  ) {}

  getPatientTypes(
    page: number = 1,
    limit: number = 6,
    search?: string,
    ativo?: boolean
  ): Observable<PaginatedResponse<PatientType>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    // Só enviamos o parâmetro ativo se for true
    // Quando for false ou undefined, não enviamos o parâmetro para trazer todos os registros
    if (ativo === true) {
      params = params.set('ativo', 'true');
    }

    return this.http
      .get<PaginatedResponse<PatientType>>(this.apiUrl, { params })
      .pipe(
        tap((response) => {
          console.log(
            `Tipos de paciente carregados: ${response.data.length} de ${response.total}`
          );
          console.log(
            `Página ${response.page} de ${Math.ceil(
              response.total / response.limit
            )}`
          );
        })
      );
  }

  // Método para compatibilidade com código existente que espera um array
  getAllPatientTypes(onlyActive: boolean = false): Observable<PatientType[]> {
    // Usamos o endpoint com parâmetro limit alto para obter todos os tipos
    let params = new HttpParams().set('limit', '100');

    // Se onlyActive for true, adicionamos o filtro
    if (onlyActive) {
      params = params.set('ativo', 'true');
    }

    return this.http
      .get<PaginatedResponse<PatientType>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          // Verificar se a resposta é paginada ou um array direto
          if (Array.isArray(response)) {
            return response;
          } else if ('data' in response) {
            // Se for paginada, retornar apenas o array de dados
            return response.data;
          }
          return [];
        }),
        tap((patientTypes) => {
          console.log(
            `Todos os tipos de paciente carregados: ${patientTypes.length}`
          );
        })
      );
  }

  getPatientType(id: number): Observable<PatientType> {
    return this.http.get<PatientType>(`${this.apiUrl}/${id}`).pipe(
      tap((patientType) => {
        console.log('Tipo de paciente carregado:', patientType.id);
      })
    );
  }

  createPatientType(
    patientType: Omit<PatientType, 'id' | 'createdAt' | 'updatedAt'>
  ): Observable<PatientType> {
    return this.http.post<PatientType>(this.apiUrl, patientType).pipe(
      tap((newPatientType) => {
        this.notificationService.success(
          'Tipo de paciente cadastrado com sucesso!'
        );
        console.log('Tipo de paciente criado:', newPatientType.id);
      })
    );
  }

  updatePatientType(
    id: number,
    patientType: Partial<PatientType>
  ): Observable<PatientType> {
    return this.http
      .patch<PatientType>(`${this.apiUrl}/${id}`, patientType)
      .pipe(
        tap((updatedPatientType) => {
          this.notificationService.success(
            'Tipo de paciente atualizado com sucesso!'
          );
          console.log('Tipo de paciente atualizado:', updatedPatientType.id);
        })
      );
  }

  deletePatientType(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      tap(() => {
        this.notificationService.success(
          'Tipo de paciente removido com sucesso!'
        );
        console.log('Tipo de paciente removido:', id);
      })
    );
  }
}
