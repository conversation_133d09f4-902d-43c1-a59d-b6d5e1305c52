import { Injectable, inject } from '@angular/core';
import {
  HttpRequest,
  HttpHand<PERSON>,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NotificationService } from '../services/notification.service';
import { Router } from '@angular/router';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(
    private notificationService: NotificationService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'Ocorreu um erro desconhecido';

        if (error.error && error.error.message) {
          // Erro com mensagem definida
          errorMessage = `Erro: ${error.error.message}`;
        } else {
          // Erro retornado pelo backend
          if (error.status === 0) {
            errorMessage = 'Não foi possível conectar ao servidor. Verifique sua conexão com a internet.';
          } else if (error.status === 401) {
            errorMessage = 'Você não está autorizado a acessar este recurso. Faça login novamente.';
            const router = inject(Router);
            router.navigate(['/auth/login']);
          } else if (error.status === 403) {
            errorMessage = 'Você não tem permissão para acessar este recurso.';
          } else if (error.status === 404) {
            errorMessage = 'O recurso solicitado não foi encontrado.';
          } else if (error.status === 422 || error.status === 400) {
            // Erros de validação
            if (error.error && error.error.message) {
              if (Array.isArray(error.error.message)) {
                errorMessage = error.error.message.join(', ');
              } else {
                errorMessage = error.error.message;
              }
            } else {
              errorMessage = 'Dados inválidos. Verifique os campos e tente novamente.';
            }
          } else if (error.status >= 500) {
            errorMessage = 'Ocorreu um erro no servidor. Tente novamente mais tarde.';
          }
        }

        // Verificar se é um erro de prontuário não encontrado
        const isProntuarioError =
          (error.status === 404 &&
           (error.error?.message === 'Prontuário não encontrado' ||
            errorMessage.includes('Prontuário não encontrado')));

        // Exibir notificação de erro apenas se não for erro de prontuário
        if (!isProntuarioError) {
          this.notificationService.error(errorMessage);
        } else {
          // Se for erro de prontuário, apenas logar no console
          console.log('Prontuário não encontrado, isso é normal:', error);
        }

        // Retornar o erro para que o componente possa tratá-lo se necessário
        return throwError(() => error);
      })
    );
  }
}
