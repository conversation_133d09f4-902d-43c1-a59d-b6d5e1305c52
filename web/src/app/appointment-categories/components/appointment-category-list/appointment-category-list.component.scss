/* Estilos específicos para a listagem de categorias de agendamento */

/* Animações personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* Estilização da paginação */
.pagination-controls {
  button {
    transition: all 0.2s ease-in-out;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }
}

/* Estilo para tabela responsiva */
.overflow-x-auto {
  transition: opacity 0.3s ease-in-out;
}

/* Estilização do seletor de itens por página */
select {
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #3b82f6;
  }
}

/* Animações suaves para hover */
.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Estilo para botões de ação */
.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Estilo para status badges */
.status-badge {
  transition: all 0.2s ease-in-out;
}

/* Estilo para filtros */
.filter-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Animação para loading */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Estilo para empty state */
.empty-state {
  background: radial-gradient(circle at center, #f8fafc 0%, #ffffff 100%);
}
