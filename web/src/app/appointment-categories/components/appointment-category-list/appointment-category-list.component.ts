import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormsModule,
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { AppointmentCategoriesService } from '../../services/appointment-categories.service';
import {
  AppointmentCategory,
  AppointmentCategoryFilter,
} from '../../models/appointment-category.model';
import { ColorCircleComponent } from '../color-circle/color-circle.component';
import { AppointmentCategoryFormComponent } from '../appointment-category-form/appointment-category-form.component';
import { ConfirmationDialogComponent } from '../../../shared/components/confirmation-dialog/confirmation-dialog.component';
import { ToggleSwitchComponent } from '../../../shared/components/toggle-switch/toggle-switch.component';
import { NotificationService } from '../../../core/services/notification.service';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-appointment-category-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    ColorCircleComponent,
    AppointmentCategoryFormComponent,
    ConfirmationDialogComponent,
    ToggleSwitchComponent,
  ],
  templateUrl: './appointment-category-list.component.html',
  styleUrls: ['./appointment-category-list.component.scss'],
})
export class AppointmentCategoryListComponent implements OnInit {
  categories: AppointmentCategory[] = [];
  filteredCategories: AppointmentCategory[] = [];
  isLoading = false;
  showForm = false;
  selectedCategory: AppointmentCategory | null = null;

  // Modal de confirmação
  showStatusConfirmation = false;
  categoryToToggle: AppointmentCategory | null = null;
  isTogglingStatus = false;

  // Filtros
  filterForm: FormGroup;

  // Paginação
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  totalPages = 0;

  // Ordenação
  sortField: 'name' | 'isActive' | 'createdAt' = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';

  constructor(
    private fb: FormBuilder,
    private appointmentCategoriesService: AppointmentCategoriesService,
    private notificationService: NotificationService
  ) {
    this.filterForm = this.createFilterForm();
  }

  ngOnInit() {
    this.setupFilterSubscription();
    this.loadCategories();
  }

  private createFilterForm(): FormGroup {
    return this.fb.group({
      name: [''],
      isActive: [null], // null = todos, true = ativos, false = inativos
    });
  }

  private setupFilterSubscription() {
    this.filterForm.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => {
        this.currentPage = 1;
        this.loadCategories();
      });
  }

  loadCategories() {
    this.isLoading = true;

    const filters: AppointmentCategoryFilter = {
      page: this.currentPage,
      limit: this.itemsPerPage,
    };

    const formValue = this.filterForm.value;
    if (formValue.name?.trim()) {
      filters.name = formValue.name.trim();
    }
    if (formValue.isActive !== null) {
      filters.isActive = formValue.isActive;
    }

    this.appointmentCategoriesService.getAllCategories(filters).subscribe({
      next: (response) => {
        this.categories = response.data;
        this.totalItems = response.total;
        this.totalPages =
          response.totalPages || Math.ceil(response.total / this.itemsPerPage);
        this.applySorting();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar categorias:', error);
        this.isLoading = false;
      },
    });
  }

  applySorting() {
    this.filteredCategories = [...this.categories].sort((a, b) => {
      let aValue: any = a[this.sortField];
      let bValue: any = b[this.sortField];

      if (this.sortField === 'name') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  onSort(field: 'name' | 'isActive' | 'createdAt') {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.applySorting();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.loadCategories();
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se o total de páginas for menor ou igual ao máximo visível, mostra todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar
      let startPage = Math.max(
        2,
        this.currentPage - Math.floor(maxVisiblePages / 2)
      );
      let endPage = Math.min(
        this.totalPages - 1,
        startPage + maxVisiblePages - 3
      );

      // Ajustar se estiver próximo do início
      if (startPage === 2) {
        endPage = Math.min(this.totalPages - 1, maxVisiblePages - 1);
      }

      // Ajustar se estiver próximo do fim
      if (endPage === this.totalPages - 1) {
        startPage = Math.max(2, this.totalPages - maxVisiblePages + 2);
      }

      // Adicionar elipses antes do intervalo se necessário
      if (startPage > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Adicionar elipses se necessário
      if (endPage < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadCategories();
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadCategories();
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }

  openCreateForm() {
    this.selectedCategory = null;
    this.showForm = true;
  }

  openEditForm(category: AppointmentCategory) {
    this.selectedCategory = category;
    this.showForm = true;
  }

  onFormClose() {
    this.showForm = false;
    this.selectedCategory = null;
  }

  onFormSave(category: AppointmentCategory) {
    this.loadCategories(); // Recarrega a lista

    // Determina se é criação ou edição baseado no selectedCategory
    const isEdit = this.selectedCategory !== null;
    const action = isEdit ? 'atualizada' : 'criada';

    this.notificationService.success(
      `Categoria "${category.name}" ${action} com sucesso!`
    );
  }

  onToggleStatus(category: AppointmentCategory) {
    this.categoryToToggle = category;
    this.showStatusConfirmation = true;
  }

  confirmToggleStatus() {
    if (this.categoryToToggle && !this.isTogglingStatus) {
      this.isTogglingStatus = true;
      const categoryName = this.categoryToToggle.name;
      const newStatus = !this.categoryToToggle.isActive;
      const action = newStatus ? 'ativar' : 'inativar';

      this.appointmentCategoriesService
        .toggleCategoryStatus(this.categoryToToggle.id, newStatus)
        .subscribe({
          next: () => {
            this.loadCategories();
            this.closeStatusConfirmation();
            this.notificationService.success(
              `Categoria "${categoryName}" ${
                newStatus ? 'ativada' : 'inativada'
              } com sucesso!`
            );
          },
          error: (error) => {
            console.error(`Erro ao ${action} categoria:`, error);
            this.isTogglingStatus = false;
            this.notificationService.error(
              `Erro ao ${action} a categoria "${categoryName}". Tente novamente.`
            );
          },
        });
    }
  }

  closeStatusConfirmation() {
    this.showStatusConfirmation = false;
    this.categoryToToggle = null;
    this.isTogglingStatus = false;
  }

  clearFilters() {
    this.filterForm.reset({
      name: '',
      isActive: null,
    });
  }

  getEndItem(): number {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
  }

  get statusConfirmationMessage(): string {
    if (this.categoryToToggle) {
      const action = this.categoryToToggle.isActive ? 'inativar' : 'ativar';
      const status = this.categoryToToggle.isActive ? 'inativa' : 'ativa';
      return `Tem certeza que deseja ${action} a categoria "${this.categoryToToggle.name}"? Ela ficará ${status} no sistema.`;
    }
    return 'Tem certeza que deseja alterar o status desta categoria?';
  }

  get statusConfirmationTitle(): string {
    if (this.categoryToToggle) {
      return this.categoryToToggle.isActive
        ? 'Inativar Categoria'
        : 'Ativar Categoria';
    }
    return 'Alterar Status';
  }

  get statusConfirmationButtonText(): string {
    if (this.categoryToToggle) {
      return this.categoryToToggle.isActive ? 'Inativar' : 'Ativar';
    }
    return 'Confirmar';
  }

  get statusConfirmationLoadingText(): string {
    if (this.categoryToToggle) {
      return this.categoryToToggle.isActive ? 'Inativando...' : 'Ativando...';
    }
    return 'Processando...';
  }
}
