import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-color-circle',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="inline-flex items-center justify-center w-6 h-6 rounded-full border-2 border-gray-200 shadow-sm"
      [style.background-color]="color"
      [title]="title || color"
    >
      <span class="sr-only">{{ title || color }}</span>
    </div>
  `
})
export class ColorCircleComponent {
  @Input() color: string = '#000000';
  @Input() title?: string;
}
