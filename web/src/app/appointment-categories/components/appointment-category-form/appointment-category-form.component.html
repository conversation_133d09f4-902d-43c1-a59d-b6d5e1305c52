<!-- <PERSON><PERSON> de Formulário -->
<app-modal
  [title]="title"
  [isOpen]="isVisible"
  (close)="onClose()"
  [showDefaultFooter]="true"
  [fullscreen]="isMobile"
>
  <!-- <PERSON><PERSON><PERSON><PERSON> do <PERSON> -->
  <div
    class="flex-1 flex flex-col overflow-visible w-full min-w-[440px] max-w-[520px] mx-auto"
  >
    <div class="flex-1 overflow-y-auto">
      <form
        id="category-form"
        [formGroup]="form"
        (ngSubmit)="onSubmit()"
        class="p-4 sm:p-6 md:p-8 lg:p-10 space-y-8 w-full"
      >
        <!-- Nome -->
        <div class="w-full">
          <label
            for="name"
            class="block text-sm font-medium text-gray-700 mb-2"
          >
            Nome da Categoria *
          </label>
          <input
            id="name"
            type="text"
            formControlName="name"
            placeholder="Ex: Avaliação, Retorno, Limpeza, Emergência..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1.5 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            [class.border-red-500]="
              form.get('name')?.invalid && form.get('name')?.touched
            "
          />
          <div
            *ngIf="form.get('name')?.invalid && form.get('name')?.touched"
            class="mt-2 text-sm text-red-600"
          >
            <span *ngIf="form.get('name')?.errors?.['required']"
              >Nome é obrigatório</span
            >
            <span *ngIf="form.get('name')?.errors?.['maxlength']"
              >Nome deve ter no máximo 100 caracteres</span
            >
          </div>
        </div>

        <!-- Cor -->
        <div>
          <app-color-select
            [colors]="availableColors"
            [disabled]="isLoadingColors"
            formControlName="color"
            label="Cor da Categoria"
            placeholder="Selecione uma cor para identificar a categoria"
            [required]="true"
          ></app-color-select>

          <!-- Loading das cores -->
          <div
            *ngIf="isLoadingColors"
            class="flex items-center justify-center py-6"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
            ></div>
            <span class="ml-3 text-base text-gray-600"
              >Carregando cores disponíveis...</span
            >
          </div>
        </div>

        <!-- Descrição -->
        <div>
          <label
            for="description"
            class="block text-sm font-medium text-gray-700 mb-3"
          >
            Descrição
            <span class="text-gray-500 font-normal">(opcional)</span>
          </label>
          <textarea
            id="description"
            formControlName="description"
            rows="4"
            placeholder="Descreva o propósito desta categoria de agendamento..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1.5 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
          ></textarea>
        </div>

        <!-- Espaçamento adicional para melhor distribuição -->
        <div class="py-4"></div>
      </form>
    </div>
  </div>

  <!-- Footer do Modal -->
  <div
    footer
    class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3"
  >
    <button
      type="button"
      (click)="onClose()"
      class="w-full sm:w-auto px-6 py-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1.5 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      Cancelar
    </button>
    <button
      type="submit"
      form="category-form"
      [disabled]="form.invalid || isLoading"
      class="w-full sm:w-auto px-6 py-3 text-base font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-1.5 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
    >
      <span *ngIf="isLoading" class="flex items-center">
        <div
          class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
        ></div>
        Salvando...
      </span>
      <span *ngIf="!isLoading">
        {{ isEditMode ? "Atualizar" : "Criar" }}
      </span>
    </button>
  </div>
</app-modal>
