import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { AppointmentCategoriesService } from '../../services/appointment-categories.service';
import {
  AppointmentCategory,
  AppColor,
} from '../../models/appointment-category.model';
import { ModalComponent } from '../../../shared/components/modal/modal.component';
import {
  ColorSelectComponent,
  ColorOption,
} from '../../../shared/components/color-select/color-select.component';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-appointment-category-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent,
    ColorSelectComponent,
  ],
  templateUrl: './appointment-category-form.component.html',
  styleUrls: ['./appointment-category-form.component.scss'],
})
export class AppointmentCategoryFormComponent implements OnInit {
  @Input() category: AppointmentCategory | null = null;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<AppointmentCategory>();

  form: FormGroup;
  availableColors: ColorOption[] = [];
  isLoading = false;
  isLoadingColors = false;
  isMobile = false;

  constructor(
    private fb: FormBuilder,
    private appointmentCategoriesService: AppointmentCategoriesService,
    private notificationService: NotificationService
  ) {
    this.form = this.createForm();
  }

  ngOnInit() {
    this.checkScreenSize();
    this.loadAvailableColors();
    if (this.category) {
      this.populateForm();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isMobile = window.innerWidth < 768; // Tailwind md breakpoint
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: [''],
      color: [
        '',
        [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)],
      ],
    });
  }

  private populateForm() {
    if (this.category) {
      this.form.patchValue({
        name: this.category.name,
        description: this.category.description || '',
        color: this.category.color,
      });
    }
  }

  private loadAvailableColors() {
    this.isLoadingColors = true;
    this.appointmentCategoriesService.getAvailableColors().subscribe({
      next: (colors) => {
        // Converter AppColor para ColorOption
        this.availableColors = colors.map((color, index) => ({
          id: index + 1, // Usar índice como ID
          name: color.name,
          hex: color.hex,
        }));
        this.isLoadingColors = false;
      },
      error: (error) => {
        console.error('Erro ao carregar cores:', error);
        this.isLoadingColors = false;
        this.notificationService.error(
          'Erro ao carregar as cores disponíveis. Tente recarregar a página.'
        );
      },
    });
  }

  onSubmit() {
    if (this.form.valid && !this.isLoading) {
      this.isLoading = true;
      const formData = { ...this.form.value };

      // Para criação, sempre definir isActive como true
      if (!this.category) {
        formData.isActive = true;
      }

      const request = this.category
        ? this.appointmentCategoriesService.updateCategory(
            this.category.id,
            formData
          )
        : this.appointmentCategoriesService.createCategory(formData);

      request.subscribe({
        next: (result) => {
          this.save.emit(result);
          this.onClose();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erro ao salvar categoria:', error);
          this.isLoading = false;

          // Determina a mensagem de erro baseada no tipo de operação
          const action = this.isEditMode ? 'atualizar' : 'criar';
          let errorMessage = `Erro ao ${action} a categoria. Tente novamente.`;

          // Verifica se é erro de nome duplicado
          if (
            error.status === 409 ||
            error.error?.message?.includes('já existe')
          ) {
            errorMessage =
              'Já existe uma categoria com este nome. Escolha um nome diferente.';
          }

          this.notificationService.error(errorMessage);
        },
      });
    }
  }

  onClose() {
    this.form.reset();
    this.category = null;
    this.close.emit();
  }

  get isEditMode(): boolean {
    return !!this.category;
  }

  get title(): string {
    return this.isEditMode ? 'Editar Categoria' : 'Nova Categoria';
  }
}
