export interface AppointmentCategory {
  id: number;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAppointmentCategoryDto {
  name: string;
  description?: string;
  color: string;
  isActive?: boolean;
}

export interface UpdateAppointmentCategoryDto {
  name?: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

export interface AppointmentCategoryFilter {
  name?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface AppointmentCategoryResponse {
  data: AppointmentCategory[];
  total: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

export interface AppColor {
  name: string;
  hex: string;
}
