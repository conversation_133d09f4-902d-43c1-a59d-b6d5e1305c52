import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  AppointmentCategory,
  CreateAppointmentCategoryDto,
  UpdateAppointmentCategoryDto,
  AppointmentCategoryFilter,
  AppointmentCategoryResponse,
  AppColor,
} from '../models/appointment-category.model';

@Injectable({
  providedIn: 'root',
})
export class AppointmentCategoriesService {
  private readonly apiUrl = `${environment.apiUrl}/appointment-categories`;
  private readonly colorsUrl = `${environment.apiUrl}/colors`;

  constructor(private http: HttpClient) {}

  /**
   * Lista todas as categorias de agendamento com filtros opcionais
   */
  getAllCategories(
    filters?: AppointmentCategoryFilter
  ): Observable<AppointmentCategoryResponse> {
    let params = new HttpParams();

    if (filters) {
      if (filters.name) {
        params = params.set('name', filters.name);
      }
      if (filters.isActive !== undefined) {
        params = params.set('isActive', filters.isActive.toString());
      }
      if (filters.page) {
        params = params.set('page', filters.page.toString());
      }
      if (filters.limit) {
        params = params.set('limit', filters.limit.toString());
      }
    }

    return this.http.get<AppointmentCategoryResponse>(this.apiUrl, { params });
  }

  /**
   * Busca uma categoria específica por ID
   */
  getCategory(id: number): Observable<AppointmentCategory> {
    return this.http.get<AppointmentCategory>(`${this.apiUrl}/${id}`);
  }

  /**
   * Cria uma nova categoria de agendamento
   */
  createCategory(
    data: CreateAppointmentCategoryDto
  ): Observable<AppointmentCategory> {
    return this.http.post<AppointmentCategory>(this.apiUrl, data);
  }

  /**
   * Atualiza uma categoria de agendamento existente
   */
  updateCategory(
    id: number,
    data: UpdateAppointmentCategoryDto
  ): Observable<AppointmentCategory> {
    return this.http.patch<AppointmentCategory>(`${this.apiUrl}/${id}`, data);
  }

  /**
   * Remove uma categoria de agendamento
   */
  deleteCategory(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  /**
   * Alterna o status ativo/inativo de uma categoria de agendamento
   */
  toggleCategoryStatus(
    id: number,
    isActive: boolean
  ): Observable<AppointmentCategory> {
    return this.http.patch<AppointmentCategory>(`${this.apiUrl}/${id}`, {
      isActive,
    });
  }

  /**
   * Lista todas as cores disponíveis no sistema
   */
  getAvailableColors(): Observable<AppColor[]> {
    return this.http.get<AppColor[]>(this.colorsUrl);
  }
}
