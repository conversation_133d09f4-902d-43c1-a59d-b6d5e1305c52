import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

import { AppointmentCategoriesRoutingModule } from './appointment-categories-routing.module';
import { AppointmentCategoryListComponent } from './components/appointment-category-list/appointment-category-list.component';
import { AppointmentCategoryFormComponent } from './components/appointment-category-form/appointment-category-form.component';
import { ColorCircleComponent } from './components/color-circle/color-circle.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HttpClientModule,
    AppointmentCategoriesRoutingModule,
    // Componentes standalone
    AppointmentCategoryListComponent,
    AppointmentCategoryFormComponent,
    ColorCircleComponent
  ],
  providers: []
})
export class AppointmentCategoriesModule { }
