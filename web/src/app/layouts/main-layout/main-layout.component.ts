import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from '../../shared/header/header.component';
import { SidebarComponent } from '../../shared/sidebar/sidebar.component';
import { SidebarService } from '../../core/services/sidebar.service';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],
  template: `
    <div class="flex h-screen bg-gray-50 overflow-hidden">
      <!-- Overlay para fechar o sidebar em dispositivos móveis -->
      <div
        *ngIf="isSidebarOpen && !isLargeScreen"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ease-in-out"
        (click)="closeSidebar()"
      ></div>

      <!-- Sidebar - Fixa na lateral esquerda -->
      <div class="fixed inset-y-0 left-0 z-30 transition-transform duration-300 ease-in-out transform"
           [ngClass]="{'translate-x-0': isSidebarOpen, '-translate-x-full': !isSidebarOpen, 'lg:translate-x-0': isLargeScreen}">
        <app-sidebar></app-sidebar>
      </div>

      <!-- Área principal - Com margem para a sidebar -->
      <div class="flex flex-col flex-1 w-full transition-all duration-300 ease-in-out"
           [ngClass]="{
             'lg:ml-64': (isSidebarOpen || isLargeScreen) && !isSidebarCollapsed,
             'lg:ml-20': isLargeScreen && isSidebarCollapsed
           }">
        <!-- Header - Fixo no topo -->
        <header class="sticky top-0 z-20 bg-white shadow-sm">
          <app-header></app-header>
        </header>

        <!-- Conteúdo principal - Com scroll único -->
        <main class="flex-1 overflow-y-auto overflow-x-hidden p-4 md:p-6 bg-gray-50">
          <div class="mx-auto pb-16"
               [ngClass]="{
                 'max-w-7xl': !isSidebarCollapsed || !isLargeScreen,
                 'max-w-none px-4': isSidebarCollapsed && isLargeScreen
               }">
            <router-outlet></router-outlet>
          </div>
        </main>
      </div>
    </div>
  `,
})
export class MainLayoutComponent implements OnInit {
  isSidebarOpen = true;
  isSidebarCollapsed = false;
  isLargeScreen = false;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit(): void {
    this.sidebarService.isOpen$.subscribe((isOpen) => {
      this.isSidebarOpen = isOpen;
    });

    this.sidebarService.isCollapsed$.subscribe((isCollapsed) => {
      this.isSidebarCollapsed = isCollapsed;
    });

    // Verificar o tamanho da tela
    this.checkScreenSize();
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  private checkScreenSize(): void {
    this.isLargeScreen = window.innerWidth >= 1024; // lg breakpoint do Tailwind
  }

  closeSidebar(): void {
    this.sidebarService.close();
  }
}
