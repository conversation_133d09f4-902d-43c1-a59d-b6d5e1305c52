import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { SidebarComponent } from '../../shared/sidebar/sidebar.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { SidebarService } from '../../core/services/sidebar.service';
import { MobileNavComponent } from '../../shared/components/mobile-nav/mobile-nav.component';
// import { BreadcrumbsComponent } from '../../shared/components/breadcrumbs/breadcrumbs.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-pro-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    SidebarComponent,
    HeaderComponent,
    MobileNavComponent
  ],
  template: `
    <div class="flex h-screen overflow-hidden bg-gray-100">
      <!-- Desktop Sidebar -->
      <aside class="hidden lg:block bg-white shadow-md z-30 h-screen overflow-hidden transition-all duration-300 ease-in-out"
             [ngClass]="{
               'w-72': !isSidebarCollapsed,
               'w-20': isSidebarCollapsed
             }">
        <app-sidebar></app-sidebar>
      </aside>

      <!-- Mobile Header -->
      <div class="lg:hidden bg-white px-4 h-14 fixed w-full flex items-center top-0 z-20 shadow-md">
        <app-mobile-nav></app-mobile-nav>
      </div>

      <!-- Mobile Sidebar Overlay -->
      <div
        *ngIf="isSidebarOpen && !isLargeScreen"
        class="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden transition-opacity duration-300 ease-in-out"
        (click)="closeSidebar()"
      ></div>

      <!-- Mobile Sidebar -->
      <div
        class="fixed inset-y-0 left-0 z-30 transition-transform duration-300 ease-in-out transform lg:hidden bg-white w-[280px]"
        [ngClass]="{'translate-x-0': isSidebarOpen, '-translate-x-full': !isSidebarOpen}"
      >
        <app-sidebar></app-sidebar>
      </div>

      <!-- Main Content Area -->
      <div class="flex flex-col flex-1 w-full h-screen overflow-hidden">
        <!-- Desktop Header -->
        <header class="hidden lg:block sticky top-0 z-20 bg-white shadow-sm">
          <app-header></app-header>
        </header>

        <!-- Content Container -->
        <main class="flex-1 overflow-y-auto pt-14 lg:pt-0">
          <div class="flex flex-col py-4 md:py-7 mx-auto"
               [ngClass]="{
                 'px-4 md:px-7 max-w-[1980px]': !isSidebarCollapsed || !isLargeScreen,
                 'px-6 md:px-8 max-w-none': isSidebarCollapsed && isLargeScreen
               }">
            <!-- <app-breadcrumbs class="mb-4"></app-breadcrumbs> -->
            <router-outlet></router-outlet>
          </div>
        </main>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  `]
})
export class ProLayoutComponent implements OnInit, OnDestroy {
  isSidebarOpen = true;
  isSidebarCollapsed = false;
  isLargeScreen = false;
  private subscription: Subscription = new Subscription();

  constructor(private sidebarService: SidebarService) {}

  ngOnInit(): void {
    this.subscription.add(
      this.sidebarService.isOpen$.subscribe((isOpen) => {
        this.isSidebarOpen = isOpen;
      })
    );

    this.subscription.add(
      this.sidebarService.isCollapsed$.subscribe((isCollapsed) => {
        this.isSidebarCollapsed = isCollapsed;
      })
    );

    // Check screen size
    this.checkScreenSize();
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    window.removeEventListener('resize', () => this.checkScreenSize());
  }

  private checkScreenSize(): void {
    this.isLargeScreen = window.innerWidth >= 1024; // lg breakpoint

    // Auto-open sidebar on large screens
    if (this.isLargeScreen && !this.isSidebarOpen) {
      this.sidebarService.open();
    }
  }

  closeSidebar(): void {
    this.sidebarService.close();
  }
}
