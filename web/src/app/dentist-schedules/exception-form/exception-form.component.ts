import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DentistScheduleService } from '../../core/services/dentist-schedule.service';

@Component({
  selector: 'app-exception-form',
  templateUrl: './exception-form.component.html',
  styleUrls: ['./exception-form.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class ExceptionFormComponent implements OnInit {
  @Input() dentistId!: number;
  @Input() dentistName!: string;
  @Input() isOpen = false;
  @Output() closeModal = new EventEmitter<void>();
  @Output() exceptionCreated = new EventEmitter<void>();

  exceptionForm!: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  exceptionTypes = [
    { value: 'day-off', label: 'Folga' },
    { value: 'custom-hours', label: 'Hor<PERSON>rio Personalizado' }
  ];

  constructor(
    private fb: FormBuilder,
    private dentistScheduleService: DentistScheduleService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (this.isOpen) {
      this.resetForm();
    }
  }

  initializeForm(): void {
    this.exceptionForm = this.fb.group({
      date: ['', [Validators.required]],
      type: ['day-off', [Validators.required]],
      reason: [''],
      customHours: this.fb.array([])
    });

    // Observar mudanças no tipo para mostrar/esconder horários personalizados
    this.exceptionForm.get('type')?.valueChanges.subscribe(type => {
      const customHoursArray = this.getCustomHoursArray();
      if (type === 'custom-hours') {
        if (customHoursArray.length === 0) {
          this.addCustomHour();
        }
      } else {
        customHoursArray.clear();
      }
    });
  }

  getCustomHoursArray(): FormArray {
    return this.exceptionForm.get('customHours') as FormArray;
  }

  createCustomHourGroup(): FormGroup {
    return this.fb.group({
      start: ['', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]],
      end: ['', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]]
    });
  }

  addCustomHour(): void {
    const customHoursArray = this.getCustomHoursArray();
    customHoursArray.push(this.createCustomHourGroup());
  }

  removeCustomHour(index: number): void {
    const customHoursArray = this.getCustomHoursArray();
    customHoursArray.removeAt(index);
  }

  onSubmit(): void {
    if (this.exceptionForm.invalid) {
      this.markFormGroupTouched(this.exceptionForm);
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    const formValue = this.exceptionForm.value;
    const exceptionData: any = {
      date: formValue.date,
      type: formValue.type,
      reason: formValue.reason || undefined
    };

    if (formValue.type === 'custom-hours' && formValue.customHours.length > 0) {
      exceptionData.customHours = formValue.customHours.filter((hour: any) => 
        hour.start && hour.end
      );
    }

    this.dentistScheduleService.createException(this.dentistId, exceptionData)
      .subscribe({
        next: () => {
          this.exceptionCreated.emit();
          this.close();
        },
        error: (err) => {
          console.error('Erro ao criar exceção:', err);
          this.error = err.error?.message || 'Erro ao criar exceção. Tente novamente.';
          this.isSubmitting = false;
        }
      });
  }

  close(): void {
    this.resetForm();
    this.closeModal.emit();
  }

  resetForm(): void {
    this.exceptionForm.reset({
      date: '',
      type: 'day-off',
      reason: ''
    });
    this.getCustomHoursArray().clear();
    this.error = null;
    this.isSubmitting = false;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Getter para facilitar o acesso no template
  get isCustomHours(): boolean {
    return this.exceptionForm.get('type')?.value === 'custom-hours';
  }

  get customHoursControls() {
    return this.getCustomHoursArray().controls;
  }
}
