/* Modal animations */
.modal-backdrop {
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form validation styles */
.ng-invalid.ng-touched {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ng-valid.ng-touched {
  border-color: #10b981;
}

/* Custom hour row animation */
.custom-hour-row {
  animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-content {
    width: 95%;
    margin: 1rem auto;
    top: 1rem;
  }
  
  .flex.items-center.space-x-2 {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .flex.items-center.space-x-2 > span {
    display: none;
  }
}
