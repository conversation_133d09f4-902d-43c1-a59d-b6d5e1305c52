<!-- <PERSON><PERSON> Backdrop -->
<div
  *ngIf="isOpen"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
  (click)="close()"
>
  <!-- Modal Content -->
  <div
    class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 xl:w-1/3 shadow-lg rounded-md bg-white"
    (click)="$event.stopPropagation()"
  >
    <!-- Modal Header -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900">
        Adicionar Exceção - {{ dentistName }}
      </h3>
      <button
        type="button"
        (click)="close()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Error Message -->
    <div
      *ngIf="error"
      class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4"
    >
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-red-400 mr-2"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="text-red-700 text-sm">{{ error }}</span>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="exceptionForm" (ngSubmit)="onSubmit()" class="space-y-4">
      <!-- Data -->
      <div>
        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">
          Data *
        </label>
        <input
          type="date"
          id="date"
          formControlName="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          [class.border-red-500]="exceptionForm.get('date')?.invalid && exceptionForm.get('date')?.touched"
        >
        <div
          *ngIf="exceptionForm.get('date')?.invalid && exceptionForm.get('date')?.touched"
          class="text-red-500 text-xs mt-1"
        >
          Data é obrigatória
        </div>
      </div>

      <!-- Tipo -->
      <div>
        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
          Tipo *
        </label>
        <select
          id="type"
          formControlName="type"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option *ngFor="let type of exceptionTypes" [value]="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>

      <!-- Horários Personalizados (apenas se tipo for custom-hours) -->
      <div *ngIf="isCustomHours">
        <div class="flex items-center justify-between mb-2">
          <label class="block text-sm font-medium text-gray-700">
            Horários Personalizados *
          </label>
          <button
            type="button"
            (click)="addCustomHour()"
            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Adicionar
          </button>
        </div>

        <div formArrayName="customHours" class="space-y-2">
          <div
            *ngFor="let hour of customHoursControls; let i = index"
            [formGroupName]="i"
            class="flex items-center space-x-2"
          >
            <div class="flex-1">
              <input
                type="time"
                formControlName="start"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Início"
              >
            </div>
            <span class="text-gray-500 text-sm">até</span>
            <div class="flex-1">
              <input
                type="time"
                formControlName="end"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Fim"
              >
            </div>
            <button
              type="button"
              (click)="removeCustomHour(i)"
              class="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          </div>
        </div>

        <div
          *ngIf="customHoursControls.length === 0"
          class="text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg"
        >
          <p class="text-sm">Nenhum horário configurado</p>
          <p class="text-xs">Clique em "Adicionar" para definir horários</p>
        </div>
      </div>

      <!-- Motivo/Observação -->
      <div>
        <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">
          Motivo/Observação
        </label>
        <textarea
          id="reason"
          formControlName="reason"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Descreva o motivo da exceção (opcional)"
        ></textarea>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          (click)="close()"
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          [disabled]="isSubmitting"
        >
          Cancelar
        </button>
        <button
          type="submit"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
          [disabled]="isSubmitting || exceptionForm.invalid"
        >
          <svg
            *ngIf="isSubmitting"
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {{ isSubmitting ? 'Salvando...' : 'Salvar Exceção' }}
        </button>
      </div>
    </form>
  </div>
</div>
