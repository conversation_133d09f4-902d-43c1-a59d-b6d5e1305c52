import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ScheduleOverviewComponent } from './schedule-overview/schedule-overview.component';
import { ScheduleEditComponent } from './schedule-edit/schedule-edit.component';

const routes: Routes = [
  {
    path: '',
    component: ScheduleOverviewComponent
  },
  {
    path: 'edit/:dentistId',
    component: ScheduleEditComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DentistSchedulesRoutingModule { }
