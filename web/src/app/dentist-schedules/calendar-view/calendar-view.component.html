<!-- Calendar Header -->
<div class="bg-white rounded-lg shadow p-6 mb-6">
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center space-x-4">
      <h2 class="text-2xl font-bold text-gray-900">{{ getMonthYear() }}</h2>
      <div class="flex items-center space-x-2">
        <button
          (click)="previousMonth()"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          [disabled]="isLoading"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          (click)="nextMonth()"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          [disabled]="isLoading"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
    
    <div class="flex items-center space-x-3">
      <button
        (click)="goToToday()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        [disabled]="isLoading"
      >
        Hoje
      </button>
    </div>
  </div>

  <!-- Legend -->
  <div class="flex flex-wrap items-center gap-4 text-sm mb-6">
    <div class="flex items-center">
      <div class="w-4 h-4 bg-blue-100 border border-blue-200 rounded mr-2"></div>
      <span class="text-gray-600">Hoje</span>
    </div>
    <div class="flex items-center">
      <div class="w-4 h-4 bg-red-100 border border-red-200 rounded mr-2"></div>
      <span class="text-gray-600">Feriado</span>
    </div>
    <div class="flex items-center">
      <div class="w-4 h-4 bg-gray-100 border border-gray-200 rounded mr-2"></div>
      <span class="text-gray-600">Domingo</span>
    </div>
    <div class="flex items-center">
      <div class="w-4 h-4 bg-yellow-50 border border-yellow-200 rounded mr-2"></div>
      <span class="text-gray-600">Poucos disponíveis</span>
    </div>
    <div class="flex items-center">
      <div class="w-4 h-4 bg-red-50 border border-red-200 rounded mr-2"></div>
      <span class="text-gray-600">Nenhum disponível</span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="flex items-center space-x-3">
      <svg class="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="text-gray-600">Carregando calendário...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
    <div class="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
      <span class="text-red-700">{{ error }}</span>
    </div>
  </div>

  <!-- Calendar Grid -->
  <div *ngIf="!isLoading && !error" class="calendar-grid">
    <!-- Day Headers -->
    <div class="grid grid-cols-7 gap-1 mb-2">
      <div
        *ngFor="let dayName of dayNames"
        class="text-center text-sm font-medium text-gray-500 py-2"
      >
        {{ dayName }}
      </div>
    </div>

    <!-- Calendar Days -->
    <div class="grid grid-cols-7 gap-1">
      <div
        *ngFor="let day of calendarDays"
        class="min-h-[120px] border border-gray-200 rounded-lg p-2 transition-colors"
        [class]="getDayStatusClass(day) + (day.isCurrentMonth && !day.isHoliday ? ' hover:bg-gray-50 cursor-pointer' : ' cursor-default')"
        (click)="openDayDetail(day)"
        [title]="getTooltipText(day)"
      >
        <!-- Day Number and Status -->
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium">{{ day.dayNumber }}</span>
          <div class="flex items-center space-x-1">
            <!-- Holiday Indicator -->
            <span
              *ngIf="day.isHoliday"
              class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
              [title]="day.holiday?.name"
            >
              🎉
            </span>
            <!-- Weekend Indicator -->
            <span
              *ngIf="day.isWeekend && !day.isHoliday"
              class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
            >
              📅
            </span>
          </div>
        </div>

        <!-- Holiday Name -->
        <div *ngIf="day.isHoliday" class="text-xs text-red-600 font-medium mb-2 truncate">
          {{ day.holiday?.name }}
        </div>

        <!-- Dentist Availability Summary -->
        <div *ngIf="!day.isHoliday && day.isCurrentMonth" class="space-y-1">
          <!-- Available Count -->
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">Disponíveis:</span>
            <span class="font-medium" [class]="getAvailableDentistsCount(day) === 0 ? 'text-red-600' : 'text-green-600'">
              {{ getAvailableDentistsCount(day) }}/{{ getTotalDentistsCount() }}
            </span>
          </div>

          <!-- Dentist List (first few) -->
          <div class="space-y-1">
            <div
              *ngFor="let schedule of day.dentistSchedules.slice(0, 3); let i = index"
              class="flex items-center justify-between text-xs"
            >
              <span class="truncate flex-1 mr-1 text-gray-500" [title]="schedule.dentist.name || 'N/A'">
                {{ (schedule.dentist.name || 'N/A')}}
              </span>
              <span
                class="inline-flex items-center px-1 py-0.5 rounded text-xs"
                [class]="schedule.isAvailable ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'"
              >
                {{ schedule.isAvailable ? '✓' : '✗' }}
              </span>
            </div>
            
            <!-- More indicator -->
            <div *ngIf="day.dentistSchedules.length > 3" class="text-xs text-gray-500 text-center">
              +{{ day.dentistSchedules.length - 3 }} mais
            </div>
          </div>
        </div>

        <!-- Weekend/Holiday Message -->
        <div *ngIf="day.isWeekend && !day.isHoliday && day.isCurrentMonth" class="text-xs text-gray-500 text-center mt-4">
          Clínica fechada
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Day Detail Modal -->
<app-day-detail-modal
  [isOpen]="showDayDetailModal"
  [selectedDay]="selectedDay"
  (closeModal)="closeDayDetailModal()"
></app-day-detail-modal>
