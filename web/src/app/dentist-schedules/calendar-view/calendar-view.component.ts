import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DayDetailModalComponent } from '../day-detail-modal/day-detail-modal.component';
import { DentistScheduleService } from '../../core/services/dentist-schedule.service';
import { HolidayService } from '../../core/services/holiday.service';
import { DentistService } from '../../core/services/dentist.service';
import { DentistScheduleOverview, DentistException } from '../../core/models/dentist-schedule.model';
import { Holiday } from '../../core/models/holiday.model';
import { Dentist } from '../../core/models/dentist.model';
import { finalize } from 'rxjs/operators';

interface CalendarDay {
  date: Date;
  dayNumber: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isWeekend: boolean;
  isHoliday: boolean;
  holiday?: Holiday;
  dentistSchedules: DentistDaySchedule[];
}

interface DentistDaySchedule {
  dentist: Dentist;
  hasRegularSchedule: boolean;
  hasException: boolean;
  exceptionType?: 'day-off' | 'custom-hours';
  timeSlots: { start: string; end: string }[];
  isAvailable: boolean;
}

@Component({
  selector: 'app-calendar-view',
  templateUrl: './calendar-view.component.html',
  styleUrls: ['./calendar-view.component.css'],
  standalone: true,
  imports: [CommonModule, DayDetailModalComponent]
})
export class CalendarViewComponent implements OnInit {
  currentDate = new Date();
  calendarDays: CalendarDay[] = [];
  dentists: Dentist[] = [];
  holidays: Holiday[] = [];
  scheduleOverview: DentistScheduleOverview[] = [];
  exceptions: DentistException[] = [];
  
  isLoading = true;
  error: string | null = null;

  // Modal de detalhes do dia
  showDayDetailModal = false;
  selectedDay: CalendarDay | null = null;

  // Navegação
  currentMonth: number;
  currentYear: number;
  monthNames = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  
  dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  workingDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

  constructor(
    private dentistScheduleService: DentistScheduleService,
    private holidayService: HolidayService,
    private dentistService: DentistService
  ) {
    this.currentMonth = this.currentDate.getMonth();
    this.currentYear = this.currentDate.getFullYear();
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;
    this.error = null;

    // Carregar dentistas e overview primeiro, depois exceções
    Promise.all([
      this.loadDentists(),
      this.loadScheduleOverview(),
      this.loadHolidays()
    ]).then(() => {
      // Agora carregar exceções com a lista de dentistas disponível
      return this.loadExceptions();
    }).then(() => {
      this.generateCalendar();
      this.isLoading = false;
    }).catch(err => {
      console.error('Erro ao carregar dados:', err);
      this.error = 'Erro ao carregar dados do calendário';
      this.isLoading = false;
    });
  }

  private loadDentists(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.dentistService.getAllDentists().subscribe({
        next: (dentists) => {
          this.dentists = dentists;
          resolve();
        },
        error: reject
      });
    });
  }

  private loadScheduleOverview(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.dentistScheduleService.getScheduleOverview().subscribe({
        next: (schedules) => {
          this.scheduleOverview = schedules;
          resolve();
        },
        error: reject
      });
    });
  }

  private loadHolidays(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.holidayService.getHolidays(this.currentYear).subscribe({
        next: (holidays) => {
          this.holidays = holidays;
          resolve();
        },
        error: reject
      });
    });
  }

  private loadExceptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Carregar exceções para o mês atual e próximo
      const startDate = new Date(this.currentYear, this.currentMonth, 1);
      const endDate = new Date(this.currentYear, this.currentMonth + 2, 0); // Fim do próximo mês

      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Carregar exceções de todos os dentistas para o período
      const exceptionPromises = this.dentists.map(dentist =>
        this.dentistScheduleService.getExceptions(dentist.id, startDateStr, endDateStr).toPromise()
      );

      Promise.all(exceptionPromises).then(results => {
        this.exceptions = results.flat().filter(Boolean) as DentistException[];
        resolve();
      }).catch(reject);
    });
  }

  generateCalendar(): void {
    const firstDay = new Date(this.currentYear, this.currentMonth, 1);
    const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
    const startDate = new Date(firstDay);
    
    // Ajustar para começar no domingo da semana
    startDate.setDate(startDate.getDate() - startDate.getDay());
    
    const endDate = new Date(lastDay);
    // Ajustar para terminar no sábado da semana
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    this.calendarDays = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const calendarDay = this.createCalendarDay(new Date(currentDate));
      this.calendarDays.push(calendarDay);
      currentDate.setDate(currentDate.getDate() + 1);
    }
  }

  private createCalendarDay(date: Date): CalendarDay {
    const today = new Date();
    const isCurrentMonth = date.getMonth() === this.currentMonth;
    const isToday = date.toDateString() === today.toDateString();
    const isWeekend = date.getDay() === 0; // Domingo
    
    // Verificar se é feriado
    const dateString = date.toISOString().split('T')[0];
    const holiday = this.holidays.find(h => h.date === dateString);
    const isHoliday = !!holiday;

    // Gerar escalas dos dentistas para este dia
    const dentistSchedules = this.generateDentistSchedulesForDay(date);

    return {
      date,
      dayNumber: date.getDate(),
      isCurrentMonth,
      isToday,
      isWeekend,
      isHoliday,
      holiday,
      dentistSchedules
    };
  }

  private generateDentistSchedulesForDay(date: Date): DentistDaySchedule[] {
    const dayOfWeek = this.getDayOfWeekKey(date);
    const dateString = date.toISOString().split('T')[0];

    // Usar todos os dentistas, não apenas os que estão no scheduleOverview
    return this.dentists.map(dentist => {
      // Encontrar a escala deste dentista no overview
      const schedule = this.scheduleOverview.find(s => s.dentistId === dentist.id);

      // Verificar se tem escala regular para este dia
      const regularSchedule = schedule?.weeklySchedule?.[dayOfWeek as keyof typeof schedule.weeklySchedule] || [];
      const hasRegularSchedule = regularSchedule.length > 0;

      // Verificar se há exceção para esta data específica
      const exception = this.exceptions.find(ex =>
        ex.dentistId === dentist.id && ex.date === dateString
      );

      let timeSlots: { start: string; end: string }[] = [];
      let isAvailable = false;

      if (exception) {
        // Há exceção para este dia
        if (exception.type === 'day-off') {
          // Folga - não disponível
          isAvailable = false;
          timeSlots = [];
        } else if (exception.type === 'custom-hours' && exception.customHours) {
          // Horário personalizado
          isAvailable = true;
          timeSlots = exception.customHours;
        }
      } else {
        // Sem exceção - usar escala regular
        isAvailable = hasRegularSchedule;
        timeSlots = hasRegularSchedule ? regularSchedule : [];
      }

      return {
        dentist,
        hasRegularSchedule,
        hasException: !!exception,
        exceptionType: exception?.type,
        timeSlots,
        isAvailable
      };
    });
  }

  private getDayOfWeekKey(date: Date): string {
    const dayIndex = date.getDay();
    const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return dayKeys[dayIndex];
  }

  // Navegação do calendário
  previousMonth(): void {
    if (this.currentMonth === 0) {
      this.currentMonth = 11;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
    this.loadData();
  }

  nextMonth(): void {
    if (this.currentMonth === 11) {
      this.currentMonth = 0;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
    this.loadData();
  }

  goToToday(): void {
    const today = new Date();
    this.currentMonth = today.getMonth();
    this.currentYear = today.getFullYear();
    this.loadData();
  }

  // Métodos utilitários
  getMonthYear(): string {
    return `${this.monthNames[this.currentMonth]} ${this.currentYear}`;
  }

  getAvailableDentistsCount(day: CalendarDay): number {
    return day.dentistSchedules.filter(ds => ds.isAvailable).length;
  }

  getTotalDentistsCount(): number {
    return this.dentists.length;
  }

  getDayStatusClass(day: CalendarDay): string {
    if (!day.isCurrentMonth) return 'text-gray-300';
    if (day.isHoliday) return 'bg-red-100 text-red-800';
    if (day.isWeekend) return 'bg-gray-100 text-gray-600';
    if (day.isToday) return 'bg-blue-100 text-blue-800 font-semibold';

    const availableCount = this.getAvailableDentistsCount(day);
    const totalCount = this.getTotalDentistsCount();

    if (availableCount === 0) return 'bg-red-50 text-red-600';
    if (availableCount < totalCount / 2) return 'bg-yellow-50 text-yellow-600';
    return 'text-gray-900';
  }

  // Métodos do modal de detalhes
  openDayDetail(day: CalendarDay): void {
    // Não abrir modal para feriados ou dias fora do mês atual
    if (day.isCurrentMonth && !day.isHoliday) {
      this.selectedDay = day;
      this.showDayDetailModal = true;
    }
  }

  closeDayDetailModal(): void {
    this.showDayDetailModal = false;
    this.selectedDay = null;
  }

  getTooltipText(day: CalendarDay): string {
    if (!day.isCurrentMonth) {
      return '';
    }

    if (day.isHoliday) {
      return `${day.holiday?.name} - Clínica fechada`;
    }

    if (day.isWeekend) {
      return 'Domingo - Clínica fechada';
    }

    return 'Clique para ver detalhes dos dentistas';
  }
}
