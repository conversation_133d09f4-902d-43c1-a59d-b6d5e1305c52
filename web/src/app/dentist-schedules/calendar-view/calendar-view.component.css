/* Calendar Grid Styling */
.calendar-grid {
  font-family: 'Inter', sans-serif;
}

/* Day Cell Hover Effects */
.calendar-grid > div > div:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Day Cell Transitions */
.calendar-grid > div > div {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

/* Today Highlight */
.bg-blue-100 {
  background-color: #dbeafe !important;
  border-color: #3b82f6 !important;
}

/* Holiday Styling */
.bg-red-100 {
  background-color: #fee2e2 !important;
  border-color: #ef4444 !important;
}

/* Weekend Styling */
.bg-gray-100 {
  background-color: #f3f4f6 !important;
}

/* Availability Status Colors */
.text-green-600 {
  color: #059669 !important;
}

.text-red-600 {
  color: #dc2626 !important;
}

.text-yellow-600 {
  color: #d97706 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calendar-grid > div > div {
    min-height: 80px;
    padding: 0.5rem;
  }
  
  .calendar-grid .text-xs {
    font-size: 0.65rem;
  }
  
  /* Hide some details on mobile */
  .calendar-grid .space-y-1 > div:nth-child(n+3) {
    display: none;
  }
}

@media (max-width: 640px) {
  .calendar-grid > div > div {
    min-height: 60px;
    padding: 0.25rem;
  }
  
  /* Show only essential info on small screens */
  .calendar-grid .space-y-1 > div:nth-child(n+2) {
    display: none;
  }
}

/* Loading Animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Calendar Navigation Buttons */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: inherit !important;
  color: inherit !important;
}

/* Legend Styling */
.legend-item {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.legend-color {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
  margin-right: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* Day Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Dentist Availability Bars */
.availability-bar {
  height: 4px;
  border-radius: 2px;
  margin-top: 0.25rem;
}

.availability-high {
  background-color: #10b981;
}

.availability-medium {
  background-color: #f59e0b;
}

.availability-low {
  background-color: #ef4444;
}

.availability-none {
  background-color: #6b7280;
}

/* Smooth Transitions */
* {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Focus States for Accessibility */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Calendar Cell Focus */
.calendar-grid > div > div:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* Print Styles */
@media print {
  .calendar-grid {
    break-inside: avoid;
  }
  
  .calendar-grid > div > div {
    break-inside: avoid;
    min-height: 100px;
  }
  
  /* Hide interactive elements when printing */
  button {
    display: none;
  }
}
