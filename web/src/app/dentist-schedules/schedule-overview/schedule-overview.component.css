/* Animações personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
  .grid-cols-9 {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .col-span-2 {
    width: 100%;
  }
  
  .schedule-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    width: 100%;
  }
}

/* Melhorias visuais para os slots de horário */
.time-slot {
  transition: all 0.2s ease;
}

.time-slot:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
