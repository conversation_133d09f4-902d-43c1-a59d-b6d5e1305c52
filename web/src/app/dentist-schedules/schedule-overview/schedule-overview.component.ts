import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { DentistScheduleService } from '../../core/services/dentist-schedule.service';
import { HolidayService } from '../../core/services/holiday.service';
import { NotificationService } from '../../core/services/notification.service';
import { DentistScheduleOverview } from '../../core/models/dentist-schedule.model';
import { Holiday, CreateHolidayDto } from '../../core/models/holiday.model';
import { ExceptionFormComponent } from '../exception-form/exception-form.component';
import { ExceptionManagerComponent } from '../exception-manager/exception-manager.component';
import { ScheduleEditModalComponent } from '../schedule-edit-modal/schedule-edit-modal.component';
import { CalendarViewComponent } from '../calendar-view/calendar-view.component';

@Component({
  selector: 'app-schedule-overview',
  templateUrl: './schedule-overview.component.html',
  styleUrls: ['./schedule-overview.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ExceptionFormComponent, ExceptionManagerComponent, ScheduleEditModalComponent, CalendarViewComponent]
})
export class ScheduleOverviewComponent implements OnInit {
  schedules: DentistScheduleOverview[] = [];
  holidays: Holiday[] = [];
  isLoading = true;
  error: string | null = null;

  // Modal de exceções
  showExceptionModal = false;
  showExceptionManagerModal = false;
  selectedDentistId: number | null = null;
  selectedDentistName: string = '';

  // Modal de edição de escala
  showScheduleEditModal = false;
  selectedDentistForEdit: DentistScheduleOverview | null = null;

  // Modal de feriados
  showHolidayModal = false;
  holidayForm!: FormGroup;
  isSubmittingHoliday = false;

  // Visualização
  currentView: 'list' | 'calendar' = 'list';

  // Dias da semana (segunda a sábado)
  workingDays = [
    { key: 'monday', name: 'Segunda-feira', short: 'Seg' },
    { key: 'tuesday', name: 'Terça-feira', short: 'Ter' },
    { key: 'wednesday', name: 'Quarta-feira', short: 'Qua' },
    { key: 'thursday', name: 'Quinta-feira', short: 'Qui' },
    { key: 'friday', name: 'Sexta-feira', short: 'Sex' },
    { key: 'saturday', name: 'Sábado', short: 'Sáb' }
  ];

  constructor(
    private dentistScheduleService: DentistScheduleService,
    private holidayService: HolidayService,
    private notificationService: NotificationService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.initializeHolidayForm();
  }

  ngOnInit(): void {
    this.loadScheduleOverview();
    this.loadHolidays();
  }

  initializeHolidayForm(): void {
    this.holidayForm = this.fb.group({
      name: ['', [Validators.required]],
      date: ['', [Validators.required]],
      description: ['']
    });
  }

  loadScheduleOverview(): void {
    this.isLoading = true;
    this.error = null;

    this.dentistScheduleService.getScheduleOverview()
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (schedules) => {
          this.schedules = schedules;
        },
        error: (err) => {
          console.error('Erro ao carregar escalas:', err);
          this.error = 'Não foi possível carregar as escalas dos dentistas. Tente novamente mais tarde.';
        }
      });
  }

  loadHolidays(): void {
    const currentYear = new Date().getFullYear();
    this.holidayService.getHolidays(currentYear).subscribe({
      next: (holidays) => {
        this.holidays = holidays;
      },
      error: (err) => {
        console.error('Erro ao carregar feriados:', err);
      }
    });
  }

  // Métodos para modal de edição de escala
  openScheduleEditModal(schedule: DentistScheduleOverview): void {
    this.selectedDentistForEdit = schedule;
    this.showScheduleEditModal = true;
  }

  closeScheduleEditModal(): void {
    this.showScheduleEditModal = false;
    this.selectedDentistForEdit = null;
  }

  onScheduleUpdated(): void {
    this.loadScheduleOverview();
    this.closeScheduleEditModal();
  }

  // Métodos para feriados
  openHolidayModal(): void {
    this.showHolidayModal = true;
    this.holidayForm.reset();
  }

  closeHolidayModal(): void {
    this.showHolidayModal = false;
    this.holidayForm.reset();
  }

  onSubmitHoliday(): void {
    if (this.holidayForm.invalid) {
      return;
    }

    this.isSubmittingHoliday = true;
    const holidayData: CreateHolidayDto = this.holidayForm.value;

    this.holidayService.createHoliday(holidayData)
      .pipe(
        finalize(() => {
          this.isSubmittingHoliday = false;
        })
      )
      .subscribe({
        next: (holiday) => {
          this.notificationService.success(
            `Feriado "${holiday.name}" adicionado com sucesso!`
          );
          this.loadHolidays();
          this.closeHolidayModal();
        },
        error: (err) => {
          console.error('Erro ao criar feriado:', err);
          this.notificationService.error(
            err.error?.message || 'Erro ao criar feriado. Tente novamente.'
          );
        }
      });
  }

  deleteHoliday(holiday: Holiday): void {
    if (confirm(`Tem certeza que deseja remover o feriado "${holiday.name}"?`)) {
      this.holidayService.deleteHoliday(holiday.id).subscribe({
        next: () => {
          this.notificationService.success(
            `Feriado "${holiday.name}" removido com sucesso!`
          );
          this.loadHolidays();
        },
        error: (err) => {
          console.error('Erro ao remover feriado:', err);
          this.notificationService.error(
            'Erro ao remover feriado. Tente novamente.'
          );
        }
      });
    }
  }

  // Métodos utilitários
  formatTimeSlots(timeSlots: { start: string; end: string }[]): string {
    return this.dentistScheduleService.formatTimeSlots(timeSlots);
  }

  getScheduleForDay(schedule: DentistScheduleOverview, day: string): { start: string; end: string }[] {
    if (!schedule.weeklySchedule || !schedule.weeklySchedule[day as keyof typeof schedule.weeklySchedule]) {
      return [];
    }
    return schedule.weeklySchedule[day as keyof typeof schedule.weeklySchedule] || [];
  }

  hasAnySchedule(schedule: DentistScheduleOverview): boolean {
    if (!schedule.weeklySchedule) return false;

    return this.workingDays.some(day => {
      const daySchedule = this.getScheduleForDay(schedule, day.key);
      return daySchedule && daySchedule.length > 0;
    });
  }

  refresh(): void {
    this.loadScheduleOverview();
    this.loadHolidays();
  }

  // Métodos para alternar visualização
  setView(view: 'list' | 'calendar'): void {
    this.currentView = view;
  }

  isListView(): boolean {
    return this.currentView === 'list';
  }

  isCalendarView(): boolean {
    return this.currentView === 'calendar';
  }

  openExceptionModal(dentistId: number, dentistName: string): void {
    this.selectedDentistId = dentistId;
    this.selectedDentistName = dentistName;
    this.showExceptionModal = true;
  }

  closeExceptionModal(): void {
    this.showExceptionModal = false;
    this.selectedDentistId = null;
    this.selectedDentistName = '';
  }

  onExceptionCreated(): void {
    this.loadScheduleOverview(); // Recarregar para atualizar contadores
    this.closeExceptionModal();
  }

  openExceptionManagerModal(dentistId: number, dentistName: string): void {
    this.selectedDentistId = dentistId;
    this.selectedDentistName = dentistName;
    this.showExceptionManagerModal = true;
  }

  closeExceptionManagerModal(): void {
    this.showExceptionManagerModal = false;
    this.selectedDentistId = null;
    this.selectedDentistName = '';
  }

  onExceptionsUpdated(): void {
    this.loadScheduleOverview(); // Recarregar para atualizar contadores
    this.closeExceptionManagerModal();
  }
}
