<div class="bg-white shadow rounded-lg p-6">
  <!-- Header -->
  <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Escala de Dentistas</h1>
      <p class="text-gray-600"><PERSON><PERSON><PERSON><PERSON> os horários de trabalho dos dentistas e feriados</p>
    </div>

    <div class="flex flex-wrap gap-2">
      <!-- View Toggle Buttons -->
      <div class="flex bg-gray-100 rounded-lg p-1 mr-4">
        <button
          (click)="setView('list')"
          class="px-3 py-1 text-sm rounded-md transition-colors flex items-center"
          [class]="isListView() ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 10h16M4 14h16M4 18h16"
            />
          </svg>
          Lista
        </button>
        <button
          (click)="setView('calendar')"
          class="px-3 py-1 text-sm rounded-md transition-colors flex items-center"
          [class]="isCalendarView() ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          Calendário
        </button>
      </div>

      <button
        (click)="openHolidayModal()"
        class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        Gerenciar Feriados
      </button>
      <button
        (click)="refresh()"
        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center"
        [disabled]="isLoading"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-2"
          [class.animate-spin]="isLoading"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Atualizar
      </button>
    </div>
  </div>

  <!-- Feriados Section -->
  <div *ngIf="holidays.length > 0" class="mb-6">
    <h3 class="text-lg font-medium text-gray-900 mb-3">Feriados Cadastrados</h3>
    <div class="flex flex-wrap gap-2">
      <div
        *ngFor="let holiday of holidays"
        class="inline-flex items-center bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm"
      >
        <span>{{ holiday.name }} - {{ holiday.date | date:'dd/MM' }}</span>
        <button
          (click)="deleteHoliday(holiday)"
          class="ml-2 text-orange-600 hover:text-orange-800"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div
    *ngIf="isLoading"
    class="flex justify-center items-center py-12"
  >
    <div class="flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
      <span class="text-gray-600">Carregando escalas...</span>
    </div>
  </div>

  <!-- Error State -->
  <div
    *ngIf="!isLoading && error"
    class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
  >
    <div class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 text-red-400 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
          clip-rule="evenodd"
        />
      </svg>
      <span class="text-red-700">{{ error }}</span>
    </div>
  </div>

  <!-- Empty State -->
  <div
    *ngIf="!isLoading && !error && schedules.length === 0"
    class="text-center py-12"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-16 w-16 text-gray-400 mx-auto mb-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
    </svg>
    <h2 class="text-xl font-semibold text-gray-700 mb-2">Nenhum dentista encontrado</h2>
    <p class="text-gray-500">Cadastre dentistas para gerenciar suas escalas.</p>
  </div>

  <!-- List View -->
  <div
    *ngIf="!isLoading && !error && schedules.length > 0 && isListView()"
    class="space-y-4"
  >
    <!-- Desktop View -->
    <div class="hidden lg:block overflow-x-auto">
      <div class="min-w-full">
        <!-- Header da tabela -->
        <div class="grid grid-cols-8 gap-2 mb-4 text-sm font-medium text-gray-500 uppercase tracking-wider">
          <div class="col-span-2 px-4 py-2">Dentista</div>
          <div class="px-2 py-2 text-center">Seg</div>
          <div class="px-2 py-2 text-center">Ter</div>
          <div class="px-2 py-2 text-center">Qua</div>
          <div class="px-2 py-2 text-center">Qui</div>
          <div class="px-2 py-2 text-center">Sex</div>
          <div class="px-2 py-2 text-center">Sáb</div>
        </div>

        <!-- Linhas dos dentistas -->
        <div class="space-y-3">
          <div
            *ngFor="let schedule of schedules"
            class="grid grid-cols-8 gap-2 bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <!-- Informações do dentista -->
            <div class="col-span-2 flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900">{{ schedule.dentistName }}</div>
                <div class="text-sm text-gray-500">{{ schedule.dentistSpecialty }}</div>
                <div class="flex items-center mt-2 space-x-2">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    [class]="schedule.hasSchedule ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                  >
                    {{ schedule.hasSchedule ? 'Configurada' : 'Não configurada' }}
                  </span>
                  <span
                    *ngIf="schedule.upcomingExceptions > 0"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                  >
                    {{ schedule.upcomingExceptions }} exceção(ões)
                  </span>
                </div>
                <div class="flex flex-wrap gap-2 mt-3">
                  <button
                    (click)="openExceptionModal(schedule.dentistId, schedule.dentistName)"
                    class="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors"
                    title="Adicionar exceção"
                  >
                    Nova Exceção
                  </button>
                  <button
                    *ngIf="schedule.upcomingExceptions > 0"
                    (click)="openExceptionManagerModal(schedule.dentistId, schedule.dentistName)"
                    class="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700 transition-colors"
                    title="Gerenciar exceções existentes"
                  >
                    Gerenciar Exceções
                  </button>
                  <button
                    (click)="openScheduleEditModal(schedule)"
                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Editar Escala
                  </button>
                </div>
              </div>
            </div>

            <!-- Horários por dia da semana -->
            <div
              *ngFor="let day of workingDays"
              class="px-2 py-2 text-center"
            >
              <div class="text-xs space-y-1">
                <ng-container *ngIf="getScheduleForDay(schedule, day.key).length > 0; else noDaySchedule">
                  <div
                    *ngFor="let slot of getScheduleForDay(schedule, day.key)"
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded whitespace-nowrap"
                  >
                    {{ slot.start }}-{{ slot.end }}
                  </div>
                </ng-container>
                <ng-template #noDaySchedule>
                  <span class="text-gray-400">Folga</span>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile View -->
    <div class="lg:hidden space-y-4">
      <div
        *ngFor="let schedule of schedules"
        class="bg-white border border-gray-200 rounded-lg p-4"
      >
        <!-- Dentist Info -->
        <div class="flex items-center justify-between mb-4">
          <div>
            <div class="font-medium text-gray-900">{{ schedule.dentistName }}</div>
            <div class="text-sm text-gray-500">{{ schedule.dentistSpecialty }}</div>
          </div>
          <div class="flex space-x-2">
            <button
              (click)="openExceptionModal(schedule.dentistId, schedule.dentistName)"
              class="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors"
            >
              Exceção
            </button>
            <button
              (click)="openScheduleEditModal(schedule)"
              class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
            >
              Editar
            </button>
          </div>
        </div>

        <!-- Status -->
        <div class="flex items-center space-x-2 mb-4">
          <span
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
            [class]="schedule.hasSchedule ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
          >
            {{ schedule.hasSchedule ? 'Configurada' : 'Não configurada' }}
          </span>
          <span
            *ngIf="schedule.upcomingExceptions > 0"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
          >
            {{ schedule.upcomingExceptions }} exceção(ões)
          </span>
        </div>

        <!-- Schedule Grid -->
        <div class="grid grid-cols-2 gap-3">
          <div
            *ngFor="let day of workingDays"
            class="text-sm"
          >
            <div class="font-medium text-gray-700 mb-1">{{ day.short }}</div>
            <div class="space-y-1">
              <ng-container *ngIf="getScheduleForDay(schedule, day.key).length > 0; else noDayScheduleMobile">
                <div
                  *ngFor="let slot of getScheduleForDay(schedule, day.key)"
                  class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                >
                  {{ slot.start }}-{{ slot.end }}
                </div>
              </ng-container>
              <ng-template #noDayScheduleMobile>
                <span class="text-gray-400 text-xs">Folga</span>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar View -->
  <div *ngIf="!isLoading && !error && isCalendarView()">
    <app-calendar-view></app-calendar-view>
  </div>

  <!-- Modal de Exceções -->
  <app-exception-form
    *ngIf="selectedDentistId"
    [dentistId]="selectedDentistId"
    [dentistName]="selectedDentistName"
    [isOpen]="showExceptionModal"
    (closeModal)="closeExceptionModal()"
    (exceptionCreated)="onExceptionCreated()"
  ></app-exception-form>

  <!-- Modal de Edição de Escala -->
  <app-schedule-edit-modal
    [isOpen]="showScheduleEditModal"
    [dentist]="selectedDentistForEdit"
    (closeModal)="closeScheduleEditModal()"
    (scheduleUpdated)="onScheduleUpdated()"
  ></app-schedule-edit-modal>

  <!-- Modal de Gerenciamento de Exceções -->
  <app-exception-manager
    *ngIf="selectedDentistId"
    [dentistId]="selectedDentistId"
    [dentistName]="selectedDentistName"
    [isOpen]="showExceptionManagerModal"
    (closeModal)="closeExceptionManagerModal()"
    (exceptionsUpdated)="onExceptionsUpdated()"
  ></app-exception-manager>

  <!-- Modal de Feriados -->
  <div
    *ngIf="showHolidayModal"
    class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4"
    (click)="closeHolidayModal()"
  >
    <div
      class="relative bg-white rounded-lg shadow-xl w-full max-w-md"
      (click)="$event.stopPropagation()"
    >
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Adicionar Feriado</h3>
        <button
          type="button"
          (click)="closeHolidayModal()"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <form [formGroup]="holidayForm" (ngSubmit)="onSubmitHoliday()" class="p-6 space-y-4">
        <div>
          <label for="holidayName" class="block text-sm font-medium text-gray-700 mb-1">
            Nome do Feriado *
          </label>
          <input
            type="text"
            id="holidayName"
            formControlName="name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Ex: Natal, Ano Novo..."
          >
        </div>

        <div>
          <label for="holidayDate" class="block text-sm font-medium text-gray-700 mb-1">
            Data *
          </label>
          <input
            type="date"
            id="holidayDate"
            formControlName="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
        </div>

        <div>
          <label for="holidayDescription" class="block text-sm font-medium text-gray-700 mb-1">
            Descrição
          </label>
          <textarea
            id="holidayDescription"
            formControlName="description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Descrição opcional do feriado"
          ></textarea>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            (click)="closeHolidayModal()"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            [disabled]="isSubmittingHoliday"
          >
            Cancelar
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors flex items-center"
            [disabled]="isSubmittingHoliday || holidayForm.invalid"
          >
            <svg
              *ngIf="isSubmittingHoliday"
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ isSubmittingHoliday ? 'Salvando...' : 'Salvar Feriado' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
