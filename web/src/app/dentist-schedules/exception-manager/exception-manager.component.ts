import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';

import { DentistScheduleService } from '../../core/services/dentist-schedule.service';
import { NotificationService } from '../../core/services/notification.service';
import { DentistException } from '../../core/models/dentist-schedule.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-exception-manager',
  templateUrl: './exception-manager.component.html',
  styleUrls: ['./exception-manager.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class ExceptionManagerComponent implements OnInit {
  @Input() dentistId!: number;
  @Input() dentistName!: string;
  @Input() isOpen = false;
  @Output() closeModal = new EventEmitter<void>();
  @Output() exceptionsUpdated = new EventEmitter<void>();

  exceptions: DentistException[] = [];
  isLoading = false;
  error: string | null = null;

  // Modal de edição
  showEditModal = false;
  editingException: DentistException | null = null;
  editForm!: FormGroup;
  isSubmitting = false;

  // Dialog de confirmação
  showConfirmDialog = false;
  exceptionToDelete: DentistException | null = null;

  exceptionTypes = [
    { value: 'day-off', label: 'Folga' },
    { value: 'custom-hours', label: 'Horário Personalizado' }
  ];

  constructor(
    private fb: FormBuilder,
    private dentistScheduleService: DentistScheduleService,
    private notificationService: NotificationService
  ) {
    this.initializeEditForm();
  }

  ngOnInit(): void {
    if (this.isOpen && this.dentistId) {
      this.loadExceptions();
    }
  }

  initializeEditForm(): void {
    this.editForm = this.fb.group({
      date: ['', [Validators.required]],
      type: ['day-off', [Validators.required]],
      reason: [''],
      customHours: this.fb.array([])
    });

    // Observar mudanças no tipo
    this.editForm.get('type')?.valueChanges.subscribe(type => {
      const customHoursArray = this.getCustomHoursArray();
      if (type === 'custom-hours') {
        if (customHoursArray.length === 0) {
          this.addCustomHour();
        }
      } else {
        customHoursArray.clear();
      }
    });
  }

  loadExceptions(): void {
    this.isLoading = true;
    this.error = null;

    // Carregar exceções futuras (a partir de hoje)
    const today = new Date().toISOString().split('T')[0];
    
    this.dentistScheduleService.getExceptions(this.dentistId, today)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (exceptions) => {
          this.exceptions = exceptions;
        },
        error: (err) => {
          console.error('Erro ao carregar exceções:', err);
          this.error = 'Erro ao carregar exceções. Tente novamente.';
        }
      });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString + 'T00:00:00');
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getExceptionTypeLabel(type: string): string {
    const typeObj = this.exceptionTypes.find(t => t.value === type);
    return typeObj?.label || type;
  }

  formatTimeSlots(timeSlots: { start: string; end: string }[]): string {
    if (!timeSlots || timeSlots.length === 0) return 'N/A';
    return timeSlots.map(slot => `${slot.start} - ${slot.end}`).join(', ');
  }

  // Edição de exceção
  openEditModal(exception: DentistException): void {
    this.editingException = exception;
    this.editForm.patchValue({
      date: exception.date,
      type: exception.type,
      reason: exception.reason || ''
    });

    // Configurar horários personalizados se existirem
    const customHoursArray = this.getCustomHoursArray();
    customHoursArray.clear();
    
    if (exception.type === 'custom-hours' && exception.customHours) {
      exception.customHours.forEach(hour => {
        customHoursArray.push(this.createCustomHourGroup(hour));
      });
    }

    this.showEditModal = true;
  }

  closeEditModal(): void {
    this.showEditModal = false;
    this.editingException = null;
    this.editForm.reset();
    this.getCustomHoursArray().clear();
    this.isSubmitting = false;
  }

  getCustomHoursArray(): FormArray {
    return this.editForm.get('customHours') as FormArray;
  }

  createCustomHourGroup(hour?: { start: string; end: string }): FormGroup {
    return this.fb.group({
      start: [hour?.start || '', [Validators.required]],
      end: [hour?.end || '', [Validators.required]]
    });
  }

  addCustomHour(): void {
    this.getCustomHoursArray().push(this.createCustomHourGroup());
  }

  removeCustomHour(index: number): void {
    this.getCustomHoursArray().removeAt(index);
  }

  onEditSubmit(): void {
    if (!this.editingException || this.editForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formValue = this.editForm.value;
    
    const updateData: any = {
      date: formValue.date,
      type: formValue.type,
      reason: formValue.reason || undefined
    };

    if (formValue.type === 'custom-hours' && formValue.customHours.length > 0) {
      updateData.customHours = formValue.customHours.filter((hour: any) => 
        hour.start && hour.end
      );
    }

    this.dentistScheduleService.updateException(this.dentistId, this.editingException.id, updateData)
      .pipe(finalize(() => this.isSubmitting = false))
      .subscribe({
        next: () => {
          this.notificationService.success('Exceção atualizada com sucesso!');
          this.loadExceptions();
          this.closeEditModal();
          this.exceptionsUpdated.emit();
        },
        error: (err) => {
          console.error('Erro ao atualizar exceção:', err);
          this.notificationService.error(
            err.error?.message || 'Erro ao atualizar exceção. Tente novamente.'
          );
        }
      });
  }

  deleteException(exception: DentistException): void {
    this.exceptionToDelete = exception;
    this.showConfirmDialog = true;
  }

  confirmDelete(): void {
    if (!this.exceptionToDelete) return;

    this.dentistScheduleService.deleteException(this.dentistId, this.exceptionToDelete.id)
      .subscribe({
        next: () => {
          this.notificationService.success('Exceção removida com sucesso!');
          this.loadExceptions();
          this.exceptionsUpdated.emit();
          this.closeConfirmDialog();
        },
        error: (err) => {
          console.error('Erro ao remover exceção:', err);
          this.notificationService.error(
            err.error?.message || 'Erro ao remover exceção. Tente novamente.'
          );
          this.closeConfirmDialog();
        }
      });
  }

  closeConfirmDialog(): void {
    this.showConfirmDialog = false;
    this.exceptionToDelete = null;
  }

  close(): void {
    this.closeModal.emit();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
