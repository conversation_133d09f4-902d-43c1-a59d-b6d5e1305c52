/* Modal Styling */
.z-60 {
  z-index: 60;
}

/* Smooth Transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Card Hover Effects */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Loading Animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom Scrollbar */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Button States */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: inherit !important;
  color: inherit !important;
}

/* Focus States */
button:focus,
input:focus,
select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Form Styling */
.border-red-500 {
  border-color: #ef4444 !important;
}

/* Badge Colors */
.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .max-w-4xl {
    max-width: 95vw;
  }
  
  .max-w-2xl {
    max-width: 95vw;
  }
  
  .p-6 {
    padding: 1rem;
  }
  
  .space-x-2 > * + * {
    margin-left: 0.25rem;
  }
  
  .flex-col {
    flex-direction: column;
  }
  
  .space-y-2 > * + * {
    margin-top: 0.5rem;
  }
}

@media (max-width: 640px) {
  .text-lg {
    font-size: 1rem;
  }
  
  .px-3 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Stack action buttons on mobile */
  .flex.items-center.space-x-2 {
    flex-direction: column;
    space: 0;
  }
  
  .flex.items-center.space-x-2 > * + * {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

/* Animation for Modal */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bg-white.rounded-lg.shadow-xl {
  animation: fadeIn 0.2s ease-out;
}

/* Exception Type Badges */
.inline-flex.items-center.px-2\.5.py-0\.5.rounded-full {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Action Buttons */
.bg-blue-600:hover {
  background-color: #2563eb;
}

.bg-red-600:hover {
  background-color: #dc2626;
}

.bg-green-600:hover {
  background-color: #16a34a;
}

/* Form Array Styling */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

/* Time Input Styling */
input[type="time"] {
  -webkit-appearance: none;
  -moz-appearance: textfield;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
}

/* Error States */
.bg-red-50 {
  background-color: #fef2f2;
}

.border-red-200 {
  border-color: #fecaca;
}

.text-red-400 {
  color: #f87171;
}

.text-red-700 {
  color: #b91c1c;
}

/* Success States */
.bg-green-50 {
  background-color: #f0fdf4;
}

.border-green-200 {
  border-color: #bbf7d0;
}

.text-green-700 {
  color: #15803d;
}
