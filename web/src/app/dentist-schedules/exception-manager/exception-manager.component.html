<!-- <PERSON><PERSON> Backdrop -->
<div
  *ngIf="isOpen"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  (click)="onBackdropClick($event)"
>
  <!-- Modal Content -->
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
    (click)="$event.stopPropagation()"
  >
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <div>
        <h3 class="text-xl font-semibold text-gray-900">
          Gerenciar Exceções - {{ dentistName }}
        </h3>
        <p class="text-sm text-gray-600 mt-1">Visualize, edite ou remova exceções futuras</p>
      </div>
      
      <button
        type="button"
        (click)="close()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center py-12">
        <div class="flex items-center space-x-3">
          <svg class="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-gray-600">Carregando exceções...</span>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span class="text-red-700">{{ error }}</span>
        </div>
        <button
          (click)="loadExceptions()"
          class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
        >
          Tentar novamente
        </button>
      </div>

      <!-- Exceptions List -->
      <div *ngIf="!isLoading && !error">
        <!-- Empty State -->
        <div *ngIf="exceptions.length === 0" class="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma exceção encontrada</h3>
          <p class="text-gray-500">Este dentista não possui exceções futuras cadastradas.</p>
        </div>

        <!-- Exceptions Cards -->
        <div *ngIf="exceptions.length > 0" class="space-y-4">
          <div
            *ngFor="let exception of exceptions"
            class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <!-- Date and Type -->
                <div class="flex items-center mb-2">
                  <h4 class="text-lg font-medium text-gray-900 mr-3">
                    {{ formatDate(exception.date) }}
                  </h4>
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    [class]="exception.type === 'day-off' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'"
                  >
                    {{ getExceptionTypeLabel(exception.type) }}
                  </span>
                </div>

                <!-- Details -->
                <div class="space-y-1 text-sm text-gray-600">
                  <div *ngIf="exception.reason">
                    <strong>Motivo:</strong> {{ exception.reason }}
                  </div>
                  <div *ngIf="exception.type === 'custom-hours' && exception.customHours">
                    <strong>Horários:</strong> {{ formatTimeSlots(exception.customHours) }}
                  </div>
                  <div *ngIf="exception.type === 'day-off'">
                    <strong>Status:</strong> Folga - Não disponível
                  </div>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center space-x-2 ml-4">
                <button
                  (click)="openEditModal(exception)"
                  class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  title="Editar exceção"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Editar
                </button>
                
                <button
                  (click)="deleteException(exception)"
                  class="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors flex items-center"
                  title="Remover exceção"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Remover
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Exception Modal -->
<div
  *ngIf="showEditModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4"
  (click)="closeEditModal()"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
    (click)="$event.stopPropagation()"
  >
    <!-- Edit Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">
        Editar Exceção
      </h3>
      <button
        type="button"
        (click)="closeEditModal()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Edit Form -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
      <form [formGroup]="editForm" (ngSubmit)="onEditSubmit()" class="space-y-4">
        <!-- Data -->
        <div>
          <label for="edit-date" class="block text-sm font-medium text-gray-700 mb-1">
            Data *
          </label>
          <input
            type="date"
            id="edit-date"
            formControlName="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [class.border-red-500]="editForm.get('date')?.invalid && editForm.get('date')?.touched"
          >
        </div>

        <!-- Tipo -->
        <div>
          <label for="edit-type" class="block text-sm font-medium text-gray-700 mb-1">
            Tipo *
          </label>
          <select
            id="edit-type"
            formControlName="type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option *ngFor="let type of exceptionTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <!-- Motivo -->
        <div>
          <label for="edit-reason" class="block text-sm font-medium text-gray-700 mb-1">
            Motivo
          </label>
          <input
            type="text"
            id="edit-reason"
            formControlName="reason"
            placeholder="Ex: Consulta médica, viagem, etc."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        </div>

        <!-- Horários Personalizados -->
        <div *ngIf="editForm.get('type')?.value === 'custom-hours'">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Horários Personalizados *
          </label>
          <div formArrayName="customHours" class="space-y-2">
            <div
              *ngFor="let hour of getCustomHoursArray().controls; let i = index"
              [formGroupName]="i"
              class="flex items-center space-x-2"
            >
              <input
                type="time"
                formControlName="start"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Início"
              >
              <span class="text-gray-500">até</span>
              <input
                type="time"
                formControlName="end"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Fim"
              >
              <button
                type="button"
                (click)="removeCustomHour(i)"
                class="p-2 text-red-600 hover:text-red-800 transition-colors"
                title="Remover horário"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
          <button
            type="button"
            (click)="addCustomHour()"
            class="mt-2 px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
          >
            Adicionar Horário
          </button>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="closeEditModal()"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          <button
            type="submit"
            [disabled]="editForm.invalid || isSubmitting"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span *ngIf="isSubmitting">Salvando...</span>
            <span *ngIf="!isSubmitting">Salvar Alterações</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Confirmation Dialog -->
<div
  *ngIf="showConfirmDialog"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4"
  (click)="closeConfirmDialog()"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-md"
    (click)="$event.stopPropagation()"
  >
    <!-- Dialog Header -->
    <div class="flex items-center p-6 border-b border-gray-200">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-medium text-gray-900">
            Remover Exceção
          </h3>
        </div>
      </div>
    </div>

    <!-- Dialog Body -->
    <div class="p-6">
      <p class="text-sm text-gray-600">
        Tem certeza que deseja remover a exceção de
        <strong>{{ exceptionToDelete ? formatDate(exceptionToDelete.date) : '' }}</strong>?
      </p>
      <p class="text-sm text-gray-500 mt-2">
        Esta ação não pode ser desfeita.
      </p>
    </div>

    <!-- Dialog Actions -->
    <div class="flex justify-end space-x-3 p-6 border-t border-gray-200">
      <button
        type="button"
        (click)="closeConfirmDialog()"
        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
      >
        Cancelar
      </button>
      <button
        type="button"
        (click)="confirmDelete()"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
      >
        Remover
      </button>
    </div>
  </div>
</div>
