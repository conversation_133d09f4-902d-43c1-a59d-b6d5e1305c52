import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { finalize } from 'rxjs/operators';
import { DentistScheduleService } from '../../core/services/dentist-schedule.service';
import { NotificationService } from '../../core/services/notification.service';
import { DentistScheduleOverview, WeeklySchedule, TimeSlot } from '../../core/models/dentist-schedule.model';
import { ConfirmationDialogComponent } from '../../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-schedule-edit-modal',
  templateUrl: './schedule-edit-modal.component.html',
  styleUrls: ['./schedule-edit-modal.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ConfirmationDialogComponent]
})
export class ScheduleEditModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() dentist: DentistScheduleOverview | null = null;
  @Output() closeModal = new EventEmitter<void>();
  @Output() scheduleUpdated = new EventEmitter<void>();

  scheduleForm!: FormGroup;
  isSaving = false;
  error: string | null = null;

  // Propriedades para o dialog de confirmação
  showClearConfirmation = false;

  // Dias da semana (segunda a sábado)
  workingDays = [
    { key: 'monday', name: 'Segunda-feira' },
    { key: 'tuesday', name: 'Terça-feira' },
    { key: 'wednesday', name: 'Quarta-feira' },
    { key: 'thursday', name: 'Quinta-feira' },
    { key: 'friday', name: 'Sexta-feira' },
    { key: 'saturday', name: 'Sábado' }
  ];

  constructor(
    private fb: FormBuilder,
    private dentistScheduleService: DentistScheduleService,
    private notificationService: NotificationService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (this.isOpen && this.dentist) {
      this.loadScheduleData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen']) {
      if (this.isOpen && this.dentist) {
        this.loadScheduleData();
      } else if (!this.isOpen) {
        // Limpar inputs quando modal fecha
        this.clearActiveTimeInputs();
      }
    }
  }

  initializeForm(): void {
    const formControls: any = {};
    
    this.workingDays.forEach(day => {
      formControls[day.key] = this.fb.array([]);
    });

    this.scheduleForm = this.fb.group(formControls);
  }

  loadScheduleData(): void {
    if (!this.dentist) return;

    // Limpar formulário
    this.workingDays.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      dayArray.clear();
    });

    // Carregar dados existentes
    if (this.dentist.weeklySchedule) {
      this.populateForm(this.dentist.weeklySchedule);
    }

    this.error = null;
  }

  populateForm(weeklySchedule: WeeklySchedule): void {
    this.workingDays.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      const daySchedule = weeklySchedule[day.key as keyof WeeklySchedule] || [];
      
      daySchedule.forEach(slot => {
        dayArray.push(this.createTimeSlotGroup(slot));
      });
    });
  }

  getDayFormArray(day: string): FormArray {
    return this.scheduleForm.get(day) as FormArray;
  }

  createTimeSlotGroup(slot?: TimeSlot): FormGroup {
    return this.fb.group({
      start: [slot?.start || '', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]],
      end: [slot?.end || '', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]]
    });
  }

  addTimeSlot(day: string): void {
    this.clearActiveTimeInputs();
    const dayArray = this.getDayFormArray(day);
    dayArray.push(this.createTimeSlotGroup());
  }

  removeTimeSlot(day: string, index: number): void {
    this.clearActiveTimeInputs();
    const dayArray = this.getDayFormArray(day);
    dayArray.removeAt(index);
  }

  onTimeInputClick(event: Event): void {
    event.stopPropagation();
  }

  onTimeInputFocus(event: Event): void {
    event.stopPropagation();
  }

  onTimeInputBlur(event: Event): void {
    event.stopPropagation();
  }

  fillDefaultSchedule(): void {
    // Horário padrão: 08:00-12:00 e 14:00-19:00
    const defaultTimeSlots = [
      { start: '08:00', end: '12:00' },
      { start: '14:00', end: '19:00' }
    ];

    // Aplicar para todos os dias da semana (segunda a sábado)
    this.workingDays.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);

      // Limpar horários existentes
      dayArray.clear();

      // Adicionar horários padrão
      defaultTimeSlots.forEach(slot => {
        dayArray.push(this.createTimeSlotGroup(slot));
      });
    });

    // Limpar qualquer input ativo
    this.clearActiveTimeInputs();

    // Mostrar toast informativo
    this.notificationService.info(
      'Horário padrão aplicado em todos os dias (08h-12h e 14h-19h)'
    );
  }

  clearAllSchedules(): void {
    // Abrir dialog de confirmação personalizado
    this.showClearConfirmation = true;
  }

  confirmClearAllSchedules(): void {
    // Limpar todos os horários de todos os dias
    this.workingDays.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      dayArray.clear();
    });

    // Limpar qualquer input ativo
    this.clearActiveTimeInputs();

    // Fechar dialog de confirmação
    this.showClearConfirmation = false;

    // Mostrar toast informativo
    this.notificationService.info(
      'Todos os horários foram removidos'
    );
  }

  cancelClearAllSchedules(): void {
    this.showClearConfirmation = false;
  }

  onBackdropClick(event: Event): void {
    // Só fecha se o clique foi no backdrop, não em um elemento filho
    if (event.target === event.currentTarget) {
      this.close();
    }
  }

  onSubmit(): void {
    if (!this.dentist || this.scheduleForm.invalid) {
      this.markFormGroupTouched(this.scheduleForm);
      return;
    }

    this.isSaving = true;
    this.error = null;

    const weeklySchedule: WeeklySchedule = {};

    this.workingDays.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      weeklySchedule[day.key as keyof WeeklySchedule] = dayArray.value
        .filter((slot: TimeSlot) => slot.start && slot.end)
        .sort((a: TimeSlot, b: TimeSlot) => a.start.localeCompare(b.start));
    });

    this.dentistScheduleService.updateSchedule(this.dentist.dentistId, weeklySchedule)
      .pipe(
        finalize(() => {
          this.isSaving = false;
        })
      )
      .subscribe({
        next: () => {
          // Mostrar toast de sucesso
          this.notificationService.success(
            `Escala de ${this.dentist?.dentistName} atualizada com sucesso!`
          );

          // Fechar modal e atualizar lista
          this.scheduleUpdated.emit();
        },
        error: (err) => {
          console.error('Erro ao salvar escala:', err);

          // Mostrar toast de erro
          this.notificationService.error(
            'Erro ao salvar escala. Tente novamente.'
          );
        }
      });
  }

  close(): void {
    // Limpar qualquer foco ativo antes de fechar
    this.clearActiveTimeInputs();
    this.closeModal.emit();
  }

  private clearActiveTimeInputs(): void {
    // Remove foco de qualquer input de time ativo
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && (activeElement as HTMLInputElement).type === 'time') {
      activeElement.blur();
    }

    // Força o fechamento de qualquer picker de time aberto no modal
    setTimeout(() => {
      const modalElement = document.querySelector('.schedule-edit-modal');
      if (modalElement) {
        const timeInputs = modalElement.querySelectorAll('input[type="time"]');
        timeInputs.forEach(input => {
          (input as HTMLInputElement).blur();
        });
      }
    }, 0);
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Validação de horários
  isValidTimeRange(day: string, index: number): boolean {
    const dayArray = this.getDayFormArray(day);
    const slot = dayArray.at(index);
    
    if (!slot) return true;
    
    const start = slot.get('start')?.value;
    const end = slot.get('end')?.value;
    
    if (!start || !end) return true;
    
    return start < end;
  }

  hasOverlappingTimes(day: string): boolean {
    const dayArray = this.getDayFormArray(day);
    const slots = dayArray.value.filter((slot: TimeSlot) => slot.start && slot.end);
    
    for (let i = 0; i < slots.length; i++) {
      for (let j = i + 1; j < slots.length; j++) {
        const slot1 = slots[i];
        const slot2 = slots[j];
        
        if (this.timesOverlap(slot1.start, slot1.end, slot2.start, slot2.end)) {
          return true;
        }
      }
    }
    
    return false;
  }

  private timesOverlap(start1: string, end1: string, start2: string, end2: string): boolean {
    return start1 < end2 && start2 < end1;
  }

  getTimeSlotError(day: string, index: number): string | null {
    if (!this.isValidTimeRange(day, index)) {
      return 'Horário de início deve ser menor que o de fim';
    }
    
    if (this.hasOverlappingTimes(day)) {
      return 'Horários não podem se sobrepor';
    }
    
    return null;
  }
}
