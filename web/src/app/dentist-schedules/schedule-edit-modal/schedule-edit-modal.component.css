/* Modal animations */
.modal-backdrop {
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Time input styling */
input[type="time"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: textfield;
  position: relative;
  z-index: 1;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
  z-index: 2;
}

/* Prevent time picker from interfering with modal */
input[type="time"]:focus {
  z-index: 10;
}

/* Ensure time inputs don't trigger modal close */
input[type="time"]::-webkit-datetime-edit,
input[type="time"]::-webkit-datetime-edit-fields-wrapper,
input[type="time"]::-webkit-datetime-edit-hour-field,
input[type="time"]::-webkit-datetime-edit-minute-field,
input[type="time"]::-webkit-datetime-edit-ampm-field {
  pointer-events: auto;
}

/* Fix for mobile time inputs */
@media (max-width: 768px) {
  input[type="time"] {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Form validation styles */
.ng-invalid.ng-touched {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ng-valid.ng-touched {
  border-color: #10b981;
}

/* Time slot animations */
.time-slot-row {
  animation: slideInSlot 0.3s ease-out;
}

@keyframes slideInSlot {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .flex.items-center.space-x-2 {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .flex.items-center.space-x-2 > span {
    display: none;
  }
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
