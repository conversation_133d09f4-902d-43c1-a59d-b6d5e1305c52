<!-- Modal Backdrop -->
<div
  *ngIf="isOpen"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4"
  (click)="onBackdropClick($event)"
>
  <!-- Modal Content -->
  <div
    class="schedule-edit-modal relative bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
    (click)="$event.stopPropagation()"
  >
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-xl font-semibold text-gray-900">
              Editar <PERSON> - {{ dentist?.dentistName }}
            </h3>
            <p class="text-sm text-gray-600 mt-1">{{ dentist?.dentistSpecialty }}</p>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center space-x-2 mr-4">
            <button
              type="button"
              (click)="fillDefaultSchedule()"
              class="px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors flex items-center"
              title="Preencher horário padrão (08h-12h e 14h-19h) em todos os dias"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <span class="hidden sm:inline">Horário Padrão</span>
              <span class="sm:hidden">Padrão</span>
            </button>

            <button
              type="button"
              (click)="clearAllSchedules()"
              class="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors flex items-center"
              title="Limpar todos os horários"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
              <span class="hidden sm:inline">Limpar Tudo</span>
              <span class="sm:hidden">Limpar</span>
            </button>
          </div>
        </div>
      </div>

      <button
        type="button"
        (click)="close()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
      <!-- Error Message -->
      <div
        *ngIf="error"
        class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-red-400 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-red-700 text-sm">{{ error }}</span>
        </div>
      </div>

      <!-- Info Box -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div class="text-sm">
            <p class="text-blue-800 font-medium mb-1">Horário Padrão da Clínica</p>
            <p class="text-blue-700">
              <strong>Manhã:</strong> 08:00 às 12:00 •
              <strong>Tarde:</strong> 14:00 às 19:00
            </p>
            <p class="text-blue-600 text-xs mt-1">
              Use o botão "Horário Padrão" para aplicar automaticamente em todos os dias da semana.
            </p>
          </div>
        </div>
      </div>

      <!-- Form -->
      <form [formGroup]="scheduleForm" (ngSubmit)="onSubmit()" class="space-y-6">
        <!-- Days Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            *ngFor="let day of workingDays"
            class="bg-gray-50 rounded-lg p-4 border border-gray-200"
          >
            <!-- Day Header -->
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-lg font-medium text-gray-900">{{ day.name }}</h4>
              <button
                type="button"
                (click)="addTimeSlot(day.key); $event.stopPropagation()"
                class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Adicionar
              </button>
            </div>

            <!-- Time Slots -->
            <div formArrayName="{{ day.key }}" class="space-y-3">
              <div
                *ngFor="let slot of getDayFormArray(day.key).controls; let i = index"
                [formGroupName]="i"
                class="space-y-2"
              >
                <!-- Time Inputs -->
                <div class="flex items-center space-x-2">
                  <div class="flex-1">
                    <label class="block text-xs text-gray-600 mb-1">Início</label>
                    <input
                      type="time"
                      formControlName="start"
                      [id]="'start-' + day.key + '-' + i"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                      (click)="onTimeInputClick($event)"
                      (focus)="onTimeInputFocus($event)"
                      (blur)="onTimeInputBlur($event)"
                    >
                  </div>
                  <span class="text-gray-500 text-sm mt-5">até</span>
                  <div class="flex-1">
                    <label class="block text-xs text-gray-600 mb-1">Fim</label>
                    <input
                      type="time"
                      formControlName="end"
                      [id]="'end-' + day.key + '-' + i"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                      (click)="onTimeInputClick($event)"
                      (focus)="onTimeInputFocus($event)"
                      (blur)="onTimeInputBlur($event)"
                    >
                  </div>
                  <button
                    type="button"
                    (click)="removeTimeSlot(day.key, i); $event.stopPropagation()"
                    class="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors mt-5"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>

                <!-- Error Message -->
                <div
                  *ngIf="getTimeSlotError(day.key, i)"
                  class="text-red-500 text-xs"
                >
                  {{ getTimeSlotError(day.key, i) }}
                </div>
              </div>

              <!-- Empty State -->
              <div
                *ngIf="getDayFormArray(day.key).length === 0"
                class="text-center py-6 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 mx-auto mb-2 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <p class="text-sm">Folga</p>
                <p class="text-xs">Clique em "Adicionar" para definir horários</p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Modal Footer -->
    <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
      <button
        type="button"
        (click)="close()"
        class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        [disabled]="isSaving"
      >
        Cancelar
      </button>
      <button
        type="submit"
        (click)="onSubmit()"
        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        [disabled]="isSaving || scheduleForm.invalid"
      >
        <svg
          *ngIf="isSaving"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isSaving ? 'Salvando...' : 'Salvar Escala' }}
      </button>
    </div>
  </div>
</div>

<!-- Dialog de confirmação para limpar todos os horários -->
<app-confirmation-dialog
  [isOpen]="showClearConfirmation"
  title="Limpar Todos os Horários"
  message="Tem certeza que deseja limpar todos os horários? Esta ação não pode ser desfeita."
  confirmButtonText="Limpar Tudo"
  cancelButtonText="Cancelar"
  confirmButtonClass="bg-red-600 hover:bg-red-700"
  type="danger"
  (confirm)="confirmClearAllSchedules()"
  (cancel)="cancelClearAllSchedules()"
></app-confirmation-dialog>
