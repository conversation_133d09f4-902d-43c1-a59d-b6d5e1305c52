import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { ScheduleOverviewComponent } from './schedule-overview/schedule-overview.component';
import { ScheduleEditComponent } from './schedule-edit/schedule-edit.component';
import { ExceptionFormComponent } from './exception-form/exception-form.component';
import { ScheduleEditModalComponent } from './schedule-edit-modal/schedule-edit-modal.component';
import { CalendarViewComponent } from './calendar-view/calendar-view.component';
import { DayDetailModalComponent } from './day-detail-modal/day-detail-modal.component';
import { ExceptionManagerComponent } from './exception-manager/exception-manager.component';

const routes = [
  {
    path: '',
    component: ScheduleOverviewComponent
  },
  {
    path: 'edit/:dentistId',
    component: ScheduleEditComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule.forChild(routes),
    ScheduleOverviewComponent,
    ScheduleEditComponent,
    ExceptionFormComponent,
    ScheduleEditModalComponent,
    CalendarViewComponent,
    DayDetailModalComponent,
    ExceptionManagerComponent
  ]
})
export class DentistSchedulesModule {
  // Módulo de escalas de dentistas
}
