<div class="bg-white shadow rounded-lg p-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">
        Editar Escala
        <span *ngIf="dentist" class="text-blue-600">- {{ dentist.name }}</span>
      </h1>
      <p class="text-gray-600">Configure os horários de trabalho semanais</p>
    </div>
    
    <button
      (click)="goBack()"
      class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        />
      </svg>
      Voltar
    </button>
  </div>

  <!-- Loading State -->
  <div
    *ngIf="isLoading"
    class="flex justify-center items-center py-12"
  >
    <div class="flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
      <span class="text-gray-600">Carregando escala...</span>
    </div>
  </div>

  <!-- Error State -->
  <div
    *ngIf="!isLoading && error"
    class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
  >
    <div class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 text-red-400 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
          clip-rule="evenodd"
        />
      </svg>
      <span class="text-red-700">{{ error }}</span>
    </div>
  </div>

  <!-- Success Message -->
  <div
    *ngIf="successMessage"
    class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6"
  >
    <div class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 text-green-400 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clip-rule="evenodd"
        />
      </svg>
      <span class="text-green-700">{{ successMessage }}</span>
    </div>
  </div>

  <!-- Form -->
  <form
    *ngIf="!isLoading && !error"
    [formGroup]="scheduleForm"
    (ngSubmit)="onSubmit()"
    class="space-y-6"
  >
    <!-- Days of Week -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div
        *ngFor="let day of daysOfWeek"
        class="border border-gray-200 rounded-lg p-4"
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ day.name }}</h3>
          <button
            type="button"
            (click)="addTimeSlot(day.key)"
            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Adicionar
          </button>
        </div>

        <!-- Time Slots -->
        <div formArrayName="{{ day.key }}" class="space-y-3">
          <div
            *ngFor="let slot of getDayFormArray(day.key).controls; let i = index"
            [formGroupName]="i"
            class="flex items-center space-x-2"
          >
            <div class="flex-1">
              <input
                type="time"
                formControlName="start"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Início"
              >
            </div>
            <span class="text-gray-500">até</span>
            <div class="flex-1">
              <input
                type="time"
                formControlName="end"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Fim"
              >
            </div>
            <button
              type="button"
              (click)="removeTimeSlot(day.key, i)"
              class="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          </div>

          <!-- Empty state for day -->
          <div
            *ngIf="getDayFormArray(day.key).length === 0"
            class="text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 mx-auto mb-2 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            <p class="text-sm">Nenhum horário configurado</p>
            <p class="text-xs">Clique em "Adicionar" para definir horários</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        (click)="goBack()"
        class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        [disabled]="isSaving"
      >
        Cancelar
      </button>
      <button
        type="submit"
        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        [disabled]="isSaving || scheduleForm.invalid"
      >
        <svg
          *ngIf="isSaving"
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isSaving ? 'Salvando...' : 'Salvar Escala' }}
      </button>
    </div>
  </form>
</div>
