import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { finalize } from 'rxjs/operators';
import { DentistScheduleService } from '../../core/services/dentist-schedule.service';
import { DentistService } from '../../core/services/dentist.service';
import { Dentist } from '../../core/models/dentist.model';
import { DentistSchedule, WeeklySchedule, TimeSlot } from '../../core/models/dentist-schedule.model';

@Component({
  selector: 'app-schedule-edit',
  templateUrl: './schedule-edit.component.html',
  styleUrls: ['./schedule-edit.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class ScheduleEditComponent implements OnInit {
  dentistId!: number;
  dentist: Dentist | null = null;
  scheduleForm!: FormGroup;
  isLoading = true;
  isSaving = false;
  error: string | null = null;
  successMessage: string | null = null;

  daysOfWeek = [
    { key: 'monday', name: 'Segunda-feira' },
    { key: 'tuesday', name: 'Terça-feira' },
    { key: 'wednesday', name: 'Quarta-feira' },
    { key: 'thursday', name: 'Quinta-feira' },
    { key: 'friday', name: 'Sexta-feira' },
    { key: 'saturday', name: 'Sábado' },
    { key: 'sunday', name: 'Domingo' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private dentistScheduleService: DentistScheduleService,
    private dentistService: DentistService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.dentistId = Number(this.route.snapshot.paramMap.get('dentistId'));
    this.loadDentist();
    this.loadSchedule();
  }

  initializeForm(): void {
    const formControls: any = {};
    
    this.daysOfWeek.forEach(day => {
      formControls[day.key] = this.fb.array([]);
    });

    this.scheduleForm = this.fb.group(formControls);
  }

  loadDentist(): void {
    this.dentistService.getDentist(this.dentistId).subscribe({
      next: (dentist) => {
        this.dentist = dentist;
      },
      error: (err) => {
        console.error('Erro ao carregar dentista:', err);
        this.error = 'Dentista não encontrado.';
      }
    });
  }

  loadSchedule(): void {
    this.isLoading = true;
    this.error = null;

    this.dentistScheduleService.getSchedule(this.dentistId)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (schedule) => {
          this.populateForm(schedule.weeklySchedule);
        },
        error: (err) => {
          console.error('Erro ao carregar escala:', err);
          // Se não existe escala, inicializa com formulário vazio
          if (err.status === 404) {
            this.populateForm({});
          } else {
            this.error = 'Erro ao carregar escala do dentista.';
          }
        }
      });
  }

  populateForm(weeklySchedule: WeeklySchedule): void {
    this.daysOfWeek.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      dayArray.clear();

      const daySchedule = weeklySchedule[day.key as keyof WeeklySchedule] || [];
      daySchedule.forEach(slot => {
        dayArray.push(this.createTimeSlotGroup(slot));
      });
    });
  }

  getDayFormArray(day: string): FormArray {
    return this.scheduleForm.get(day) as FormArray;
  }

  createTimeSlotGroup(slot?: TimeSlot): FormGroup {
    return this.fb.group({
      start: [slot?.start || '', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]],
      end: [slot?.end || '', [Validators.required, Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]]
    });
  }

  addTimeSlot(day: string): void {
    const dayArray = this.getDayFormArray(day);
    dayArray.push(this.createTimeSlotGroup());
  }

  removeTimeSlot(day: string, index: number): void {
    const dayArray = this.getDayFormArray(day);
    dayArray.removeAt(index);
  }

  onSubmit(): void {
    if (this.scheduleForm.invalid) {
      this.markFormGroupTouched(this.scheduleForm);
      return;
    }

    this.isSaving = true;
    this.error = null;
    this.successMessage = null;

    const weeklySchedule: WeeklySchedule = {};
    
    this.daysOfWeek.forEach(day => {
      const dayArray = this.getDayFormArray(day.key);
      weeklySchedule[day.key as keyof WeeklySchedule] = dayArray.value.filter((slot: TimeSlot) => 
        slot.start && slot.end
      );
    });

    this.dentistScheduleService.updateSchedule(this.dentistId, weeklySchedule)
      .pipe(
        finalize(() => {
          this.isSaving = false;
        })
      )
      .subscribe({
        next: () => {
          this.successMessage = 'Escala salva com sucesso!';
          setTimeout(() => {
            this.router.navigate(['/dentist-schedules']);
          }, 2000);
        },
        error: (err) => {
          console.error('Erro ao salvar escala:', err);
          this.error = 'Erro ao salvar escala. Tente novamente.';
        }
      });
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/dentist-schedules']);
  }
}
