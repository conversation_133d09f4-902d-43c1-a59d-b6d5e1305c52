/* Estilos para inputs de tempo */
input[type="time"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: textfield;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
}

/* Estados de validação */
.ng-invalid.ng-touched {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ng-valid.ng-touched {
  border-color: #10b981;
}

/* Animações */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.time-slot-row {
  animation: slideIn 0.3s ease-out;
}

/* Responsividade */
@media (max-width: 768px) {
  .grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .flex.items-center.space-x-2 {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .flex.items-center.space-x-2 > span {
    display: none;
  }
}
