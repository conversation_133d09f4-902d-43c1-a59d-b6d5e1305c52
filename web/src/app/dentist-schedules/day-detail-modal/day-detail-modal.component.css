/* Modal Styling */
.modal-backdrop {
  backdrop-filter: blur(2px);
}

/* Smooth Transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Card Hover Effects */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Status Icons */
.status-icon {
  font-size: 1rem;
  line-height: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .max-w-4xl {
    max-width: 95vw;
  }
  
  .p-6 {
    padding: 1rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }
}

/* Custom Scrollbar */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Focus States */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation for Modal */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bg-white.rounded-lg.shadow-xl {
  animation: fadeIn 0.2s ease-out;
}

/* Badge Styling */
.inline-flex.items-center.px-2\.5.py-0\.5.rounded-full {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Card Styling */
.bg-white.border.border-gray-200.rounded-lg {
  border: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.bg-gray-50.border.border-gray-200.rounded-lg {
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* Summary Cards */
.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

/* Text Colors */
.text-green-400 {
  color: #4ade80;
}

.text-red-400 {
  color: #f87171;
}

.text-blue-400 {
  color: #60a5fa;
}

.text-green-800 {
  color: #166534;
}

.text-red-800 {
  color: #991b1b;
}

.text-blue-800 {
  color: #1e40af;
}

.text-green-900 {
  color: #14532d;
}

.text-red-900 {
  color: #7f1d1d;
}

.text-blue-900 {
  color: #1e3a8a;
}

/* Border Colors */
.border-green-200 {
  border-color: #bbf7d0;
}

.border-red-200 {
  border-color: #fecaca;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

/* Exception Badge Colors */
.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-gray-600 {
  color: #4b5563;
}
