import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

interface CalendarDay {
  date: Date;
  dayNumber: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isWeekend: boolean;
  isHoliday: boolean;
  holiday?: any;
  dentistSchedules: DentistDaySchedule[];
}

interface DentistDaySchedule {
  dentist: any;
  hasRegularSchedule: boolean;
  hasException: boolean;
  exceptionType?: 'day-off' | 'custom-hours';
  timeSlots: { start: string; end: string }[];
  isAvailable: boolean;
}

@Component({
  selector: 'app-day-detail-modal',
  templateUrl: './day-detail-modal.component.html',
  styleUrls: ['./day-detail-modal.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class DayDetailModalComponent {
  @Input() isOpen = false;
  @Input() selectedDay: CalendarDay | null = null;
  @Output() closeModal = new EventEmitter<void>();

  constructor() {}

  close(): void {
    this.closeModal.emit();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }

  getFormattedDate(): string {
    if (!this.selectedDay) return '';
    
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    return this.selectedDay.date.toLocaleDateString('pt-BR', options);
  }

  getAvailableDentists(): DentistDaySchedule[] {
    if (!this.selectedDay) return [];
    return this.selectedDay.dentistSchedules.filter(ds => ds.isAvailable);
  }

  getUnavailableDentists(): DentistDaySchedule[] {
    if (!this.selectedDay) return [];
    return this.selectedDay.dentistSchedules.filter(ds => !ds.isAvailable);
  }

  formatTimeSlots(timeSlots: { start: string; end: string }[]): string {
    if (!timeSlots || timeSlots.length === 0) return 'Sem horários';
    
    return timeSlots
      .map(slot => `${slot.start} - ${slot.end}`)
      .join(', ');
  }

  getExceptionLabel(schedule: DentistDaySchedule): string {
    if (!schedule.hasException) return '';
    
    switch (schedule.exceptionType) {
      case 'day-off':
        return 'Folga';
      case 'custom-hours':
        return 'Horário Personalizado';
      default:
        return 'Exceção';
    }
  }

  getStatusClass(schedule: DentistDaySchedule): string {
    if (schedule.isAvailable) {
      return schedule.hasException ? 'text-blue-600' : 'text-green-600';
    } else {
      return 'text-red-600';
    }
  }

  getStatusIcon(schedule: DentistDaySchedule): string {
    if (schedule.isAvailable) {
      return schedule.hasException ? '🔄' : '✅';
    } else {
      return schedule.hasException ? '🚫' : '❌';
    }
  }

  getDayTypeLabel(): string {
    if (!this.selectedDay) return '';
    
    if (this.selectedDay.isHoliday) {
      return `Feriado - ${this.selectedDay.holiday?.name}`;
    }
    
    if (this.selectedDay.isWeekend) {
      return 'Domingo - Clínica Fechada';
    }
    
    return 'Dia Útil';
  }

  getDayTypeClass(): string {
    if (!this.selectedDay) return '';
    
    if (this.selectedDay.isHoliday) {
      return 'bg-red-100 text-red-800';
    }
    
    if (this.selectedDay.isWeekend) {
      return 'bg-gray-100 text-gray-600';
    }
    
    return 'bg-blue-100 text-blue-800';
  }
}
