<!-- <PERSON>dal Backdrop -->
<div
  *ngIf="isOpen"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  (click)="onBackdropClick($event)"
>
  <!-- Modal Content -->
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
    (click)="$event.stopPropagation()"
  >
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <div>
        <h3 class="text-xl font-semibold text-gray-900">
          Detalhes do Dia
        </h3>
        <p class="text-sm text-gray-600 mt-1">{{ getFormattedDate() }}</p>
        
        <!-- Day Type Badge -->
        <div class="mt-2">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            [class]="getDayTypeClass()"
          >
            {{ getDayTypeLabel() }}
          </span>
        </div>
      </div>
      
      <button
        type="button"
        (click)="close()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- Available Dentists -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">Disponíveis</p>
              <p class="text-2xl font-bold text-green-900">{{ getAvailableDentists().length }}</p>
            </div>
          </div>
        </div>

        <!-- Unavailable Dentists -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-red-800">Indisponíveis</p>
              <p class="text-2xl font-bold text-red-900">{{ getUnavailableDentists().length }}</p>
            </div>
          </div>
        </div>

        <!-- Total Dentists -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-blue-800">Total</p>
              <p class="text-2xl font-bold text-blue-900">{{ (selectedDay?.dentistSchedules || []).length }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Available Dentists Section -->
      <div *ngIf="getAvailableDentists().length > 0" class="mb-6">
        <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Dentistas Disponíveis
        </h4>
        
        <div class="space-y-3">
          <div
            *ngFor="let schedule of getAvailableDentists()"
            class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <h5 class="text-sm font-medium text-gray-900">{{ schedule.dentist.name }}</h5>
                  <span class="ml-2 text-xs" [class]="getStatusClass(schedule)">
                    {{ getStatusIcon(schedule) }}
                  </span>
                  <span
                    *ngIf="schedule.hasException"
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {{ getExceptionLabel(schedule) }}
                  </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ schedule.dentist.specialty || 'Dentista' }}</p>
                <div class="mt-2">
                  <p class="text-sm text-gray-700">
                    <strong>Horários:</strong> {{ formatTimeSlots(schedule.timeSlots) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unavailable Dentists Section -->
      <div *ngIf="getUnavailableDentists().length > 0">
        <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Dentistas Indisponíveis
        </h4>
        
        <div class="space-y-3">
          <div
            *ngFor="let schedule of getUnavailableDentists()"
            class="bg-gray-50 border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <h5 class="text-sm font-medium text-gray-700">{{ schedule.dentist.name }}</h5>
                  <span class="ml-2 text-xs" [class]="getStatusClass(schedule)">
                    {{ getStatusIcon(schedule) }}
                  </span>
                  <span
                    *ngIf="schedule.hasException"
                    class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800"
                  >
                    {{ getExceptionLabel(schedule) }}
                  </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ schedule.dentist.specialty || 'Dentista' }}</p>
                <div class="mt-2">
                  <p class="text-sm text-gray-600">
                    <strong>Motivo:</strong> 
                    <span *ngIf="schedule.hasException">{{ getExceptionLabel(schedule) }}</span>
                    <span *ngIf="!schedule.hasException && !schedule.hasRegularSchedule">Sem escala definida</span>
                    <span *ngIf="!schedule.hasException && schedule.hasRegularSchedule">Não trabalha neste dia</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="(selectedDay?.dentistSchedules || []).length === 0" class="text-center py-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum dentista encontrado</h3>
        <p class="text-gray-500">Não há informações de dentistas para este dia.</p>
      </div>
    </div>
  </div>
</div>
