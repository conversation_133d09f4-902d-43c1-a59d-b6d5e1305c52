<div class="bg-white shadow rounded-lg p-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Fluxo de Tratamento</h1>
  </div>

  <!-- Layout em duas colunas -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Coluna Esquerda: Formulário e Tabela -->
    <div class="flex flex-col space-y-6">
      <!-- Formulário para adicionar/editar fluxo -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">
          {{ editingId ? 'Editar' : 'Adicionar' }} Etapa do Fluxo
        </h2>
        <form [formGroup]="flowStepForm" (ngSubmit)="onSubmit()" class="space-y-4">
          <div class="flex flex-wrap items-end gap-4">
            <!-- Procedimento de origem -->
            <div class="flex-1 min-w-[200px]">
              <label for="fromProcedureId" class="block text-sm font-medium text-gray-700 mb-1">
                Origem *
              </label>
              <select
                id="fromProcedureId"
                formControlName="fromProcedureId"
                class="w-full bg-white border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-blue-300]="flowStepForm.get('fromProcedureId')?.value"
              >
                <option value="">Selecione</option>
                <option *ngFor="let procedure of procedures" [value]="procedure.id">
                  {{ procedure.name }}
                </option>
              </select>
            </div>

            <!-- Procedimento de destino -->
            <div class="flex-1 min-w-[200px]">
              <label for="toProcedureId" class="block text-sm font-medium text-gray-700 mb-1">
                Destino *
              </label>
              <select
                id="toProcedureId"
                formControlName="toProcedureId"
                class="w-full bg-white border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-blue-300]="flowStepForm.get('toProcedureId')?.value"
              >
                <option value="">Selecione</option>
                <option *ngFor="let procedure of procedures" [value]="procedure.id">
                  {{ procedure.name }}
                </option>
              </select>
            </div>

            <!-- Dias mínimos -->
            <div class="w-[120px]">
              <label for="minDaysAfter" class="block text-sm font-medium text-gray-700 mb-1">
                Dias *
              </label>
              <input
                type="number"
                id="minDaysAfter"
                formControlName="minDaysAfter"
                min="0"
                class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                [class.border-blue-300]="flowStepForm.get('minDaysAfter')?.value !== null && flowStepForm.get('minDaysAfter')?.value !== undefined"
              />
            </div>

            <!-- Observações -->
            <div class="flex-1 min-w-[200px]">
              <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
                Observações
              </label>
              <input
                type="text"
                id="notes"
                formControlName="notes"
                class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Botões do formulário -->
            <div class="flex space-x-2">
              <button
                *ngIf="editingId"
                type="button"
                (click)="cancelEdit()"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                type="submit"
                [disabled]="isSubmitting || !isFormValid"
                [class.bg-blue-600]="isFormValid"
                [class.bg-blue-400]="!isFormValid"
                [class.hover:bg-blue-700]="isFormValid"
                [class.cursor-not-allowed]="!isFormValid"
                class="px-4 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 relative group"
                [title]="!isFormValid ? 'Preencha os campos obrigatórios (Origem, Destino e Dias Mínimos)' : ''"
              >
                <!-- Tooltip personalizado -->
                <div *ngIf="!isFormValid" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 shadow-lg">
                  Preencha os campos obrigatórios (Origem, Destino e Dias)
                  <!-- Triângulo do tooltip -->
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
                <span *ngIf="isSubmitting" class="mr-2">
                  <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
                {{ editingId ? 'Atualizar' : 'Adicionar' }}
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Tabela de fluxos -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">Fluxos Configurados</h2>
      <div class="overflow-x-auto">
        <div *ngIf="isLoading" class="flex justify-center items-center py-8">
          <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>

        <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {{ error }}
        </div>

        <table *ngIf="!isLoading && !error && flowSteps.length > 0" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Procedimento de Origem
              </th>
              <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Procedimento de Destino
              </th>
              <th scope="col" class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Dias Mín.
              </th>
              <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Observações
              </th>
              <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Ações
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let flowStep of paginatedFlowSteps" class="hover:bg-gray-50">
              <td class="px-4 py-2 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getProcedureName(flowStep.fromProcedureId) }}</div>
              </td>
              <td class="px-4 py-2 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getProcedureName(flowStep.toProcedureId) }}</div>
              </td>
              <td class="px-4 py-2 whitespace-nowrap text-center">
                <div class="text-sm text-gray-900 font-medium">{{ flowStep.minDaysAfter }}</div>
              </td>
              <td class="px-4 py-2">
                <div class="text-sm text-gray-900 truncate max-w-xs">{{ flowStep.notes || '-' }}</div>
              </td>
              <td class="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                <button
                  (click)="editFlowStep(flowStep)"
                  class="text-blue-600 hover:text-blue-900 mr-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                </button>
                <button
                  (click)="deleteFlowStep(flowStep.id!)"
                  class="text-red-600 hover:text-red-900"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Paginação -->
        <div *ngIf="!isLoading && !error && flowSteps.length > pageSize" class="flex justify-between items-center mt-4 px-4 py-2 bg-white border-t border-gray-200">
          <div class="text-sm text-gray-700">
            Mostrando <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> a
            <span class="font-medium">{{ Math.min(currentPage * pageSize, flowSteps.length) }}</span> de
            <span class="font-medium">{{ flowSteps.length }}</span> resultados
          </div>
          <div class="flex space-x-2">
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1"
              [class.opacity-50]="currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Anterior
            </button>
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages"
              [class.opacity-50]="currentPage === totalPages"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Próxima
            </button>
          </div>
        </div>

        <div *ngIf="!isLoading && !error && flowSteps.length === 0" class="text-center py-8">
          <p class="text-gray-500">Nenhum fluxo de tratamento configurado.</p>
          <p class="text-gray-500 mt-2">Use o formulário acima para adicionar um novo fluxo.</p>
        </div>
      </div>
      </div>
    </div>

    <!-- Coluna Direita: Visualização do Grafo -->
    <div class="bg-gray-50 p-4 rounded-lg h-full">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Visualização do Fluxo</h2>
        <div class="flex space-x-2">
          <button
            (click)="centerGraph()"
            class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
            title="Centralizar grafo">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
            </svg>
          </button>
          <button
            (click)="zoomToFit()"
            class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
            title="Ajustar zoom">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
          <button
            (click)="updateGraph()"
            class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
            title="Atualizar layout">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          <button
            (click)="openFullScreenGraph()"
            class="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            title="Abrir em tela cheia">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mensagem quando não há dados -->
      <div *ngIf="!isLoading && (!nodes.length || !links.length)" class="flex flex-col items-center justify-center h-[600px] text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
        </svg>
        <p class="text-gray-500">Nenhum fluxo de tratamento configurado.</p>
        <p class="text-gray-500 mt-2">Adicione fluxos para visualizar o grafo.</p>
      </div>

      <!-- Loading spinner -->
      <div *ngIf="isLoading" class="flex justify-center items-center h-[600px]">
        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <!-- Visualização do grafo -->
      <div *ngIf="!isLoading && nodes.length && links.length" class="graph-container overflow-hidden relative">
        

        <!-- Mensagem de ajuda -->
        <div class="absolute top-4 left-4 bg-white bg-opacity-80 p-2 rounded shadow-sm text-xs text-gray-600 max-w-[200px]">
          <p>Arraste os nós para reorganizar o grafo. Use a roda do mouse para zoom.</p>
        </div>

        <ngx-graph
          class="chart-container"
          [nodes]="nodes"
          [links]="links"
          [update$]="update$"
          [center$]="center$"
          [autoZoom]="true"
          [autoCenter]="true"
          [layout]="layout"
          [layoutSettings]="layoutSettings"
          [curve]="curve"
          [enableZoom]="true"
          [draggingEnabled]="true"
          [panningEnabled]="true"
          (select)="onNodeSelect($event)"
        >
          <!-- Definições para o grafo -->
          <ng-template #defsTemplate>
            <svg:marker id="arrow" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="10" markerHeight="10" orient="auto">
              <svg:path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
            </svg:marker>
          </ng-template>

          <!-- Template para os nós -->
          <ng-template #nodeTemplate let-node>
            <svg:g class="node">
              <svg:rect
                [attr.width]="node.dimension.width"
                [attr.height]="node.dimension.height"
                [attr.rx]="8"
                [attr.ry]="8"
                class="node-rect"
                fill="#f0f9ff"
                stroke="#3b82f6"
                stroke-width="2"
              ></svg:rect>

              <!-- Usar foreignObject para permitir quebra de linha e melhor controle do texto -->
              <svg:foreignObject
                [attr.width]="node.dimension.width"
                [attr.height]="node.dimension.height"
                [attr.x]="0"
                [attr.y]="0"
              >
                <xhtml:div
                  style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; padding: 8px; box-sizing: border-box; text-align: center; overflow: hidden;"
                >
                  <xhtml:span
                    style="font-family: Arial, sans-serif; font-size: 12px; font-weight: 500; color: #1e40af; line-height: 1.3; word-break: break-word; overflow-wrap: break-word;"
                  >
                    {{ node.label }}
                  </xhtml:span>
                </xhtml:div>
              </svg:foreignObject>
            </svg:g>
          </ng-template>

          <!-- Template para as arestas -->
          <ng-template #linkTemplate let-link>
            <svg:g class="edge">
              <svg:path
                class="line"
                stroke="#64748b"
                stroke-width="1.5"
                marker-end="url(#arrow)"
                fill="none"
              ></svg:path>
            </svg:g>
          </ng-template>
        </ngx-graph>
      </div>
    </div>
  </div>
</div>

<!-- Modal de tela cheia para o grafo -->
<div *ngIf="showFullScreenModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
  <div class="bg-white rounded-lg shadow-xl w-[95vw] h-[90vh] flex flex-col">
    <!-- Cabeçalho do modal -->
    <div class="flex justify-between items-center p-4 border-b">
      <h3 class="text-lg font-semibold">Visualização do Fluxo de Tratamento</h3>
      <div class="flex space-x-2">
        <button
          (click)="centerGraph()"
          class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          title="Centralizar grafo">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
          </svg>
        </button>
        <button
          (click)="zoomToFit()"
          class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          title="Ajustar zoom">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
        <button
          (click)="updateGraph()"
          class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          title="Atualizar layout">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
        <button
          (click)="closeFullScreenGraph()"
          class="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
          title="Fechar">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Conteúdo do modal -->
    <div class="flex-1 p-4 relative">
      <!-- Visualização do grafo em tela cheia -->
      <div class="graph-container h-full w-full overflow-hidden relative">


        <!-- Mensagem de ajuda -->
        <div class="absolute top-4 left-4 bg-white bg-opacity-80 p-2 rounded shadow-sm text-xs text-gray-600 max-w-[200px]">
          <p>Arraste os nós para reorganizar o grafo. Use a roda do mouse para zoom.</p>
        </div>

        <ngx-graph
          class="chart-container"
          [nodes]="nodes"
          [links]="links"
          [update$]="update$"
          [center$]="center$"
          [autoZoom]="true"
          [autoCenter]="true"
          [layout]="layout"
          [layoutSettings]="layoutSettings"
          [curve]="curve"
          [enableZoom]="true"
          [draggingEnabled]="true"
          [panningEnabled]="true"
          (select)="onNodeSelect($event)"
        >
          <!-- Definições para o grafo -->
          <ng-template #defsTemplate>
            <svg:marker id="arrow-modal" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="10" markerHeight="10" orient="auto">
              <svg:path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
            </svg:marker>
          </ng-template>

          <!-- Template para os nós -->
          <ng-template #nodeTemplate let-node>
            <svg:g class="node">
              <svg:rect
                [attr.width]="node.dimension.width"
                [attr.height]="node.dimension.height"
                [attr.rx]="8"
                [attr.ry]="8"
                class="node-rect"
                fill="#f0f9ff"
                stroke="#3b82f6"
                stroke-width="2"
              ></svg:rect>

              <!-- Usar foreignObject para permitir quebra de linha e melhor controle do texto -->
              <svg:foreignObject
                [attr.width]="node.dimension.width"
                [attr.height]="node.dimension.height"
                [attr.x]="0"
                [attr.y]="0"
              >
                <xhtml:div
                  style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; padding: 8px; box-sizing: border-box; text-align: center; overflow: hidden;"
                >
                  <xhtml:span
                    style="font-family: Arial, sans-serif; font-size: 12px; font-weight: 500; color: #1e40af; line-height: 1.3; word-break: break-word; overflow-wrap: break-word;"
                  >
                    {{ node.label }}
                  </xhtml:span>
                </xhtml:div>
              </svg:foreignObject>
            </svg:g>
          </ng-template>

          <!-- Template para as arestas -->
          <ng-template #linkTemplate let-link>
            <svg:g class="edge">
              <svg:path
                class="line"
                stroke="#64748b"
                stroke-width="1.5"
                marker-end="url(#arrow-modal)"
                fill="none"
              ></svg:path>
            </svg:g>
          </ng-template>
        </ngx-graph>
      </div>
    </div>
  </div>
</div>
