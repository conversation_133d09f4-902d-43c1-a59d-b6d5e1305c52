import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TreatmentFlowService } from '../core/services/treatment-flow.service';
import { ProcedureService } from '../core/services/procedure.service';
import { TreatmentFlowStep } from '../core/models/treatment-flow-step.model';
import { Procedure } from '../core/models/procedure.model';
import { NotificationService } from '../core/services/notification.service';
import { finalize } from 'rxjs/operators';
import { NgxGraphModule, Edge, Node, ClusterNode } from '@swimlane/ngx-graph';
import { Subject } from 'rxjs';
import * as shape from 'd3-shape';

@Component({
  selector: 'app-treatment-flow',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, NgxGraphModule],
  templateUrl: './treatment-flow.component.html',
  styleUrls: ['./treatment-flow.component.scss']
})
export class TreatmentFlowComponent implements OnInit {
  flowSteps: TreatmentFlowStep[] = [];
  procedures: Procedure[] = [];
  flowStepForm: FormGroup;
  isLoading = true;
  isSubmitting = false;
  error: string | null = null;
  editingId: number | null = null;

  // Propriedades do grafo
  nodes: Node[] = [];
  links: Edge[] = [];
  clusters: ClusterNode[] = [];
  update$: Subject<boolean> = new Subject();
  center$: Subject<boolean> = new Subject();

  // Propriedades de paginação
  currentPage = 1;
  pageSize = 20;
  totalPages = 1;

  // Referência ao objeto Math para usar no template
  Math = Math;

  // Controle do modal de tela cheia
  showFullScreenModal = false;

  // Configurações do layout do grafo
  layout = 'dagre';
  layoutSettings = {
    orientation: 'TB', // Top to Bottom
    marginX: 50,
    marginY: 50,
    edgePadding: 30,
    rankPadding: 100,
    nodePadding: 80,
    ranker: 'network-simplex' // Algoritmo de layout
  };

  // Dimensões padrão para os nós
  nodeWidth = 180;
  nodeHeight = 60;

  // Dimensões mínimas para os nós
  minNodeWidth = 180;
  minNodeHeight = 60;

  // Fator de escala para o texto
  textScaleFactor = 8; // Aproximadamente 8px por caractere

  // Curva para as arestas
  curve = shape.curveBundle.beta(1);

  constructor(
    private fb: FormBuilder,
    private treatmentFlowService: TreatmentFlowService,
    private procedureService: ProcedureService,
    private notificationService: NotificationService
  ) {
    this.flowStepForm = this.fb.group({
      fromProcedureId: ['', [Validators.required]],
      toProcedureId: ['', [Validators.required]],
      minDaysAfter: [0, [Validators.required, Validators.min(0)]],
      notes: ['']
    });
  }

  ngOnInit(): void {
    this.loadProcedures();
    this.loadFlowSteps();

    // Inicializar o grafo após um breve atraso
    setTimeout(() => {
      this.updateGraph();
    }, 1000);
  }

  /**
   * Verifica se os campos obrigatórios do formulário estão preenchidos
   */
  get isFormValid(): boolean {
    const form = this.flowStepForm;
    return form.get('fromProcedureId')?.value &&
           form.get('toProcedureId')?.value &&
           form.get('minDaysAfter')?.value !== null &&
           form.get('minDaysAfter')?.value !== undefined;
  }

  loadProcedures(): void {
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.procedures = procedures;
        // Se já temos os fluxos, podemos atualizar o grafo
        if (this.flowSteps.length > 0) {
          this.updateGraphData();
        }
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.notificationService.error('Não foi possível carregar a lista de procedimentos.');
      }
    });
  }

  loadFlowSteps(): void {
    this.isLoading = true;
    this.treatmentFlowService.getAllFlowSteps()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (flowSteps) => {
          this.flowSteps = flowSteps;
          this.calculateTotalPages();
          this.updateGraphData();
        },
        error: (error) => {
          console.error('Erro ao carregar fluxos de tratamento:', error);
          this.error = 'Não foi possível carregar os fluxos de tratamento.';
        }
      });
  }

  /**
   * Atualiza os dados do grafo com base nos fluxos de tratamento
   */
  updateGraphData(): void {
    if (!this.procedures.length || !this.flowSteps.length) {
      return;
    }

    // Criar nós para cada procedimento único no fluxo
    const uniqueProcedureIds = new Set<number>();

    // Adicionar todos os procedimentos de origem e destino
    this.flowSteps.forEach(step => {
      uniqueProcedureIds.add(step.fromProcedureId);
      uniqueProcedureIds.add(step.toProcedureId);
    });

    // Limpar arrays existentes
    this.nodes = [];
    this.links = [];

    // Criar os nós
    this.nodes = Array.from(uniqueProcedureIds).map(id => {
      const procedure = this.procedures.find(p => p.id === id);
      const label = procedure ? procedure.name : `Procedimento ${id}`;

      // Calcular o tamanho do nó com base no texto
      const nodeSize = this.calculateNodeSize(label);

      return {
        id: id.toString(),
        label: label,
        data: {
          procedureId: id,
          name: label
        },
        position: {
          x: 0,
          y: 0
        },
        dimension: {
          width: nodeSize.width,
          height: nodeSize.height
        }
      };
    });

    // Criar as arestas
    this.links = this.flowSteps.map(step => {
      const daysLabel = step.minDaysAfter > 0 ? `${step.minDaysAfter} dias` : '';

      return {
        id: `edge-${step.id}`,
        source: step.fromProcedureId.toString(),
        target: step.toProcedureId.toString(),
        label: daysLabel,
        data: {
          minDaysAfter: step.minDaysAfter,
          notes: step.notes
        }
      };
    });

    // Log para depuração
    console.log('Nós:', this.nodes);
    console.log('Links:', this.links);

    // Atualizar o grafo com um atraso maior para garantir que o DOM esteja pronto
    setTimeout(() => {
      this.update$.next(true);
      this.center$.next(true);
    }, 500);
  }

  onSubmit(): void {
    if (this.flowStepForm.invalid) {
      this.flowStepForm.markAllAsTouched();
      return;
    }

    this.isSubmitting = true;
    const formValue = this.flowStepForm.value;

    // Converter os valores para os tipos corretos
    const flowStepData = {
      fromProcedureId: Number(formValue.fromProcedureId),
      toProcedureId: Number(formValue.toProcedureId),
      minDaysAfter: Number(formValue.minDaysAfter),
      notes: formValue.notes
    };

    // Verificar se os procedimentos são diferentes
    if (flowStepData.fromProcedureId === flowStepData.toProcedureId) {
      this.notificationService.error('Os procedimentos de origem e destino não podem ser iguais.');
      this.isSubmitting = false;
      return;
    }

    // Verificar se já existe um fluxo com os mesmos procedimentos (exceto o atual sendo editado)
    if (this.editingId) {
      const duplicateFlow = this.flowSteps.find(step =>
        step.id !== this.editingId &&
        step.fromProcedureId === flowStepData.fromProcedureId &&
        step.toProcedureId === flowStepData.toProcedureId
      );

      if (duplicateFlow) {
        this.notificationService.error('Já existe um fluxo configurado entre esses procedimentos.');
        this.isSubmitting = false;
        return;
      }
    }

    const saveObservable = this.editingId
      ? this.treatmentFlowService.updateFlowStep(this.editingId, flowStepData)
      : this.treatmentFlowService.createFlowStep(flowStepData);

    saveObservable
      .pipe(finalize(() => this.isSubmitting = false))
      .subscribe({
        next: () => {
          this.notificationService.success(
            this.editingId
              ? 'Fluxo de tratamento atualizado com sucesso!'
              : 'Fluxo de tratamento criado com sucesso!'
          );
          this.resetForm();
          this.loadFlowSteps();
          // Atualizar o grafo após um breve atraso para garantir que os dados foram carregados
          setTimeout(() => this.updateGraphData(), 300);
        },
        error: (error) => {
          console.error('Erro ao salvar fluxo de tratamento:', error);

          // Verificar se é um erro de duplicação
          if (error.error?.message?.includes('existe um fluxo configurado')) {
            this.notificationService.error('Já existe um fluxo configurado entre esses procedimentos.');
          }
          // Verificar se é um erro de procedimento não encontrado
          else if (error.error?.message?.includes('Procedimento com ID')) {
            this.notificationService.error('Um dos procedimentos selecionados não foi encontrado.');
          }
          // Verificar se é um erro de procedimentos iguais
          else if (error.error?.message?.includes('não podem ser iguais')) {
            this.notificationService.error('Os procedimentos de origem e destino não podem ser iguais.');
          }
          // Mensagem genérica para outros erros
          else {
            this.notificationService.error(
              'Erro ao salvar fluxo de tratamento. Tente novamente ou entre em contato com o suporte.'
            );

            // Se for uma atualização que falhou, tentar recuperar os dados originais
            if (this.editingId) {
              this.tryRecoverOriginalData(this.editingId);
            }
          }
        }
      });
  }

  editFlowStep(flowStep: TreatmentFlowStep): void {
    this.editingId = flowStep.id!;
    this.flowStepForm.patchValue({
      fromProcedureId: flowStep.fromProcedureId.toString(),
      toProcedureId: flowStep.toProcedureId.toString(),
      minDaysAfter: flowStep.minDaysAfter,
      notes: flowStep.notes || ''
    });
  }

  deleteFlowStep(id: number): void {
    if (confirm('Tem certeza que deseja excluir este fluxo de tratamento?')) {
      this.treatmentFlowService.deleteFlowStep(id).subscribe({
        next: () => {
          this.notificationService.success('Fluxo de tratamento excluído com sucesso!');
          this.loadFlowSteps();
          // Atualizar o grafo após um breve atraso para garantir que os dados foram carregados
          setTimeout(() => this.updateGraphData(), 300);
        },
        error: (error) => {
          console.error('Erro ao excluir fluxo de tratamento:', error);
          this.notificationService.error('Erro ao excluir fluxo de tratamento.');
        }
      });
    }
  }

  resetForm(): void {
    this.flowStepForm.reset({
      fromProcedureId: '',
      toProcedureId: '',
      minDaysAfter: 0,
      notes: ''
    });
    this.editingId = null;
  }

  cancelEdit(): void {
    this.resetForm();
  }

  getProcedureName(id: number): string {
    const procedure = this.procedures.find(p => p.id === id);
    return procedure ? procedure.name : 'Procedimento não encontrado';
  }

  /**
   * Centraliza o grafo
   */
  centerGraph(): void {
    this.center$.next(true);
  }

  /**
   * Ajusta o zoom do grafo para exibir todos os nós
   * (Usa o center$ pois o zoomToFit$ não está funcionando corretamente)
   */
  zoomToFit(): void {
    this.center$.next(true);
  }

  /**
   * Atualiza o layout do grafo
   */
  updateGraph(): void {
    this.update$.next(true);
  }

  /**
   * Manipula o clique em um nó do grafo
   */
  onNodeSelect(node: Node): void {
    console.log('Nó selecionado:', node);
    // Aqui você pode implementar alguma ação ao clicar em um nó, se necessário
  }

  /**
   * Aumenta o zoom do grafo
   */
  zoomIn(): void {
    // Emite um evento para aumentar o zoom
    // Como não temos acesso direto ao zoom do ngx-graph, usamos o update$ para forçar uma atualização
    this.update$.next(true);
  }

  /**
   * Diminui o zoom do grafo
   */
  zoomOut(): void {
    // Emite um evento para diminuir o zoom
    // Como não temos acesso direto ao zoom do ngx-graph, usamos o update$ para forçar uma atualização
    this.update$.next(true);
  }

  /**
   * Retorna os fluxos para a página atual
   */
  get paginatedFlowSteps(): TreatmentFlowStep[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.flowSteps.slice(startIndex, endIndex);
  }

  /**
   * Calcula o número total de páginas
   */
  calculateTotalPages(): void {
    this.totalPages = Math.ceil(this.flowSteps.length / this.pageSize);
    if (this.totalPages === 0) {
      this.totalPages = 1;
    }

    // Ajustar a página atual se necessário
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  /**
   * Vai para a página anterior
   */
  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  /**
   * Vai para a próxima página
   */
  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  /**
   * Abre o grafo em tela cheia em um modal
   */
  openFullScreenGraph(): void {
    this.showFullScreenModal = true;

    // Atualizar o grafo após um breve atraso para garantir que o DOM esteja pronto
    setTimeout(() => {
      this.update$.next(true);
      this.center$.next(true);
    }, 300);
  }

  /**
   * Fecha o modal de tela cheia
   */
  closeFullScreenGraph(): void {
    this.showFullScreenModal = false;
  }

  /**
   * Tenta recuperar os dados originais de um fluxo após uma falha na atualização
   * @param id ID do fluxo a ser recuperado
   */
  tryRecoverOriginalData(id: number): void {
    this.treatmentFlowService.getFlowStep(id).subscribe({
      next: (flowStep) => {
        // Atualizar o formulário com os dados originais
        this.flowStepForm.patchValue({
          fromProcedureId: flowStep.fromProcedureId.toString(),
          toProcedureId: flowStep.toProcedureId.toString(),
          minDaysAfter: flowStep.minDaysAfter,
          notes: flowStep.notes || ''
        });
        this.notificationService.info('Os dados originais foram recuperados. Tente fazer suas alterações novamente.');
      },
      error: () => {
        // Se não conseguir recuperar, apenas resetar o formulário
        this.resetForm();
        this.notificationService.info('Não foi possível recuperar os dados originais. O formulário foi resetado.');
      }
    });
  }

  /**
   * Calcula o tamanho necessário para o nó com base no texto
   * @param text Texto a ser exibido no nó
   * @returns Objeto com largura e altura calculadas
   */
  calculateNodeSize(text: string): { width: number, height: number } {
    if (!text) {
      return { width: this.minNodeWidth, height: this.minNodeHeight };
    }

    // Dividir o texto em linhas (se tiver quebras de linha)
    const lines = text.split('\n');

    // Encontrar a linha mais longa
    const maxLineLength = Math.max(...lines.map(line => line.length));

    // Calcular a largura com base no comprimento do texto
    // Adicionar um padding para garantir que o texto não fique muito próximo das bordas
    const calculatedWidth = Math.max(maxLineLength * this.textScaleFactor + 40, this.minNodeWidth);

    // Calcular a altura com base no número de linhas
    // Se o texto for muito longo, considerar quebras de linha automáticas
    const estimatedLines = lines.length > 1 ? lines.length : Math.ceil(text.length / (calculatedWidth / this.textScaleFactor));
    const calculatedHeight = Math.max(estimatedLines * 20 + 20, this.minNodeHeight);

    return {
      width: calculatedWidth,
      height: calculatedHeight
    };
  }
}
