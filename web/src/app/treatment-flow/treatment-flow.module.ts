import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { TreatmentFlowComponent } from './treatment-flow.component';
import { NgxGraphModule } from '@swimlane/ngx-graph';

const routes: Routes = [
  {
    path: '',
    component: TreatmentFlowComponent
  }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgxGraphModule,
    RouterModule.forChild(routes),
    TreatmentFlowComponent
  ]
})
export class TreatmentFlowModule { }
