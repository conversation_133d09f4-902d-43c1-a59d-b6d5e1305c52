/* Estilos específicos para o componente de fluxo de tratamento */

/* Estilos para o container do grafo */
.graph-container {
  width: 100%;
  height: 100%;
  border-radius: 0.375rem;
  overflow: hidden;

  ::ng-deep {
    .chart-container {
      width: 100%;
    }

    /* Estilos para os nós */
    .node {
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      .node-rect {
        transition: fill 0.2s ease, stroke 0.2s ease;
        filter: drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1));
      }

      &:hover .node-rect {
        fill: #dbeafe;
        stroke: #2563eb;
        stroke-width: 2.5px;
        filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.15));
      }

      foreignObject {
        overflow: visible;

        div {
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          height: 100%;
          width: 100%;

          span {
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            font-family: Arial, sans-serif;
            font-size: 12px;
            font-weight: 500;
            color: #1e40af;
            line-height: 1.3;
          }
        }
      }
    }

    /* Estilos para as arestas */
    .edge {
      .line {
        transition: stroke 0.2s ease, stroke-width 0.2s ease;
        stroke-dasharray: none;
      }

      &:hover .line {
        stroke: #3b82f6;
        stroke-width: 2px;
      }

      .edge-label-container {
        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.05));
      }

      .edge-label {
        font-family: 'Arial', sans-serif;
        font-size: 11px;
        font-weight: 500;
        pointer-events: none;
      }
    }

    /* Estilos para o zoom e pan */
    .graph {
      .zoom-buttons {
        position: absolute;
        bottom: 16px;
        right: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }
}

/* Estilos para os botões de controle do grafo */
button {
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  &:hover {
    background-color: #2b5fc7;
  }

  &:active {
    background-color: #1a60c9;
  }
}
