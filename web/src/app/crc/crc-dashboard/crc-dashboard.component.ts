import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SchedulingService } from '../../core/services/scheduling.service';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { PatientService } from '../../core/services/patient.service';
import { forkJoin } from 'rxjs';
import { Scheduling } from '../../core/models/scheduling.model';
import { PaginatedResponse } from '../../core/models/pagination.model';

// Interface para o modelo Treatment
interface Treatment {
  id: number;
  name: string;
  patientId: number;
  patientName?: string;
  dentistId: number;
  dentistName?: string;
  status: string;
  createdAt?: Date;
  cost?: number;
  paid?: boolean;
  notes?: string;
  [key: string]: any; // Para propriedades adicionais
}

@Component({
  selector: 'app-crc-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './crc-dashboard.component.html',
  styleUrl: './crc-dashboard.component.scss',
})
export class CrcDashboardComponent implements OnInit {
  // Contadores para os cards
  unconfirmedSchedulingsCount = 0;
  birthdaysThisMonthCount = 0;
  unscheduledCount = 0;
  missedSchedulingsCount = 0;
  // missedTreatmentsCount removido conforme solicitado
  unpaidSchedulingsCount = 0;
  unpaidTreatmentsCount = 0;

  isLoading = true;

  constructor(
    private schedulingService: SchedulingService,
    private treatmentPlanService: TreatmentPlanService,
    private patientService: PatientService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;

    // Carregar todos os dados necessários
    forkJoin({
      schedulings: this.schedulingService.getAllSchedulings(),
      treatmentPlans: this.treatmentPlanService.getAllTreatmentPlans(),
      patients: this.patientService.getAllPatients(),
    }).subscribe({
      next: (result) => {
        const today = new Date();
        const currentMonth = today.getMonth();

        // Processar os agendamentos (pode ser array ou objeto paginado)
        let schedulings: Scheduling[] = [];

        if (Array.isArray(result.schedulings)) {
          schedulings = result.schedulings;
        } else if (
          result.schedulings &&
          typeof result.schedulings === 'object' &&
          'data' in result.schedulings
        ) {
          schedulings = (result.schedulings as PaginatedResponse<Scheduling>)
            .data;
        }

        // Agendamentos não confirmados
        this.unconfirmedSchedulingsCount = schedulings.filter(
          (s: Scheduling) => s.status === 'scheduled-unconfirmed'
        ).length;

        // Aniversariantes do mês
        this.birthdaysThisMonthCount = result.patients.filter(
          (p) =>
            p.birthDate && new Date(p.birthDate).getMonth() === currentMonth
        ).length;

        // Agendamentos desmarcados
        this.unscheduledCount = schedulings.filter(
          (s: Scheduling) => s.status === 'unscheduled'
        ).length;

        // Faltas em agendamentos (agendados confirmados com data anterior à atual)
        this.missedSchedulingsCount = schedulings.filter(
          (s: Scheduling) =>
            s.status === 'scheduled-confirmed' && new Date(s.date) < today
        ).length;

        // Código para faltas em tratamentos removido conforme solicitado

        // Agendamentos inadimplentes (concluídos mas não pagos)
        this.unpaidSchedulingsCount = schedulings.filter(
          (s: Scheduling) => s.status === 'completed' && !s.paid
        ).length;

        // Extrair procedimentos de todos os planos
        const procedures = result.treatmentPlans.flatMap((plan: any) =>
          (plan.procedures || []).map((proc: any) => ({
            id: proc.id,
            name: proc.name,
            patientId: plan.patientId,
            patientName: plan.patient?.name,
            dentistId: proc.professionalId,
            dentistName: proc.professional?.name,
            status: proc.status,
            createdAt: proc.createdAt,
            cost: proc.value,
            paid: proc.status === 'completed',
            notes: proc.notes
          }))
        );

        // Tratamentos inadimplentes (concluídos mas não pagos)
        this.unpaidTreatmentsCount = procedures.filter(
          (t: Treatment) => t.status === 'completed' && !t.paid
        ).length;

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar dados para o dashboard CRC:', error);
        this.isLoading = false;
      },
    });
  }

  // Métodos para navegação ao clicar nos cards
  navigateToUnconfirmedSchedulings(): void {
    this.router.navigate(['/schedulings'], {
      queryParams: { status: 'scheduled-unconfirmed' },
    });
  }

  navigateToUnscheduled(): void {
    this.router.navigate(['/schedulings'], {
      queryParams: { status: 'unscheduled' },
    });
  }

  navigateToMissedSchedulings(): void {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const formattedDate = this.formatDateForUrl(yesterday);

    this.router.navigate(['/schedulings'], {
      queryParams: {
        status: 'scheduled-confirmed',
        endDate: formattedDate,
      },
    });
  }

  // Método navigateToMissedTreatments removido conforme solicitado

  navigateToUnpaidSchedulings(): void {
    this.router.navigate(['/schedulings'], {
      queryParams: {
        status: 'completed',
        paid: 'false',
      },
    });
  }

  navigateToUnpaidTreatments(): void {
    this.router.navigate(['/treatments'], {
      queryParams: {
        status: 'completed',
        paid: 'false',
      },
    });
  }

  private formatDateForUrl(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
