import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Procedure,
  ProcedureType,
  Status,
  ProcedureTypeLabels,
  StatusLabels
} from '../../core/models/procedure.model';
import { ProcedureService } from '../../core/services/procedure.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-procedure-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './procedure-form.component.html',
  styleUrls: ['./procedure-form.component.scss'],
})
export class ProcedureFormComponent implements OnInit, OnChanges {
  @Input() procedure: Procedure | null = null;
  @Output() saved = new EventEmitter<Procedure>();
  @Output() cancelled = new EventEmitter<void>();

  procedureForm: FormGroup;
  isSubmitting = false;
  error: string | null = null;

  // Enums para o template
  procedureTypes = Object.values(ProcedureType);
  statusOptions = Object.values(Status);

  // Labels para exibição
  procedureTypeLabels = ProcedureTypeLabels;
  statusLabels = StatusLabels;

  // Propriedade para armazenar o valor formatado do preço
  formattedPrice: string = '0,00';

  constructor(
    private fb: FormBuilder,
    private procedureService: ProcedureService
  ) {
    this.procedureForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      description: [''],
      defaultPrice: [0, [Validators.required, Validators.min(0)]],
      estimatedDuration: [30, [Validators.required, Validators.min(1)]],
      type: [ProcedureType.CLINICAL, [Validators.required]],
      status: [Status.ACTIVE, [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.resetForm();
  }

  /**
   * Formata um valor numérico para o formato de moeda brasileira
   */
  formatCurrency(value: number): string {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * Processa a entrada do usuário no campo de moeda
   */
  onPriceInput(value: string): void {
    // Remove todos os caracteres não numéricos
    let numericValue = value.replace(/\D/g, '');

    // Converte para número (em centavos)
    let cents = parseInt(numericValue, 10);

    // Se não for um número válido, define como zero
    if (isNaN(cents)) {
      cents = 0;
    }

    // Converte centavos para o formato de moeda (divide por 100 para obter o valor real)
    const realValue = cents / 100;

    // Formata o valor como moeda brasileira
    this.formattedPrice = realValue.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    // Atualiza o valor no formulário
    this.procedureForm.get('defaultPrice')?.setValue(realValue);
  }

  // Propriedade para controlar o estado do switch
  get isActive(): boolean {
    return this.statusControl?.value === Status.ACTIVE;
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Limpar erros anteriores
    this.error = null;

    if (changes['procedure']) {
      // Resetar o formulário para limpar todos os estados de validação
      this.procedureForm.reset({
        name: '',
        description: '',
        defaultPrice: 0,
        estimatedDuration: 30,
        type: ProcedureType.CLINICAL,
        status: Status.ACTIVE,
      });

      // Se tiver um procedimento, preencher o formulário com seus dados
      if (this.procedure) {
        this.procedureForm.patchValue({
          name: this.procedure.name,
          description: this.procedure.description || '',
          defaultPrice: this.procedure.defaultPrice,
          estimatedDuration: this.procedure.estimatedDuration,
          type: this.procedure.type,
          status: this.procedure.status,
        });

        // Atualiza o valor formatado
        this.formattedPrice = this.formatCurrency(this.procedure.defaultPrice);
      } else {
        // Se não tiver procedimento, manter o formulário resetado
        this.formattedPrice = '0,00';
      }

      // Limpar estados de validação
      Object.keys(this.procedureForm.controls).forEach(key => {
        const control = this.procedureForm.get(key);
        control?.markAsUntouched();
        control?.markAsPristine();
      });
    }
  }

  // Método para alternar o status entre ativo e inativo
  toggleStatus(): void {
    const newStatus = this.isActive ? Status.INACTIVE : Status.ACTIVE;
    this.procedureForm.get('status')?.setValue(newStatus);
  }

  resetForm(): void {
    this.procedureForm.reset({
      name: '',
      description: '',
      defaultPrice: 0,
      estimatedDuration: 30,
      type: ProcedureType.CLINICAL,
      status: Status.ACTIVE,
    });
    this.formattedPrice = '0,00';
    this.error = null;
  }

  /**
   * Método público para submeter o formulário
   */
  async onSubmit(): Promise<void> {
    if (this.procedureForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.procedureForm.controls).forEach((key) => {
        const control = this.procedureForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    this.error = null;

    try {
      const formData = this.procedureForm.value;
      let savedProcedure: Procedure;

      if (this.procedure) {
        // Atualizar procedimento existente
        savedProcedure = await firstValueFrom(
          this.procedureService.updateProcedure(
            this.procedure.id,
            formData
          )
        );
      } else {
        // Criar novo procedimento
        savedProcedure = await firstValueFrom(
          this.procedureService.createProcedure(formData)
        );
      }

      this.saved.emit(savedProcedure);
      this.resetForm();
    } catch (err: any) {
      console.error('Erro ao salvar procedimento:', err);

      // Verificar se é um erro de validação com mensagens específicas
      if (err.error?.message && Array.isArray(err.error.message)) {
        // Juntar todas as mensagens de erro em uma única string
        this.error = err.error.message.join(', ');
      } else if (err.error?.message) {
        // Mensagem de erro única
        this.error = err.error.message;
      } else {
        // Mensagem genérica
        this.error = 'Ocorreu um erro ao salvar o procedimento. Por favor, tente novamente.';
      }
    } finally {
      this.isSubmitting = false;
    }
  }

  onCancel(): void {
    this.cancelled.emit();
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get nameControl() {
    return this.procedureForm.get('name');
  }
  get descriptionControl() {
    return this.procedureForm.get('description');
  }
  get defaultPriceControl() {
    return this.procedureForm.get('defaultPrice');
  }
  get estimatedDurationControl() {
    return this.procedureForm.get('estimatedDuration');
  }
  get typeControl() {
    return this.procedureForm.get('type');
  }

  get statusControl() {
    return this.procedureForm.get('status');
  }
}
