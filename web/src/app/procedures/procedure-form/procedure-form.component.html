<div class="p-6">
  <form [formGroup]="procedureForm" (ngSubmit)="onSubmit()" #formElement="ngForm">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Nome do procedimento e Status -->
      <div class="md:col-span-3">
        <div class="flex justify-between md:items-center gap-6 flex-col-reverse md:flex-row">
          <div class="w-full">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
            Nome <span class="text-red-500">*</span>
          </label>
          <input
          type="text"
          id="name"
          formControlName="name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              nameControl?.invalid && nameControl?.touched
          }"
        />
          </div>

          <!-- Status (Switch) -->
          <div class="flex items-center">
            <label class="inline-flex items-center cursor-pointer">
              <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
              <div class="flex gap-1 items-center">
                <input
                type="checkbox"
                [checked]="isActive"
                (change)="toggleStatus()"
                class="sr-only peer"
              />
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
              ></div>
              <span class="ms-3 text-sm font-medium text-gray-700">
                {{ isActive ? 'Ativo' : 'Inativo' }}
              </span>
              </div>

            </label>
          </div>
        </div>


        <div
          *ngIf="nameControl?.invalid && nameControl?.touched"
          class="mt-1 text-sm text-red-600"
        >
          <div *ngIf="nameControl?.errors?.['required']">
            O nome do procedimento é obrigatório.
          </div>
          <div *ngIf="nameControl?.errors?.['minlength']">
            O nome deve ter pelo menos 2 caracteres.
          </div>
          <div *ngIf="nameControl?.errors?.['maxlength']">
            O nome deve ter no máximo 100 caracteres.
          </div>
        </div>
      </div>

      <!-- Descrição -->
      <div class="md:col-span-3">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
          Descrição
        </label>
        <textarea
          id="description"
          formControlName="description"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <!-- Tipo de procedimento -->
      <div class="col-span-1">
        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
          Tipo <span class="text-red-500">*</span>
        </label>
        <select
          id="type"
          formControlName="type"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              typeControl?.invalid && typeControl?.touched
          }"
        >
          <option *ngFor="let type of procedureTypes" [value]="type">
            {{ procedureTypeLabels[type] }}
          </option>
        </select>
        <div
          *ngIf="typeControl?.invalid && typeControl?.touched"
          class="mt-1 text-sm text-red-600"
        >
          <div *ngIf="typeControl?.errors?.['required']">
            O tipo de procedimento é obrigatório.
          </div>
        </div>
      </div>

      <!-- Valor padrão -->
      <div class="col-span-1">
        <label for="defaultPrice" class="block text-sm font-medium text-gray-700 mb-1">
          R$ Valor Padrão <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <input
            type="text"
            id="defaultPrice"
            #priceInput
            [value]="formattedPrice"
            (input)="onPriceInput(priceInput.value)"
            placeholder="0,00"
            class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            [ngClass]="{
              'border-red-300 focus:border-red-500 focus:ring-red-500':
                defaultPriceControl?.invalid && defaultPriceControl?.touched
            }"
          />
        </div>
        <div
          *ngIf="defaultPriceControl?.invalid && defaultPriceControl?.touched"
          class="mt-1 text-sm text-red-600"
        >
          <div *ngIf="defaultPriceControl?.errors?.['required']">
            O valor padrão é obrigatório.
          </div>
          <div *ngIf="defaultPriceControl?.errors?.['min']">
            O valor padrão não pode ser negativo.
          </div>
        </div>
      </div>

      <!-- Duração estimada -->
      <div class="col-span-1">
        <label for="estimatedDuration" class="block text-sm font-medium text-gray-700 mb-1">
          Duração (minutos) <span class="text-red-500">*</span>
        </label>
        <input
          type="number"
          id="estimatedDuration"
          formControlName="estimatedDuration"
          min="1"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          [ngClass]="{
            'border-red-300 focus:border-red-500 focus:ring-red-500':
              estimatedDurationControl?.invalid && estimatedDurationControl?.touched
          }"
        />
        <div
          *ngIf="estimatedDurationControl?.invalid && estimatedDurationControl?.touched"
          class="mt-1 text-sm text-red-600"
        >
          <div *ngIf="estimatedDurationControl?.errors?.['required']">
            A duração estimada é obrigatória.
          </div>
          <div *ngIf="estimatedDurationControl?.errors?.['min']">
            A duração estimada deve ser maior que zero.
          </div>
        </div>
      </div>

    </div>

    <!-- Mensagem de erro -->
    <div *ngIf="error" class="mt-4 p-4 bg-red-50 border border-red-300 text-red-700 rounded-md flex items-start">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 mt-0.5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <div>{{ error }}</div>
    </div>
  </form>
</div>
