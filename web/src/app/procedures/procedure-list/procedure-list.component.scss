/* Animações personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* Estilização da paginação */
.pagination-controls {
  button {
    transition: all 0.2s ease-in-out;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }
}

/* Estilização da tabela */
.overflow-x-auto {
  transition: opacity 0.3s ease-in-out;
}

/* Estilização do seletor de itens por página */
select {
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #3b82f6;
  }
}

/* Estilização dos botões de filtro */
button.rounded-full {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

/* Estilização do modal de procedimentos */
:host ::ng-deep .procedure-modal {
  .bg-white {
    width: 100%;
    max-width: 640px;
    margin: 0 auto;

    @media (max-width: 768px) {
      max-width: 95%;
      margin: 0 auto;
    }

    @media (max-height: 700px) {
      max-height: 95vh;
    }
  }

  /* Garantir que o modal não fique muito grande em telas pequenas */
  .modal-content-scroll {
    max-height: calc(80vh - 80px);

    @media (max-height: 600px) {
      max-height: calc(90vh - 80px);
    }
  }

  /* Ajustar o footer para garantir que os botões não sejam cortados */
  [footer] {

    .action-button {
      white-space: nowrap;
      min-width: 120px;
      width: 120px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }
}
