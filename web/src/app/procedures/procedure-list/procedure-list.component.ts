import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import {
  Procedure,
  ProcedureType,
  Status,
  ProcedureTypeLabels,
  StatusLabels
} from '../../core/models/procedure.model';
import { ProcedureService } from '../../core/services/procedure.service';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { ProcedureFormComponent } from '../procedure-form/procedure-form.component';
import { ModalComponent } from '../../shared/components/modal/modal.component';

@Component({
  selector: 'app-procedure-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ProcedureFormComponent,
    ModalComponent,
  ],
  templateUrl: './procedure-list.component.html',
  styleUrls: ['./procedure-list.component.scss'],
})
export class ProcedureListComponent implements OnInit {
  procedures: Procedure[] = [];
  isLoading = true;
  error: string | null = null;
  searchTerm: string = '';

  // Filtros
  selectedType: ProcedureType | '' = '';
  selectedStatus: Status | '' = '';

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  // Modal
  isModalOpen = false;
  modalTitle = '';
  selectedProcedure: Procedure | null = null;

  // Referência ao componente de formulário
  @ViewChild('procedureForm') procedureFormComponent!: ProcedureFormComponent;

  // Enums para o template
  procedureTypes = Object.values(ProcedureType);
  statusOptions = Object.values(Status);

  // Labels para exibição
  procedureTypeLabels = ProcedureTypeLabels;
  statusLabels = StatusLabels;

  // Expose Math to the template
  Math = Math;

  constructor(private procedureService: ProcedureService) {}

  ngOnInit(): void {
    this.loadProcedures();
  }

  loadProcedures(page: number = 1): void {
    // Adiciona uma pequena animação de fade-out antes de carregar novos dados
    const tableContent = document.querySelector('.overflow-x-auto');
    if (tableContent) {
      tableContent.classList.add(
        'opacity-60',
        'transition-opacity',
        'duration-300'
      );
    }

    this.isLoading = true;
    this.error = null;

    this.procedureService
      .getProcedures(
        page,
        this.itemsPerPage,
        this.searchTerm,
        this.selectedType || undefined,
        this.selectedStatus || undefined
      )
      .pipe(
        finalize(() => {
          this.isLoading = false;
          // Restaura a opacidade com uma pequena animação de fade-in
          setTimeout(() => {
            if (tableContent) {
              tableContent.classList.remove('opacity-60');
            }
          }, 100);
        })
      )
      .subscribe({
        next: (response: PaginatedResponse<Procedure>) => {
          this.procedures = response.data;
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },
        error: (err) => {
          console.error('Erro ao carregar procedimentos:', err);
          this.error =
            'Não foi possível carregar a lista de procedimentos. Por favor, tente novamente mais tarde.';
        },
      });
  }

  searchProcedures(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.currentPage = 1; // Voltar para a primeira página ao pesquisar
    this.loadProcedures(this.currentPage);
  }

  filterByType(type: ProcedureType | ''): void {
    this.selectedType = type;
    this.currentPage = 1;
    this.loadProcedures(this.currentPage);
  }

  // Método removido - categoria não é mais usada

  filterByStatus(status: Status | ''): void {
    this.selectedStatus = status;
    this.currentPage = 1;
    this.loadProcedures(this.currentPage);
  }

  toggleActiveFilter(): void {
    this.selectedStatus = this.selectedStatus === Status.ACTIVE ? '' : Status.ACTIVE;
    this.currentPage = 1;
    this.loadProcedures(this.currentPage);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedType = '';
    this.selectedStatus = '';
    this.currentPage = 1;
    this.loadProcedures(this.currentPage);
  }

  openNewProcedureModal(): void {
    this.selectedProcedure = null;
    this.modalTitle = 'Novo Procedimento';
    this.isModalOpen = true;
  }

  openEditProcedureModal(procedure: Procedure): void {
    this.selectedProcedure = procedure;
    this.modalTitle = 'Editar Procedimento';
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    // Aguardar a animação de fechamento do modal antes de resetar o procedimento selecionado
    setTimeout(() => {
      this.selectedProcedure = null;
      // Resetar o formulário se o componente estiver disponível
      if (this.procedureFormComponent) {
        this.procedureFormComponent.resetForm();
      }
    }, 300);
  }

  onProcedureSaved(): void {
    this.closeModal();
    this.loadProcedures(this.currentPage);
  }

  // Método para submeter o formulário
  submitProcedureForm(): void {
    if (this.procedureFormComponent) {
      this.procedureFormComponent.onSubmit();
    }
  }

  // Getter para verificar se o formulário está sendo submetido
  get isFormSubmitting(): boolean {
    return this.procedureFormComponent ? this.procedureFormComponent.isSubmitting : false;
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   * Inclui a página atual, algumas páginas adjacentes e elipses para páginas distantes
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5; // Número máximo de páginas visíveis (sem contar elipses)

    if (this.totalPages <= maxVisiblePages) {
      // Se o total de páginas for menor ou igual ao máximo visível, mostra todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar
      let startPage = Math.max(2, this.currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(this.totalPages - 1, startPage + maxVisiblePages - 3);

      // Ajustar se estiver próximo do início
      if (startPage === 2) {
        endPage = Math.min(this.totalPages - 1, maxVisiblePages - 1);
      }

      // Ajustar se estiver próximo do fim
      if (endPage === this.totalPages - 1) {
        startPage = Math.max(2, this.totalPages - maxVisiblePages + 2);
      }

      // Adicionar elipses antes do intervalo se necessário
      if (startPage > 2) {
        pages.push('...');
      }

      // Adicionar páginas do intervalo
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Adicionar elipses se necessário
      if (endPage < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  /**
   * Altera o número de itens por página e recarrega os dados
   */
  changeItemsPerPage(): void {
    // Voltar para a primeira página ao mudar o número de itens por página
    this.currentPage = 1;
    this.loadProcedures(this.currentPage);
  }

  goToPage(page: number | string): void {
    // Se for uma string (como '...'), não faz nada
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadProcedures(this.currentPage);
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }
}
