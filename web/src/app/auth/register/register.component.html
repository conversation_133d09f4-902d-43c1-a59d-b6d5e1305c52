<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900"><PERSON><PERSON><PERSON></h2>
      <p class="mt-2 text-sm text-gray-600">Cadastre-se para acessar o sistema</p>
    </div>

    <form class="mt-8 space-y-6" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm space-y-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Nome completo</label>
          <input id="name" name="name" type="text" formControlName="name" required
                 class="appearance-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm mt-1"
                 placeholder="Nome completo">
          <div *ngIf="registerForm.get('name')?.invalid && registerForm.get('name')?.touched"
               class="text-red-500 text-xs mt-1">
            <span *ngIf="registerForm.get('name')?.errors?.['required']">Nome é obrigatório</span>
          </div>
        </div>

        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">E-mail</label>
          <input id="email" name="email" type="email" formControlName="email" required
                 class="appearance-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm mt-1"
                 placeholder="E-mail">
          <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
               class="text-red-500 text-xs mt-1">
            <span *ngIf="registerForm.get('email')?.errors?.['required']">E-mail é obrigatório</span>
            <span *ngIf="registerForm.get('email')?.errors?.['email']">E-mail inválido</span>
          </div>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Senha</label>
          <input id="password" name="password" type="password" formControlName="password" required
                 class="appearance-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm mt-1"
                 placeholder="Senha">
          <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
               class="text-red-500 text-xs mt-1">
            <span *ngIf="registerForm.get('password')?.errors?.['required']">Senha é obrigatória</span>
            <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Senha deve ter pelo menos 6 caracteres</span>
          </div>
        </div>

        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirmar senha</label>
          <input id="confirmPassword" name="confirmPassword" type="password" formControlName="confirmPassword" required
                 class="appearance-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm mt-1"
                 placeholder="Confirmar senha">
          <div *ngIf="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched"
               class="text-red-500 text-xs mt-1">
            <span *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Confirmação de senha é obrigatória</span>
            <span *ngIf="registerForm.get('confirmPassword')?.errors?.['mismatch']">As senhas não coincidem</span>
          </div>
        </div>
      </div>

      <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ errorMessage }}</span>
      </div>

      <div>
        <button type="submit" [disabled]="registerForm.invalid || isSubmitting"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent
                       text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700
                       focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                       disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="isSubmitting" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <!-- Loading spinner -->
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Cadastrar
        </button>
      </div>

      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          Já tem uma conta?
          <a routerLink="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">Faça login</a>
        </p>
      </div>
    </form>
  </div>
</div>
