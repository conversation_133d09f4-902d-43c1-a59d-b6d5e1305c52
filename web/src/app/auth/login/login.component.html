<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900">CRM Odonto</h2>
      <p class="mt-2 text-sm text-gray-600">Faça login para acessar o sistema</p>
    </div>

    <form class="mt-8 space-y-6" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="email" class="sr-only">E-mail</label>
          <input id="email" name="email" type="email" formControlName="email" required
                 class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                 placeholder="E-mail">
          <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
               class="text-red-500 text-xs mt-1 ml-1">
            <span *ngIf="loginForm.get('email')?.errors?.['required']">E-mail é obrigatório</span>
            <span *ngIf="loginForm.get('email')?.errors?.['email']">E-mail inválido</span>
          </div>
        </div>

        <div class="mt-4">
          <label for="password" class="sr-only">Senha</label>
          <input id="password" name="password" type="password" formControlName="password" required
                 class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300
                        placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                 placeholder="Senha">
          <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
               class="text-red-500 text-xs mt-1 ml-1">
            <span *ngIf="loginForm.get('password')?.errors?.['required']">Senha é obrigatória</span>
            <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Senha deve ter pelo menos 6 caracteres</span>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input id="remember-me" name="remember-me" type="checkbox"
                 class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label for="remember-me" class="ml-2 block text-sm text-gray-900">Lembrar-me</label>
        </div>

        <div class="text-sm">
          <a routerLink="/auth/recover-password" class="font-medium text-blue-600 hover:text-blue-500">Esqueceu sua senha?</a>
        </div>
      </div>

      <div *ngIf="loginError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ loginError }}</span>
      </div>

      <div>
        <button type="submit" [disabled]="loginForm.invalid || isSubmitting"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent
                       text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700
                       focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                       disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="isSubmitting" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <!-- Loading spinner -->
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Entrar
        </button>
      </div>

      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          Não tem uma conta?
          <a routerLink="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">Cadastre-se</a>
        </p>
      </div>
    </form>
  </div>
</div>
