<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Recupera<PERSON></h2>
      <p class="mt-2 text-sm text-gray-600">Digite seu e-mail para receber instruções de recuperação</p>
    </div>
    
    <form class="mt-8 space-y-6" [formGroup]="recoverForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm">
        <div>
          <label for="email" class="sr-only">E-mail</label>
          <input id="email" name="email" type="email" formControlName="email" required 
                 class="appearance-none relative block w-full px-3 py-2 border border-gray-300 
                        placeholder-gray-500 text-gray-900 rounded-md focus:outline-none 
                        focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                 placeholder="E-mail">
          <div *ngIf="recoverForm.get('email')?.invalid && recoverForm.get('email')?.touched" 
               class="text-red-500 text-xs mt-1 ml-1">
            <span *ngIf="recoverForm.get('email')?.errors?.['required']">E-mail é obrigatório</span>
            <span *ngIf="recoverForm.get('email')?.errors?.['email']">E-mail inválido</span>
          </div>
        </div>
      </div>

      <div *ngIf="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ successMessage }}</span>
      </div>

      <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ errorMessage }}</span>
      </div>

      <div>
        <button type="submit" [disabled]="recoverForm.invalid || isSubmitting"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent 
                       text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 
                       focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 
                       disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="isSubmitting" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <!-- Loading spinner -->
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Enviar Instruções
        </button>
      </div>

      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          <a routerLink="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">Voltar para o login</a>
        </p>
      </div>
    </form>
  </div>
</div>
