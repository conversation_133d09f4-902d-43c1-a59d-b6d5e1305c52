import { Component } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-recover-password',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './recover-password.component.html',
  styleUrl: './recover-password.component.scss'
})
export class RecoverPasswordComponent {
  recoverForm: FormGroup;
  isSubmitting = false;
  successMessage = '';
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private router: Router
  ) {
    this.recoverForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.recoverForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';
    this.successMessage = '';

    // Simulação de envio de e-mail - substituir por chamada real ao serviço
    setTimeout(() => {
      this.isSubmitting = false;
      this.successMessage = 'Instruções de recuperação de senha foram enviadas para o seu e-mail.';
      
      // Para simular erro, descomente as linhas abaixo e comente as de cima
      // this.isSubmitting = false;
      // this.errorMessage = 'Não foi possível enviar as instruções. Por favor, tente novamente.';
    }, 1000);
  }
}
