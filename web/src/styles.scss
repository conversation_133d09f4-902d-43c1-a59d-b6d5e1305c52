/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos personalizados para scrollbar */
@layer utilities {
  /* Aplicar scrollbar fino por padrão em toda a aplicação */
  html,
  body {
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
    overflow-x: hidden; /* Prevenir scrollbar horizontal */
    height: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    overflow: hidden; /* Prevenir scrollbar na página inteira */
  }

  /* Garantir que o app-root ocupe toda a altura disponível */
  app-root {
    display: block;
    height: 100%;
    overflow: hidden;
    background-color: rgb(255, 255, 255);
  }

  /* Chrome, Edge, Safari */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: theme("colors.gray.100");
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: theme("colors.gray.300");
    border-radius: 3px;

    &:hover {
      background-color: theme("colors.gray.400");
    }
  }

  /* Classe para scrollbar fino específico */
  .scrollbar-thin {
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");

    /* Chrome, Edge, Safari */
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: theme("colors.gray.100");
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: theme("colors.gray.300");
      border-radius: 2px;

      &:hover {
        background-color: theme("colors.gray.400");
      }
    }
  }

  /* Scrollbar ultra fino para as colunas do board */
  .scrollbar-ultra-thin {
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.100") transparent;

    /* Chrome, Edge, Safari */
    &::-webkit-scrollbar {
      width: 3px; /* Scrollbar ultra fino de 3px */
      height: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 1.5px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: theme("colors.gray.300");
      border-radius: 1.5px;

      &:hover {
        background-color: theme("colors.gray.400");
      }
    }

    /* Esconder o scrollbar quando não estiver em uso */
    &::-webkit-scrollbar-thumb:vertical {
      min-height: 30px;
    }

    /* Mostrar o scrollbar apenas quando passar o mouse */
    &:not(:hover)::-webkit-scrollbar-thumb {
      background-color: theme("colors.gray.200");
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: theme("colors.gray.300");
    }
  }
}

/* Estilos globais de tipografia */
@layer base {
  html {
    @apply font-sans text-gray-600;
  }

  h1 {
    @apply font-bold text-gray-800;
  }

  h2 {
    @apply font-semibold text-gray-800;
  }

  h3 {
    @apply font-semibold text-gray-800;
  }

  h4 {
    @apply font-medium text-gray-800;
  }

  p {
    @apply text-gray-600;
  }

  a {
    @apply transition-colors;
  }

  small {
    @apply text-sm text-gray-500;
  }

  /* Padronização de elementos de formulário */
  input,
  select,
  textarea,
  .mat-form-field {
    @apply h-[42px] box-border;
  }

  textarea {
    @apply h-auto min-h-[100px];
  }

  select {
    @apply appearance-none bg-no-repeat bg-white;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  }

  /* Ajuste de espaçamento para melhor legibilidade */
  .container {
    @apply px-4;
  }

  /* Garantir que o body ocupe toda a altura da viewport */
  body {
    @apply min-h-screen;
  }

  /* Estilos para o layout profissional */
  .pro-layout {
    @apply bg-gray-100 h-full;
  }

  .pro-sidebar {
    @apply bg-white shadow-md h-full;
  }

  .pro-content {
    @apply bg-gray-100 p-6;
  }

  .pro-card {
    @apply bg-white rounded-lg shadow-sm p-6 transition-all duration-200;
    &:hover {
      @apply shadow-md;
    }
  }
}

/* Estilos globais para tooltips */
.tooltip {
  position: fixed;
  background-color: white;
  color: theme("colors.gray.700");
  padding: 0.75rem;
  border-radius: 0.375rem;
  box-shadow: theme("boxShadow.lg");
  font-size: theme("fontSize.sm[0]");
  font-family: theme("fontFamily.sans");
  z-index: 99999;
  max-width: 16rem;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;

  &.tooltip-visible {
    opacity: 1;
    visibility: visible;
  }

  &::before {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: white;
    transform: rotate(45deg);
  }

  &.tooltip-top::before {
    bottom: -6px;
    left: 50%;
    margin-left: -6px;
  }

  &.tooltip-bottom::before {
    top: -6px;
    left: 50%;
    margin-left: -6px;
  }

  &.tooltip-left::before {
    right: -6px;
    top: 50%;
    margin-top: -6px;
  }

  &.tooltip-right::before {
    left: -6px;
    top: 50%;
    margin-top: -6px;
  }
}

/* Melhorar a aparência dos campos de formulário */
input,
select,
textarea {
  transition: all 0.2s ease-in-out;
  border-radius: 0.375rem;
  height: 42px; /* Altura padronizada para todos os campos */
  padding: 0.5rem 0.75rem;
  box-sizing: border-box;

  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  &:hover:not(:focus) {
    border-color: #93c5fd;
  }
}

/* Ajuste específico para textareas */
textarea {
  height: auto;
  min-height: 100px;
}

/* Ajuste específico para selects */
select {
  appearance: none;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Estilizar labels */
label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.375rem;
  display: block;
}

/* Estilizar os ícones nas seções */
svg {
  transition: all 0.3s ease;
}

/* Melhorar a aparência dos botões */
button,
a[routerLink] {
  transition: all 0.2s;
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }

  &:active {
    transform: translateY(1px);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Estilizar campos obrigatórios */
label:has(+ input:required)::after,
label:has(+ select:required)::after {
  content: "*";
  color: #ef4444;
  margin-left: 0.25rem;
}

/* Estilos para impressão de pacientes */
@media print {
  @page {
    size: A4;
    margin: 15mm;
  }

  body {
    background-color: white;
    font-size: 12pt;
  }

  /* Garantir que as cores de fundo sejam impressas */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Quebrar páginas adequadamente */
  .page-break {
    page-break-after: always;
  }

  /* Evitar quebras dentro de elementos importantes */
  .no-break {
    page-break-inside: avoid;
  }

  /* Estilos específicos para impressão de pacientes */
  .patient-print-content {
    display: block !important;
  }
}
