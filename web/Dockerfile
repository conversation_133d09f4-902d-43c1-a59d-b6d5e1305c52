# =============================================================================
# CRM ODONTO WEB - UNIFIED DOCKERFILE
# =============================================================================
# Multi-stage Dockerfile que funciona tanto para desenvolvimento quanto produção
# Controlado pela variável NODE_ENV:
# - NODE_ENV=development: Angular dev server com hot reload
# - NODE_ENV=production: Build otimizado servido pelo Nginx
# =============================================================================

# =============================================================================
# BASE STAGE - Configuração comum para todos os ambientes
# =============================================================================
FROM node:20-slim AS base

WORKDIR /app

# Copia arquivos de dependências
COPY package*.json ./

# =============================================================================
# DEVELOPMENT STAGE - Para desenvolvimento com hot reload
# =============================================================================
FROM base AS development

# Instala todas as dependências
RUN npm ci

# Copia o código fonte
COPY . .

# Expõe a porta do Angular dev server
EXPOSE 4200

# Comando para desenvolvimento com hot reload
CMD ["npm", "run", "dev"]

# =============================================================================
# BUILD STAGE - Para construir a aplicação para produção
# =============================================================================
FROM base AS build

# Instala todas as dependências
RUN npm ci

# Copia o código fonte
COPY . .

# Faz o build da aplicação para produção
RUN npm run build

# =============================================================================
# PRODUCTION STAGE - Para produção com Nginx
# =============================================================================
FROM nginx:alpine AS production

# Copia os arquivos buildados do stage anterior
COPY --from=build /app/dist/crm-odonto-web/browser/ /usr/share/nginx/html/

# Copia a configuração do Nginx
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expõe a porta do Nginx (80 para produção)
EXPOSE 80

# Comando para produção
CMD ["nginx", "-g", "daemon off;"]
