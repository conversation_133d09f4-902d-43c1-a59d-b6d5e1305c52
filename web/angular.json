{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"CRM-Odonto-web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/crm-odonto-web", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "allowedCommonJsDependencies": ["core-js/modules/es.promise.js", "core-js/modules/es.string.match.js", "core-js/modules/es.string.replace.js", "core-js/modules/es.string.starts-with.js", "core-js/modules/es.array.iterator.js", "core-js/modules/web.dom-collections.iterator.js", "core-js/modules/es.array.reduce.js", "core-js/modules/es.string.ends-with.js", "core-js/modules/es.string.split.js", "core-js/modules/es.string.trim.js", "core-js/modules/es.array.index-of.js", "core-js/modules/es.string.includes.js", "core-js/modules/es.array.reverse.js", "core-js/modules/es.regexp.to-string.js", "raf", "rgbcolor", "dagre", "webcola", "html2canvas"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "700kB", "maximumError": "1.2MB"}, {"type": "anyComponentStyle", "maximumWarning": "6kB", "maximumError": "10kB"}], "outputHashing": "all"}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "CRM-Odonto-web:build:production"}, "development": {"buildTarget": "CRM-Odonto-web:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "34a46e4c-4018-4e37-8fe8-697af72df4d1"}}