"""
Orquestrador para a feature comercial.
"""

from loguru import logger

from orchestrators.orquestrador_base import OrquestradorBase
from agents.comercial.agent_vendas import AgentVendas
from services.paciente_service import PacienteService

class OrquestradorComercial(OrquestradorBase):
    """
    Orquestrador para a feature comercial.
    Coordena a execução do agente de vendas.
    """
    
    def __init__(self):
        """
        Inicializa o orquestrador comercial.
        """
        super().__init__("Comercial")
        
        # Inicializa os serviços
        self.paciente_service = PacienteService()
        
        # Inicializa os agentes
        self.agent_vendas = AgentVendas(
            paciente_service=self.paciente_service
        )
    
    async def _executar_implementacao(self):
        """
        Implementação específica do orquestrador comercial.
        Executa o fluxo de vendas.
        """
        logger.info("Iniciando fluxo comercial")
        
        # Executa o agente de vendas
        await self.agent_vendas.executar()
        
        logger.info("Fluxo comercial concluído com sucesso")
