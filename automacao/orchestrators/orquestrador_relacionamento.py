"""
Orquestrador para a feature de relacionamento com pacientes.
"""

from loguru import logger

from orchestrators.orquestrador_base import OrquestradorBase
from agents.relacionamento.agent_contato import AgentContato
from services.paciente_service import PacienteService
from services.sugestoes_service import SugestoesService

class OrquestradorRelacionamento(OrquestradorBase):
    """
    Orquestrador para a feature de relacionamento com pacientes.
    Coordena a execução do agente de contato.
    """
    
    def __init__(self):
        """
        Inicializa o orquestrador de relacionamento.
        """
        super().__init__("Relacionamento com Pacientes")
        
        # Inicializa os serviços
        self.paciente_service = PacienteService()
        self.sugestoes_service = SugestoesService()
        
        # Inicializa os agentes
        self.agent_contato = AgentContato(
            paciente_service=self.paciente_service,
            sugestoes_service=self.sugestoes_service
        )
    
    async def _executar_implementacao(self):
        """
        Implementação específica do orquestrador de relacionamento.
        Executa o fluxo de contato com pacientes.
        """
        logger.info("Iniciando fluxo de relacionamento com pacientes")
        
        # Executa o agente de contato
        await self.agent_contato.executar()
        
        logger.info("Fluxo de relacionamento com pacientes concluído com sucesso")
