"""
Orquestrador para a feature de sugestões de tratamento.
"""

from loguru import logger

from orchestrators.orquestrador_base import OrquestradorBase
from agents.suggestions.agent_triagem import AgentTriagem
from agents.suggestions.agent_odontologico import AgentOdontologico
from services.api_service import ApiService
from services.paciente_service import PacienteService
from services.sugestoes_service import SugestoesService
from services.fluxograma_service import FluxogramaService
from services.prontuario_service import ProntuarioService
from core.constants import (
    SUGGESTION_STATUS_IN_ANALYSIS,
    STATUS_IDLE,
    STATUS_RUNNING,
    STATUS_COMPLETED,
    STATUS_ERROR
)

class OrquestradorSuggestions(OrquestradorBase):
    """
    Orquestrador para a feature de sugestões de tratamento.
    Coordena a execução dos agentes de triagem e odontológico.
    """

    def __init__(self):
        """
        Inicializa o orquestrador de sugestões.
        """
        super().__init__("Sugestões de Tratamento")

        # Inicializa os serviços
        self.api_service = ApiService()
        self.paciente_service = PacienteService()
        self.sugestoes_service = SugestoesService()
        self.fluxograma_service = FluxogramaService()
        self.prontuario_service = ProntuarioService()

        # Inicializa os agentes
        self.agent_triagem = AgentTriagem(
            paciente_service=self.paciente_service,
            sugestoes_service=self.sugestoes_service
        )

        self.agent_odontologico = AgentOdontologico(
            api_service=self.api_service
        )

    async def _executar_implementacao(self):
        """
        Implementação específica do orquestrador de sugestões.
        Executa o fluxo completo de geração de sugestões.
        """
        logger.info("Iniciando fluxo de sugestões de tratamento")

        # Passo 1: Executa o agente de triagem para identificar pacientes e criar sugestões iniciais
        logger.info("Executando agente de triagem")
        resultado_triagem = await self.agent_triagem.executar()

        # Verificar se resultado_triagem é uma lista ou dicionário
        if isinstance(resultado_triagem, list):
            sugestoes_criadas = len(resultado_triagem)
        elif isinstance(resultado_triagem, dict):
            sugestoes_criadas = resultado_triagem.get('sugestoes_criadas', 0)
        else:
            sugestoes_criadas = 0

        if not resultado_triagem or sugestoes_criadas == 0:
            logger.info("Nenhuma sugestão criada pelo agente de triagem")
            return

        logger.info(f"Agente de triagem criou {sugestoes_criadas} sugestões")

        # Passo 2: Executa o agente odontológico para analisar todas as sugestões IN_ANALYSIS
        logger.info("Executando agente odontológico para analisar sugestões")
        resultado_odontologico = await self.agent_odontologico.executar()

        logger.info(f"Agente odontológico processou {resultado_odontologico.get('sugestoes_processadas', 0)} sugestões")
        logger.info("Fluxo de sugestões de tratamento concluído com sucesso")

    async def executar_triagem(self):
        """
        Executa apenas o agente de triagem.
        Identifica pacientes que concluíram tratamento e cria sugestões iniciais.
        """
        logger.info("Iniciando execução isolada do agente de triagem")
        self._status = STATUS_RUNNING

        try:
            # Executa o agente de triagem
            sugestoes_criadas = await self.agent_triagem.executar()

            if not sugestoes_criadas:
                logger.info("Nenhuma sugestão criada pelo agente de triagem")
            else:
                logger.info(f"Agente de triagem criou {len(sugestoes_criadas)} sugestões")

            self._status = STATUS_COMPLETED
            return sugestoes_criadas
        except Exception as e:
            logger.error(f"Erro ao executar agente de triagem: {str(e)}")
            self._status = STATUS_ERROR
            raise

    async def executar_odontologico(self):
        """
        Executa apenas o agente odontológico para sugestões existentes com status IN_ANALYSIS.
        Gera recomendações de tratamento usando IA.
        """
        logger.info("Iniciando execução isolada do agente odontológico")
        self._status = STATUS_RUNNING

        try:
            # Executar o agente odontológico
            resultado = await self.agent_odontologico.executar()

            logger.info(f"Agente odontológico executado: {resultado}")
            self._status = STATUS_COMPLETED
            return resultado

        except Exception as e:
            logger.error(f"Erro ao executar agente odontológico: {str(e)}")
            self._status = STATUS_ERROR
            raise
