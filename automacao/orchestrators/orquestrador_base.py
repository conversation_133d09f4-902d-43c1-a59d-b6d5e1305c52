"""
Orquestrador base para todos os orquestradores do sistema.
"""

from loguru import logger

from core.constants import STATUS_IDLE, STATUS_RUNNING, STATUS_COMPLETED, STATUS_ERROR

class OrquestradorBase:
    """
    Classe base para todos os orquestradores do sistema.
    Define a interface comum e funcionalidades básicas.
    """
    
    def __init__(self, nome):
        """
        Inicializa o orquestrador.
        
        Args:
            nome (str): Nome do orquestrador
        """
        self.nome = nome
        self._status = STATUS_IDLE
        logger.info(f"Orquestrador {self.nome} inicializado")
    
    @property
    def status(self):
        """
        Retorna o status atual do orquestrador.
        
        Returns:
            str: Status atual
        """
        return self._status
    
    async def executar(self):
        """
        Método principal para execução do orquestrador.
        Deve ser implementado pelas classes filhas.
        """
        try:
            self._status = STATUS_RUNNING
            logger.info(f"Iniciando execução do orquestrador {self.nome}")
            
            # Implementação específica nas classes filhas
            await self._executar_implementacao()
            
            self._status = STATUS_COMPLETED
            logger.info(f"Execução do orquestrador {self.nome} concluída com sucesso")
        except Exception as e:
            self._status = STATUS_ERROR
            logger.error(f"Erro na execução do orquestrador {self.nome}: {str(e)}")
            raise
    
    async def _executar_implementacao(self):
        """
        Implementação específica do orquestrador.
        Deve ser sobrescrita pelas classes filhas.
        
        Raises:
            NotImplementedError: Se não for implementado pela classe filha
        """
        raise NotImplementedError("Método _executar_implementacao deve ser implementado pela classe filha")
