#!/usr/bin/env python3
"""
Script de teste para o agente de triagem melhorado.
"""

import asyncio
import sys
import os

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_agent_triagem():
    """
    Testa o agente de triagem com dados simulados.
    """
    print("🧪 Testando o agente de triagem melhorado...")
    
    # Simular dados de pacientes com sugestões
    pacientes_teste = [
        {
            "id": 1,
            "name": "<PERSON>",
            "cpf": "123.456.789-00",
            "treatmentConcludedAt": "2025-05-20T15:30:00.000Z",
            "suggestions": []  # Sem sugestões anteriores
        },
        {
            "id": 2,
            "name": "<PERSON>",
            "cpf": "987.654.321-00",
            "treatmentConcludedAt": "2025-05-15T10:00:00.000Z",
            "suggestions": [
                {
                    "id": "uuid-1",
                    "status": "IN_ANALYSIS",
                    "iaReasoning": None,
                    "humanComment": None,
                    "createdAt": "2025-05-23T08:00:00.000Z"
                }
            ]
        },
        {
            "id": 3,
            "name": "Pedro Costa",
            "cpf": "456.789.123-00",
            "treatmentConcludedAt": "2025-04-10T14:20:00.000Z",
            "suggestions": [
                {
                    "id": "uuid-2",
                    "status": "REJECTED",
                    "iaReasoning": "Paciente necessita de limpeza",
                    "humanComment": "Paciente está fora do país",
                    "createdAt": "2025-04-15T09:00:00.000Z"
                }
            ]
        },
        {
            "id": 4,
            "name": "Ana Oliveira",
            "cpf": "789.123.456-00",
            "treatmentConcludedAt": "2025-03-01T11:30:00.000Z",
            "suggestions": [
                {
                    "id": "uuid-3",
                    "status": "REJECTED",
                    "iaReasoning": "Necessário acompanhamento",
                    "humanComment": "Voltar a revisar daqui a um mês",
                    "createdAt": "2025-03-05T10:00:00.000Z"
                }
            ]
        }
    ]
    
    # Importar e testar o agente
    try:
        from agents.suggestions.agent_triagem import AgentTriagem
        
        # Criar mock dos serviços
        class MockPacienteService:
            async def listar_pacientes_com_tratamento_concluido(self, dias):
                return pacientes_teste
        
        class MockSugestoesService:
            def __init__(self):
                self.sugestoes_criadas = []
            
            async def criar_sugestao(self, dados):
                sugestao = {
                    "id": f"mock-{len(self.sugestoes_criadas) + 1}",
                    **dados
                }
                self.sugestoes_criadas.append(sugestao)
                return sugestao
        
        # Inicializar agente
        paciente_service = MockPacienteService()
        sugestoes_service = MockSugestoesService()
        agente = AgentTriagem(paciente_service, sugestoes_service)
        
        # Executar teste
        print("\n🚀 Executando agente de triagem...")
        resultado = await agente.executar(30)
        
        print(f"\n📊 Resultado do teste:")
        print(f"  ✅ Sugestões criadas: {len(resultado)}")
        print(f"  📋 Detalhes das sugestões criadas:")
        
        for i, sugestao in enumerate(resultado, 1):
            print(f"    {i}. ID: {sugestao.get('id')} - Paciente: {sugestao.get('patientId')}")
        
        print(f"\n🎯 Análise esperada:")
        print(f"  - João Silva (ID: 1): DEVE criar (sem sugestões anteriores)")
        print(f"  - Maria Santos (ID: 2): NÃO deve criar (já tem sugestão IN_ANALYSIS)")
        print(f"  - Pedro Costa (ID: 3): NÃO deve criar (motivo definitivo: 'fora do país')")
        print(f"  - Ana Oliveira (ID: 4): PODE criar (motivo temporário + tempo passou)")
        
        print("\n✅ Teste concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_agent_triagem())
    sys.exit(0 if success else 1)
