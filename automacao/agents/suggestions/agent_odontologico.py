"""
Agente odontológico para análise e geração de sugestões de tratamento.
Utiliza o Google ADK Agents para gerar recomendações personalizadas.
"""

import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

try:
    # Importações do Google ADK 1.0.0
    from google.adk.agents import Agent
    from google.adk.runners import Runner
    from google.adk.sessions import InMemorySessionService
    from google.genai import types
    ADK_AVAILABLE = True
    logger.info("Google ADK 1.0.0 importado com sucesso!")
except ImportError as e:
    logger.warning(f"Google ADK não está disponível. Erro: {str(e)}")
    logger.warning("Instale o pacote google-adk==1.0.0 para usar este agente.")
    ADK_AVAILABLE = False

from services.api_service import ApiService

class AgentOdontologico:
    """
    Agente odontológico para análise e geração de sugestões de tratamento.
    Utiliza o Google ADK Agents para gerar recomendações personalizadas.
    """

    def __init__(self, api_service: ApiService):
        """
        Inicializa o agente odontológico.

        Args:
            api_service: Serviço de acesso à API do sistema
        """
        self.api_service = api_service

        # Verificar se a chave da API do Google está configurada
        self.google_api_key = os.getenv('GOOGLE_API_KEY')
        if not self.google_api_key:
            logger.warning("A variável de ambiente GOOGLE_API_KEY não está definida.")
            logger.warning("Certifique-se de configurar sua chave de API.")

        logger.info("Agente odontológico inicializado")

    async def executar(self) -> Dict[str, Any]:
        """
        Executa o agente odontológico para processar sugestões com status IN_ANALYSIS.

        🧠 Função do Agente Odontológico:
        É um especialista automatizado que analisa as sugestões com status IN_ANALYSIS,
        verifica todo o histórico do paciente (procedimentos, prontuário, sugestões anteriores)
        e decide qual procedimento recomendar ou se não deve recomendar nada no momento,
        atualizando o status da sugestão para PENDING_REVIEW.

        Returns:
            Dict[str, Any]: Resultado da execução
        """
        try:
            logger.info("🧠 Iniciando execução do agente odontológico especialista")

            # 1. Aprendizado Inicial (conhecimento da clínica)
            logger.info("📚 Fase 1: Aprendizado inicial - conhecimento da clínica")
            procedimentos_clinica = await self._buscar_procedimentos_clinica()
            fluxo_tratamento = await self._buscar_fluxo_tratamento()

            if not procedimentos_clinica:
                logger.error("❌ Não foi possível carregar procedimentos da clínica")
                return {
                    "status": "error",
                    "message": "Falha ao carregar conhecimento da clínica"
                }

            logger.info(f"✅ Carregados {len(procedimentos_clinica)} procedimentos da clínica")
            logger.info(f"✅ Fluxo de tratamento {'carregado' if fluxo_tratamento else 'não disponível'}")

            # 2. Buscar sugestões em análise
            logger.info("🔍 Fase 2: Buscando sugestões com status IN_ANALYSIS")
            sugestoes = await self._buscar_sugestoes_in_analysis()

            if not sugestoes:
                logger.info("ℹ️ Nenhuma sugestão com status IN_ANALYSIS encontrada")
                return {
                    "status": "success",
                    "message": "Nenhuma sugestão para processar",
                    "sugestoes_processadas": 0
                }

            logger.info(f"📋 Encontradas {len(sugestoes)} sugestões para processar")

            sugestoes_processadas = 0
            sugestoes_rejeitadas = 0

            # 3. Processar cada sugestão com análise especializada
            for i, sugestao in enumerate(sugestoes, 1):
                try:
                    logger.info(f"🔬 Processando sugestão {i}/{len(sugestoes)}: {sugestao['id']}")

                    resultado = await self._processar_sugestao_especializada(
                        sugestao,
                        fluxo_tratamento,
                        procedimentos_clinica
                    )

                    if resultado['status'] == 'processed':
                        sugestoes_processadas += 1
                        logger.info(f"✅ Sugestão {sugestao['id']} processada com sucesso")
                    elif resultado['status'] == 'rejected':
                        sugestoes_rejeitadas += 1
                        logger.info(f"🚫 Sugestão {sugestao['id']} rejeitada pelo agente")
                    else:
                        logger.warning(f"⚠️ Falha ao processar sugestão {sugestao['id']}")

                except Exception as e:
                    logger.error(f"❌ Erro ao processar sugestão {sugestao['id']}: {str(e)}")
                    continue

            return {
                "status": "success",
                "message": f"Processamento concluído",
                "sugestoes_processadas": sugestoes_processadas,
                "sugestoes_rejeitadas": sugestoes_rejeitadas,
                "total_sugestoes": len(sugestoes)
            }

        except Exception as e:
            logger.error(f"❌ Erro na execução do agente odontológico: {str(e)}")
            return {
                "status": "error",
                "message": f"Erro na execução: {str(e)}"
            }

    async def _buscar_sugestoes_in_analysis(self) -> List[Dict[str, Any]]:
        """
        Busca sugestões com status IN_ANALYSIS.

        Returns:
            List[Dict[str, Any]]: Lista de sugestões
        """
        try:
            logger.info("Buscando sugestões com status IN_ANALYSIS")
            response = await self.api_service.get("/suggestions", params={"status": "IN_ANALYSIS"})

            if response and isinstance(response, list):
                logger.info(f"Encontradas {len(response)} sugestões com status IN_ANALYSIS")
                return response
            else:
                logger.warning("Resposta inválida ao buscar sugestões")
                return []

        except Exception as e:
            logger.error(f"Erro ao buscar sugestões: {str(e)}")
            return []

    async def _buscar_fluxo_tratamento(self) -> Optional[Dict[str, Any]]:
        """
        Busca o fluxo de tratamento da clínica.

        Returns:
            Optional[Dict[str, Any]]: Dados do fluxo de tratamento
        """
        try:
            logger.info("Buscando fluxo de tratamento")
            response = await self.api_service.get("/treatment-flow")

            if response:
                logger.info("Fluxo de tratamento obtido com sucesso")
                return response
            else:
                logger.warning("Fluxo de tratamento não encontrado")
                return None

        except Exception as e:
            logger.error(f"Erro ao buscar fluxo de tratamento: {str(e)}")
            return None

    async def _buscar_procedimentos_clinica(self) -> List[Dict[str, Any]]:
        """
        Busca todos os procedimentos disponíveis na clínica.

        Returns:
            List[Dict[str, Any]]: Lista de procedimentos da clínica
        """
        try:
            logger.info("Buscando procedimentos da clínica")
            response = await self.api_service.get("/procedures?page=1&limit=1000&noPagination=false")

            if response:
                # Verificar se a resposta tem a estrutura {data: [...]}
                if isinstance(response, dict) and 'data' in response:
                    procedimentos = response['data']
                    logger.info(f"Encontrados {len(procedimentos)} procedimentos na clínica")
                    return procedimentos
                elif isinstance(response, list):
                    logger.info(f"Encontrados {len(response)} procedimentos na clínica")
                    return response
                else:
                    logger.warning("Estrutura de resposta inesperada para procedimentos")
                    return []
            else:
                logger.warning("Nenhum procedimento encontrado na clínica")
                return []

        except Exception as e:
            logger.error(f"Erro ao buscar procedimentos da clínica: {str(e)}")
            return []

    async def _buscar_prontuario_completo(self, patient_id: int) -> Optional[Dict[str, Any]]:
        """
        Busca o prontuário completo de um paciente.

        Args:
            patient_id: ID do paciente

        Returns:
            Optional[Dict[str, Any]]: Dados do prontuário completo
        """
        try:
            logger.info(f"Buscando prontuário completo do paciente {patient_id}")
            response = await self.api_service.get(f"/medical-records/complete/{patient_id}")

            if response:
                logger.info(f"Prontuário do paciente {patient_id} obtido com sucesso")
                return response
            else:
                logger.warning(f"Prontuário não encontrado para o paciente {patient_id}")
                return None

        except Exception as e:
            logger.error(f"Erro ao buscar prontuário do paciente {patient_id}: {str(e)}")
            return None

    async def _processar_sugestao_especializada(self, sugestao: Dict[str, Any], fluxo_tratamento: Optional[Dict[str, Any]], procedimentos_clinica: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Processa uma sugestão específica com análise especializada completa.

        🧩 Etapas do Comportamento:
        1. Buscar prontuário completo do paciente
        2. Analisar histórico de procedimentos realizados
        3. Analisar sugestões anteriores e seus status/comentários
        4. Aplicar regras de decisão clínica
        5. Gerar recomendação ou rejeitar com justificativa

        Args:
            sugestao: Dados da sugestão
            fluxo_tratamento: Dados do fluxo de tratamento
            procedimentos_clinica: Lista de procedimentos disponíveis na clínica

        Returns:
            Dict[str, Any]: Resultado do processamento com status e detalhes
        """
        try:
            patient_id = sugestao.get('patientId')
            suggestion_id = sugestao.get('id')

            logger.info(f"🔬 Iniciando análise especializada para paciente {patient_id}")

            # 3. Entendimento do paciente - buscar prontuário completo
            prontuario = await self._buscar_prontuario_completo(patient_id)
            if not prontuario:
                logger.error(f"❌ Não foi possível obter o prontuário do paciente {patient_id}")
                return {"status": "error", "message": "Prontuário não encontrado"}

            # Extrair dados do prontuário
            paciente = prontuario.get('patient', {})
            sugestoes_anteriores = prontuario.get('suggestions', [])
            treatment_plans = prontuario.get('treatmentPlans', [])
            schedulings = prontuario.get('schedulings', [])
            budgets = prontuario.get('budgets', [])

            logger.info(f"📊 Dados coletados: {len(sugestoes_anteriores)} sugestões anteriores, {len(treatment_plans)} planos, {len(schedulings)} agendamentos")

            # 📋 Aplicar Regras de Decisão
            decisao = await self._aplicar_regras_decisao(
                paciente,
                sugestoes_anteriores,
                treatment_plans,
                schedulings,
                budgets,
                procedimentos_clinica,
                fluxo_tratamento
            )

            if decisao['deve_recomendar']:
                # ✅ Gerar recomendação
                logger.info(f"✅ Decisão: RECOMENDAR - {decisao['justificativa']}")

                ia_reasoning = await self._gerar_raciocinio_especializado(
                    prontuario,
                    decisao,
                    procedimentos_clinica,
                    fluxo_tratamento
                )

                procedures = self._formatar_procedimentos_recomendados(
                    decisao['procedimentos_recomendados'],
                    procedimentos_clinica
                )

                # Atualizar para PENDING_REVIEW
                payload = {
                    "status": "PENDING_REVIEW",
                    "iaReasoning": ia_reasoning,
                    "procedures": procedures
                }

                sucesso = await self._atualizar_sugestao(suggestion_id, payload)

                if sucesso:
                    logger.info(f"✅ Sugestão {suggestion_id} atualizada para PENDING_REVIEW")
                    return {
                        "status": "processed",
                        "action": "PENDING_REVIEW",
                        "procedures_count": len(procedures)
                    }
                else:
                    return {"status": "error", "message": "Falha ao atualizar sugestão"}

            else:
                # 🚫 Rejeitar sugestão
                logger.info(f"🚫 Decisão: REJEITAR - {decisao['justificativa']}")

                # Atualizar para REJECTED com justificativa do agente
                payload = {
                    "status": "REJECTED",
                    "iaReasoning": f"AGENTE AUTOMÁTICO: {decisao['justificativa']}",
                    "procedures": []
                }

                sucesso = await self._atualizar_sugestao(suggestion_id, payload)

                if sucesso:
                    logger.info(f"🚫 Sugestão {suggestion_id} rejeitada pelo agente")
                    return {
                        "status": "rejected",
                        "action": "REJECTED",
                        "reason": decisao['justificativa']
                    }
                else:
                    return {"status": "error", "message": "Falha ao rejeitar sugestão"}

        except Exception as e:
            logger.error(f"❌ Erro ao processar sugestão especializada: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def _aplicar_regras_decisao(self, paciente: Dict[str, Any], sugestoes_anteriores: List[Dict[str, Any]],
                                    treatment_plans: List[Dict[str, Any]], schedulings: List[Dict[str, Any]],
                                    budgets: List[Dict[str, Any]], procedimentos_clinica: List[Dict[str, Any]],
                                    fluxo_tratamento: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Aplica as regras de decisão clínica para determinar se deve recomendar procedimentos.

        📋 Regras de Decisão:
        ✅ Procedimentos já realizados - Não recomendar novamente
        ⚠️ Sugestões anteriores:
        - Se REJECTED com motivo permanente → não recomendar
        - Se REJECTED com motivo temporário → avaliar se faz sentido agora
        - Se APPROVED → considerar próximo passo no fluxo

        Args:
            paciente: Dados do paciente
            sugestoes_anteriores: Histórico de sugestões
            treatment_plans: Planos de tratamento
            schedulings: Agendamentos
            budgets: Orçamentos
            procedimentos_clinica: Procedimentos disponíveis
            fluxo_tratamento: Fluxo de tratamento da clínica

        Returns:
            Dict[str, Any]: Decisão com justificativa e procedimentos recomendados
        """
        try:
            logger.info("📋 Aplicando regras de decisão clínica")

            # Extrair informações do paciente
            nome = paciente.get('name', 'Paciente')
            idade = self._extrair_idade_numerica(paciente.get('birthDate', ''))
            observacoes = paciente.get('notes', '').lower()

            logger.info(f"👤 Analisando {nome}, {idade} anos")

            # 1. ✅ Verificar procedimentos já realizados
            procedimentos_realizados = self._extrair_procedimentos_realizados(treatment_plans, schedulings, budgets)
            logger.info(f"📝 Procedimentos já realizados: {len(procedimentos_realizados)}")

            # 2. ⚠️ Analisar sugestões anteriores
            analise_sugestoes = self._analisar_sugestoes_anteriores(sugestoes_anteriores)
            logger.info(f"🔍 Análise de sugestões anteriores: {analise_sugestoes['resumo']}")

            # 3. 🚫 Verificar motivos de rejeição permanente
            if analise_sugestoes['tem_rejeicao_permanente']:
                return {
                    "deve_recomendar": False,
                    "justificativa": f"Paciente possui rejeição permanente anterior: {analise_sugestoes['motivo_rejeicao']}",
                    "procedimentos_recomendados": []
                }

            # 4. ⏰ Verificar se é muito cedo para nova sugestão
            if analise_sugestoes['muito_recente']:
                return {
                    "deve_recomendar": False,
                    "justificativa": f"Sugestão muito recente ({analise_sugestoes['dias_ultima_sugestao']} dias). Aguardar mais tempo.",
                    "procedimentos_recomendados": []
                }

            # 5. 🔄 Verificar sobrecarga de procedimentos
            if self._tem_sobrecarga_procedimentos(schedulings):
                return {
                    "deve_recomendar": False,
                    "justificativa": "Paciente possui múltiplos procedimentos agendados recentemente. Evitar sobrecarga.",
                    "procedimentos_recomendados": []
                }

            # 6. 🎯 Determinar próximo procedimento no fluxo
            proximo_procedimento = self._determinar_proximo_procedimento(
                procedimentos_realizados,
                fluxo_tratamento,
                procedimentos_clinica,
                idade,
                observacoes
            )

            if proximo_procedimento:
                return {
                    "deve_recomendar": True,
                    "justificativa": proximo_procedimento['justificativa'],
                    "procedimentos_recomendados": [proximo_procedimento]
                }

            # 7. 🔍 Verificar necessidades baseadas em idade e observações
            necessidade_idade = self._verificar_necessidades_por_idade(idade, procedimentos_realizados, procedimentos_clinica)
            if necessidade_idade:
                return {
                    "deve_recomendar": True,
                    "justificativa": necessidade_idade['justificativa'],
                    "procedimentos_recomendados": [necessidade_idade]
                }

            # 8. 🩺 Verificar necessidades baseadas em observações clínicas
            necessidade_clinica = self._verificar_necessidades_clinicas(observacoes, procedimentos_realizados, procedimentos_clinica)
            if necessidade_clinica:
                return {
                    "deve_recomendar": True,
                    "justificativa": necessidade_clinica['justificativa'],
                    "procedimentos_recomendados": [necessidade_clinica]
                }

            # 9. 💰 Verificar orçamentos em aberto (NOVA REGRA)
            orcamento_aberto = self._verificar_orcamentos_em_aberto(budgets)
            if orcamento_aberto:
                return {
                    "deve_recomendar": True,
                    "justificativa": orcamento_aberto['justificativa'],
                    "procedimentos_recomendados": [orcamento_aberto]
                }

            # 10. 🚫 Nenhuma recomendação válida no momento
            return {
                "deve_recomendar": False,
                "justificativa": "Não há necessidade clínica identificada no momento. Paciente em acompanhamento adequado.",
                "procedimentos_recomendados": []
            }

        except Exception as e:
            logger.error(f"❌ Erro ao aplicar regras de decisão: {str(e)}")
            return {
                "deve_recomendar": False,
                "justificativa": f"Erro na análise clínica: {str(e)}",
                "procedimentos_recomendados": []
            }

    def _extrair_procedimentos_realizados(self, treatment_plans: List[Dict[str, Any]],
                                        schedulings: List[Dict[str, Any]],
                                        budgets: List[Dict[str, Any]]) -> List[str]:
        """Extrai lista de procedimentos já realizados pelo paciente."""
        procedimentos = set()

        try:
            # Extrair de planos de tratamento concluídos
            for plan in treatment_plans:
                if plan.get('status') == 'completed':
                    procedures = plan.get('procedures', [])
                    for proc in procedures:
                        if proc.get('status') == 'completed':
                            procedimentos.add(proc.get('name', '').lower())

            # Extrair de agendamentos concluídos
            for scheduling in schedulings:
                if scheduling.get('status') == 'completed':
                    procedures = scheduling.get('procedures', [])
                    for proc in procedures:
                        procedimentos.add(proc.get('name', '').lower())

            # Extrair de orçamentos aprovados (assumir realizados)
            for budget in budgets:
                if budget.get('status') == 'approved':
                    items = budget.get('items', [])
                    for item in items:
                        procedimentos.add(item.get('procedureName', '').lower())

        except Exception as e:
            logger.warning(f"Erro ao extrair procedimentos realizados: {str(e)}")

        return list(procedimentos)

    def _analisar_sugestoes_anteriores(self, sugestoes_anteriores: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analisa sugestões anteriores para identificar padrões e restrições."""
        from datetime import datetime, timedelta

        analise = {
            "tem_rejeicao_permanente": False,
            "motivo_rejeicao": "",
            "muito_recente": False,
            "dias_ultima_sugestao": 999,
            "resumo": "Sem histórico"
        }

        try:
            if not sugestoes_anteriores:
                return analise

            hoje = datetime.now()
            sugestoes_rejeitadas = []
            ultima_sugestao_data = None

            for sugestao in sugestoes_anteriores:
                status = sugestao.get('status', '')
                created_at = sugestao.get('createdAt', '')
                human_comment = (sugestao.get('humanComment') or '').lower()

                # Calcular dias desde a última sugestão
                try:
                    if created_at:
                        data_sugestao = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        if not ultima_sugestao_data or data_sugestao > ultima_sugestao_data:
                            ultima_sugestao_data = data_sugestao
                except:
                    pass

                # Analisar rejeições
                if status == 'REJECTED' and human_comment:
                    sugestoes_rejeitadas.append(human_comment)

                    # Verificar motivos permanentes
                    motivos_permanentes = [
                        'fora do país', 'mudou de cidade', 'recusou tratamento',
                        'não quer mais', 'desistiu', 'cancelou', 'não retorna',
                        'sem interesse', 'não aceita'
                    ]

                    if any(motivo in human_comment for motivo in motivos_permanentes):
                        analise["tem_rejeicao_permanente"] = True
                        analise["motivo_rejeicao"] = human_comment

            # Calcular dias desde última sugestão
            if ultima_sugestao_data:
                dias = (hoje - ultima_sugestao_data).days
                analise["dias_ultima_sugestao"] = dias
                analise["muito_recente"] = dias < 30  # Menos de 30 dias

            # Resumo
            total = len(sugestoes_anteriores)
            rejeitadas = len(sugestoes_rejeitadas)
            analise["resumo"] = f"{total} sugestões, {rejeitadas} rejeitadas"

        except Exception as e:
            logger.warning(f"Erro ao analisar sugestões anteriores: {str(e)}")

        return analise

    def _tem_sobrecarga_procedimentos(self, schedulings: List[Dict[str, Any]]) -> bool:
        """Verifica se há sobrecarga de procedimentos agendados."""
        from datetime import datetime, timedelta

        try:
            hoje = datetime.now()
            limite = hoje - timedelta(days=30)  # Últimos 30 dias

            agendamentos_recentes = 0
            for scheduling in schedulings:
                try:
                    data_str = scheduling.get('date', '')
                    if data_str:
                        data = datetime.strptime(data_str, "%Y-%m-%d")
                        if data > limite:
                            agendamentos_recentes += 1
                except:
                    continue

            # Considerar sobrecarga se mais de 3 agendamentos em 30 dias
            return agendamentos_recentes > 3

        except Exception as e:
            logger.warning(f"Erro ao verificar sobrecarga: {str(e)}")
            return False

    def _determinar_proximo_procedimento(self, procedimentos_realizados: List[str],
                                       fluxo_tratamento: Optional[Dict[str, Any]],
                                       procedimentos_clinica: List[Dict[str, Any]],
                                       idade: int, observacoes: str) -> Optional[Dict[str, Any]]:
        """Determina o próximo procedimento baseado no fluxo de tratamento."""
        try:
            if not fluxo_tratamento or not isinstance(fluxo_tratamento, list):
                return None

            # Analisar fluxo de tratamento
            for fluxo_item in fluxo_tratamento:
                from_procedure = fluxo_item.get('fromProcedure', {})
                to_procedure = fluxo_item.get('toProcedure', {})
                days_after = fluxo_item.get('daysAfter', 0)

                from_name = from_procedure.get('name', '').lower()
                to_name = to_procedure.get('name', '')
                to_id = to_procedure.get('id')

                # Verificar se o procedimento anterior foi realizado
                if from_name in procedimentos_realizados:
                    # Verificar se o próximo procedimento ainda não foi feito
                    if to_name.lower() not in procedimentos_realizados:
                        # Encontrar o procedimento na lista da clínica
                        for proc_clinica in procedimentos_clinica:
                            if proc_clinica.get('id') == to_id:
                                return {
                                    "id": to_id,
                                    "name": to_name,
                                    "justificativa": f"Sequência do fluxo de tratamento: após {from_procedure.get('name', '')}, recomenda-se {to_name} após {days_after} dias"
                                }

            return None

        except Exception as e:
            logger.warning(f"Erro ao determinar próximo procedimento: {str(e)}")
            return None

    def _verificar_necessidades_por_idade(self, idade: int, procedimentos_realizados: List[str],
                                        procedimentos_clinica: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Verifica necessidades específicas baseadas na idade do paciente."""
        try:
            # Regras baseadas em idade
            if idade >= 60:
                # Idosos: priorizar prevenção e acompanhamento
                if not any('limpeza' in proc or 'profilaxia' in proc for proc in procedimentos_realizados):
                    return self._encontrar_procedimento_por_nome('limpeza', procedimentos_clinica,
                                                               "manutenção preventiva recomendada para pacientes acima de 60 anos")

                if not any('radiografia' in proc or 'raio' in proc for proc in procedimentos_realizados):
                    return self._encontrar_procedimento_por_nome('radiografia', procedimentos_clinica,
                                                               "exame preventivo recomendado para pacientes idosos")

            elif idade >= 40:
                # Adultos: foco em prevenção e detecção precoce
                if not any('radiografia' in proc or 'raio' in proc for proc in procedimentos_realizados):
                    return self._encontrar_procedimento_por_nome('radiografia', procedimentos_clinica,
                                                               "exame preventivo recomendado para pacientes acima de 40 anos")

            elif idade <= 18:
                # Jovens: foco em prevenção e ortodontia
                if not any('limpeza' in proc or 'profilaxia' in proc for proc in procedimentos_realizados):
                    return self._encontrar_procedimento_por_nome('limpeza', procedimentos_clinica,
                                                               "manutenção preventiva essencial para pacientes jovens")

            return None

        except Exception as e:
            logger.warning(f"Erro ao verificar necessidades por idade: {str(e)}")
            return None

    def _verificar_necessidades_clinicas(self, observacoes: str, procedimentos_realizados: List[str],
                                       procedimentos_clinica: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Verifica necessidades baseadas em observações clínicas."""
        try:
            # Analisar observações para sintomas específicos
            if any(palavra in observacoes for palavra in ['dor', 'dolorido', 'doendo', 'machuca']):
                return self._encontrar_procedimento_por_nome('urgência', procedimentos_clinica,
                                                           "relatos de dor requerem avaliação urgente")

            if any(palavra in observacoes for palavra in ['sangramento', 'sangra', 'gengiva', 'periodontal']):
                return self._encontrar_procedimento_por_nome('periodontal', procedimentos_clinica,
                                                           "sinais de problemas gengivais ou periodontais")

            if any(palavra in observacoes for palavra in ['bruxismo', 'ranger', 'apertamento']):
                return self._encontrar_procedimento_por_nome('placa', procedimentos_clinica,
                                                           "histórico de bruxismo requer proteção com placa miorrelaxante")

            if any(palavra in observacoes for palavra in ['cárie', 'cariado', 'buraco', 'restauração']):
                return self._encontrar_procedimento_por_nome('restauração', procedimentos_clinica,
                                                           "necessidade de tratamento restaurador")

            # Verificar necessidade de limpeza geral
            if not any('limpeza' in proc or 'profilaxia' in proc for proc in procedimentos_realizados):
                return self._encontrar_procedimento_por_nome('limpeza', procedimentos_clinica,
                                                           "manutenção preventiva da saúde bucal")

            return None

        except Exception as e:
            logger.warning(f"Erro ao verificar necessidades clínicas: {str(e)}")
            return None

    def _encontrar_procedimento_por_nome(self, palavra_chave: str, procedimentos_clinica: List[Dict[str, Any]],
                                       justificativa: str) -> Optional[Dict[str, Any]]:
        """Encontra um procedimento na clínica baseado em palavra-chave."""
        try:
            for proc in procedimentos_clinica:
                nome = proc.get('name', '').lower()
                if palavra_chave.lower() in nome:
                    return {
                        "id": proc.get('id'),
                        "name": proc.get('name'),
                        "justificativa": justificativa
                    }
            return None
        except Exception as e:
            logger.warning(f"Erro ao encontrar procedimento por nome: {str(e)}")
            return None

    def _verificar_orcamentos_em_aberto(self, budgets: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Verifica se há orçamentos em aberto que ainda não viraram tratamento.

        💰 NOVA REGRA: Para pacientes como Eduardo Almeida que possuem orçamento em aberto
        que ainda não virou tratamento, o agente deve colocar status PENDING_REVIEW
        e recomendar verificar o orçamento para entrar em contato com o cliente.

        Args:
            budgets: Lista de orçamentos do paciente

        Returns:
            Optional[Dict[str, Any]]: Recomendação para orçamento em aberto ou None
        """
        try:
            logger.info(f"🔍 Verificando orçamentos em aberto - Total: {len(budgets)}")

            orcamentos_abertos = []

            for budget in budgets:
                status = budget.get('status', '').lower()
                valor = budget.get('totalValue', 0)
                dentista = budget.get('dentistName', 'N/A')
                created_at = budget.get('createdAt', '')
                items = budget.get('items', [])

                logger.info(f"📋 Orçamento: Status={status}, Valor=R${valor}, Dentista={dentista}")

                # Verificar se é um orçamento em aberto (open, pending, etc.)
                if status in ['open', 'pending', 'em_aberto', 'pendente']:
                    orcamentos_abertos.append({
                        'status': status,
                        'valor': valor,
                        'dentista': dentista,
                        'created_at': created_at,
                        'items_count': len(items),
                        'items': items
                    })

            if orcamentos_abertos:
                # Pegar o orçamento mais recente ou de maior valor
                orcamento_principal = max(orcamentos_abertos,
                                        key=lambda x: (x.get('created_at', ''), float(x.get('valor', 0))))

                valor_formatado = f"R$ {orcamento_principal['valor']}"
                dentista = orcamento_principal['dentista']
                items_count = orcamento_principal['items_count']

                logger.info(f"💰 Orçamento em aberto encontrado: {valor_formatado} com {items_count} itens")

                # Criar recomendação específica para orçamento em aberto
                justificativa = (f"Paciente possui orçamento em aberto de {valor_formatado} "
                               f"com {items_count} procedimentos (Dr. {dentista}). "
                               f"Recomenda-se entrar em contato para retomar e aprovar o orçamento "
                               f"para conversão em plano de tratamento.")

                # Usar um procedimento genérico de "Consulta de Retorno" ou "Contato Comercial"
                return {
                    "id": 1,  # ID genérico - será mapeado para procedimento real
                    "name": "Contato para Aprovação de Orçamento",
                    "justificativa": justificativa,
                    "tipo": "orcamento_aberto",
                    "valor_orcamento": valor_formatado,
                    "items_count": items_count
                }

            logger.info("ℹ️ Nenhum orçamento em aberto encontrado")
            return None

        except Exception as e:
            logger.error(f"❌ Erro ao verificar orçamentos em aberto: {str(e)}")
            return None

    async def _gerar_raciocinio_especializado(self, prontuario: Dict[str, Any], decisao: Dict[str, Any],
                                            procedimentos_clinica: List[Dict[str, Any]],
                                            fluxo_tratamento: Optional[Dict[str, Any]]) -> str:
        """
        Gera o raciocínio clínico especializado para a recomendação.

        🧠 Raciocínio da recomendação (iaReasoning):
        A IA deve gerar uma justificativa baseada em:
        - Diagnósticos do prontuário
        - Procedimentos anteriores e sua data
        - Histórico de bruxismo, cáries, gengivite, restaurações etc.
        - Sugestões anteriores e seus desfechos
        """
        try:
            paciente = prontuario.get('patient', {})
            nome = paciente.get('name', 'Paciente')
            idade = self._extrair_idade_numerica(paciente.get('birthDate', ''))
            observacoes = paciente.get('notes', '')

            # Extrair informações relevantes
            treatment_plans = prontuario.get('treatmentPlans', [])
            schedulings = prontuario.get('schedulings', [])
            sugestoes_anteriores = prontuario.get('suggestions', [])

            # Construir raciocínio baseado na decisão
            justificativa = decisao.get('justificativa', '')
            procedimentos_recomendados = decisao.get('procedimentos_recomendados', [])

            # Usar IA para gerar raciocínio mais elaborado se disponível
            if ADK_AVAILABLE and self.google_api_key:
                try:
                    prompt_raciocinio = f"""
                    Como dentista especialista, crie uma recomendação DIRETA e OBJETIVA seguindo EXATAMENTE este formato:

                    "Sugiro o procedimento [nome do procedimento] devido a [justificativa específica e direta]."

                    DADOS:
                    - PACIENTE: {nome}, {idade} anos
                    - OBSERVAÇÕES: {observacoes}
                    - JUSTIFICATIVA: {justificativa}
                    - PROCEDIMENTO: {[p.get('name', '') for p in procedimentos_recomendados]}

                    REGRAS OBRIGATÓRIAS:
                    - Use EXATAMENTE o formato: "Sugiro o procedimento [X] devido a [Y]."
                    - Seja DIRETO e OBJETIVO
                    - Máximo 1 frase
                    - Não adicione explicações extras
                    - Foque na justificativa clínica principal

                    EXEMPLO: "Sugiro o procedimento limpeza dental devido ao acúmulo de tártaro observado no último exame."
                    """

                    raciocinio_ia = await self._gerar_com_ia_simples(prompt_raciocinio)
                    if raciocinio_ia and len(raciocinio_ia) > 50:
                        return raciocinio_ia

                except Exception as e:
                    logger.warning(f"Erro ao gerar raciocínio com IA: {str(e)}")

            # Fallback: gerar raciocínio baseado em regras
            raciocinio_partes = []

            # Adicionar contexto do paciente
            if idade >= 60:
                raciocinio_partes.append(f"Considerando a idade do paciente ({idade} anos), há necessidade de acompanhamento preventivo mais frequente.")
            elif idade <= 18:
                raciocinio_partes.append(f"Para paciente jovem ({idade} anos), é essencial manter cuidados preventivos regulares.")

            # Adicionar observações clínicas
            if observacoes:
                if any(palavra in observacoes.lower() for palavra in ['dor', 'sangramento', 'bruxismo']):
                    raciocinio_partes.append(f"As observações clínicas indicam: {observacoes[:100]}...")

            # Adicionar justificativa principal
            raciocinio_partes.append(justificativa)

            # Adicionar benefícios esperados
            if procedimentos_recomendados:
                proc_names = [p.get('name', '') for p in procedimentos_recomendados]
                raciocinio_partes.append(f"O(s) procedimento(s) {', '.join(proc_names)} proporcionará(ão) melhoria na saúde bucal e prevenção de complicações futuras.")

            return " ".join(raciocinio_partes)

        except Exception as e:
            logger.error(f"Erro ao gerar raciocínio especializado: {str(e)}")
            return decisao.get('justificativa', 'Recomendação baseada em análise clínica especializada.')

    def _formatar_procedimentos_recomendados(self, procedimentos_recomendados: List[Dict[str, Any]],
                                           procedimentos_clinica: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Formata os procedimentos recomendados para o formato esperado pela API.

        🔄 Resultado Final Esperado:
        {
          "procedureId": "uuid-do-procedimento",
          "expectedDate": "2024-01-15",
          "notes": "Observações específicas"
        }
        """
        from datetime import datetime, timedelta

        procedures = []

        try:
            for i, proc_recomendado in enumerate(procedimentos_recomendados):
                procedure_id = proc_recomendado.get('id')
                procedure_name = proc_recomendado.get('name', '')
                justificativa = proc_recomendado.get('justificativa', '')
                tipo = proc_recomendado.get('tipo', '')

                # Calcular data esperada (escalonada: 7, 14, 21 dias)
                days_offset = 7 + (i * 7)  # 7, 14, 21 dias
                expected_date = (datetime.now() + timedelta(days=days_offset)).strftime("%Y-%m-%d")

                # Tratamento especial para orçamentos em aberto
                if tipo == 'orcamento_aberto':
                    # Buscar um procedimento de "Consulta" ou "Retorno" na clínica
                    procedure_id_real = self._encontrar_procedimento_consulta(procedimentos_clinica)

                    # Se não encontrar, usar o primeiro procedimento disponível
                    if not procedure_id_real and procedimentos_clinica:
                        procedure_id_real = procedimentos_clinica[0].get('id', 1)

                    # Usar data mais próxima para orçamentos (3 dias)
                    expected_date = (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d")

                    procedures.append({
                        "procedureId": procedure_id_real or 1,
                        "expectedDate": expected_date,
                        "notes": f"ORÇAMENTO EM ABERTO - {justificativa}"
                    })

                    logger.info(f"💰 Orçamento em aberto formatado: ID={procedure_id_real} para {expected_date}")
                else:
                    # Formatação normal para outros procedimentos
                    procedures.append({
                        "procedureId": procedure_id,
                        "expectedDate": expected_date,
                        "notes": f"{procedure_name} - {justificativa}"
                    })

                    logger.info(f"✅ Procedimento formatado: {procedure_name} (ID: {procedure_id}) para {expected_date}")

        except Exception as e:
            logger.error(f"Erro ao formatar procedimentos recomendados: {str(e)}")

        return procedures

    def _encontrar_procedimento_consulta(self, procedimentos_clinica: List[Dict[str, Any]]) -> Optional[int]:
        """Encontra um procedimento de consulta/retorno na clínica."""
        try:
            palavras_consulta = ['consulta', 'retorno', 'avaliação', 'revisão', 'acompanhamento']

            for proc in procedimentos_clinica:
                nome = proc.get('name', '').lower()
                if any(palavra in nome for palavra in palavras_consulta):
                    return proc.get('id')

            return None
        except Exception as e:
            logger.warning(f"Erro ao encontrar procedimento de consulta: {str(e)}")
            return None

    async def _gerar_com_ia_simples(self, prompt: str) -> str:
        """Gera resposta usando IA de forma simplificada."""
        try:
            # Tentar usar Gemini direto
            try:
                import google.generativeai as genai

                genai.configure(api_key=self.google_api_key)
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content(prompt)

                if response and response.text:
                    return response.text.strip()

            except Exception as e:
                logger.warning(f"Erro ao usar Gemini para raciocínio: {str(e)}")

            return ""

        except Exception as e:
            logger.error(f"Erro geral ao gerar com IA simples: {str(e)}")
            return ""

    async def _processar_sugestao(self, sugestao: Dict[str, Any], fluxo_tratamento: Optional[Dict[str, Any]], procedimentos_clinica: List[Dict[str, Any]]) -> bool:
        """
        Processa uma sugestão específica.

        Args:
            sugestao: Dados da sugestão
            fluxo_tratamento: Dados do fluxo de tratamento
            procedimentos_clinica: Lista de procedimentos disponíveis na clínica

        Returns:
            bool: True se processada com sucesso, False caso contrário
        """
        try:
            patient_id = sugestao.get('patientId')
            suggestion_id = sugestao.get('id')

            logger.info(f"Processando sugestão {suggestion_id} para o paciente {patient_id}")

            # Buscar prontuário completo do paciente
            prontuario = await self._buscar_prontuario_completo(patient_id)

            if not prontuario:
                logger.error(f"Não foi possível obter o prontuário do paciente {patient_id}")
                return False

            # Verificar se o Google ADK está disponível
            if not ADK_AVAILABLE:
                logger.error("Google ADK não está disponível. Instale o pacote google-adk para usar este agente.")
                return False

            # Gerar sugestões usando Google ADK
            ia_reasoning = await self._gerar_sugestoes_com_adk(prontuario, fluxo_tratamento, procedimentos_clinica)

            if not ia_reasoning:
                logger.error("Falha ao gerar sugestões com IA")
                return False

            # Extrair procedimentos da resposta da IA
            procedures = self._extrair_procedimentos_da_resposta(ia_reasoning, procedimentos_clinica)

            # Atualizar a sugestão
            payload = {
                "status": "PENDING_REVIEW",
                "iaReasoning": ia_reasoning,
                "procedures": procedures
            }

            resultado = await self._atualizar_sugestao(suggestion_id, payload)

            if resultado:
                logger.info(f"Sugestão {suggestion_id} atualizada com sucesso")
                return True
            else:
                logger.error(f"Falha ao atualizar sugestão {suggestion_id}")
                return False

        except Exception as e:
            logger.error(f"Erro ao processar sugestão: {str(e)}")
            return False

    async def _atualizar_sugestao(self, suggestion_id: str, payload: Dict[str, Any]) -> bool:
        """
        Atualiza uma sugestão via API.

        Args:
            suggestion_id: ID da sugestão
            payload: Dados para atualização

        Returns:
            bool: True se atualizada com sucesso, False caso contrário
        """
        try:
            logger.info(f"Atualizando sugestão {suggestion_id}")
            response = await self.api_service.patch(f"/suggestions/{suggestion_id}", data=payload)

            if response:
                logger.info(f"Sugestão {suggestion_id} atualizada com sucesso")
                return True
            else:
                logger.error(f"Falha ao atualizar sugestão {suggestion_id}")
                return False

        except Exception as e:
            logger.error(f"Erro ao atualizar sugestão {suggestion_id}: {str(e)}")
            return False

    def _formatar_tratamentos(self, fluxograma):
        """
        Formata os tratamentos do fluxograma para o prompt.

        Args:
            fluxograma (dict): Dados do fluxograma

        Returns:
            str: Texto formatado com os tratamentos
        """
        if not fluxograma or "procedures" not in fluxograma:
            return "Nenhum tratamento anterior registrado."

        procedimentos = fluxograma.get("procedures", [])
        texto = "Tratamentos realizados:\n"

        for proc in procedimentos:
            nome = proc.get("name", "Procedimento sem nome")
            data = proc.get("date", "Data não registrada")
            status = proc.get("status", "Status não registrado")

            texto += f"- {nome} ({data}): {status}\n"

        return texto

    async def _gerar_sugestoes_com_adk(self, prontuario: Dict[str, Any], fluxo_tratamento: Optional[Dict[str, Any]], procedimentos_clinica: List[Dict[str, Any]]) -> str:
        """
        Gera sugestões de tratamento usando o Google ADK Agents 1.0.0.

        Args:
            prontuario: Dados completos do prontuário do paciente
            fluxo_tratamento: Dados do fluxo de tratamento da clínica
            procedimentos_clinica: Lista de procedimentos disponíveis na clínica

        Returns:
            str: Resposta gerada pelo agente
        """
        try:
            logger.info("Iniciando geração de sugestões com Google ADK 1.0.0")

            # Criar o agente odontológico
            agente = Agent(
                name="agente_odontologico",
                model="gemini-1.5-pro",
                description="Agente especialista em odontologia que sugere tratamentos.",
                tools=[],
                instruction=f"""
                    Você é um dentista especialista em análise de prontuários e planejamento de tratamentos.

                    PROCEDIMENTOS DISPONÍVEIS NA CLÍNICA:
                    {self._formatar_procedimentos_clinica(procedimentos_clinica)}

                    FLUXO DE TRATAMENTO DA CLÍNICA:
                    {self._formatar_fluxo_tratamento_inteligente(fluxo_tratamento)}

                    Analise o prontuário do paciente e recomende APENAS os tratamentos realmente necessários baseados em:
                    - Histórico médico e odontológico do paciente
                    - Tratamentos já realizados (evite duplicações)
                    - Idade e condições específicas do paciente
                    - APENAS procedimentos disponíveis na clínica (lista acima)
                    - Fluxo de tratamento da clínica (sequência e intervalos)

                    REGRAS IMPORTANTES:
                    - Seja SELETIVO: recomende apenas 1-3 procedimentos realmente necessários
                    - Use APENAS procedimentos da lista disponível na clínica
                    - NÃO recomende procedimentos já realizados recentemente
                    - Siga o fluxo de tratamento: se o paciente fez X, sugira o próximo procedimento Y após Z dias
                    - Priorize: urgências > preventivos > estéticos
                    - Considere intervalos adequados entre procedimentos conforme o fluxo
                    - Base suas recomendações em evidências clínicas
                    - Faça uma recomendação ou mais detalhada, mas não faça mais de uma recomendação por vez.

                    FORMATO DA RESPOSTA (seja direto e conciso):
                    Sugiro tratamento de [procedimento1] e [procedimento2] devido a [justificativa clínica específica].

                    EXEMPLOS:
                    - "Sugiro tratamento de Limpeza Dental Profissional devido ao intervalo de 8 meses desde a última profilaxia."
                    - "Sugiro tratamento de Radiografia Panorâmica e Consulta de Retorno devido à necessidade de acompanhamento pós-tratamento periodontal."
                    - "Sugiro tratamento de Consulta de Urgência devido a relatos de dor na anamnese."

                    Seja objetivo, profissional e recomende apenas procedimentos da lista disponível.
                """
            )

            # Preparar dados do paciente
            paciente_info = prontuario.get('patient', {})
            anamneses = prontuario.get('anamneses', [])
            budgets = prontuario.get('budgets', [])
            schedulings = prontuario.get('schedulings', [])
            treatment_plans = prontuario.get('treatmentPlans', [])
            exams = prontuario.get('exams', [])

            # Preparar a entrada para o agente
            entrada_do_agente = f"""
DADOS DO PACIENTE:
Nome: {paciente_info.get('name', 'N/A')}
Idade: {self._calcular_idade(paciente_info.get('birthDate', ''))}
Gênero: {paciente_info.get('gender', 'N/A')}
Profissão: {paciente_info.get('profession', 'N/A')}
Observações: {paciente_info.get('notes', 'Nenhuma')}

ANAMNESES:
{self._formatar_anamneses(anamneses)}

ORÇAMENTOS:
{self._formatar_budgets(budgets)}

AGENDAMENTOS:
{self._formatar_schedulings(schedulings)}

PLANOS DE TRATAMENTO:
{self._formatar_treatment_plans(treatment_plans)}

EXAMES:
{self._formatar_exams(exams)}

FLUXO DE TRATAMENTO DA CLÍNICA:
{self._formatar_fluxo_tratamento(fluxo_tratamento)}
"""

            logger.info(f"Entrada preparada para o agente: {entrada_do_agente[:200]}...")

            # Criar sessão e runner com abordagem mais robusta
            import time
            import uuid

            # Gerar IDs únicos baseados em timestamp e UUID
            timestamp = int(time.time() * 1000)
            unique_id = str(uuid.uuid4())[:8]
            session_id = f"session_{timestamp}_{unique_id}"
            user_id = f"user_{timestamp}"
            app_name = f"OdontoApp_{timestamp}"

            logger.info(f"Criando sessão com ID único: {session_id}")

            # Criar serviço de sessão
            session_service = InMemorySessionService()

            # Criar sessão com parâmetros únicos
            try:
                session = session_service.create_session(
                    app_name=app_name,
                    user_id=user_id,
                    session_id=session_id
                )
                logger.info(f"Sessão criada com sucesso: {session_id}")
            except Exception as session_error:
                logger.error(f"Erro ao criar sessão: {str(session_error)}")
                raise

            # Criar runner com os mesmos parâmetros da sessão
            try:
                runner = Runner(
                    agent=agente,
                    app_name=app_name,
                    session_service=session_service
                )
                logger.info("Runner criado com sucesso")
            except Exception as runner_error:
                logger.error(f"Erro ao criar runner: {str(runner_error)}")
                raise

            # Preparar conteúdo da mensagem
            try:
                content = types.Content(
                    role="user",
                    parts=[types.Part(text=entrada_do_agente)]
                )
                logger.info("Conteúdo preparado com sucesso")
            except Exception as content_error:
                logger.error(f"Erro ao preparar conteúdo: {str(content_error)}")
                raise

            logger.info("Iniciando execução do agente...")

            # Executar o agente com tratamento de erro robusto
            final_response = ""
            try:
                events = runner.run(
                    user_id=user_id,
                    session_id=session_id,
                    new_message=content
                )

                for event in events:
                    logger.info(f"Evento recebido: {type(event).__name__}")

                    # Verificar se é resposta final
                    if hasattr(event, 'is_final_response') and callable(event.is_final_response):
                        if event.is_final_response():
                            logger.info("Resposta final detectada")
                            if hasattr(event, 'content') and event.content:
                                for part in event.content.parts:
                                    if hasattr(part, 'text') and part.text:
                                        final_response += part.text + "\n"

                    # Fallback: coletar qualquer conteúdo de texto
                    elif hasattr(event, 'content') and event.content:
                        if hasattr(event.content, 'parts'):
                            for part in event.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    final_response += part.text + "\n"

                logger.info(f"Execução concluída. Resposta coletada: {len(final_response)} caracteres")

            except Exception as execution_error:
                logger.error(f"Erro durante execução do agente: {str(execution_error)}")
                logger.exception("Detalhes completos do erro:")
                raise

            # Verificar se obtivemos uma resposta válida
            if final_response.strip():
                logger.info(f"Resposta final gerada com sucesso: {final_response[:100]}...")
                return final_response.strip()
            else:
                logger.warning("Nenhuma resposta foi gerada pelo agente ADK, tentando Gemini direto")
                return await self._tentar_gemini_direto(entrada_do_agente, prontuario, fluxo_tratamento, procedimentos_clinica)

        except Exception as e:
            logger.error(f"Erro ao gerar sugestões com ADK 1.0.0: {str(e)}")
            logger.info("Tentando usar Gemini API diretamente")
            return await self._tentar_gemini_direto(entrada_do_agente, prontuario, fluxo_tratamento, procedimentos_clinica)

    async def _tentar_gemini_direto(self, entrada_do_agente: str, prontuario: Dict[str, Any], fluxo_tratamento: Optional[Dict[str, Any]], procedimentos_clinica: List[Dict[str, Any]]) -> str:
        """
        Tenta usar o Google Gemini API diretamente quando o ADK falha.

        Args:
            entrada_do_agente: Prompt formatado para o agente
            prontuario: Dados do prontuário
            fluxo_tratamento: Fluxo de tratamento da clínica

        Returns:
            str: Resposta da IA ou fallback
        """
        try:
            logger.info("Tentando usar Google Gemini API diretamente")

            # Tentar importar e usar o Gemini diretamente
            try:
                import google.generativeai as genai
                import os

                # Configurar API key (se disponível)
                api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
                if not api_key:
                    logger.warning("API key do Google não encontrada, usando fallback")
                    return self._gerar_sugestoes_fallback(prontuario, fluxo_tratamento)

                genai.configure(api_key=api_key)

                # Criar modelo
                model = genai.GenerativeModel('gemini-1.5-flash')

                # Prompt melhorado com procedimentos da clínica
                prompt_completo = f"""
Você é um dentista especialista em análise de prontuários e planejamento de tratamentos.

PROCEDIMENTOS DISPONÍVEIS NA CLÍNICA:
{self._formatar_procedimentos_clinica(procedimentos_clinica)}

FLUXO DE TRATAMENTO DA CLÍNICA:
{self._formatar_fluxo_tratamento_inteligente(fluxo_tratamento)}

Analise o prontuário do paciente e recomende APENAS os tratamentos realmente necessários baseados em:
- Histórico médico e odontológico do paciente
- Tratamentos já realizados (evite duplicações)
- Idade e condições específicas do paciente
- APENAS procedimentos disponíveis na clínica (lista acima)
- Fluxo de tratamento da clínica (sequência e intervalos)

REGRAS IMPORTANTES:
- Seja SELETIVO: recomende apenas 1-3 procedimentos realmente necessários
- Use APENAS procedimentos da lista disponível na clínica
- NÃO recomende procedimentos já realizados recentemente
- Siga o fluxo de tratamento: se o paciente fez X, sugira o próximo procedimento Y após Z dias
- Priorize: urgências > preventivos > estéticos
- Considere intervalos adequados entre procedimentos conforme o fluxo
- Base suas recomendações em evidências clínicas

FORMATO DA RESPOSTA OBRIGATÓRIO:
"Sugiro o procedimento [nome exato do procedimento] devido a [justificativa específica e direta]."

REGRAS OBRIGATÓRIAS:
- Use EXATAMENTE o formato: "Sugiro o procedimento [X] devido a [Y]."
- Seja DIRETO e OBJETIVO - máximo 1 frase
- Use o nome EXATO do procedimento da lista disponível
- Justificativa deve ser específica e clínica
- NÃO adicione explicações extras

EXEMPLOS CORRETOS:
- "Sugiro o procedimento Limpeza Dental devido ao acúmulo de tártaro observado."
- "Sugiro o procedimento Restauração devido à presença de cárie detectada."
- "Sugiro o procedimento Consulta de Retorno devido ao tempo decorrido desde o último atendimento."

DADOS DO PACIENTE:
{entrada_do_agente}

Responda APENAS com uma frase no formato obrigatório, usando procedimento da lista disponível.
"""

                # Gerar resposta
                response = model.generate_content(prompt_completo)

                if response and response.text:
                    logger.info("Resposta obtida com sucesso via Gemini direto")
                    logger.info(f"Resposta do Gemini: {response.text[:200]}...")
                    return response.text.strip()
                else:
                    logger.warning("Resposta vazia do Gemini, usando fallback")
                    return self._gerar_sugestoes_fallback(prontuario, fluxo_tratamento)

            except ImportError:
                logger.warning("Google Generative AI não disponível, usando fallback")
                return self._gerar_sugestoes_fallback(prontuario, fluxo_tratamento)
            except Exception as gemini_error:
                logger.error(f"Erro ao usar Gemini direto: {str(gemini_error)}")
                return self._gerar_sugestoes_fallback(prontuario, fluxo_tratamento)

        except Exception as e:
            logger.error(f"Erro geral ao tentar Gemini direto: {str(e)}")
            return self._gerar_sugestoes_fallback(prontuario, fluxo_tratamento)

    def _formatar_procedimentos_clinica(self, procedimentos_clinica: List[Dict[str, Any]]) -> str:
        """
        Formata a lista de procedimentos disponíveis na clínica.

        Args:
            procedimentos_clinica: Lista de procedimentos da clínica

        Returns:
            str: Texto formatado com os procedimentos
        """
        if not procedimentos_clinica:
            return "Nenhum procedimento disponível."

        texto = ""
        for proc in procedimentos_clinica:
            id_proc = proc.get('id', 'N/A')
            nome = proc.get('name', 'N/A')
            descricao = proc.get('description', '')
            tipo = proc.get('type', '')
            preco = proc.get('defaultPrice', '')
            duracao = proc.get('estimatedDuration', '')

            texto += f"- ID {id_proc}: {nome}"
            if tipo:
                texto += f" (Tipo: {tipo})"
            if preco:
                texto += f" - Preço: R$ {preco}"
            if duracao:
                texto += f" - Duração: {duracao}min"
            if descricao:
                texto += f"\n  Descrição: {descricao}"
            texto += "\n"

        return texto

    def _formatar_fluxo_tratamento_inteligente(self, fluxo_tratamento: Optional[Any]) -> str:
        """
        Formata o fluxo de tratamento da clínica de forma inteligente.

        Args:
            fluxo_tratamento: Dados do fluxo de tratamento

        Returns:
            str: Texto formatado com o fluxo
        """
        if not fluxo_tratamento:
            return "Fluxo de tratamento não disponível."

        if isinstance(fluxo_tratamento, list):
            texto = "Sequência de tratamentos recomendada:\n"
            for item in fluxo_tratamento:
                from_proc = item.get('fromProcedure', {})
                to_proc = item.get('toProcedure', {})
                days_after = item.get('daysAfter', 'N/A')

                from_name = from_proc.get('name', 'N/A')
                to_name = to_proc.get('name', 'N/A')

                texto += f"- Após '{from_name}', aguardar {days_after} dias para '{to_name}'\n"
        else:
            texto = "Procedimentos disponíveis:\n"
            procedures = fluxo_tratamento.get('procedures', [])
            for proc in procedures:
                name = proc.get('name', 'N/A')
                description = proc.get('description', '')
                texto += f"- {name}: {description}\n"

        return texto

    def _gerar_sugestoes_fallback(self, prontuario: Dict[str, Any], fluxo_tratamento: Optional[Dict[str, Any]]) -> str:
        """
        Gera sugestões de fallback baseadas em regras clínicas inteligentes quando a IA falha.

        Args:
            prontuario: Dados completos do prontuário do paciente
            fluxo_tratamento: Fluxo de tratamento da clínica

        Returns:
            str: Sugestões de tratamento baseadas em regras
        """
        try:
            logger.info("Gerando sugestões de fallback baseadas em regras clínicas inteligentes")

            paciente = prontuario.get('patient', {})
            budgets = prontuario.get('budgets', [])
            treatment_plans = prontuario.get('treatmentPlans', [])
            schedulings = prontuario.get('schedulings', [])
            anamneses = prontuario.get('anamneses', [])

            # Análise detalhada do paciente
            nome = paciente.get('name', 'Paciente')
            idade_anos = self._extrair_idade_numerica(paciente.get('birthDate', ''))
            observacoes = paciente.get('notes', '').lower()
            profissao = paciente.get('profession', '').lower()

            logger.info(f"Analisando paciente {nome}, {idade_anos} anos, profissão: {profissao}")

            # Analisar histórico de tratamentos
            tratamentos_recentes = self._analisar_tratamentos_recentes(treatment_plans, schedulings)
            ultima_limpeza = self._calcular_tempo_ultima_limpeza(schedulings, budgets)

            # Analisar anamneses para sintomas
            sintomas_reportados = self._analisar_sintomas_anamneses(anamneses)

            # Sistema de decisão inteligente
            procedimentos_necessarios = []
            justificativas = []

            # 1. Verificar urgências baseadas em sintomas
            if sintomas_reportados['dor'] or 'dor' in observacoes:
                procedimentos_necessarios.append("Consulta de Urgência")
                justificativas.append("relatos de dor ou desconforto")
            elif sintomas_reportados['sangramento'] or any(palavra in observacoes for palavra in ['sangramento', 'gengiva', 'periodontal']):
                procedimentos_necessarios.append("Consulta Periodontal")
                justificativas.append("sinais de problemas gengivais ou periodontais")

            # 2. Verificar necessidade de limpeza baseada no tempo
            elif ultima_limpeza > 6:  # Mais de 6 meses
                procedimentos_necessarios.append("Limpeza Dental Profissional")
                justificativas.append(f"intervalo de {ultima_limpeza} meses desde a última profilaxia")
            elif ultima_limpeza > 4 and idade_anos > 60:  # Idosos precisam de mais frequência
                procedimentos_necessarios.append("Limpeza Dental Profissional")
                justificativas.append("manutenção preventiva recomendada para pacientes acima de 60 anos")

            # 3. Verificar necessidade de acompanhamento
            if tratamentos_recentes['tem_tratamento_concluido']:
                if not procedimentos_necessarios:  # Se não há urgência
                    procedimentos_necessarios.append("Consulta de Retorno")
                    justificativas.append("acompanhamento pós-tratamento")
                else:
                    procedimentos_necessarios.append("Consulta de Retorno")
                    justificativas.append("acompanhamento pós-tratamento")

            # 4. Verificar necessidade de exames baseado na idade
            if idade_anos > 40 and not self._tem_radiografia_recente(schedulings):
                if len(procedimentos_necessarios) < 2:  # Não sobrecarregar
                    procedimentos_necessarios.append("Radiografia Panorâmica")
                    justificativas.append("exame preventivo recomendado para pacientes acima de 40 anos")

            # 5. Fallback para casos sem indicação específica
            if not procedimentos_necessarios:
                if ultima_limpeza > 3:  # Mais de 3 meses
                    procedimentos_necessarios.append("Limpeza Dental Profissional")
                    justificativas.append("manutenção preventiva da saúde bucal")
                else:
                    procedimentos_necessarios.append("Consulta de Avaliação")
                    justificativas.append("avaliação clínica de rotina")

            # Gerar resposta personalizada no formato direto
            if len(procedimentos_necessarios) == 1:
                analise = f"Sugiro o procedimento {procedimentos_necessarios[0]} devido a {justificativas[0]}."
            else:
                # Para múltiplos procedimentos, focar no mais importante
                analise = f"Sugiro o procedimento {procedimentos_necessarios[0]} devido a {justificativas[0]}."

            logger.info(f"Sugestão personalizada gerada para {nome}: {analise[:100]}...")
            return analise.strip()

        except Exception as e:
            logger.error(f"Erro ao gerar sugestões de fallback: {str(e)}")
            return "Sugiro o procedimento Consulta de Avaliação devido à necessidade de exame clínico detalhado."

    def _calcular_idade(self, birth_date: str) -> str:
        """Calcula a idade a partir da data de nascimento."""
        if not birth_date:
            return "N/A"
        try:
            from datetime import datetime
            birth = datetime.strptime(birth_date, "%Y-%m-%d")
            today = datetime.now()
            age = today.year - birth.year - ((today.month, today.day) < (birth.month, birth.day))
            return f"{age} anos"
        except:
            return "N/A"

    def _extrair_idade_numerica(self, birth_date: str) -> int:
        """Extrai a idade numérica a partir da data de nascimento."""
        if not birth_date:
            return 30  # Idade padrão
        try:
            from datetime import datetime
            birth = datetime.strptime(birth_date, "%Y-%m-%d")
            today = datetime.now()
            age = today.year - birth.year - ((today.month, today.day) < (birth.month, birth.day))
            return age
        except:
            return 30  # Idade padrão

    def _analisar_tratamentos_recentes(self, treatment_plans: List[Dict[str, Any]], schedulings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analisa tratamentos recentes do paciente."""
        from datetime import datetime, timedelta

        resultado = {
            'tem_tratamento_concluido': False,
            'ultimo_tratamento_dias': 999,
            'tipos_tratamento': []
        }

        try:
            # Verificar planos de tratamento concluídos
            for plan in treatment_plans:
                if plan.get('status') == 'completed':
                    resultado['tem_tratamento_concluido'] = True
                    # Aqui poderia calcular dias desde conclusão se tivesse data

            # Verificar agendamentos recentes
            hoje = datetime.now()
            for scheduling in schedulings:
                try:
                    data_agendamento = datetime.strptime(scheduling.get('date', ''), "%Y-%m-%d")
                    dias_passados = (hoje - data_agendamento).days

                    if dias_passados < resultado['ultimo_tratamento_dias']:
                        resultado['ultimo_tratamento_dias'] = dias_passados

                except:
                    continue

        except Exception as e:
            logger.warning(f"Erro ao analisar tratamentos recentes: {str(e)}")

        return resultado

    def _calcular_tempo_ultima_limpeza(self, schedulings: List[Dict[str, Any]], budgets: List[Dict[str, Any]]) -> int:
        """Calcula quantos meses se passaram desde a última limpeza."""
        from datetime import datetime

        try:
            ultima_limpeza = None
            hoje = datetime.now()

            # Verificar agendamentos de limpeza
            for scheduling in schedulings:
                notes = scheduling.get('notes', '').lower()
                if any(palavra in notes for palavra in ['limpeza', 'profilaxia', 'higiene']):
                    try:
                        data = datetime.strptime(scheduling.get('date', ''), "%Y-%m-%d")
                        if not ultima_limpeza or data > ultima_limpeza:
                            ultima_limpeza = data
                    except:
                        continue

            # Verificar orçamentos de limpeza
            for budget in budgets:
                items = budget.get('items', [])
                for item in items:
                    proc_name = item.get('procedureName', '').lower()
                    if any(palavra in proc_name for palavra in ['limpeza', 'profilaxia', 'higiene']):
                        # Assumir que foi realizado (simplificação)
                        try:
                            data = datetime.strptime(budget.get('createdAt', ''), "%Y-%m-%d")
                            if not ultima_limpeza or data > ultima_limpeza:
                                ultima_limpeza = data
                        except:
                            continue

            if ultima_limpeza:
                meses = (hoje.year - ultima_limpeza.year) * 12 + (hoje.month - ultima_limpeza.month)
                return max(0, meses)
            else:
                return 12  # Assumir 12 meses se não encontrar histórico

        except Exception as e:
            logger.warning(f"Erro ao calcular tempo da última limpeza: {str(e)}")
            return 8  # Valor padrão

    def _analisar_sintomas_anamneses(self, anamneses: List[Dict[str, Any]]) -> Dict[str, bool]:
        """Analisa sintomas reportados nas anamneses."""
        sintomas = {
            'dor': False,
            'sangramento': False,
            'sensibilidade': False,
            'mobilidade': False
        }

        try:
            for anamnese in anamneses:
                notes = anamnese.get('notes', '').lower()

                if any(palavra in notes for palavra in ['dor', 'dolorido', 'doendo', 'machuca']):
                    sintomas['dor'] = True

                if any(palavra in notes for palavra in ['sangramento', 'sangra', 'sangue']):
                    sintomas['sangramento'] = True

                if any(palavra in notes for palavra in ['sensibilidade', 'sensível', 'gelado', 'quente']):
                    sintomas['sensibilidade'] = True

                if any(palavra in notes for palavra in ['mobilidade', 'mole', 'balança']):
                    sintomas['mobilidade'] = True

        except Exception as e:
            logger.warning(f"Erro ao analisar sintomas: {str(e)}")

        return sintomas

    def _tem_radiografia_recente(self, schedulings: List[Dict[str, Any]]) -> bool:
        """Verifica se há radiografia recente (últimos 12 meses)."""
        from datetime import datetime, timedelta

        try:
            limite = datetime.now() - timedelta(days=365)

            for scheduling in schedulings:
                notes = scheduling.get('notes', '').lower()
                if any(palavra in notes for palavra in ['radiografia', 'raio-x', 'rx']):
                    try:
                        data = datetime.strptime(scheduling.get('date', ''), "%Y-%m-%d")
                        if data > limite:
                            return True
                    except:
                        continue

        except Exception as e:
            logger.warning(f"Erro ao verificar radiografia recente: {str(e)}")

        return False

    def _formatar_anamneses(self, anamneses: List[Dict[str, Any]]) -> str:
        """Formata as anamneses do paciente."""
        if not anamneses:
            return "Nenhuma anamnese registrada."

        texto = ""
        for i, anamnese in enumerate(anamneses, 1):
            texto += f"Anamnese {i}:\n"
            # Adicionar detalhes da anamnese conforme estrutura da API
            texto += f"- Data: {anamnese.get('createdAt', 'N/A')}\n"
            texto += f"- Observações: {anamnese.get('notes', 'Nenhuma')}\n\n"
        return texto

    def _formatar_budgets(self, budgets: List[Dict[str, Any]]) -> str:
        """Formata os orçamentos do paciente."""
        if not budgets:
            return "Nenhum orçamento registrado."

        texto = ""
        for budget in budgets:
            status = budget.get('status', 'N/A')
            valor = budget.get('totalValue', 'N/A')
            dentista = budget.get('dentistName', 'N/A')
            texto += f"- Orçamento: R$ {valor} - Status: {status} - Dentista: {dentista}\n"

            items = budget.get('items', [])
            for item in items:
                proc_name = item.get('procedureName', 'N/A')
                tooth = item.get('tooth', '')
                value = item.get('value', 'N/A')
                tooth_info = f" (Dente {tooth})" if tooth else ""
                texto += f"  • {proc_name}{tooth_info}: R$ {value}\n"
            texto += "\n"
        return texto

    def _formatar_schedulings(self, schedulings: List[Dict[str, Any]]) -> str:
        """Formata os agendamentos do paciente."""
        if not schedulings:
            return "Nenhum agendamento registrado."

        texto = ""
        for scheduling in schedulings:
            date = scheduling.get('date', 'N/A')
            time = scheduling.get('time', 'N/A')
            status = scheduling.get('status', 'N/A')
            dentista = scheduling.get('dentistName', 'N/A')
            notes = scheduling.get('notes', 'Nenhuma')
            texto += f"- {date} às {time} - Status: {status} - Dentista: {dentista}\n"
            texto += f"  Observações: {notes}\n\n"
        return texto

    def _formatar_treatment_plans(self, treatment_plans: List[Dict[str, Any]]) -> str:
        """Formata os planos de tratamento do paciente."""
        if not treatment_plans:
            return "Nenhum plano de tratamento registrado."

        texto = ""
        for plan in treatment_plans:
            valor = plan.get('totalValue', 'N/A')
            percentage = plan.get('completionPercentage', 'N/A')
            status = plan.get('status', 'N/A')
            texto += f"- Plano: R$ {valor} - Progresso: {percentage}% - Status: {status}\n"
        return texto

    def _formatar_exams(self, exams: List[Dict[str, Any]]) -> str:
        """Formata os exames do paciente."""
        if not exams:
            return "Nenhum exame registrado."

        texto = ""
        for exam in exams:
            # Adicionar detalhes dos exames conforme estrutura da API
            texto += f"- Exame: {exam.get('type', 'N/A')} - Data: {exam.get('date', 'N/A')}\n"
        return texto

    def _formatar_fluxo_tratamento(self, fluxo_tratamento: Optional[Any]) -> str:
        """Formata o fluxo de tratamento da clínica."""
        if not fluxo_tratamento:
            return "Fluxo de tratamento não disponível."

        # Verificar se é uma lista ou dicionário
        if isinstance(fluxo_tratamento, list):
            # Se for uma lista, tratar cada item como um procedimento
            texto = "Fluxo de tratamento da clínica:\n"
            for item in fluxo_tratamento:
                from_proc = item.get('fromProcedure', {})
                to_proc = item.get('toProcedure', {})
                days_after = item.get('daysAfter', 'N/A')

                from_name = from_proc.get('name', 'N/A')
                to_name = to_proc.get('name', 'N/A')

                texto += f"- Após {from_name}, aguardar {days_after} dias para {to_name}\n"
        else:
            # Se for um dicionário, usar a estrutura original
            texto = "Procedimentos disponíveis na clínica:\n"
            procedures = fluxo_tratamento.get('procedures', [])
            for proc in procedures:
                name = proc.get('name', 'N/A')
                description = proc.get('description', '')
                texto += f"- {name}: {description}\n"

        return texto

    def _extrair_procedimentos_da_resposta(self, resposta_ia: str, procedimentos_clinica: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extrai procedimentos sugeridos da resposta da IA no novo formato, mapeando para IDs reais da clínica.

        Args:
            resposta_ia: Resposta gerada pela IA
            procedimentos_clinica: Lista de procedimentos disponíveis na clínica

        Returns:
            List[Dict[str, Any]]: Lista de procedimentos sugeridos com IDs reais
        """
        procedimentos = []

        try:
            logger.info(f"Tentando extrair procedimentos da resposta: {resposta_ia[:200]}...")

            # Novo formato: "Sugiro tratamento de [procedimento1] e [procedimento2] devido a [justificativa]"
            import re

            # Buscar padrão "Sugiro tratamento de ... devido a ..."
            match = re.search(r'Sugiro tratamento de (.+?) devido a (.+?)\.?$', resposta_ia, re.IGNORECASE | re.DOTALL)

            if match:
                procedimentos_texto = match.group(1).strip()
                justificativa = match.group(2).strip()

                logger.info(f"Padrão encontrado - Procedimentos: '{procedimentos_texto}', Justificativa: '{justificativa}'")

                # Separar procedimentos por "e" ou ","
                nomes_procedimentos = []
                if ' e ' in procedimentos_texto:
                    nomes_procedimentos = [p.strip() for p in procedimentos_texto.split(' e ')]
                elif ', ' in procedimentos_texto:
                    nomes_procedimentos = [p.strip() for p in procedimentos_texto.split(', ')]
                else:
                    nomes_procedimentos = [procedimentos_texto.strip()]

                logger.info(f"Procedimentos separados: {nomes_procedimentos}")

                # Criar entrada para cada procedimento
                from datetime import datetime, timedelta

                for i, nome_proc in enumerate(nomes_procedimentos):
                    if nome_proc:
                        # Buscar ID real do procedimento na clínica
                        procedure_id = self._encontrar_id_procedimento(nome_proc, procedimentos_clinica)
                        logger.info(f"Procedimento '{nome_proc}' mapeado para ID {procedure_id}")

                        # Calcular data esperada (escalonada: 7, 14, 21 dias)
                        days_offset = 7 + (i * 7)  # 7, 14, 21 dias
                        expected_date = (datetime.now() + timedelta(days=days_offset)).strftime("%Y-%m-%d")

                        procedimentos.append({
                            "procedureId": procedure_id,
                            "expectedDate": expected_date,
                            "notes": f"{nome_proc} - {justificativa}"
                        })

                logger.info(f"Total de procedimentos extraídos: {len(procedimentos)}")
            else:
                logger.warning(f"Padrão não encontrado na resposta: {resposta_ia}")

            # Fallback: se não encontrou o padrão, tentar extrair procedimentos de forma mais simples
            if not procedimentos:
                logger.warning("Não foi possível extrair procedimentos no novo formato, usando fallback")
                # Buscar qualquer menção a procedimentos da clínica
                for proc_clinica in procedimentos_clinica:
                    nome_proc = proc_clinica.get('name', '')
                    if nome_proc.lower() in resposta_ia.lower():
                        from datetime import datetime, timedelta
                        expected_date = (datetime.now() + timedelta(days=14)).strftime("%Y-%m-%d")

                        procedimentos.append({
                            "procedureId": proc_clinica.get('id', 1),
                            "expectedDate": expected_date,
                            "notes": f"{nome_proc} - Sugerido pela IA"
                        })
                        logger.info(f"Fallback: encontrado procedimento '{nome_proc}' na resposta")
                        break  # Adicionar apenas o primeiro encontrado

            # Fallback mais agressivo: se ainda não encontrou nada, usar palavras-chave
            if not procedimentos:
                logger.warning("Fallback simples falhou, tentando busca por palavras-chave")
                palavras_chave_resposta = {
                    'limpeza': ['limpeza', 'profilaxia', 'higiene'],
                    'consulta': ['consulta', 'avaliação', 'exame'],
                    'radiografia': ['radiografia', 'raio-x', 'rx'],
                    'urgência': ['urgência', 'emergência', 'dor'],
                    'periodontal': ['periodontal', 'gengiva', 'periodontia'],
                    'retorno': ['retorno', 'revisão', 'acompanhamento']
                }

                resposta_lower = resposta_ia.lower()
                for categoria, palavras in palavras_chave_resposta.items():
                    if any(palavra in resposta_lower for palavra in palavras):
                        # Encontrar procedimento correspondente na clínica
                        for proc_clinica in procedimentos_clinica:
                            nome_proc = proc_clinica.get('name', '').lower()
                            if any(palavra in nome_proc for palavra in palavras):
                                from datetime import datetime, timedelta
                                expected_date = (datetime.now() + timedelta(days=14)).strftime("%Y-%m-%d")

                                procedimentos.append({
                                    "procedureId": proc_clinica.get('id', 1),
                                    "expectedDate": expected_date,
                                    "notes": f"{proc_clinica.get('name', 'Procedimento')} - Identificado por palavra-chave"
                                })
                                logger.info(f"Fallback por palavra-chave: encontrado '{proc_clinica.get('name')}'")
                                break
                        if procedimentos:  # Se encontrou algo, parar
                            break

        except Exception as e:
            logger.error(f"Erro ao extrair procedimentos: {str(e)}")

        # Fallback final - garantir que sempre tenha pelo menos um procedimento
        if not procedimentos:
            logger.warning("Nenhum procedimento foi extraído, usando fallback final")
            from datetime import datetime, timedelta
            expected_date = (datetime.now() + timedelta(days=14)).strftime("%Y-%m-%d")

            # Tentar usar o primeiro procedimento da clínica
            if procedimentos_clinica:
                primeiro_proc = procedimentos_clinica[0]
                procedimentos.append({
                    "procedureId": primeiro_proc.get('id', 1),
                    "expectedDate": expected_date,
                    "notes": f"{primeiro_proc.get('name', 'Consulta')} - Procedimento padrão"
                })
                logger.info(f"Fallback final: usando '{primeiro_proc.get('name', 'Consulta')}'")
            else:
                procedimentos.append({
                    "procedureId": 1,
                    "expectedDate": expected_date,
                    "notes": "Consulta de Avaliação - Procedimento padrão"
                })
                logger.info("Fallback final: usando procedimento genérico")

        logger.info(f"Extração finalizada com {len(procedimentos)} procedimento(s)")
        return procedimentos

    def _encontrar_id_procedimento(self, nome_procedimento: str, procedimentos_clinica: List[Dict[str, Any]]) -> int:
        """
        Encontra o ID de um procedimento baseado no nome.

        Args:
            nome_procedimento: Nome do procedimento sugerido pela IA
            procedimentos_clinica: Lista de procedimentos da clínica

        Returns:
            int: ID do procedimento encontrado ou 1 como fallback
        """
        nome_lower = nome_procedimento.lower().strip()

        # Busca exata
        for proc in procedimentos_clinica:
            if proc.get('name', '').lower().strip() == nome_lower:
                return proc.get('id', 1)

        # Busca parcial (contém)
        for proc in procedimentos_clinica:
            proc_name = proc.get('name', '').lower()
            if nome_lower in proc_name or proc_name in nome_lower:
                return proc.get('id', 1)

        # Busca por palavras-chave
        palavras_chave = {
            'limpeza': ['limpeza', 'profilaxia', 'higiene'],
            'consulta': ['consulta', 'avaliação', 'exame'],
            'radiografia': ['radiografia', 'raio-x', 'rx'],
            'urgência': ['urgência', 'emergência', 'dor'],
            'periodontal': ['periodontal', 'gengiva', 'periodontia'],
            'retorno': ['retorno', 'revisão', 'acompanhamento']
        }

        for categoria, palavras in palavras_chave.items():
            if any(palavra in nome_lower for palavra in palavras):
                for proc in procedimentos_clinica:
                    proc_name = proc.get('name', '').lower()
                    if any(palavra in proc_name for palavra in palavras):
                        return proc.get('id', 1)

        # Fallback: retornar primeiro procedimento ou ID 1
        if procedimentos_clinica:
            primeiro_proc = procedimentos_clinica[0]
            return primeiro_proc.get('id', 1)
        return 1
