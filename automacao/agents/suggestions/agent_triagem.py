"""
Agente de triagem para sugestões de tratamento.
Identifica pacientes que concluíram tratamento e cria sugestões iniciais.
"""

from datetime import datetime
from loguru import logger

from core.constants import (
    SUGGESTION_STATUS_IN_ANALYSIS,
    SUGGESTION_STATUS_REJECTED
)

class AgentTriagem:
    """
    Agente de triagem para sugestões de tratamento.
    Identifica pacientes que concluíram tratamento e cria sugestões iniciais.
    """

    def __init__(self, paciente_service, sugestoes_service):
        """
        Inicializa o agente de triagem.

        Args:
            paciente_service: Serviço de acesso a dados de pacientes
            sugestoes_service: Serviço de acesso a dados de sugestões
        """
        self.paciente_service = paciente_service
        self.sugestoes_service = sugestoes_service
        logger.info("Agente de triagem inicializado")

    async def executar(self, dias=30):
        """
        Executa o agente de triagem com análise inteligente de sugestões existentes.

        Args:
            dias (int): Número de dias para filtrar pacientes com tratamento concluído

        Returns:
            list: Lista de sugestões criadas
        """
        logger.info(f"Iniciando triagem inteligente de pacientes com tratamento concluído nos últimos {dias} dias")

        # Obtém pacientes com tratamento concluído (agora inclui sugestões existentes)
        pacientes = await self.paciente_service.listar_pacientes_com_tratamento_concluido(dias)

        if not pacientes:
            logger.info("Nenhum paciente encontrado com tratamento concluído no período")
            return []

        logger.info(f"Encontrados {len(pacientes)} pacientes com tratamento concluído")

        # Analisa cada paciente e suas sugestões existentes
        sugestoes_criadas = []
        pacientes_analisados = 0
        pacientes_ignorados = 0

        for paciente in pacientes:
            paciente_id = paciente.get('id')
            paciente_nome = paciente.get('name', 'Desconhecido')
            sugestoes_existentes = paciente.get('suggestions', [])

            if not paciente_id:
                logger.warning(f"Paciente sem ID: {paciente}")
                continue

            pacientes_analisados += 1
            logger.info(f"Analisando paciente {paciente_nome} (ID: {paciente_id}) - {len(sugestoes_existentes)} sugestões existentes")

            # Aplica regras de análise inteligente
            deve_criar_sugestao = await self._deve_criar_nova_sugestao(paciente, sugestoes_existentes)

            if deve_criar_sugestao:
                # Cria uma nova sugestão com status IN_ANALYSIS
                nova_sugestao = {
                    "patientId": paciente_id,
                    "status": SUGGESTION_STATUS_IN_ANALYSIS
                }

                try:
                    sugestao_criada = await self.sugestoes_service.criar_sugestao(nova_sugestao)

                    if sugestao_criada:
                        logger.info(f"✅ Sugestão criada com sucesso para o paciente {paciente_nome} (ID: {paciente_id})")
                        sugestoes_criadas.append(sugestao_criada)
                    else:
                        logger.error(f"❌ Falha ao criar sugestão para o paciente {paciente_nome} (ID: {paciente_id})")
                except Exception as e:
                    logger.error(f"❌ Erro ao criar sugestão para o paciente {paciente_nome} (ID: {paciente_id}): {str(e)}")
            else:
                pacientes_ignorados += 1
                logger.info(f"⏭️ Paciente {paciente_nome} (ID: {paciente_id}) ignorado conforme regras de triagem")

        logger.info(f"Triagem concluída:")
        logger.info(f"  📊 Pacientes analisados: {pacientes_analisados}")
        logger.info(f"  ✅ Sugestões criadas: {len(sugestoes_criadas)}")
        logger.info(f"  ⏭️ Pacientes ignorados: {pacientes_ignorados}")

        return sugestoes_criadas

    async def _deve_criar_nova_sugestao(self, paciente, sugestoes_existentes):
        """
        Analisa se deve criar uma nova sugestão baseado nas regras de triagem inteligente.

        Args:
            paciente (dict): Dados do paciente
            sugestoes_existentes (list): Lista de sugestões existentes do paciente

        Returns:
            bool: True se deve criar nova sugestão, False caso contrário
        """
        paciente_nome = paciente.get('name', 'Desconhecido')

        # Regra 1: Verificar se já existe sugestão com status IN_ANALYSIS
        sugestao_em_analise = self._tem_sugestao_em_analise(sugestoes_existentes)
        if sugestao_em_analise:
            logger.info(f"🔍 Paciente {paciente_nome} já possui sugestão em análise (ID: {sugestao_em_analise.get('id')})")
            return False

        # Regra 2: Analisar sugestões rejeitadas
        sugestoes_rejeitadas = self._obter_sugestoes_rejeitadas(sugestoes_existentes)
        if sugestoes_rejeitadas:
            deve_criar = self._analisar_sugestoes_rejeitadas(paciente_nome, sugestoes_rejeitadas)
            if not deve_criar:
                return False

        # Regra 3: Verificar se não há alterações relevantes desde o último tratamento
        if not self._tem_alteracoes_relevantes(paciente, sugestoes_existentes):
            logger.info(f"📅 Paciente {paciente_nome} não apresenta alterações relevantes desde o último tratamento")
            return False

        # Se passou por todas as verificações, pode criar nova sugestão
        logger.info(f"✅ Paciente {paciente_nome} atende aos critérios para nova sugestão")
        return True

    def _tem_sugestao_em_analise(self, sugestoes_existentes):
        """
        Verifica se existe alguma sugestão com status IN_ANALYSIS.

        Args:
            sugestoes_existentes (list): Lista de sugestões existentes

        Returns:
            dict or None: Sugestão em análise se encontrada, None caso contrário
        """
        for sugestao in sugestoes_existentes:
            if sugestao.get('status') == SUGGESTION_STATUS_IN_ANALYSIS:
                return sugestao
        return None

    def _obter_sugestoes_rejeitadas(self, sugestoes_existentes):
        """
        Obtém todas as sugestões com status REJECTED.

        Args:
            sugestoes_existentes (list): Lista de sugestões existentes

        Returns:
            list: Lista de sugestões rejeitadas
        """
        return [s for s in sugestoes_existentes if s.get('status') == SUGGESTION_STATUS_REJECTED]

    def _analisar_sugestoes_rejeitadas(self, paciente_nome, sugestoes_rejeitadas):
        """
        Analisa sugestões rejeitadas para determinar se deve criar uma nova sugestão.

        Args:
            paciente_nome (str): Nome do paciente
            sugestoes_rejeitadas (list): Lista de sugestões rejeitadas

        Returns:
            bool: True se pode criar nova sugestão, False caso contrário
        """
        logger.info(f"🔍 Analisando {len(sugestoes_rejeitadas)} sugestões rejeitadas para {paciente_nome}")

        # Analisar a sugestão rejeitada mais recente
        sugestao_mais_recente = max(sugestoes_rejeitadas, key=lambda s: s.get('createdAt', ''))
        human_comment = (sugestao_mais_recente.get('humanComment') or '').lower()
        ia_reasoning = (sugestao_mais_recente.get('iaReasoning') or '').lower()
        data_criacao = sugestao_mais_recente.get('createdAt', '')

        logger.info(f"📝 Última rejeição: '{human_comment}' (Data: {data_criacao})")

        # Motivos definitivos/permanentes - NÃO criar nova sugestão
        motivos_definitivos = [
            'fora do país',
            'mudou de cidade',
            'não tem condições financeiras',
            'recusou qualquer novo tratamento',
            'não quer mais tratamento',
            'transferiu para outro dentista',
            'não retorna mais',
            'desistiu do tratamento',
            'paciente faleceu',
            'não aceita sugestões'
        ]

        for motivo in motivos_definitivos:
            if motivo in human_comment:
                logger.info(f"🚫 Motivo definitivo encontrado: '{motivo}' - NÃO criar nova sugestão")
                return False

        # Motivos temporários - PODE criar nova sugestão após análise temporal
        motivos_temporarios = [
            'voltar a revisar',
            'aguardando melhora',
            'aguardando recuperação',
            'temporariamente indisponível',
            'revisar em',
            'reavaliar em',
            'aguardando resultado',
            'aguardando exame',
            'em tratamento médico',
            'grávida',
            'gestante'
        ]

        for motivo in motivos_temporarios:
            if motivo in human_comment:
                # Verificar se já passou tempo suficiente
                dias_desde_rejeicao = self._calcular_dias_desde_data(data_criacao)
                if dias_desde_rejeicao >= 30:  # 30 dias é um período razoável para reavaliar
                    logger.info(f"⏰ Motivo temporário '{motivo}' - {dias_desde_rejeicao} dias se passaram - PODE criar nova sugestão")
                    return True
                else:
                    logger.info(f"⏰ Motivo temporário '{motivo}' - apenas {dias_desde_rejeicao} dias se passaram - aguardar mais")
                    return False

        # Se não encontrou motivos específicos, analisar baseado no iaReasoning anterior
        if ia_reasoning and len(ia_reasoning) > 10:  # Se havia uma justificativa da IA
            dias_desde_rejeicao = self._calcular_dias_desde_data(data_criacao)
            if dias_desde_rejeicao >= 60:  # Período maior para casos sem motivo claro
                logger.info(f"🤔 Sem motivo específico, mas {dias_desde_rejeicao} dias se passaram - PODE criar nova sugestão")
                return True
            else:
                logger.info(f"🤔 Sem motivo específico, apenas {dias_desde_rejeicao} dias se passaram - aguardar mais")
                return False

        # Caso padrão: se não há informações suficientes, ser conservador
        logger.info(f"❓ Informações insuficientes para análise - NÃO criar nova sugestão por segurança")
        return False

    def _calcular_dias_desde_data(self, data_str):
        """
        Calcula quantos dias se passaram desde uma data específica.

        Args:
            data_str (str): Data em formato ISO string

        Returns:
            int: Número de dias desde a data
        """
        try:
            if not data_str:
                return 999  # Valor alto para datas inválidas

            # Remover informações de timezone se presentes
            if 'T' in data_str:
                data_str = data_str.split('T')[0]

            data_criacao = datetime.strptime(data_str.split('T')[0] if 'T' in data_str else data_str, '%Y-%m-%d')
            hoje = datetime.now()
            return (hoje - data_criacao).days
        except Exception as e:
            logger.warning(f"Erro ao calcular dias desde data '{data_str}': {str(e)}")
            return 999  # Valor alto para erros

    def _tem_alteracoes_relevantes(self, paciente, sugestoes_existentes):
        """
        Verifica se houve alterações relevantes desde o último tratamento.

        Args:
            paciente (dict): Dados do paciente
            sugestoes_existentes (list): Lista de sugestões existentes

        Returns:
            bool: True se há alterações relevantes, False caso contrário
        """
        treatment_concluded_at = paciente.get('treatmentConcludedAt', '')

        # Se não há sugestões anteriores, sempre criar
        if not sugestoes_existentes:
            logger.info(f"📋 Paciente sem sugestões anteriores - PODE criar nova sugestão")
            return True

        # Verificar se a última sugestão é muito recente (menos de 7 dias)
        sugestao_mais_recente = max(sugestoes_existentes, key=lambda s: s.get('createdAt', ''))
        dias_desde_ultima_sugestao = self._calcular_dias_desde_data(sugestao_mais_recente.get('createdAt', ''))

        if dias_desde_ultima_sugestao < 7:
            logger.info(f"⏰ Última sugestão muito recente ({dias_desde_ultima_sugestao} dias) - aguardar mais")
            return False

        # Verificar se o tratamento foi concluído recentemente (últimos 30 dias)
        dias_desde_conclusao = self._calcular_dias_desde_data(treatment_concluded_at)

        if dias_desde_conclusao <= 30:
            logger.info(f"🆕 Tratamento concluído recentemente ({dias_desde_conclusao} dias) - PODE criar nova sugestão")
            return True

        # Para tratamentos mais antigos, ser mais seletivo
        if dias_desde_conclusao > 90:
            logger.info(f"📅 Tratamento muito antigo ({dias_desde_conclusao} dias) - verificar necessidade")
            # Aqui poderia adicionar lógica mais complexa baseada no histórico
            return True

        # Caso padrão: permitir nova sugestão se passou tempo razoável
        logger.info(f"📅 Tempo adequado desde conclusão ({dias_desde_conclusao} dias) - PODE criar nova sugestão")
        return True