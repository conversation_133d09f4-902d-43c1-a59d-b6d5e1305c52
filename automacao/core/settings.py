"""
Configurações do projeto de automação.
Carrega variáveis de ambiente do arquivo .env
"""

import os
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Carrega as variáveis de ambiente do arquivo .env
load_dotenv()

class Settings(BaseSettings):
    """
    Configurações do projeto carregadas do arquivo .env
    """
    # API do sistema odontológico
    API_URL: str = Field(default="http://api:3000/api")
    API_TOKEN: str = Field(default="")

    # Endpoints específicos da API
    API_PATIENTS_ENDPOINT: str = Field(default="/patients")
    API_PATIENTS_WITH_CONCLUDED_TREATMENT_ENDPOINT: str = Field(default="/patients/with-concluded-treatment")
    API_SUGGESTIONS_ENDPOINT: str = Field(default="/suggestions")
    API_MEDICAL_RECORDS_ENDPOINT: str = Field(default="/medical-records")
    API_TREATMENT_FLOW_ENDPOINT: str = Field(default="/treatment-flow")

    # Google Gemini AI
    GOOGLE_API_KEY: str = Field(default="")

    # Configurações do serviço
    LOG_LEVEL: str = Field(default="INFO")
    AUTOMACAO_PORT: int = Field(default=8000)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Instancia as configurações
settings = Settings()

# Verifica se as configurações obrigatórias estão presentes
if not settings.API_URL:
    raise ValueError("A variável de ambiente API_URL não está configurada")

if not settings.GOOGLE_API_KEY:
    import warnings
    warnings.warn("A variável de ambiente GOOGLE_API_KEY não está configurada. Os agentes de IA não funcionarão corretamente.")
