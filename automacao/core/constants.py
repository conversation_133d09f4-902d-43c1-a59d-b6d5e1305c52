"""
Constantes utilizadas no projeto de automação.
"""

# Features disponíveis
FEATURE_SUGGESTIONS = "suggestions"
FEATURE_RELACIONAMENTO = "relacionamento"
FEATURE_COMERCIAL = "comercial"

# Status de execução
STATUS_IDLE = "idle"
STATUS_RUNNING = "running"
STATUS_COMPLETED = "completed"
STATUS_ERROR = "error"

# Endpoints da API
# Estes valores serão carregados do arquivo .env em tempo de execução
# através da classe ApiService
API_PATIENTS = "/api/patients"
API_PATIENTS_WITH_CONCLUDED_TREATMENT = "/api/patients/with-concluded-treatment"
API_SUGGESTIONS = "/api/suggestions"
API_MEDICAL_RECORDS = "/api/medical-records"
API_TREATMENT_FLOW = "/api/treatment-flow"

# Tipos de sugestão
SUGGESTION_STATUS_IN_ANALYSIS = "IN_ANALYSIS"
SUGGESTION_STATUS_PENDING_REVIEW = "PENDING_REVIEW"
SUGGESTION_STATUS_REJECTED = "REJECTED"
SUGGESTION_STATUS_APPROVED = "APPROVED"
