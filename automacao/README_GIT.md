# Projeto de Automação - Sistema Odontológico

Este projeto contém os agentes de automação e inteligência artificial para o sistema odontológico.

## Estrutura do Projeto

```
automacao/
│
├── agents/                       # Agentes organizados por feature
│   ├── suggestions/              # Agentes de sugestões de tratamento
│   ├── relacionamento/           # Agentes de relacionamento com pacientes
│   └── comercial/                # Agentes comerciais
│
├── orchestrators/                # Orquestradores para cada feature
│
├── services/                     # Serviços de acesso a APIs
│
├── core/                         # Lógica compartilhada
│
├── utils/                        # Funções utilitárias
│
├── main.py                       # Arquivo principal
└── requirements.txt              # Dependências
```

## Requisitos

- Python 3.11+
- Docker e Docker Compose
- Acesso à API do Google Gemini

## Configuração

1. Clone o repositório
2. Copie o arquivo `.env.example` para `.env`:
   ```bash
   cp .env.example .env
   ```
3. Configure as variáveis de ambiente no arquivo `.env`:
   - `API_TOKEN`: Token de acesso à API do sistema odontológico
   - `GOOGLE_API_KEY`: Chave de API do Google Gemini

## Execução

### Com Docker Compose (recomendado)

Para executar todo o ambiente de desenvolvimento, incluindo o projeto de automação:

```bash
./run-dev.sh
```

Para reconstruir apenas o serviço de automação:

```bash
./rebuild-automacao.sh
```

### Manualmente (sem Docker)

1. Instale as dependências:
   ```bash
   pip install -r requirements.txt
   ```

2. Execute o projeto:
   ```bash
   python main.py
   ```

## API

O serviço expõe os seguintes endpoints:

- `GET /`: Página inicial da API
- `POST /executar/{feature}`: Executa um agente específico (suggestions, relacionamento, comercial)
- `GET /status/{feature}`: Verifica o status de um agente específico

### Endpoints específicos para agentes de sugestões

- `POST /executar/suggestions/triagem`: Executa apenas o agente de triagem (identifica pacientes e cria sugestões iniciais)
- `POST /executar/suggestions/odontologico`: Executa apenas o agente odontológico (processa sugestões existentes com IA)
- `GET /status/suggestions/triagem`: Verifica o status do agente de triagem
- `GET /status/suggestions/odontologico`: Verifica o status do agente odontológico

## Desenvolvimento

Para adicionar um novo agente:

1. Crie uma nova pasta em `agents/` para a feature
2. Implemente o agente seguindo o padrão dos existentes
3. Crie um orquestrador na pasta `orchestrators/`
4. Adicione os serviços necessários em `services/`
5. Registre o novo agente no arquivo principal `main.py`
