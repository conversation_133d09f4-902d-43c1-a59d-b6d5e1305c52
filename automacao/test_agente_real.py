#!/usr/bin/env python3
"""
Teste do agente de triagem melhorado com dados reais da API.
"""

import asyncio
import sys
import os
import aiohttp

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_agente_com_api_real():
    """
    Testa o agente de triagem melhorado com dados reais da API.
    """
    print("🧪 Testando agente de triagem melhorado com API real...")
    
    try:
        # Importar e configurar o agente
        from agents.suggestions.agent_triagem import AgentTriagem
        
        # Criar serviços reais
        class ApiService:
            def __init__(self, base_url):
                self.base_url = base_url
            
            async def get(self, endpoint, params=None):
                url = f"{self.base_url}{endpoint}"
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            print(f"❌ Erro na API: {response.status}")
                            return []
            
            async def post(self, endpoint, data):
                url = f"{self.base_url}{endpoint}"
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data) as response:
                        if response.status in [200, 201]:
                            return await response.json()
                        else:
                            print(f"❌ Erro ao criar sugestão: {response.status}")
                            return None
        
        class PacienteServiceReal:
            def __init__(self):
                self.api = ApiService("http://localhost:3000/api")
            
            async def listar_pacientes_com_tratamento_concluido(self, dias):
                return await self.api.get("/patients/with-concluded-treatment", {"days": dias})
        
        class SugestoesServiceReal:
            def __init__(self):
                self.api = ApiService("http://localhost:3000/api")
            
            async def criar_sugestao(self, dados):
                return await self.api.post("/suggestions", dados)
        
        # Inicializar agente com serviços reais
        paciente_service = PacienteServiceReal()
        sugestoes_service = SugestoesServiceReal()
        agente = AgentTriagem(paciente_service, sugestoes_service)
        
        # Executar teste
        print("\n🚀 Executando agente de triagem com dados reais...")
        resultado = await agente.executar(30)
        
        print(f"\n📊 Resultado do teste:")
        print(f"  ✅ Sugestões criadas: {len(resultado)}")
        
        if resultado:
            print(f"  📋 Detalhes das sugestões criadas:")
            for i, sugestao in enumerate(resultado, 1):
                print(f"    {i}. ID: {sugestao.get('id')} - Paciente: {sugestao.get('patientId')}")
        else:
            print("  ℹ️ Nenhuma sugestão foi criada (isso pode ser normal se as regras de triagem foram aplicadas)")
        
        print("\n✅ Teste com API real concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_agente_com_api_real())
    sys.exit(0 if success else 1)
