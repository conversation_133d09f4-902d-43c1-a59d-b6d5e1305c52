"""
Utilitários para manipulação de JSON.
"""

import json
from loguru import logger

def parse_json(json_str):
    """
    Converte uma string JSON em um objeto Python.
    
    Args:
        json_str (str): String JSON
        
    Returns:
        dict/list: Objeto Python correspondente ao JSON
    """
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"Erro ao fazer parse do JSON: {str(e)}")
        return None

def to_json(obj, pretty=False):
    """
    Converte um objeto Python em uma string JSON.
    
    Args:
        obj (dict/list): Objeto Python
        pretty (bool): Se True, formata o JSON com indentação
        
    Returns:
        str: String JSON
    """
    try:
        if pretty:
            return json.dumps(obj, indent=2, ensure_ascii=False)
        return json.dumps(obj, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Erro ao converter para JSON: {str(e)}")
        return None

def extract_json_from_text(text):
    """
    Extrai um objeto JSON de um texto.
    Útil para extrair JSON de respostas de IA.
    
    Args:
        text (str): Texto contendo JSON
        
    Returns:
        dict/list: Objeto Python correspondente ao JSON extraído
    """
    try:
        # Procura por blocos de código JSON
        start_markers = ["{", "["]
        end_markers = ["}", "]"]
        
        for start, end in zip(start_markers, end_markers):
            start_idx = text.find(start)
            if start_idx != -1:
                # Encontra o fechamento correspondente
                count = 1
                for i in range(start_idx + 1, len(text)):
                    if text[i] == start:
                        count += 1
                    elif text[i] == end:
                        count -= 1
                    
                    if count == 0:
                        # Extrai o JSON
                        json_str = text[start_idx:i+1]
                        return parse_json(json_str)
        
        return None
    except Exception as e:
        logger.error(f"Erro ao extrair JSON do texto: {str(e)}")
        return None
