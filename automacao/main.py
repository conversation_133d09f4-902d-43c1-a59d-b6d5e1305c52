#!/usr/bin/env python3
"""
Arquivo principal do projeto de automação do sistema odontológico.
Responsável por inicializar os orquestradores e expor a API.
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

# Importa as configurações do projeto
from core.settings import settings
from core.constants import FEATURE_SUGGESTIONS, FEATURE_RELACIONAMENTO, FEATURE_COMERCIAL

# Importa os orquestradores
from orchestrators.orquestrador_suggestions import OrquestradorSuggestions
from orchestrators.orquestrador_relacionamento import OrquestradorRelacionamento
from orchestrators.orquestrador_comercial import OrquestradorComercial

# Configura o logger
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
)
logger.add(
    "logs/automacao.log",
    rotation="10 MB",
    retention="1 week",
    level=settings.LOG_LEVEL,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
)

# Cria a aplicação FastAPI
app = FastAPI(
    title="Automação - Sistema Odontológico",
    description="API para execução de agentes de automação e IA",
    version="1.0.0"
)

# Configura o CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Instancia os orquestradores
orquestrador_suggestions = OrquestradorSuggestions()
orquestrador_relacionamento = OrquestradorRelacionamento()
orquestrador_comercial = OrquestradorComercial()

# Rotas da API
@app.get("/")
async def root():
    """Rota raiz da API"""
    return {"message": "API de Automação do Sistema Odontológico"}

@app.post("/executar/{feature}")
async def executar_agente(feature: str, background_tasks: BackgroundTasks):
    """
    Executa um agente específico em background

    Args:
        feature: Nome da feature (suggestions, relacionamento, comercial)
        background_tasks: Tarefas em background do FastAPI

    Returns:
        dict: Mensagem de confirmação
    """
    if feature == FEATURE_SUGGESTIONS:
        background_tasks.add_task(orquestrador_suggestions.executar)
        return {"message": f"Agente de {feature} iniciado com sucesso"}
    elif feature == FEATURE_RELACIONAMENTO:
        background_tasks.add_task(orquestrador_relacionamento.executar)
        return {"message": f"Agente de {feature} iniciado com sucesso"}
    elif feature == FEATURE_COMERCIAL:
        background_tasks.add_task(orquestrador_comercial.executar)
        return {"message": f"Agente de {feature} iniciado com sucesso"}
    else:
        raise HTTPException(status_code=404, detail=f"Feature {feature} não encontrada")

@app.post("/executar/suggestions/triagem")
async def executar_agente_triagem(background_tasks: BackgroundTasks):
    """
    Executa apenas o agente de triagem para identificar pacientes e criar sugestões iniciais

    Args:
        background_tasks: Tarefas em background do FastAPI

    Returns:
        dict: Mensagem de confirmação
    """
    background_tasks.add_task(orquestrador_suggestions.executar_triagem)
    return {"message": "Agente de triagem iniciado com sucesso"}

@app.post("/executar/suggestions/odontologico")
async def executar_agente_odontologico(background_tasks: BackgroundTasks):
    """
    Executa apenas o agente odontológico para processar sugestões existentes com status IN_ANALYSIS

    Args:
        background_tasks: Tarefas em background do FastAPI

    Returns:
        dict: Mensagem de confirmação
    """
    background_tasks.add_task(orquestrador_suggestions.executar_odontologico)
    return {"message": "Agente odontológico iniciado com sucesso"}

@app.get("/status/{feature}")
async def status_agente(feature: str):
    """
    Verifica o status de execução de um agente

    Args:
        feature: Nome da feature (suggestions, relacionamento, comercial)

    Returns:
        dict: Status do agente
    """
    if feature == FEATURE_SUGGESTIONS:
        return {"status": orquestrador_suggestions.status}
    elif feature == FEATURE_RELACIONAMENTO:
        return {"status": orquestrador_relacionamento.status}
    elif feature == FEATURE_COMERCIAL:
        return {"status": orquestrador_comercial.status}
    else:
        raise HTTPException(status_code=404, detail=f"Feature {feature} não encontrada")

@app.get("/status/suggestions/triagem")
async def status_agente_triagem():
    """
    Verifica o status de execução do agente de triagem

    Returns:
        dict: Status do agente de triagem
    """
    return {"status": orquestrador_suggestions.status}

@app.get("/status/suggestions/odontologico")
async def status_agente_odontologico():
    """
    Verifica o status de execução do agente odontológico

    Returns:
        dict: Status do agente odontológico
    """
    return {"status": orquestrador_suggestions.status}

if __name__ == "__main__":
    # Inicia o servidor
    logger.info(f"Iniciando servidor na porta {settings.AUTOMACAO_PORT}")
    uvicorn.run("main:app", host="0.0.0.0", port=settings.AUTOMACAO_PORT, reload=True)
