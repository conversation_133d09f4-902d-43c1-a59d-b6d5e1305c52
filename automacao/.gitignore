# Arquivos de ambiente e configuração
.env
.env.*
!.env.example

# Arquivos de cache Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Logs
logs/
*.log

# Arquivos temporários
.DS_Store
.idea/
.vscode/
*.swp
*.swo
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
.hypothesis/
.coverage.*

# Arquivos específicos do projeto
.python-version
.venv
venv/
ENV/
venv_new/

# Arquivos gerados pelo Google Gemini AI
*.gemini_cache

# Arquivos de saída
output/
results/
