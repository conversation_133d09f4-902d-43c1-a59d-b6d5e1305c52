"""
Serviço para acesso aos dados de fluxogramas de tratamento.
"""

from loguru import logger

from core.constants import API_TREATMENT_FLOW
from services.api_service import ApiService

class FluxogramaService(ApiService):
    """
    Serviço para acesso aos dados de fluxogramas de tratamento.
    """
    
    async def listar_fluxogramas(self, params=None):
        """
        Lista todos os fluxogramas.
        
        Args:
            params (dict, optional): Parâmetros de filtro
            
        Returns:
            list: Lista de fluxogramas
        """
        try:
            return await self.get(API_TREATMENT_FLOW, params)
        except Exception as e:
            logger.error(f"Erro ao listar fluxogramas: {str(e)}")
            return []
    
    async def obter_fluxograma(self, fluxograma_id):
        """
        Obtém os dados de um fluxograma específico.
        
        Args:
            fluxograma_id (int): ID do fluxograma
            
        Returns:
            dict: Dados do fluxograma
        """
        try:
            return await self.get(f"{API_TREATMENT_FLOW}/{fluxograma_id}")
        except Exception as e:
            logger.error(f"Erro ao obter fluxograma {fluxograma_id}: {str(e)}")
            return None
    
    async def obter_fluxograma_por_paciente(self, paciente_id):
        """
        Obtém o fluxograma de tratamento de um paciente.
        
        Args:
            paciente_id (int): ID do paciente
            
        Returns:
            dict: Fluxograma do paciente
        """
        try:
            params = {"patientId": paciente_id}
            fluxogramas = await self.get(API_TREATMENT_FLOW, params)
            
            if fluxogramas and len(fluxogramas) > 0:
                return fluxogramas[0]
            return None
        except Exception as e:
            logger.error(f"Erro ao obter fluxograma do paciente {paciente_id}: {str(e)}")
            return None
