"""
Serviço para acesso aos dados de pacientes.
"""

from loguru import logger

from core.constants import API_PATIENTS, API_PATIENTS_WITH_CONCLUDED_TREATMENT
from services.api_service import ApiService

class PacienteService(ApiService):
    """
    Serviço para acesso aos dados de pacientes.
    """
    
    async def listar_pacientes(self, params=None):
        """
        Lista todos os pacientes.
        
        Args:
            params (dict, optional): Parâmetros de filtro
            
        Returns:
            list: Lista de pacientes
        """
        try:
            return await self.get(API_PATIENTS, params)
        except Exception as e:
            logger.error(f"Erro ao listar pacientes: {str(e)}")
            return []
    
    async def obter_paciente(self, paciente_id):
        """
        Obtém os dados de um paciente específico.
        
        Args:
            paciente_id (int): ID do paciente
            
        Returns:
            dict: Dados do paciente
        """
        try:
            return await self.get(f"{API_PATIENTS}/{paciente_id}")
        except Exception as e:
            logger.error(f"Erro ao obter paciente {paciente_id}: {str(e)}")
            return None
    
    async def listar_pacientes_com_tratamento_concluido(self, dias=30):
        """
        Lista pacientes que concluíram tratamento nos últimos X dias.
        
        Args:
            dias (int): Número de dias para filtro
            
        Returns:
            list: Lista de pacientes com tratamento concluído
        """
        try:
            params = {"days": dias}
            return await self.get(API_PATIENTS_WITH_CONCLUDED_TREATMENT, params)
        except Exception as e:
            logger.error(f"Erro ao listar pacientes com tratamento concluído: {str(e)}")
            return []
