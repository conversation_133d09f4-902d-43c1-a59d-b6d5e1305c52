"""
Serviço base para comunicação com a API do sistema odontológico.
"""

import httpx
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from core.settings import settings

class ApiService:
    """
    Serviço base para comunicação com a API do sistema odontológico.
    """

    def __init__(self):
        """
        Inicializa o serviço de API.
        """
        # Garantir que a URL base não termine com /api para evitar duplicação
        base_url = settings.API_URL
        if base_url.endswith("/api"):
            base_url = base_url[:-4]
        self.base_url = base_url
        self.token = settings.API_TOKEN
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}" if self.token else ""
        }

        logger.info(f"Serviço de API inicializado com URL base: {self.base_url}")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    async def get(self, endpoint, params=None):
        """
        Realiza uma requisição GET para a API.

        Args:
            endpoint (str): Endpoint da API
            params (dict, optional): Parâmetros da requisição

        Returns:
            dict: Resposta da API
        """
        # Garantir que o endpoint tenha o prefixo /api se não tiver
        if not endpoint.startswith("/api/"):
            endpoint = f"/api{endpoint}"

        url = f"{self.base_url}{endpoint}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, headers=self.headers, timeout=30.0)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar {url}: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar {url}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erro desconhecido ao acessar {url}: {str(e)}")
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    async def post(self, endpoint, data):
        """
        Realiza uma requisição POST para a API.

        Args:
            endpoint (str): Endpoint da API
            data (dict): Dados a serem enviados

        Returns:
            dict: Resposta da API
        """
        # Garantir que o endpoint tenha o prefixo /api se não tiver
        if not endpoint.startswith("/api/"):
            endpoint = f"/api{endpoint}"

        url = f"{self.base_url}{endpoint}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=data, headers=self.headers, timeout=30.0)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar {url}: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar {url}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erro desconhecido ao acessar {url}: {str(e)}")
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    async def put(self, endpoint, data):
        """
        Realiza uma requisição PUT para a API.

        Args:
            endpoint (str): Endpoint da API
            data (dict): Dados a serem enviados

        Returns:
            dict: Resposta da API
        """
        # Garantir que o endpoint tenha o prefixo /api se não tiver
        if not endpoint.startswith("/api/"):
            endpoint = f"/api{endpoint}"

        url = f"{self.base_url}{endpoint}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(url, json=data, headers=self.headers, timeout=30.0)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar {url}: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar {url}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erro desconhecido ao acessar {url}: {str(e)}")
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    async def patch(self, endpoint, data):
        """
        Realiza uma requisição PATCH para a API.

        Args:
            endpoint (str): Endpoint da API
            data (dict): Dados a serem enviados

        Returns:
            dict: Resposta da API
        """
        # Garantir que o endpoint tenha o prefixo /api se não tiver
        if not endpoint.startswith("/api/"):
            endpoint = f"/api{endpoint}"

        url = f"{self.base_url}{endpoint}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.patch(url, json=data, headers=self.headers, timeout=30.0)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar {url}: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar {url}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erro desconhecido ao acessar {url}: {str(e)}")
            raise