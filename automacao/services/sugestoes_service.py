"""
Serviço para acesso aos dados de sugestões de tratamento.
"""

from loguru import logger

from core.constants import API_SUGGESTIONS
from services.api_service import ApiService

class SugestoesService(ApiService):
    """
    Serviço para acesso aos dados de sugestões de tratamento.
    """
    
    async def listar_sugestoes(self, params=None):
        """
        Lista todas as sugestões.
        
        Args:
            params (dict, optional): Parâmetros de filtro
            
        Returns:
            list: Lista de sugestões
        """
        try:
            return await self.get(API_SUGGESTIONS, params)
        except Exception as e:
            logger.error(f"Erro ao listar sugestões: {str(e)}")
            return []
    
    async def obter_sugestao(self, sugestao_id):
        """
        Obtém os dados de uma sugestão específica.
        
        Args:
            sugestao_id (int): ID da sugestão
            
        Returns:
            dict: Dados da sugestão
        """
        try:
            return await self.get(f"{API_SUGGESTIONS}/{sugestao_id}")
        except Exception as e:
            logger.error(f"Erro ao obter sugestão {sugestao_id}: {str(e)}")
            return None
    
    async def criar_sugestao(self, dados_sugestao):
        """
        Cria uma nova sugestão.
        
        Args:
            dados_sugestao (dict): Dados da sugestão
            
        Returns:
            dict: Sugestão criada
        """
        try:
            return await self.post(API_SUGGESTIONS, dados_sugestao)
        except Exception as e:
            logger.error(f"Erro ao criar sugestão: {str(e)}")
            return None
    
    async def atualizar_sugestao(self, sugestao_id, dados_sugestao):
        """
        Atualiza uma sugestão existente.
        
        Args:
            sugestao_id (int): ID da sugestão
            dados_sugestao (dict): Dados atualizados da sugestão
            
        Returns:
            dict: Sugestão atualizada
        """
        try:
            return await self.put(f"{API_SUGGESTIONS}/{sugestao_id}", dados_sugestao)
        except Exception as e:
            logger.error(f"Erro ao atualizar sugestão {sugestao_id}: {str(e)}")
            return None
