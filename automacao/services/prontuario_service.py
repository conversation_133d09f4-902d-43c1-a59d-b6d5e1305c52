"""
Serviço para acesso aos dados de prontuários médicos.
"""

from loguru import logger

from core.constants import API_MEDICAL_RECORDS
from services.api_service import ApiService

class ProntuarioService(ApiService):
    """
    Serviço para acesso aos dados de prontuários médicos.
    """
    
    async def listar_prontuarios(self, params=None):
        """
        Lista todos os prontuários.
        
        Args:
            params (dict, optional): Parâmetros de filtro
            
        Returns:
            list: Lista de prontuários
        """
        try:
            return await self.get(API_MEDICAL_RECORDS, params)
        except Exception as e:
            logger.error(f"Erro ao listar prontuários: {str(e)}")
            return []
    
    async def obter_prontuario(self, prontuario_id):
        """
        Obtém os dados de um prontuário específico.
        
        Args:
            prontuario_id (int): ID do prontuário
            
        Returns:
            dict: Dados do prontuário
        """
        try:
            return await self.get(f"{API_MEDICAL_RECORDS}/{prontuario_id}")
        except Exception as e:
            logger.error(f"Erro ao obter prontuário {prontuario_id}: {str(e)}")
            return None
    
    async def obter_prontuario_por_paciente(self, paciente_id):
        """
        Obtém o prontuário de um paciente.
        
        Args:
            paciente_id (int): ID do paciente
            
        Returns:
            dict: Prontuário do paciente
        """
        try:
            params = {"patientId": paciente_id}
            prontuarios = await self.get(API_MEDICAL_RECORDS, params)
            
            if prontuarios and len(prontuarios) > 0:
                return prontuarios[0]
            return None
        except Exception as e:
            logger.error(f"Erro ao obter prontuário do paciente {paciente_id}: {str(e)}")
            return None
