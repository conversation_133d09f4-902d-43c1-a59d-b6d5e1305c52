# Automação - Sistema Odontológico

Este projeto contém os agentes de automação e inteligência artificial para o sistema odontológico.

## Estrutura do Projeto

```
automacao/
│
├── agents/                       # Todos os agentes organizados por feature
│   ├── suggestions/
│   │   ├── __init__.py
│   │   ├── agent_triagem.py      # Agente de triagem (filtra pacientes)
│   │   ├── agent_odontologico.py # Agente que gera sugestões de tratamentos
│   │   └── prompts/              # Prompts específicos dos agentes dessa feature
│   │       └── odontologico.txt
│   │
│   ├── relacionamento/
│   │   ├── __init__.py
│   │   └── agent_contato.py
│   │
│   └── comercial/
│       ├── __init__.py
│       └── agent_vendas.py
│
├── orchestrators/               # Cada feature terá um orquestrador principal
│   ├── orquestrador_suggestions.py
│   ├── orquestrador_relacionamento.py
│   └── orquestrador_comercial.py
│
├── services/                    # Serviços de acesso a APIs, banco, etc.
│   ├── paciente_service.py
│   ├── fluxograma_service.py
│   └── sugestoes_service.py
│
├── core/                        # Lógica compartilhada entre agentes
│   ├── constants.py
│   ├── settings.py              # Load .env, configs globais
│   └── google_client.py         # Wrapper para Google Gemini
│
├── utils/                       # Funções utilitárias
│   └── json_utils.py
│
├── .env                         # API keys e configs sensíveis
├── main.py                      # Arquivo principal de execução
├── requirements.txt             # Dependências do projeto
```

## Requisitos

- Python 3.11+
- Acesso à API do Google Gemini
- Acesso à API do sistema odontológico

## Instalação

1. Clone o repositório
2. Configure o arquivo `.env` com as chaves necessárias
3. Instale as dependências: `pip install -r requirements.txt`
4. Execute o projeto: `python main.py`

## Desenvolvimento

Para adicionar um novo agente:

1. Crie uma nova pasta em `agents/` para a feature
2. Implemente o agente seguindo o padrão dos existentes
3. Crie um orquestrador na pasta `orchestrators/`
4. Adicione os serviços necessários em `services/`
5. Registre o novo agente no arquivo principal `main.py`
