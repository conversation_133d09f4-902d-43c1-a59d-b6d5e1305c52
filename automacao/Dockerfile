# =============================================================================
# CRM ODONTO AUTOMAÇÃO - UNIFIED DOCKERFILE
# =============================================================================
# Multi-stage Dockerfile que funciona tanto para desenvolvimento quanto produção
# Controlado pela variável NODE_ENV:
# - NODE_ENV=development: Hot reload com watchdog, logs detalhados
# - NODE_ENV=production: Otimizado, logs mínimos
# =============================================================================

# =============================================================================
# BASE STAGE - Configuração comum para todos os ambientes
# =============================================================================
FROM python:3.11-slim AS base

WORKDIR /app

# Instala dependências do sistema
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copia os arquivos de requisitos
COPY requirements.txt .

# Instala as dependências Python
RUN pip install --no-cache-dir -r requirements.txt

# Cria diretório para logs
RUN mkdir -p logs

# =============================================================================
# DEVELOPMENT STAGE - Para desenvolvimento com hot reload
# =============================================================================
FROM base AS development

# Instala dependências adicionais para desenvolvimento
RUN pip install --no-cache-dir watchdog

# Copia o código-fonte
COPY . .

# Expõe a porta da aplicação
EXPOSE 8000

# Comando para desenvolvimento com hot reload
CMD ["python", "-u", "main.py"]

# =============================================================================
# PRODUCTION STAGE - Para produção otimizada
# =============================================================================
FROM base AS production

# Copia o código-fonte
COPY . .

# Remove arquivos desnecessários para produção
RUN find . -type f -name "*.pyc" -delete && \
    find . -type d -name "__pycache__" -delete

# Expõe a porta da aplicação
EXPOSE 8000

# Comando para produção
CMD ["python", "-O", "main.py"]
