#!/bin/bash

# =============================================================================
# CRM ODONTO - SCRIPT DE DEPLOY PARA PRODUÇÃO
# =============================================================================
# Este script é específico para deploy em produção
# Para desenvolvimento, use: ./setup-dev.sh
# Uso: ./deploy.sh
# =============================================================================

# Cores para melhor visualização
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para exibir banner
show_banner() {
    echo -e "${CYAN}"
    echo "============================================================================="
    echo "                🚀 CRM ODONTO - DEPLOY PRODUÇÃO 🚀"
    echo "============================================================================="
    echo -e "${NC}"
}

# Função para verificar dependências
check_dependencies() {
    echo -e "${YELLOW}🔍 Verificando dependências...${NC}"

    # Verificar se o Docker está instalado
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker não está instalado. Por favor, instale o Docker para continuar.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker encontrado${NC}"

    # Verificar se o Docker Compose está instalado
    if ! command -v docker compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose não está instalado. Por favor, instale o Docker Compose para continuar.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker Compose encontrado${NC}"
}

# Função para configurar ambiente de produção
setup_production() {
    echo -e "${YELLOW}⚙️  Configurando ambiente de produção...${NC}"

    # Verificar se .env existe
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ Arquivo .env não encontrado!${NC}"
        echo -e "${YELLOW}💡 Crie o arquivo .env com as configurações necessárias${NC}"
        exit 1
    fi

    echo -e "${BLUE}📋 Configurando .env para produção...${NC}"

    # Garantir que NODE_ENV está como production
    if grep -q "NODE_ENV=" .env; then
        sed -i 's/NODE_ENV=.*/NODE_ENV=production/g' .env
    else
        echo "NODE_ENV=production" >> .env
    fi

    # Verificar se as portas estão definidas, mas não alterar seus valores
    if ! grep -q "FRONTEND_EXTERNAL_PORT=" .env; then
        echo -e "${YELLOW}⚠️ FRONTEND_EXTERNAL_PORT não encontrado no .env, usando valor padrão${NC}"
        echo "FRONTEND_EXTERNAL_PORT=3030" >> .env
    fi

    if ! grep -q "FRONTEND_PORT=" .env; then
        echo -e "${YELLOW}⚠️ FRONTEND_PORT não encontrado no .env, usando valor padrão${NC}"
        echo "FRONTEND_PORT=80" >> .env
    fi

    echo -e "${GREEN}✅ Configurações de produção aplicadas ao .env${NC}"

    # Carregar variáveis do .env
    export $(cat .env | grep -v '^#' | xargs)
}

# Função para parar containers existentes
stop_containers() {
    echo -e "${YELLOW}🛑 Parando containers existentes...${NC}"
    docker compose down --remove-orphans
}

# Função para construir e iniciar containers em produção
build_and_start_production() {
    echo -e "${YELLOW}🔨 Construindo e iniciando containers para produção...${NC}"

    # Build com progresso
    echo -e "${BLUE}📦 Construindo imagens Docker otimizadas...${NC}"
    docker compose build --progress=plain

    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Falha ao construir as imagens Docker.${NC}"
        exit 1
    fi

    # Iniciar containers em background (produção)
    echo -e "${BLUE}🚀 Iniciando containers em produção...${NC}"
    docker compose up -d

    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Falha ao iniciar os containers.${NC}"
        exit 1
    fi
}

# Função para verificar saúde dos containers
check_containers_health() {
    echo -e "${YELLOW}🏥 Verificando saúde dos containers...${NC}"

    # Aguardar um pouco para os containers iniciarem
    sleep 5

    # Lista de containers esperados
    containers=("crm_odonto_mysql" "crm_odonto_api" "crm_odonto_web" "crm_odonto_automacao" "crm_odonto_minio")

    for container in "${containers[@]}"; do
        if docker ps | grep -q "$container"; then
            echo -e "${GREEN}✅ $container está rodando${NC}"
        else
            echo -e "${RED}❌ $container não está rodando${NC}"
            echo -e "${YELLOW}📋 Logs do $container:${NC}"
            docker logs "$container" --tail 10
            return 1
        fi
    done

    return 0
}

# Função para exibir informações finais de produção
show_production_info() {
    echo -e "${GREEN}"
    echo "============================================================================="
    echo "                🎉 DEPLOY EM PRODUÇÃO CONCLUÍDO COM SUCESSO! 🎉"
    echo "============================================================================="
    echo -e "${NC}"

    # Carregar variáveis do .env para exibir as portas corretas
    if [ -f ".env" ]; then
        source .env
    fi

    echo -e "${CYAN}📊 URLs de Produção:${NC}"
    echo -e "${GREEN}🌐 Frontend:      http://localhost:${FRONTEND_EXTERNAL_PORT:-80}${NC}"
    echo -e "${GREEN}🔧 API:           http://localhost:${API_EXTERNAL_PORT:-3000}${NC}"
    echo -e "${GREEN}📚 Swagger:       http://localhost:${API_EXTERNAL_PORT:-3000}/api/docs${NC}"
    echo -e "${GREEN}🤖 Automação:     http://localhost:${AUTOMATION_EXTERNAL_PORT:-8000}${NC}"
    echo -e "${GREEN}💾 MinIO:         http://localhost:${MINIO_EXTERNAL_PORT:-9000}${NC}"
    echo -e "${GREEN}🎛️  MinIO Console: http://localhost:${MINIO_CONSOLE_EXTERNAL_PORT:-9001}${NC}"

    echo -e "${YELLOW}"
    echo "============================================================================="
    echo "💡 Comandos úteis para produção:"
    echo "   docker compose logs             # Ver logs dos containers"
    echo "   docker compose logs -f api      # Ver logs da API em tempo real"
    echo "   docker compose restart api     # Reiniciar apenas a API"
    echo "   docker compose down             # Parar todos os containers"
    echo "   docker ps                       # Ver status dos containers"
    echo ""
    echo "🔄 Para desenvolvimento, use:"
    echo "   ./setup-dev.sh                  # Script específico para desenvolvimento"
    echo "============================================================================="
    echo -e "${NC}"
}

# =============================================================================
# SCRIPT PRINCIPAL
# =============================================================================

# Exibir banner
show_banner

echo -e "${BLUE}🎯 Iniciando deploy para produção...${NC}"

# Verificar se não foi passado parâmetro de desenvolvimento
if [ "$1" = "development" ] || [ "$1" = "dev" ]; then
    echo -e "${RED}❌ Este script é apenas para produção!${NC}"
    echo -e "${YELLOW}💡 Para desenvolvimento, use: ./setup-dev.sh${NC}"
    exit 1
fi

# Executar etapas do deploy de produção
check_dependencies
setup_production
stop_containers
build_and_start_production

# Verificar saúde dos containers
if check_containers_health; then
    show_production_info
else
    echo -e "${RED}❌ Alguns containers falharam ao iniciar. Verifique os logs acima.${NC}"
    echo -e "${YELLOW}💡 Tente executar: docker compose logs${NC}"
    exit 1
fi
