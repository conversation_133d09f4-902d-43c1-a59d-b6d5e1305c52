# 🦷 CRM Odonto - Sistema de Gestão para Clínicas Odontológicas

Sistema completo de gestão para clínicas odontológicas com backend em NestJS, frontend em Angular e sistema de automação com IA.

## 📁 Estrutura do Projeto

```
crm-odonto/
├── api/           # Backend NestJS + TypeORM
├── web/           # Frontend Angular + Tailwind CSS
├── automacao/     # Sistema de IA em Python
├── docker-compose.yml    # Docker unificado
├── setup-dev.sh          # Script para desenvolvimento
├── deploy.sh             # Script para produção
└── .env                  # Configurações do projeto
```

## 🛠️ Requisitos

- **Docker** e **Docker Compose** (recomendado)
- **Node.js 20+** e **npm** (para desenvolvimento local)
- **Python 3.11+** (para automação)
- **Git**

```bash
# Verificar versões
docker --version && docker-compose --version
node --version && npm --version
```

## 🚀 Execução Rápida

### ⚡ Desenvolvimento (Recomendado)

```bash
# 1. C<PERSON> o repositório
git clone <url-do-repositorio>
cd crm-odonto

# 2. Execute o script de desenvolvimento
./setup-dev.sh
```

### 🏭 Produção

```bash
# Execute o script de produção
./deploy.sh
```

### 🌐 URLs de Acesso

| Ambiente | Frontend | API | Swagger | Automação | MinIO Console |
|----------|----------|-----|---------|-----------|---------------|
| **Desenvolvimento** | [localhost:4200](http://localhost:4200) | [localhost:3000](http://localhost:3000) | [localhost:3000/api/docs](http://localhost:3000/api/docs) | [localhost:3023](http://localhost:3023) | [localhost:3016](http://localhost:3016) |
| **Produção** | [localhost:80](http://localhost:80) | [localhost:3000](http://localhost:3000) | [localhost:3000/api/docs](http://localhost:3000/api/docs) | [localhost:3023](http://localhost:3023) | [localhost:3016](http://localhost:3016) |

---

## 📋 Guias Detalhados

### 🔧 Execução com Scripts (Recomendado)

#### 🛠️ Desenvolvimento

```bash
# 1. Clonar o repositório
git clone <url-do-repositorio>
cd crm-odonto

# 2. Executar script de desenvolvimento
./setup-dev.sh
```

**O script `setup-dev.sh` faz automaticamente:**
- ✅ Configura `NODE_ENV=development` no `.env`
- ✅ Constrói e inicia todos os containers
- ✅ Frontend na porta **4200** com hot reload
- ✅ **Executa seeds** para dados de teste
- ✅ Aguarda todos os serviços ficarem prontos
- ✅ Mostra URLs de acesso

#### 🏭 Produção

```bash
# Executar script de produção
./deploy.sh
```

**O script `deploy.sh` faz automaticamente:**
- ✅ Configura `NODE_ENV=production` no `.env`
- ✅ Constrói imagens otimizadas para produção
- ✅ Frontend na porta **80** com nginx
- ✅ **NÃO executa seeds** (dados reais)
- ✅ Verifica saúde dos containers
- ✅ Deploy completo e seguro

### ⚙️ Configuração do Arquivo .env

Crie o arquivo `.env` na **raiz do projeto** com:

```env
# Ambiente (development ou production)
NODE_ENV=development

# Configuração do Banco de Dados
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root123
DB_DATABASE=crm_odonto

# Configuração da API
API_PORT=3000
JWT_SECRET=crm_odonto_jwt_secret_2024_super_secure
JWT_EXPIRATION=1d

# MinIO (Armazenamento de arquivos)
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=crm-odonto
MINIO_PUBLIC_URL=http://localhost:3015

# Automação (Agentes IA)
API_BASE_URL=http://api:3000/api
GOOGLE_API_KEY=sua_chave_do_google_ai_aqui
LOG_LEVEL=INFO
AUTOMACAO_PORT=3023
```

### 🐳 Execução com Docker (Manual)

Se preferir controle total, você pode usar comandos Docker diretamente:

#### 🛠️ Desenvolvimento

```bash
# 1. Configurar ambiente
sed -i 's/NODE_ENV=.*/NODE_ENV=development/' .env

# 2. Iniciar containers
docker-compose up --build -d

# 3. Verificar status
docker-compose ps

# 4. Ver logs
docker-compose logs -f
```

**Resultado:**
- Frontend: http://localhost:**4200**
- Seeds executados automaticamente

#### 🏭 Produção

```bash
# 1. Configurar ambiente
sed -i 's/NODE_ENV=.*/NODE_ENV=production/' .env

# 2. Iniciar containers
docker-compose up --build -d

# 3. Verificar status
docker-compose ps

# 4. Ver logs
docker-compose logs
```

**Resultado:**
- Frontend: http://localhost:**80**
- Seeds NÃO executados (dados reais)

### 🔧 Comandos Docker Úteis

```bash
# Parar todos os containers
docker-compose down

# Reiniciar um serviço específico
docker-compose restart api
docker-compose restart web

# Ver logs de um serviço
docker-compose logs -f api
docker-compose logs -f web

# Executar comando dentro do container
docker-compose exec api bash
docker-compose exec web sh

# Rebuild apenas um serviço
docker-compose build api
docker-compose build web

# Remover volumes (⚠️ apaga dados do banco)
docker-compose down -v
```

### 🔍 Diferenças entre Ambientes

| Aspecto | Desenvolvimento | Produção |
|---------|----------------|----------|
| **NODE_ENV** | `development` | `production` |
| **Frontend** | Porta 4200 (Angular dev) | Porta 80 (nginx) |
| **Seeds** | ✅ Executados | ❌ Não executados |
| **Build** | Desenvolvimento | Otimizado |
| **Hot Reload** | ✅ Ativo | ❌ Desabilitado |
| **Volumes** | Código montado | Código copiado |
| **Logs** | Verbosos | Produção |

### 🎯 Recursos por Ambiente

#### 🛠️ Desenvolvimento
- ✅ **Hot reload** em todos os serviços
- ✅ **Volumes montados** - mudanças refletidas automaticamente
- ✅ **Seeds executados** - dados de teste
- ✅ **Logs detalhados** para debug
- ✅ **Ferramentas de desenvolvimento** disponíveis

#### 🏭 Produção
- ✅ **Build otimizado** para performance
- ✅ **Nginx** para servir frontend
- ✅ **Sem seeds** - dados reais preservados
- ✅ **Logs de produção** otimizados
- ✅ **Imagens Docker** menores e seguras

---

## 🗄️ Sistema de Banco de Dados

### Migrações TypeORM

O projeto usa TypeORM com sistema de migrações automático:

```bash
# Dentro do container da API
docker-compose exec api bash

# Gerar migração baseada nas entidades
npm run migration:generate src/database/migrations/NomeDaMigracao

# Executar migrações pendentes
npm run migration:run

# Reverter última migração
npm run migration:revert

# Ver status das migrações
npm run migration:show
```

### Seeds (Apenas Desenvolvimento)

```bash
# Seeds são executados automaticamente em desenvolvimento
# Para executar manualmente:
docker-compose exec api npm run seed:run

# Para resetar dados:
docker-compose exec api npm run seed:reset
```

**⚠️ Importante:** Seeds **NÃO são executados em produção** para preservar dados reais.

### Estrutura de Dados

O sistema cria automaticamente:
- **Dentistas** com especialidades
- **Funcionários** de diferentes tipos
- **Procedimentos** odontológicos completos
- **Pacientes** para testes
- **Fluxos de tratamento** profissionais

---

## 🤖 Sistema de Automação

O sistema inclui agentes de IA para automação de processos odontológicos.

### Agentes Disponíveis

- **Agente de Triagem**: Identifica pacientes para follow-up
- **Agente Odontológico**: Gera recomendações de tratamento com IA

### Endpoints da API

- `POST /api/agents/triage` - Executa agente de triagem
- `POST /api/agents/odontology` - Executa agente odontológico
- `POST /api/agents/combined` - Executa ambos os agentes em sequência
- `GET /api/agents/status` - Status geral dos agentes
- `GET /api/agents/status/triage` - Status do agente de triagem
- `GET /api/agents/status/odontology` - Status do agente odontológico

### Configuração

Configure as variáveis necessárias no arquivo `.env`:

```env
# Automação
API_URL=http://api:3000/api
AUTOMATION_URL=http://automacao:8000
LOG_LEVEL=INFO
GOOGLE_API_KEY=sua_chave_do_google_ai_aqui

# Webhook (opcional)
WEBHOOK_URL=https://your-webhook-url.com/webhook/endpoint
WEBHOOK_USERNAME=your_webhook_username
WEBHOOK_PASSWORD=your_webhook_password
```

**⚠️ Importante para Produção:**
- Use URLs e portas específicas do seu ambiente de produção
- Configure `LOG_LEVEL=INFO` ou `ERROR` para produção
- Todas as URLs são configuráveis via variáveis de ambiente

---

## 🚨 Solução de Problemas

### Problemas Comuns

#### ❌ Containers não iniciam
```bash
# Verificar status
docker-compose ps

# Ver logs
docker-compose logs

# Reiniciar containers
docker-compose restart
```

#### ❌ Erro de conexão com banco
```bash
# Verificar se MySQL está healthy
docker-compose ps

# Reiniciar MySQL
docker-compose restart mysql

# Ver logs do MySQL
docker-compose logs mysql
```

#### ❌ Frontend não carrega
```bash
# Verificar se está na porta correta
# Desenvolvimento: localhost:4200
# Produção: localhost:80

# Reiniciar frontend
docker-compose restart web
```

#### ❌ Portas ocupadas
```bash
# Verificar o que está usando a porta
lsof -i :4200
lsof -i :80
lsof -i :3000

# Parar outros serviços ou mudar portas no .env
```

#### ❌ Problemas com migrações
```bash
# Entrar no container da API
docker-compose exec api bash

# Reverter migração
npm run migration:revert

# Executar novamente
npm run migration:run
```

### Comandos de Debug

```bash
# Ver configuração do docker-compose
docker-compose config

# Verificar variáveis de ambiente
docker-compose exec api printenv | grep NODE_ENV

# Inspecionar container
docker inspect crm_odonto_web

# Ver recursos utilizados
docker stats
```

### Reset Completo

```bash
# Parar tudo e remover volumes (⚠️ apaga dados)
docker-compose down -v

# Remover imagens
docker-compose down --rmi all

# Rebuild completo
docker-compose up --build
```

---

## 📚 Documentação Adicional

### Arquitetura do Sistema

- **Backend**: NestJS + TypeORM + MySQL
- **Frontend**: Angular + Tailwind CSS
- **Automação**: Python + FastAPI + Google AI
- **Armazenamento**: MinIO (S3-compatible)
- **Containerização**: Docker + Docker Compose

### Estrutura de Pastas

```
crm-odonto/
├── api/                    # Backend NestJS
│   ├── src/
│   │   ├── modules/        # Módulos da aplicação
│   │   ├── database/       # Migrações e seeds
│   │   └── shared/         # Utilitários compartilhados
│   └── Dockerfile
├── web/                    # Frontend Angular
│   ├── src/
│   │   ├── app/           # Componentes e serviços
│   │   └── assets/        # Recursos estáticos
│   └── Dockerfile
├── automacao/             # Sistema de IA
│   ├── agents/            # Agentes de automação
│   └── requirements.txt
└── docker-compose.yml     # Orquestração Docker
```

### Tecnologias Utilizadas

| Componente | Tecnologia | Versão |
|------------|------------|--------|
| **Backend** | NestJS | 10+ |
| **Database** | MySQL | 8+ |
| **ORM** | TypeORM | 0.3+ |
| **Frontend** | Angular | 17+ |
| **Styling** | Tailwind CSS | 3+ |
| **Automação** | Python | 3.11+ |
| **API IA** | FastAPI | 0.100+ |
| **Storage** | MinIO | Latest |
| **Container** | Docker | 20+ |

### Credenciais Padrão

**MinIO Console:**
- URL: http://localhost:3016
- Usuário: `minioadmin`
- Senha: `minioadmin123`

**MySQL:**
- Host: `localhost:3306`
- Usuário: `root`
- Senha: `root123`
- Database: `crm_odonto`

---

## 🤝 Contribuição

Para contribuir com o projeto:

1. Fork o repositório
2. Crie um branch: `git checkout -b feature/nova-funcionalidade`
3. Commit suas mudanças: `git commit -m 'Adiciona nova funcionalidade'`
4. Push para o branch: `git push origin feature/nova-funcionalidade`
5. Abra um Pull Request

---

## 📄 Licença

© 2024 CRM Odonto - Sistema de Gestão para Clínicas Odontológicas

---

## 🆘 Suporte

Se encontrar problemas ou tiver dúvidas:

1. Verifique a seção [🚨 Solução de Problemas](#-solução-de-problemas)
2. Consulte os logs: `docker-compose logs`
3. Abra uma issue no repositório

**Desenvolvido com ❤️ para clínicas odontológicas**
