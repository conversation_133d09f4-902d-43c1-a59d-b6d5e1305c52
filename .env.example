# Database Configuration
DB_HOST=mysql
DB_EXTERNAL_PORT=3010
DB_PORT=3306
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_DATABASE=your_db_name

# API Configuration
API_PORT=3000
API_EXTERNAL_PORT=3011
API_URL=https://your-api-url.com/api
JWT_SECRET=your_jwt_secret
NODE_ENV=production

# Frontend Configuration
FRONTEND_EXTERNAL_PORT=3012
FRONTEND_PORT=80

# MinIO Configuration
MINIO_ENDPOINT=minio
MINIO_EXTERNAL_PORT=3015
MINIO_PORT=9000
MINIO_CONSOLE_EXTERNAL_PORT=3016
MINIO_CONSOLE_PORT=9001
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_ROOT_USER=your_minio_root_user
MINIO_ROOT_PASSWORD=your_minio_root_password
MINIO_BUCKET_NAME=your_minio_bucket
MINIO_PUBLIC_URL=https://your-public-minio-url.com

# Automacao
AUTOMATION_PORT=8000
AUTOMATION_EXTERNAL_PORT=3023
AUTOMATION_URL=http://automacao:8000
LOG_LEVEL=INFO
GOOGLE_API_KEY=your_google_api_key

# Webhook Configuration
WEBHOOK_URL=https://your-webhook-url.com/webhook/endpoint
WEBHOOK_USERNAME=your_webhook_username
WEBHOOK_PASSWORD=your_webhook_password
