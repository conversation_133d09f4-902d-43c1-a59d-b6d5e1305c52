#!/bin/bash

# =============================================================================
# CRM ODONTO - SCRIPT DE DESENVOLVIMENTO
# =============================================================================
# Este script é específico para configurar e executar o ambiente de desenvolvimento
# Uso: ./setup-dev.sh
# =============================================================================

# Cores para melhor visualização
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para exibir banner
show_banner() {
    echo -e "${CYAN}"
    echo "============================================================================="
    echo "                🚀 CRM ODONTO - SETUP DESENVOLVIMENTO 🚀"
    echo "============================================================================="
    echo -e "${NC}"
}

# Função para verificar dependências
check_dependencies() {
    echo -e "${YELLOW}🔍 Verificando dependências...${NC}"
    
    # Verificar se o Docker está instalado
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker não está instalado. Por favor, instale o Docker para continuar.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker encontrado${NC}"

    # Verificar se o Docker Compose está instalado
    if ! command -v docker compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose não está instalado. Por favor, instale o Docker Compose para continuar.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker Compose encontrado${NC}"
}

# Função para configurar ambiente de desenvolvimento
setup_development() {
    echo -e "${YELLOW}⚙️  Configurando ambiente de desenvolvimento...${NC}"
    
    # Verificar se .env existe
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}📋 Arquivo .env não encontrado. Criando arquivo .env padrão...${NC}"
        cat > .env << 'EOF'
# Database Configuration
DB_HOST=mysql
DB_EXTERNAL_PORT=3306
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=123456
DB_DATABASE=crm_odonto

# API Configuration
API_PORT=3000
API_EXTERNAL_PORT=3000
JWT_SECRET=your_jwt_secret
NODE_ENV=development

# Frontend Configuration
FRONTEND_EXTERNAL_PORT=4200
FRONTEND_PORT=4200

# MinIO Configuration
MINIO_ENDPOINT=minio
MINIO_EXTERNAL_PORT=3015
MINIO_PORT=9000
MINIO_CONSOLE_EXTERNAL_PORT=3016
MINIO_CONSOLE_PORT=9001
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_BUCKET_NAME=crm-odonto
MINIO_PUBLIC_URL=http://localhost:3015

# Automacao
AUTOMATION_PORT=8000
AUTOMATION_EXTERNAL_PORT=3023
GOOGLE_API_KEY=your_google_api_key_here
EOF
        echo -e "${GREEN}✅ Arquivo .env criado com configurações de desenvolvimento${NC}"
    else
        echo -e "${BLUE}📋 Arquivo .env encontrado. Garantindo configurações de desenvolvimento...${NC}"
        
        # Garantir que NODE_ENV está como development
        if grep -q "NODE_ENV=" .env; then
            sed -i 's/NODE_ENV=.*/NODE_ENV=development/g' .env
        else
            echo "NODE_ENV=development" >> .env
        fi
        
        # Garantir que as portas estão corretas para desenvolvimento
        if grep -q "FRONTEND_EXTERNAL_PORT=" .env; then
            sed -i 's/FRONTEND_EXTERNAL_PORT=.*/FRONTEND_EXTERNAL_PORT=4200/g' .env
        else
            echo "FRONTEND_EXTERNAL_PORT=4200" >> .env
        fi
        
        if grep -q "FRONTEND_PORT=" .env; then
            sed -i 's/FRONTEND_PORT=.*/FRONTEND_PORT=4200/g' .env
        else
            echo "FRONTEND_PORT=4200" >> .env
        fi
        
        echo -e "${GREEN}✅ Configurações de desenvolvimento aplicadas${NC}"
    fi
    
    # Carregar variáveis do .env
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
}

# Função para parar containers existentes
stop_containers() {
    echo -e "${YELLOW}🛑 Parando containers existentes...${NC}"
    docker compose down --remove-orphans
}

# Função para construir e iniciar containers
build_and_start() {
    echo -e "${YELLOW}🔨 Construindo e iniciando containers para desenvolvimento...${NC}"
    
    # Build com progresso
    echo -e "${BLUE}📦 Construindo imagens Docker...${NC}"
    docker compose build --progress=plain
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Falha ao construir as imagens Docker.${NC}"
        exit 1
    fi
    
    # Iniciar containers
    echo -e "${BLUE}🚀 Iniciando containers...${NC}"
    docker compose up -d
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Falha ao iniciar os containers.${NC}"
        exit 1
    fi
}

# Função para verificar saúde dos containers
check_containers_health() {
    echo -e "${YELLOW}🏥 Verificando saúde dos containers...${NC}"
    
    # Aguardar um pouco para os containers iniciarem
    sleep 5
    
    # Lista de containers esperados
    containers=("crm_odonto_mysql" "crm_odonto_api" "crm_odonto_web" "crm_odonto_automacao" "crm_odonto_minio")
    
    for container in "${containers[@]}"; do
        if docker ps | grep -q "$container"; then
            echo -e "${GREEN}✅ $container está rodando${NC}"
        else
            echo -e "${RED}❌ $container não está rodando${NC}"
            echo -e "${YELLOW}📋 Logs do $container:${NC}"
            docker logs "$container" --tail 10
            return 1
        fi
    done
    
    return 0
}

# Função para exibir informações finais
show_final_info() {
    echo -e "${GREEN}"
    echo "============================================================================="
    echo "                🎉 DESENVOLVIMENTO CONFIGURADO COM SUCESSO! 🎉"
    echo "============================================================================="
    echo -e "${NC}"
    
    # Carregar variáveis do .env para exibir as portas corretas
    if [ -f ".env" ]; then
        source .env
    fi
    
    echo -e "${CYAN}📊 URLs de Desenvolvimento:${NC}"
    echo -e "${GREEN}🌐 Frontend:      http://localhost:${FRONTEND_EXTERNAL_PORT:-4200}${NC}"
    echo -e "${GREEN}🔧 API:           http://localhost:${API_EXTERNAL_PORT:-3000}${NC}"
    echo -e "${GREEN}📚 Swagger:       http://localhost:${API_EXTERNAL_PORT:-3000}/api/docs${NC}"
    echo -e "${GREEN}🤖 Automação:     http://localhost:${AUTOMATION_EXTERNAL_PORT:-3023}${NC}"
    echo -e "${GREEN}💾 MinIO:         http://localhost:${MINIO_EXTERNAL_PORT:-3015}${NC}"
    echo -e "${GREEN}🎛️  MinIO Console: http://localhost:${MINIO_CONSOLE_EXTERNAL_PORT:-3016}${NC}"
    
    echo -e "${YELLOW}"
    echo "============================================================================="
    echo "💡 Comandos úteis para desenvolvimento:"
    echo "   docker compose logs -f          # Ver logs em tempo real"
    echo "   docker compose logs -f web      # Ver logs apenas do frontend"
    echo "   docker compose logs -f api      # Ver logs apenas da API"
    echo "   docker compose restart web     # Reiniciar apenas o frontend"
    echo "   docker compose down             # Parar todos os containers"
    echo "   docker ps                       # Ver status dos containers"
    echo ""
    echo "🔄 Para reiniciar o desenvolvimento:"
    echo "   ./setup-dev.sh                  # Executar este script novamente"
    echo "============================================================================="
    echo -e "${NC}"
}

# =============================================================================
# SCRIPT PRINCIPAL
# =============================================================================

# Exibir banner
show_banner

echo -e "${BLUE}🎯 Configurando ambiente de desenvolvimento...${NC}"

# Executar etapas do setup
check_dependencies
setup_development
stop_containers
build_and_start

# Verificar saúde dos containers
if check_containers_health; then
    show_final_info
else
    echo -e "${RED}❌ Alguns containers falharam ao iniciar. Verifique os logs acima.${NC}"
    echo -e "${YELLOW}💡 Tente executar: docker compose logs${NC}"
    exit 1
fi
