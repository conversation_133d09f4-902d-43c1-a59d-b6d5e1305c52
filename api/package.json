{"name": "crm-odonto-api", "version": "0.0.1", "description": "API backend para o sistema CRM Odontológico", "author": "CRM Odonto Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:watch": "nest build --watch", "build:webpack": "nest build --webpack", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:analyze": "node build-analyze.js", "build:check": "npm run lint && npm run type-check && npm run build", "type-check": "tsc --noEmit", "build:verbose": "npm run build:check 2>&1 | tee build.log", "build:full-check": "node check-build.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "init-db": "mysql -u root -p < init-db.sql", "swagger": "node swagger-server.js", "swagger:test": "node swagger-test.js", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:migrate": "cross-var npm run typeorm -- -d ./src/database/migrations-config.ts migration:generate ./src/database/migrations/$npm_config_name", "typeorm:run": "npm run build && if [ \"$NODE_ENV\" = \"production\" ]; then node ./node_modules/typeorm/cli.js -d ./dist/database/migrations-config.js migration:run; else npm run typeorm -- -d ./src/database/migrations-config.ts migration:run; fi", "typeorm:revert": "npm run typeorm -- -d ./src/database/migrations-config.ts migration:revert", "seed:run": "if [ \"$NODE_ENV\" = \"production\" ]; then node dist/database/seeds/index.js; else ts-node -r tsconfig-paths/register src/database/seeds/index.ts; fi", "seed:reset": "if [ \"$NODE_ENV\" = \"production\" ]; then node dist/database/seeds/index.js --reset; else ts-node -r tsconfig-paths/register src/database/seeds/index.ts --reset; fi"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@types/express": "^5.0.1", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "express": "^5.1.0", "minio": "^8.0.5", "mysql2": "^3.14.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0", "typescript": "^5.7.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}