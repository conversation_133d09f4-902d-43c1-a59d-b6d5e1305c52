import {
  Injectable,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Dentist } from './entities/dentist.entity';
import { CreateDentistDto } from './dto/create-dentist.dto';
import { UpdateDentistDto } from './dto/update-dentist.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { DentistPaginationDto } from './dto/dentist-pagination.dto';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';
import { ScheduleDetailsDto, TimeSlotDetailDto, TimeSlotStatus } from './dto/schedule-details.dto';
import { DentistSchedule } from '../dentist-schedules/entities/dentist-schedule.entity';
import { DentistException, ExceptionType } from '../dentist-schedules/entities/dentist-exception.entity';
import { Scheduling } from '../schedulings/entities/scheduling.entity';

@Injectable()
export class DentistsService {
  private readonly logger = new Logger(DentistsService.name);

  constructor(
    @InjectRepository(Dentist)
    private dentistsRepository: Repository<Dentist>,
    @InjectRepository(DentistSchedule)
    private dentistScheduleRepository: Repository<DentistSchedule>,
    @InjectRepository(DentistException)
    private dentistExceptionRepository: Repository<DentistException>,
    @InjectRepository(Scheduling)
    private schedulingRepository: Repository<Scheduling>,
    private dataSource: DataSource,
  ) {}

  async create(createDentistDto: CreateDentistDto): Promise<Dentist> {
    // Verificar se já existe um dentista com o mesmo CRO
    const existingDentist = await this.dentistsRepository.findOne({
      where: { cro: createDentistDto.cro },
    });

    if (existingDentist) {
      throw new ConflictException(
        `Já existe um dentista com o CRO ${createDentistDto.cro}`,
      );
    }

    const dentist = this.dentistsRepository.create(createDentistDto);
    return this.dentistsRepository.save(dentist);
  }

  async findAll(): Promise<Dentist[]> {
    this.logger.log('Finding all dentists');
    return this.dentistsRepository.find({
      order: { name: 'ASC' },
    });
  }

  async findAllActive(): Promise<Dentist[]> {
    this.logger.log('Finding all active dentists');
    return this.dentistsRepository.find({
      where: { active: true },
      order: { name: 'ASC' },
    });
  }

  async getAvailableSpecialties(): Promise<string[]> {
    try {
      const result = await this.dentistsRepository
        .createQueryBuilder('dentist')
        .select('DISTINCT dentist.specialty', 'specialty')
        .where('dentist.specialty IS NOT NULL')
        .andWhere('dentist.specialty != ""')
        .orderBy('dentist.specialty', 'ASC')
        .getRawMany();

      return result.map((item: { specialty: string }) => item.specialty);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error getting available specialties: ${errorMessage}`,
        errorStack,
      );
      return [];
    }
  }

  async findAllPaginated(
    paginationDto: DentistPaginationDto,
    activeOnly: boolean = false,
  ): Promise<PaginatedResponse<Dentist>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        active,
        specialties,
      } = paginationDto;

      // Usar o valor do DTO se fornecido, caso contrário, usar o parâmetro
      const isActiveOnly = active !== undefined ? active : activeOnly;

      this.logger.log(
        `Finding dentists with pagination: page ${page}, limit ${limit}, search: ${search}, activeOnly: ${isActiveOnly}, specialties: ${specialties?.join(', ') || 'none'}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder =
        this.dentistsRepository.createQueryBuilder('dentist');

      let hasWhere = false;

      // Aplicar filtro de status ativo
      if (isActiveOnly) {
        queryBuilder.where('dentist.active = :active', { active: true });
        hasWhere = true;
      }

      // Aplicar filtro de busca
      if (search) {
        const searchCondition =
          '(dentist.name LIKE :search OR dentist.cro LIKE :search OR dentist.specialty LIKE :search OR dentist.email LIKE :search)';
        if (hasWhere) {
          queryBuilder.andWhere(searchCondition, { search: `%${search}%` });
        } else {
          queryBuilder.where(searchCondition, { search: `%${search}%` });
          hasWhere = true;
        }
      }

      // Aplicar filtro de especialidades
      if (specialties && specialties.length > 0) {
        const specialtyConditions = specialties
          .map((_, index) => `dentist.specialty LIKE :specialty${index}`)
          .join(' OR ');
        const specialtyParams = {};
        specialties.forEach((specialty, index) => {
          specialtyParams[`specialty${index}`] = `%${specialty}%`;
        });

        if (hasWhere) {
          queryBuilder.andWhere(`(${specialtyConditions})`, specialtyParams);
        } else {
          queryBuilder.where(`(${specialtyConditions})`, specialtyParams);
          hasWhere = true;
        }
      }

      // Aplicar paginação
      queryBuilder.skip(skip).take(limit);

      // Ordenação
      queryBuilder.orderBy('dentist.name', 'ASC');

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding dentists with pagination: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao buscar dentistas');
    }
  }

  async findOne(id: number): Promise<Dentist> {
    const dentist = await this.dentistsRepository.findOne({
      where: { id },
      relations: ['schedulings'],
    });

    if (!dentist) {
      throw new NotFoundException(`Dentista com ID ${id} não encontrado`);
    }

    return dentist;
  }

  /**
   * Verifica se um dentista tem procedimentos de tratamento associados
   * @param dentistId ID do dentista
   * @returns true se o dentista tiver procedimentos, false caso contrário
   */
  async checkDentistHasProcedures(dentistId: number): Promise<boolean> {
    try {
      // Consulta direta para verificar se existem procedimentos associados ao dentista
      const count = await this.dataSource
        .getRepository(TreatmentProcedure)
        .createQueryBuilder('procedure')
        .where('procedure.professionalId = :dentistId', { dentistId })
        .getCount();

      return count > 0;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Erro ao verificar procedimentos do dentista ${dentistId}: ${errorMessage}`,
        errorStack,
      );
      // Em caso de erro, assumir que existem procedimentos por segurança
      return true;
    }
  }

  async getScheduleDetails(
    dentistId: number,
    date: string,
    slotDuration: number = 5,
  ): Promise<ScheduleDetailsDto> {
    this.logger.log(
      `Getting schedule details for dentist ${dentistId} on ${date} with ${slotDuration}min slots`,
    );

    // Verificar se o dentista existe
    const dentist = await this.dentistsRepository.findOne({
      where: { id: dentistId },
    });

    if (!dentist) {
      throw new NotFoundException(
        `Dentista com ID ${dentistId} não encontrado`,
      );
    }

    // Obter escala do dentista
    const schedule = await this.dentistScheduleRepository.findOne({
      where: { dentistId },
    });

    // Obter dia da semana
    const dayOfWeek = this.getDayOfWeek(date);

    // Verificar se há exceção para esta data
    const exception = await this.dentistExceptionRepository.findOne({
      where: { dentistId, date },
    });

    // Determinar horários de trabalho
    let workingHours: { start: string; end: string }[] = [];
    let isWorkingDay = false;

    if (exception) {
      if (exception.type === ExceptionType.DAY_OFF) {
        isWorkingDay = false;
      } else if (
        exception.type === ExceptionType.CUSTOM_HOURS &&
        exception.customHours
      ) {
        isWorkingDay = true;
        workingHours = exception.customHours;
      }
    } else if (schedule && schedule.weeklySchedule[dayOfWeek]) {
      const daySchedule = schedule.weeklySchedule[dayOfWeek];
      if (daySchedule && daySchedule.length > 0) {
        isWorkingDay = true;
        workingHours = daySchedule;
      }
    }

    // Buscar agendamentos existentes para este dentista nesta data
    const existingAppointments = await this.schedulingRepository.find({
      where: {
        dentist: { id: dentistId },
        date: new Date(date),
      },
      relations: ['patient'],
    });

    // Gerar todos os slots de tempo possíveis (08:00 - 19:00)
    const allTimeSlots = this.generateAllTimeSlots(slotDuration);

    // Categorizar cada slot
    const timeSlots: TimeSlotDetailDto[] = allTimeSlots.map((time) => {
      const slot: TimeSlotDetailDto = {
        time,
        status: TimeSlotStatus.OUT_OF_OFFICE,
        isInWorkingHours: false,
      };

      // Verificar se está dentro do horário de trabalho
      if (isWorkingDay && this.isTimeInWorkingHours(time, workingHours)) {
        slot.isInWorkingHours = true;
        slot.status = TimeSlotStatus.AVAILABLE;

        // Verificar se há agendamento neste horário
        const appointment = this.findAppointmentAtTime(time, existingAppointments, slotDuration);
        if (appointment) {
          slot.status = TimeSlotStatus.OCCUPIED;
          slot.appointmentInfo = {
            id: appointment.id,
            patientName: appointment.patient?.name || 'Paciente não encontrado',
            startTime: appointment.time,
            endTime: appointment.endTime || this.calculateEndTime(appointment.time, appointment.duration || 30),
            duration: appointment.duration || 30,
            status: appointment.status,
          };
        }
      }

      return slot;
    });

    // Calcular resumo dos agendamentos
    const appointmentsSummary = {
      total: existingAppointments.length,
      confirmed: existingAppointments.filter(apt => apt.status === 'confirmed').length,
      unconfirmed: existingAppointments.filter(apt => apt.status === 'unconfirmed').length,
      completed: existingAppointments.filter(apt => apt.status === 'completed').length,
      cancelled: existingAppointments.filter(apt => apt.status === 'cancelled').length,
    };

    return {
      dentistId,
      dentistName: dentist.name,
      date,
      dayOfWeek,
      isWorkingDay,
      workingHours,
      timeSlots,
      appointmentsSummary,
      exception: exception
        ? {
            type: exception.type,
            reason: exception.reason,
            customHours: exception.customHours,
          }
        : undefined,
    };
  }

  async update(
    id: number,
    updateDentistDto: UpdateDentistDto,
  ): Promise<Dentist> {
    // Verificar se o dentista existe
    const dentist = await this.findOne(id);

    // Verificar se está tentando atualizar o CRO para um que já existe
    if (updateDentistDto.cro && updateDentistDto.cro !== dentist.cro) {
      const existingDentist = await this.dentistsRepository.findOne({
        where: { cro: updateDentistDto.cro },
      });

      if (existingDentist) {
        throw new ConflictException(
          `Já existe um dentista com o CRO ${updateDentistDto.cro}`,
        );
      }
    }

    await this.dentistsRepository.update(id, updateDentistDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const dentist = await this.findOne(id);

    // Verificar se o dentista tem agendamentos associados
    if (dentist.schedulings?.length > 0) {
      // Em vez de excluir, apenas marcar como inativo
      await this.dentistsRepository.update(id, { active: false });
    } else {
      // Verificar se o dentista tem procedimentos de tratamento associados
      const hasProcedures = await this.checkDentistHasProcedures(id);
      if (hasProcedures) {
        // Se tiver procedimentos, apenas marcar como inativo
        await this.dentistsRepository.update(id, { active: false });
      } else {
        // Se não tiver nem agendamentos nem procedimentos, pode excluir
        await this.dentistsRepository.delete(id);
      }
    }
  }

  // Métodos auxiliares para getScheduleDetails
  private getDayOfWeek(date: string): string {
    const days = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const dateObj = new Date(date + 'T00:00:00');
    return days[dateObj.getDay()];
  }

  private generateAllTimeSlots(slotDuration: number): string[] {
    const slots: string[] = [];
    // Gerar slots de 08:00 até 19:00
    const startTime = 8 * 60; // 08:00 em minutos
    const endTime = 19 * 60; // 19:00 em minutos

    for (let minutes = startTime; minutes < endTime; minutes += slotDuration) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      const timeStr = `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
      slots.push(timeStr);
    }

    return slots;
  }

  private isTimeInWorkingHours(
    time: string,
    workingHours: { start: string; end: string }[],
  ): boolean {
    const timeMinutes = this.timeToMinutes(time);

    for (const range of workingHours) {
      const startMinutes = this.timeToMinutes(range.start);
      const endMinutes = this.timeToMinutes(range.end);

      if (timeMinutes >= startMinutes && timeMinutes < endMinutes) {
        return true;
      }
    }

    return false;
  }

  private findAppointmentAtTime(
    time: string,
    appointments: Scheduling[],
    slotDuration: number,
  ): Scheduling | null {
    const slotStartMinutes = this.timeToMinutes(time);
    const slotEndMinutes = slotStartMinutes + slotDuration;

    for (const appointment of appointments) {
      const appointmentStartMinutes = this.timeToMinutes(appointment.time);
      const appointmentDuration = appointment.duration || 30;
      const appointmentEndMinutes = appointmentStartMinutes + appointmentDuration;

      // Verificar se há sobreposição
      if (
        (slotStartMinutes >= appointmentStartMinutes &&
          slotStartMinutes < appointmentEndMinutes) ||
        (slotEndMinutes > appointmentStartMinutes &&
          slotEndMinutes <= appointmentEndMinutes) ||
        (slotStartMinutes <= appointmentStartMinutes &&
          slotEndMinutes >= appointmentEndMinutes)
      ) {
        return appointment;
      }
    }

    return null;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private calculateEndTime(startTime: string, duration: number): string {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = startMinutes + duration;
    const hours = Math.floor(endMinutes / 60);
    const mins = endMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
