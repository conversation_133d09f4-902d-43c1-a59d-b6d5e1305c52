import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Scheduling } from '../../schedulings/entities/scheduling.entity';
import { Budget } from '../../budgets/entities/budget.entity';

@Entity('dentists')
export class Dentist {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do dentista' })
  id: number;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Nome completo do dentista' })
  name: string;

  @Column({ length: 20, unique: true })
  @ApiProperty({
    description: 'Número do CRO (Conselho Regional de Odontologia)',
  })
  cro: string;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Especialidade do dentista' })
  specialty: string;

  @Column({ length: 20 })
  @ApiProperty({ description: 'Telefone de contato do dentista' })
  phone: string;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Email do dentista' })
  email: string;

  @Column({ nullable: true, length: 255 })
  @ApiProperty({ description: 'Observações sobre o dentista', required: false })
  notes: string;

  @Column({ default: true })
  @ApiProperty({ description: 'Indica se o dentista está ativo' })
  active: boolean;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;

  @OneToMany(() => Scheduling, (scheduling) => scheduling.dentist)
  schedulings: Scheduling[];

  @OneToMany(() => Budget, (budget) => budget.dentist)
  budgets: Budget[];
}
