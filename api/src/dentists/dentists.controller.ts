import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DentistsService } from './dentists.service';
import { CreateDentistDto } from './dto/create-dentist.dto';
import { UpdateDentistDto } from './dto/update-dentist.dto';
import { Dentist } from './entities/dentist.entity';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { DentistPaginationDto } from './dto/dentist-pagination.dto';

@ApiTags('dentists')
@Controller('dentists')
export class DentistsController {
  constructor(private readonly dentistsService: DentistsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo dentista' })
  @ApiResponse({
    status: 201,
    description: 'Dentista criado com sucesso',
    type: Dentist,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'Conflito - CRO já existe' })
  create(@Body() createDentistDto: CreateDentistDto): Promise<Dentist> {
    return this.dentistsService.create(createDentistDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os dentistas' })
  @ApiQuery({
    name: 'active',
    required: false,
    type: Boolean,
    description: 'Filtrar por dentistas ativos',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Termo de busca para filtrar dentistas por nome, CRO, especialidade ou email',
    type: String,
  })
  @ApiQuery({
    name: 'specialties',
    required: false,
    description:
      'Filtrar por especialidades específicas (separadas por vírgula)',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de dentistas retornada com sucesso',
  })
  async findAll(
    @Query() paginationDto: DentistPaginationDto,
  ): Promise<PaginatedResponse<Dentist> | Dentist[]> {
    // Se não houver parâmetros de paginação, retorna todos os dentistas (para compatibilidade)
    if (!paginationDto.page && !paginationDto.limit) {
      if (paginationDto.active) {
        return this.dentistsService.findAllActive();
      }
      return this.dentistsService.findAll();
    }

    // Se houver parâmetros de paginação, retorna os dentistas paginados
    return this.dentistsService.findAllPaginated(
      paginationDto,
      paginationDto.active,
    );
  }

  @Get('specialties/available')
  @ApiOperation({ summary: 'Buscar todas as especialidades disponíveis' })
  @ApiResponse({
    status: 200,
    description: 'Lista de especialidades retornada com sucesso',
    type: [String],
  })
  getAvailableSpecialties(): Promise<string[]> {
    return this.dentistsService.getAvailableSpecialties();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um dentista pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({
    status: 200,
    description: 'Dentista encontrado',
    type: Dentist,
  })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  findOne(@Param('id') id: string): Promise<Dentist> {
    return this.dentistsService.findOne(+id);
  }

  @Get(':id/schedule-details')
  @ApiOperation({ summary: 'Obter detalhes completos da agenda do dentista para uma data específica' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiQuery({ name: 'date', description: 'Data no formato YYYY-MM-DD', required: true })
  @ApiQuery({ name: 'duration', description: 'Duração do slot em minutos (padrão: 5)', required: false })
  @ApiResponse({
    status: 200,
    description: 'Detalhes da agenda retornados com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  getScheduleDetails(
    @Param('id') id: string,
    @Query('date') date: string,
    @Query('duration') duration?: number,
  ) {
    return this.dentistsService.getScheduleDetails(+id, date, duration ? Number(duration) : 5);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({
    status: 200,
    description: 'Dentista atualizado com sucesso',
    type: Dentist,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  @ApiResponse({ status: 409, description: 'Conflito - CRO já existe' })
  update(
    @Param('id') id: string,
    @Body() updateDentistDto: UpdateDentistDto,
  ): Promise<Dentist> {
    return this.dentistsService.update(+id, updateDentistDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({ status: 204, description: 'Dentista removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  remove(@Param('id') id: string): Promise<void> {
    return this.dentistsService.remove(+id);
  }
}
