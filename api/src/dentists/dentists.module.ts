import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DentistsService } from './dentists.service';
import { DentistsController } from './dentists.controller';
import { Dentist } from './entities/dentist.entity';
import { DentistSchedule } from '../dentist-schedules/entities/dentist-schedule.entity';
import { DentistException } from '../dentist-schedules/entities/dentist-exception.entity';
import { Scheduling } from '../schedulings/entities/scheduling.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Dentist, DentistSchedule, DentistException, Scheduling])],
  controllers: [DentistsController],
  providers: [DentistsService],
  exports: [DentistsService],
})
export class DentistsModule {}
