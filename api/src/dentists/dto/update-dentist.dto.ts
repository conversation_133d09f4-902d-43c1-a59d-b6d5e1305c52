import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsBoolean,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { CreateDentistDto } from './create-dentist.dto';

export class UpdateDentistDto extends PartialType(CreateDentistDto) {
  @ApiProperty({ description: 'Nome completo do dentista', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Número do CRO (Conselho Regional de Odontologia)',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(20)
  cro?: string;

  @ApiProperty({ description: 'Especialidade do dentista', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  specialty?: string;

  @ApiProperty({
    description: 'Telefone de contato do dentista',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(20)
  phone?: string;

  @ApiProperty({ description: 'Email do dentista', required: false })
  @IsEmail()
  @IsOptional()
  @MaxLength(100)
  email?: string;

  @ApiProperty({ description: 'Observações sobre o dentista', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  notes?: string;

  @ApiProperty({
    description: 'Indica se o dentista está ativo',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
