import { ApiProperty } from '@nestjs/swagger';

export enum TimeSlotStatus {
  AVAILABLE = 'available',
  OCCUPIED = 'occupied',
  OUT_OF_OFFICE = 'out_of_office',
  BREAK_TIME = 'break_time',
}

export class TimeSlotDetailDto {
  @ApiProperty({ description: 'Horário do slot (HH:MM)' })
  time: string;

  @ApiProperty({ 
    description: 'Status do slot',
    enum: TimeSlotStatus,
    example: TimeSlotStatus.AVAILABLE
  })
  status: TimeSlotStatus;

  @ApiProperty({ description: 'Se está dentro do horário de trabalho', example: true })
  isInWorkingHours: boolean;

  @ApiProperty({ 
    description: 'Informações do agendamento (se ocupado)',
    required: false
  })
  appointmentInfo?: {
    id: number;
    patientName: string;
    startTime: string;
    endTime: string;
    duration: number;
    status: string;
  };
}

export class ScheduleDetailsDto {
  @ApiProperty({ description: 'ID do dentista' })
  dentistId: number;

  @ApiProperty({ description: 'Nome do dentista' })
  dentistName: string;

  @ApiProperty({ description: 'Data da consulta (YYYY-MM-DD)' })
  date: string;

  @ApiProperty({ description: 'Dia da semana' })
  dayOfWeek: string;

  @ApiProperty({ description: 'Se o dentista trabalha neste dia' })
  isWorkingDay: boolean;

  @ApiProperty({ 
    description: 'Horários de trabalho do dia',
    type: [Object],
    example: [{ start: '08:00', end: '12:00' }, { start: '14:00', end: '18:00' }]
  })
  workingHours: { start: string; end: string }[];

  @ApiProperty({ 
    description: 'Todos os slots de tempo com seus status',
    type: [TimeSlotDetailDto]
  })
  timeSlots: TimeSlotDetailDto[];

  @ApiProperty({ 
    description: 'Resumo dos agendamentos do dia',
    type: [Object]
  })
  appointmentsSummary: {
    total: number;
    confirmed: number;
    unconfirmed: number;
    completed: number;
    cancelled: number;
  };

  @ApiProperty({ 
    description: 'Informações de exceção (se houver)',
    required: false
  })
  exception?: {
    type: string;
    reason?: string;
    customHours?: { start: string; end: string }[];
  };
}
