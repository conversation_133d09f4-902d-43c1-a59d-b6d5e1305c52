import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsBoolean,
  IsOptional,
  MaxLength,
} from 'class-validator';

export class CreateDentistDto {
  @ApiProperty({ description: 'Nome completo do dentista' })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Número do CRO (Conselho Regional de Odontologia)',
  })
  @IsString()
  @MaxLength(20)
  cro: string;

  @ApiProperty({ description: 'Especialidade do dentista' })
  @IsString()
  @MaxLength(100)
  specialty: string;

  @ApiProperty({ description: 'Telefone de contato do dentista' })
  @IsString()
  @MaxLength(20)
  phone: string;

  @ApiProperty({ description: 'Email do dentista' })
  @IsEmail()
  @MaxLength(100)
  email: string;

  @ApiProperty({ description: 'Observações sobre o dentista', required: false })
  @IsString()
  @IsOptional()
  @<PERSON>Length(255)
  notes?: string;

  @ApiProperty({
    description: 'Indica se o dentista está ativo',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
