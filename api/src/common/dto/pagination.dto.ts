import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, Min, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class PaginationDto {
  @ApiProperty({
    description: 'Número da página (começando em 1)',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    default: 6,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 6;

  @ApiProperty({
    description: 'Termo de busca para filtrar resultados',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Categoria para filtrar resultados',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages?: number;
}

export class PaginatedResponseDto {
  @ApiProperty({ description: 'Lista de itens na página atual' })
  data: unknown[];

  @ApiProperty({ description: 'Número total de itens' })
  total: number;

  @ApiProperty({ description: 'Número da página atual' })
  page: number;

  @ApiProperty({ description: 'Quantidade de itens por página' })
  limit: number;

  @ApiProperty({ description: 'Número total de páginas' })
  totalPages: number;
}
