import { Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class ValidationService {
  /**
   * Valida uma data e retorna no formato YYYY-MM-DD
   * @param date Data a ser validada
   * @param fieldName Nome do campo para mensagem de erro
   * @returns Data formatada
   */
  validateAndFormatDate(date: unknown, fieldName: string): string | null {
    if (!date) return null;

    try {
      const dateObj = new Date(
        typeof date === 'string' ||
        typeof date === 'number' ||
        date instanceof Date
          ? date
          : JSON.stringify(date),
      );

      // Verifica se a data é válida
      if (isNaN(dateObj.getTime())) {
        throw new BadRequestException(`${fieldName} inválida`);
      }

      // Retorna no formato YYYY-MM-DD
      return dateObj.toISOString().split('T')[0];
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      throw new BadRequestException(`${fieldName} inválida: ${errorMessage}`);
    }
  }

  /**
   * Valida se um valor é um número
   * @param value Valor a ser validado
   * @param fieldName Nome do campo para mensagem de erro
   * @returns Valor numérico
   */
  validateNumber(value: unknown, fieldName: string): number | null {
    if (value === undefined || value === null) return null;

    const num = Number(value);
    if (isNaN(num)) {
      throw new BadRequestException(`${fieldName} deve ser um número válido`);
    }
    return num;
  }

  /**
   * Valida se um valor está dentro de um conjunto de valores permitidos
   * @param value Valor a ser validado
   * @param allowedValues Valores permitidos
   * @param fieldName Nome do campo para mensagem de erro
   * @returns Valor validado
   */
  validateEnum<T>(
    value: unknown,
    allowedValues: T[],
    fieldName: string,
  ): T | null {
    if (!value) return null;

    if (!allowedValues.includes(value as T)) {
      throw new BadRequestException(
        `${fieldName} deve ser um dos seguintes valores: ${allowedValues.join(', ')}`,
      );
    }
    return value as T;
  }
}
