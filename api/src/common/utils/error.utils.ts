/**
 * Utilitários para tratamento de erros de forma type-safe
 */

/**
 * Extrai a mensagem de erro de forma segura
 * @param error - Erro de qualquer tipo
 * @returns Mensagem de erro como string
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as { message: unknown }).message);
  }

  return 'Erro desconhecido';
}

/**
 * Extrai o stack trace de forma segura
 * @param error - Erro de qualquer tipo
 * @returns Stack trace como string ou undefined se não disponível
 */
export function getErrorStack(error: unknown): string | undefined {
  if (error instanceof Error && error.stack) {
    return error.stack;
  }

  if (error && typeof error === 'object' && 'stack' in error) {
    const stack = (error as { stack: unknown }).stack;
    return typeof stack === 'string' ? stack : undefined;
  }

  return undefined;
}

/**
 * Formata um erro para logging de forma segura
 * @param error - Erro de qualquer tipo
 * @returns Objeto com message e stack (se disponível)
 */
export function formatErrorForLogging(error: unknown): {
  message: string;
  stack?: string;
} {
  return {
    message: getErrorMessage(error),
    stack: getErrorStack(error),
  };
}

/**
 * Verifica se um valor é um erro
 * @param value - Valor a ser verificado
 * @returns true se for um erro
 */
export function isError(value: unknown): value is Error {
  return value instanceof Error;
}

/**
 * Verifica se um erro tem stack trace
 * @param error - Erro a ser verificado
 * @returns true se tiver stack trace
 */
export function hasStack(error: unknown): error is Error & { stack: string } {
  return isError(error) && typeof error.stack === 'string';
}
