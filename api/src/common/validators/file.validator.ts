import { Injectable } from '@nestjs/common';

@Injectable()
export class FileValidator {
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_MIMETYPES = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  private readonly PDF_MIMETYPE = 'application/pdf';

  private readonly DOCUMENT_MIMETYPES = [
    'application/pdf',
    'image/jpeg',
    'image/png',
  ];

  validate(file: Express.Multer.File): string | null {
    if (file.size > this.MAX_FILE_SIZE) {
      return 'Arquivo excede o tamanho máximo permitido de 5MB';
    }

    if (!this.ALLOWED_MIMETYPES.includes(file.mimetype)) {
      return 'Tipo de arquivo não permitido';
    }

    return null;
  }

  validatePdfOnly(file: Express.Multer.File): string | null {
    if (file.size > this.MAX_FILE_SIZE) {
      return 'Arquivo excede o tamanho máximo permitido de 5MB';
    }

    if (file.mimetype !== this.PDF_MIMETYPE) {
      return 'Apenas arquivos PDF são permitidos';
    }

    return null;
  }

  validateDocumentFile(file: Express.Multer.File): string | null {
    if (file.size > this.MAX_FILE_SIZE) {
      return 'Arquivo excede o tamanho máximo permitido de 5MB';
    }

    if (!this.DOCUMENT_MIMETYPES.includes(file.mimetype)) {
      return 'Apenas arquivos PDF, JPG, JPEG e PNG são permitidos';
    }

    return null;
  }
}
