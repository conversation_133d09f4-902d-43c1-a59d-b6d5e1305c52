import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MinioStorageProvider } from './providers/minio-storage.provider';
import { FileValidator } from './validators/file.validator';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'StorageProvider',
      useClass: MinioStorageProvider,
    },
    FileValidator,
  ],
  exports: ['StorageProvider', FileValidator],
})
export class StorageModule {}
