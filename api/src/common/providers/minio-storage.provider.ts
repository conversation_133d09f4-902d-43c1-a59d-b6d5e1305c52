/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import {
  Injectable,
  Logger,
  OnModuleInit,
  BadRequestException,
} from '@nestjs/common';
import { IStorageProvider } from '../interfaces/storage.interface';
import { Client } from 'minio';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import { FileValidator } from '../validators/file.validator';
import { formatErrorForLogging } from '../utils/error.utils';

@Injectable()
export class MinioStorageProvider implements IStorageProvider, OnModuleInit {
  private readonly client: Client;
  private readonly logger = new Logger(MinioStorageProvider.name); // Logger para monitoramento
  private readonly bucketName: string;
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 segundo

  constructor(
    private configService: ConfigService,
    private fileValidator: FileValidator,
  ) {
    // Inicializa o cliente MinIO com configurações do .env
    this.client = new Client({
      endPoint:
        this.configService.get<string>('MINIO_ENDPOINT') ||
        (() => {
          throw new Error('MINIO_ENDPOINT is not defined in the configuration');
        })(),
      port: parseInt(this.configService.get('MINIO_PORT') || '9000'),
      useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
      accessKey: this.configService.get('MINIO_ACCESS_KEY'),
      secretKey: this.configService.get('MINIO_SECRET_KEY'),
    });
    const bucketName = this.configService.get<string>('MINIO_BUCKET_NAME');
    if (!bucketName) {
      throw new Error('MINIO_BUCKET_NAME is not defined in the configuration');
    }
    this.bucketName = bucketName;
  }

  private async withRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error = new Error('Operation failed after retries');
    for (let i = 0; i < this.maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;
        } else {
          lastError = new Error('Unknown error occurred');
        }
        if (i < this.maxRetries - 1) {
          await new Promise((resolve) => setTimeout(resolve, this.retryDelay));
        }
      }
    }
    throw lastError;
  }

  // Executa na inicialização do módulo para garantir que o bucket existe
  async onModuleInit() {
    try {
      const bucketExists = await this.client.bucketExists(this.bucketName);
      if (!bucketExists) {
        await this.client.makeBucket(this.bucketName, 'us-east-1');
        this.logger.log(`Bucket '${this.bucketName}' created successfully`);
      }
    } catch (error: unknown) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Failed to initialize MinIO bucket: ${errorInfo.message}`,
      );
      throw error;
    }
  }

  // Método para upload de arquivos
  async uploadFile(
    file: Express.Multer.File,
    customPath?: string,
  ): Promise<string> {
    const validationError = this.fileValidator.validate(file);
    if (validationError) {
      throw new BadRequestException(validationError);
    }

    return this.withRetry(async () => {
      try {
        const dateFolder = new Date().toISOString().split('T')[0];
        const fileExtension = path.extname(file.originalname);
        const fileName = `${uuidv4()}${fileExtension}`;
        const filePath = customPath
          ? `${customPath}/${dateFolder}/${fileName}`
          : `${dateFolder}/${fileName}`;

        await this.client.putObject(
          this.bucketName,
          filePath,
          file.buffer,
          file.size,
          { 'Content-Type': file.mimetype },
        );

        this.logger.log(
          `Arquivo "${filePath}" enviado com sucesso para o MinIO`,
        );
        this.logger.log(`Tamanho: ${file.size} bytes`);
        this.logger.log(`Tipo: ${file.mimetype}`);

        // Verificar se o arquivo existe após o upload
        try {
          const stat = await this.client.statObject(this.bucketName, filePath);
          this.logger.log(`Arquivo verificado no MinIO:`, {
            size: stat.size,
            lastModified: stat.lastModified,
            etag: stat.etag,
          });
        } catch (error: unknown) {
          const errorInfo = formatErrorForLogging(error);
          this.logger.error(
            `Erro ao verificar arquivo no MinIO: ${errorInfo.message}`,
          );
        }

        return filePath;
      } catch (error: unknown) {
        const errorInfo = formatErrorForLogging(error);
        this.logger.error(`Falha ao enviar arquivo: ${errorInfo.message}`);
        throw error;
      }
    });
  }

  // Método para deletar arquivos
  async deleteFile(filePath: string): Promise<void> {
    try {
      await this.client.removeObject(this.bucketName, filePath);
    } catch (error: unknown) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(`Failed to delete file: ${errorInfo.message}`);
      throw error;
    }
  }

  // Método para gerar URLs temporárias de acesso aos arquivos
  async getFileUrl(filePath: string): Promise<string> {
    try {
      // Verificar se existe uma URL pública configurada
      const publicUrl = this.configService.get<string>('MINIO_PUBLIC_URL');

      if (publicUrl) {
        // Agora que o bucket tem política de acesso público, podemos simplesmente retornar a URL direta
        return `${publicUrl}/${this.bucketName}/${filePath}`;
      } else {
        // Se não houver URL pública configurada, usar o método padrão de presigned URL
        const presignedUrl = await this.client.presignedGetObject(
          this.bucketName,
          filePath,
          24 * 60 * 60, // 24 horas
          {
            'response-cache-control': 'max-age=86400', // 24 horas
          },
        );
        return presignedUrl;
      }
    } catch (error: unknown) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(`Failed to get file URL: ${errorInfo.message}`);
      throw error;
    }
  }
}
