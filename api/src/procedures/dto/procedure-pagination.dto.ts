import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { ProcedureType } from '../enums/procedure-type.enum';
import { Status } from '../enums/status.enum';

export class ProcedurePaginationDto {
  @ApiProperty({
    description: 'Página atual (começando em 1)',
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(String(value)))
  page?: number = 1;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(String(value)))
  limit?: number = 10;

  @ApiProperty({
    description: 'Termo de busca para filtrar por nome ou descrição',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filtrar por tipo de procedimento',
    required: false,
    enum: ProcedureType,
  })
  @IsOptional()
  @IsEnum(ProcedureType)
  type?: ProcedureType;

  @ApiProperty({
    description: 'Filtrar por status',
    required: false,
    enum: Status,
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiProperty({
    description: 'Desativar paginação e retornar todos os registros',
    required: false,
    default: 'false',
  })
  @IsOptional()
  @IsString()
  noPagination?: string;
}
