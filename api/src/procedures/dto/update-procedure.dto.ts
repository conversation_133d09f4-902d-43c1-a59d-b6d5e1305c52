import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateProcedureDto } from './create-procedure.dto';
import { IsNumber, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateProcedureDto extends PartialType(CreateProcedureDto) {
  @ApiProperty({ description: 'Valor padrão do procedimento', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'O valor padrão deve ser um número' })
  @Min(0, { message: 'O valor padrão não pode ser negativo' })
  @Type(() => Number)
  defaultPrice?: number;
}
