import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsNumber,
  IsOptional,
  Min,
  <PERSON><PERSON>ength,
  IsNotEmpty,
} from 'class-validator';
import { ProcedureType } from '../enums/procedure-type.enum';
import { Status } from '../enums/status.enum';

export class CreateProcedureDto {
  @ApiProperty({ description: 'Nome do procedimento' })
  @IsNotEmpty({ message: 'O nome do procedimento é obrigatório' })
  @IsString({ message: 'O nome deve ser uma string' })
  @MaxLength(100, { message: 'O nome deve ter no máximo 100 caracteres' })
  name: string;

  @ApiProperty({ description: 'Descrição do procedimento', required: false })
  @IsOptional()
  @IsString({ message: 'A descrição deve ser uma string' })
  description?: string;

  @ApiProperty({ description: 'Valor padrão do procedimento' })
  @IsNotEmpty({ message: 'O valor padrão é obrigatório' })
  @IsNumber({}, { message: 'O valor padrão deve ser um número' })
  @Min(0, { message: 'O valor padrão não pode ser negativo' })
  defaultPrice: number;

  @ApiProperty({ description: 'Duração estimada em minutos' })
  @IsNotEmpty({ message: 'A duração estimada é obrigatória' })
  @IsNumber({}, { message: 'A duração estimada deve ser um número' })
  @Min(1, { message: 'A duração estimada deve ser maior que zero' })
  estimatedDuration: number;

  @ApiProperty({
    description: 'Tipo do procedimento',
    enum: ProcedureType,
  })
  @IsNotEmpty({ message: 'O tipo do procedimento é obrigatório' })
  @IsEnum(ProcedureType, { message: 'Tipo de procedimento inválido' })
  type: ProcedureType;

  @ApiProperty({
    description: 'Status do procedimento',
    enum: Status,
    default: Status.ACTIVE,
  })
  @IsOptional()
  @IsEnum(Status, { message: 'Status inválido' })
  status?: Status;
}
