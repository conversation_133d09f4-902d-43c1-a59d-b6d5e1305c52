import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ProcedureType } from '../enums/procedure-type.enum';
import { Status } from '../enums/status.enum';

@Entity('procedures')
export class Procedure {
  @PrimaryGeneratedColumn('increment')
  @ApiProperty({ description: 'ID único do procedimento' })
  id: number;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Nome do procedimento' })
  name: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Descrição do procedimento', required: false })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Valor padrão do procedimento' })
  defaultPrice: number;

  @Column({ type: 'int', default: 30 })
  @ApiProperty({ description: 'Duração estimada em minutos' })
  estimatedDuration: number;

  @Column({
    type: 'enum',
    enum: ProcedureType,
  })
  @ApiProperty({
    description: 'Tipo do procedimento',
    enum: ProcedureType,
  })
  type: ProcedureType;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE,
  })
  @ApiProperty({
    description: 'Status do procedimento',
    enum: Status,
    default: Status.ACTIVE,
  })
  status: Status;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
