import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { Procedure } from './entities/procedure.entity';
import { CreateProcedureDto } from './dto/create-procedure.dto';
import { UpdateProcedureDto } from './dto/update-procedure.dto';
import { ProcedurePaginationDto } from './dto/procedure-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@Injectable()
export class ProceduresService {
  private readonly logger = new Logger(ProceduresService.name);

  constructor(
    @InjectRepository(Procedure)
    private proceduresRepository: Repository<Procedure>,
  ) {}

  /**
   * Verifica se já existe um procedimento com o mesmo nome
   */
  private async checkNameExists(
    name: string,
    excludeId?: number,
  ): Promise<boolean> {
    try {
      const query = this.proceduresRepository
        .createQueryBuilder('procedure')
        .where('LOWER(procedure.name) = LOWER(:name)', { name });

      // Se um ID foi fornecido, exclui esse procedimento da verificação (para updates)
      if (excludeId) {
        query.andWhere('procedure.id != :id', { id: excludeId });
      }

      const count = await query.getCount();
      return count > 0;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error checking procedure name: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao verificar nome do procedimento',
      );
    }
  }

  async create(createProcedureDto: CreateProcedureDto): Promise<Procedure> {
    try {
      this.logger.log(
        `Creating procedure: ${JSON.stringify(createProcedureDto)}`,
      );

      // Verificar se já existe um procedimento com o mesmo nome
      const nameExists = await this.checkNameExists(createProcedureDto.name);
      if (nameExists) {
        throw new BadRequestException(
          `Já existe um procedimento com o nome '${createProcedureDto.name}'`,
        );
      }

      const procedure = this.proceduresRepository.create(createProcedureDto);
      return await this.proceduresRepository.save(procedure);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating procedure: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao criar procedimento');
    }
  }

  async findAll(): Promise<Procedure[]> {
    return this.proceduresRepository.find();
  }

  async findAllPaginated(
    paginationDto: ProcedurePaginationDto,
  ): Promise<PaginatedResponse<Procedure>> {
    try {
      const { page = 1, limit = 10, search, type, status } = paginationDto;
      const skip = (page - 1) * limit;

      // Construir where com condições dinâmicas
      const where: FindOptionsWhere<Procedure> = {};

      if (search) {
        where.name = Like(`%${search}%`);
      }

      if (type) {
        where.type = type;
      }

      if (status) {
        where.status = status;
      }

      const [procedures, total] = await this.proceduresRepository.findAndCount({
        where,
        skip,
        take: limit,
        order: { name: 'ASC' },
      });

      return {
        data: procedures,
        total,
        page,
        limit,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding procedures: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao buscar procedimentos');
    }
  }

  async findOne(id: number): Promise<Procedure> {
    const procedure = await this.proceduresRepository.findOne({
      where: { id },
    });

    if (!procedure) {
      throw new NotFoundException(`Procedimento com ID ${id} não encontrado`);
    }

    return procedure;
  }

  async update(
    id: number,
    updateProcedureDto: UpdateProcedureDto,
  ): Promise<Procedure> {
    try {
      const procedure = await this.findOne(id);

      // Se o nome foi alterado, verificar se já existe outro procedimento com o mesmo nome
      if (
        updateProcedureDto.name &&
        updateProcedureDto.name !== procedure.name
      ) {
        const nameExists = await this.checkNameExists(
          updateProcedureDto.name,
          id,
        );
        if (nameExists) {
          throw new BadRequestException(
            `Já existe um procedimento com o nome '${updateProcedureDto.name}'`,
          );
        }
      }

      // Atualizar os campos
      Object.assign(procedure, updateProcedureDto);

      return await this.proceduresRepository.save(procedure);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating procedure: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao atualizar procedimento');
    }
  }

  async remove(id: number): Promise<void> {
    const procedure = await this.findOne(id);
    await this.proceduresRepository.remove(procedure);
  }
}
