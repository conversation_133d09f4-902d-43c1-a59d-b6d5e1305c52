import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { ProceduresService } from './procedures.service';
import { CreateProcedureDto } from './dto/create-procedure.dto';
import { UpdateProcedureDto } from './dto/update-procedure.dto';
import { ProcedurePaginationDto } from './dto/procedure-pagination.dto';
import { Procedure } from './entities/procedure.entity';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('procedures')
@Controller('procedures')
export class ProceduresController {
  constructor(private readonly proceduresService: ProceduresService) {}

  @ApiOperation({ summary: 'Criar um novo procedimento' })
  @ApiResponse({ status: 201, description: 'Procedimento criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateProcedureDto })
  @Post()
  create(@Body() createProcedureDto: CreateProcedureDto): Promise<Procedure> {
    return this.proceduresService.create(createProcedureDto);
  }

  @ApiOperation({ summary: 'Listar todos os procedimentos' })
  @ApiResponse({
    status: 200,
    description: 'Lista de procedimentos retornada com sucesso',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar por nome ou descrição',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filtrar por tipo de procedimento',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    description: 'Filtrar por categoria de procedimento',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
  })
  @Get()
  async findAll(
    @Query() paginationDto: ProcedurePaginationDto,
  ): Promise<PaginatedResponse<Procedure> | Procedure[]> {
    const { noPagination } = paginationDto;

    // Se noPagination=true, retorna todos os procedimentos sem paginação
    if (noPagination === 'true') {
      return this.proceduresService.findAll();
    }

    // Caso contrário, retorna com paginação
    return this.proceduresService.findAllPaginated(paginationDto);
  }

  @ApiOperation({ summary: 'Buscar um procedimento pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Procedimento encontrado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Procedimento não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do procedimento' })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Procedure> {
    return this.proceduresService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar um procedimento' })
  @ApiResponse({
    status: 200,
    description: 'Procedimento atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Procedimento não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do procedimento' })
  @ApiBody({ type: UpdateProcedureDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProcedureDto: UpdateProcedureDto,
  ): Promise<Procedure> {
    return this.proceduresService.update(id, updateProcedureDto);
  }

  @ApiOperation({ summary: 'Remover um procedimento' })
  @ApiResponse({
    status: 200,
    description: 'Procedimento removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Procedimento não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do procedimento' })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.proceduresService.remove(id);
  }
}
