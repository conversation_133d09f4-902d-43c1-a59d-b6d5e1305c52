import { Controller, Get, Param, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ColorsService } from './colors.service';
import { AppColorDto } from '../common/dto/app-color.dto';

@ApiTags('colors')
@Controller('colors')
export class ColorsController {
  constructor(private readonly colorsService: ColorsService) {}

  @Get()
  @ApiOperation({
    summary: 'Listar todas as cores disponíveis',
    description:
      'Retorna uma lista com todas as cores disponíveis no sistema, incluindo nome e valor hexadecimal.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de cores retornada com sucesso',
    type: [AppColorDto],
  })
  getColors(): AppColorDto[] {
    return this.colorsService.listColors();
  }

  @Get(':name')
  @ApiOperation({
    summary: 'Buscar cor por nome',
    description: 'Retorna uma cor específica pelo nome.',
  })
  @ApiParam({
    name: 'name',
    description: 'Nome da cor (ex: Red, Blue, Green)',
    example: 'Red',
  })
  @ApiResponse({
    status: 200,
    description: 'Cor encontrada com sucesso',
    type: AppColorDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Cor não encontrada',
  })
  getColorByName(@Param('name') name: string): AppColorDto {
    const color = this.colorsService.getColorByName(name);

    if (!color) {
      throw new NotFoundException(`Cor '${name}' não encontrada`);
    }

    return color;
  }
}
