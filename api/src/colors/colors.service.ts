import { Injectable } from '@nestjs/common';
import { AppColor } from '../common/enums/app-color.enum';
import { AppColorDto } from '../common/dto/app-color.dto';

@Injectable()
export class ColorsService {
  listColors(): AppColorDto[] {
    return Object.entries(AppColor).map(([name, hex]) => ({
      name,
      hex,
    }));
  }

  getColorByName(colorName: string): AppColorDto | null {
    const hex = AppColor[colorName as keyof typeof AppColor];
    if (!hex) {
      return null;
    }

    return {
      name: colorName,
      hex,
    };
  }

  colorExists(colorName: string): boolean {
    return colorName in AppColor;
  }
}
