import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PatientsModule } from './patients/patients.module';
import { SchedulingsModule } from './schedulings/schedulings.module';
import { CommonModule } from './common/common.module';
import { ReportsModule } from './reports/reports.module';
import { MedicalRecordsModule } from './medical-records/medical-records.module';
import { DentistsModule } from './dentists/dentists.module';
import { EmployeesModule } from './employees/employees.module';
import { TasksModule } from './tasks/tasks.module';
import { PatientTypesModule } from './patient-types/patient-types.module';
import { NotificationsModule } from './notifications/notifications.module';
import { ProceduresModule } from './procedures/procedures.module';
import { BudgetsModule } from './budgets/budgets.module';
import { TreatmentPlansModule } from './treatment-plans/treatment-plans.module';
import { ReceiptsModule } from './receipts/receipts.module';
import { IndicationsModule } from './indications/indications.module';
import { AnamnesisModule } from './anamnesis/anamnesis.module';
import { PhotosModule } from './photos/photos.module';
import { ExamsModule } from './exams/exams.module';
import { DocumentsModule } from './documents/documents.module';
import { LeadsModule } from './leads/leads.module';
import { TreatmentFlowModule } from './treatment-flow/treatment-flow.module';
import { SuggestionsModule } from './suggestions/suggestions.module';
import { AppointmentObservationsModule } from './appointment-observations/appointment-observations.module';
import { DentistSchedulesModule } from './dentist-schedules/dentist-schedules.module';
import { HolidaysModule } from './holidays/holidays.module';
import { AgentsModule } from './agents/agents.module';
import { ColorsModule } from './colors/colors.module';
import { AppointmentCategoriesModule } from './appointment-categories/appointment-categories.module';
import { TypeOrmConfigService } from './database/typeorm-config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
    }),
    CommonModule,
    PatientsModule,
    SchedulingsModule,
    ReportsModule,
    MedicalRecordsModule,
    DentistsModule,
    EmployeesModule,
    TasksModule,
    PatientTypesModule,
    NotificationsModule,
    ProceduresModule,
    BudgetsModule,
    TreatmentPlansModule,
    ReceiptsModule,
    IndicationsModule,
    AnamnesisModule,
    PhotosModule,
    ExamsModule,
    DocumentsModule,
    LeadsModule,
    TreatmentFlowModule,
    SuggestionsModule,
    AppointmentObservationsModule,
    DentistSchedulesModule,
    HolidaysModule,
    AgentsModule,
    ColorsModule,
    AppointmentCategoriesModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: 'APP_INITIALIZER',
      useFactory: (): Record<string, unknown> => {
        console.log('Aplicação reiniciada para executar migrações');
        return {};
      },
    },
  ],
})
export class AppModule {}
