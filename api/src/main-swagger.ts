import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { SwaggerTagsConfig } from './swagger/tags.config';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule);

  // Configuração do CORS
  app.enableCors({
    origin: true, // Permite todas as origens em ambiente de desenvolvimento
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true,
  });

  // Configuração da validação global
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Remove propriedades não decoradas com validadores
      forbidNonWhitelisted: true, // Lança erro se propriedades não decoradas forem enviadas
      transform: true, // Transforma automaticamente os tipos
    }),
  );

  // Prefixo global para todas as rotas da API
  app.setGlobalPrefix('api');

  // Configuração do Swagger
  const config = new DocumentBuilder()
    .setTitle('CRM Odontológico API')
    .setDescription('API para o sistema CRM Odontológico')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  // Criar documento e configurar tags
  let document = SwaggerModule.createDocument(app, config);
  document = SwaggerTagsConfig.configureTags(document);

  // Configurar Swagger UI
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      docExpansion: 'none', // Inicia com todos os accordions fechados
      defaultModelsExpandDepth: -1, // Oculta a seção de modelos por padrão
      filter: true, // Adiciona campo de busca
      tagsSorter: 'alpha', // Ordena as tags em ordem alfabética
      operationsSorter: 'alpha', // Ordena as operações em ordem alfabética
    },
  });

  await app.listen(3000);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(
    `Swagger documentation available at: ${await app.getUrl()}/api/docs`,
  );
}
void bootstrap();
