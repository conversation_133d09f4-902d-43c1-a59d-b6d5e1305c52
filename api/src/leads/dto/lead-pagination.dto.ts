import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, IsString, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class LeadPaginationDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> da página (começando em 1)',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    default: 6,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 6;

  @ApiProperty({
    description: 'Termo de busca para filtrar resultados',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filtrar por leads que são pacientes existentes',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: unknown }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isExistingPatient?: boolean;

  @ApiProperty({
    description: 'Filtrar por leads que têm atualizações disponíveis',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: unknown }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  hasUpdatesAvailable?: boolean;

  @ApiProperty({
    description: 'Data de criação para filtrar resultados (formato YYYY-MM-DD)',
    required: false,
  })
  @IsOptional()
  @IsString()
  createdAt?: string;
}
