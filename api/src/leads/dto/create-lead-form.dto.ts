import { ApiProperty } from '@nestjs/swagger';
import {
  IsA<PERSON>y,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { LastProcedureTime } from '../entities/lead-form.entity';

export class CreateLeadFormDto {
  // <PERSON><PERSON> pessoais
  @ApiProperty({
    description: 'Nome completo do lead',
    example: '<PERSON>',
  })
  @IsNotEmpty({ message: 'O nome completo é obrigatório' })
  @IsString({ message: 'O nome completo deve ser uma string' })
  fullName: string;

  @ApiProperty({
    description: 'CPF do lead',
    example: '123.456.789-00',
  })
  @IsNotEmpty({ message: 'O CPF é obrigatório' })
  @IsString({ message: 'O CPF deve ser uma string' })
  cpf: string;

  @ApiProperty({
    description: 'Telefone do lead',
    example: '(11) 98765-4321',
  })
  @IsNotEmpty({ message: 'O telefone é obrigatório' })
  @IsString({ message: 'O telefone deve ser uma string' })
  phone: string;

  @ApiProperty({
    description: 'Email do lead',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'O email é obrigatório' })
  @IsEmail({}, { message: 'O email deve ser válido' })
  email: string;

  // Endereço
  @ApiProperty({
    description: 'CEP do lead',
    example: '01234-567',
  })
  @IsNotEmpty({ message: 'O CEP é obrigatório' })
  @IsString({ message: 'O CEP deve ser uma string' })
  cep: string;

  @ApiProperty({
    description: 'Rua do lead',
    example: 'Rua das Flores',
  })
  @IsNotEmpty({ message: 'A rua é obrigatória' })
  @IsString({ message: 'A rua deve ser uma string' })
  street: string;

  @ApiProperty({
    description: 'Número do endereço do lead',
    example: '123',
  })
  @IsNotEmpty({ message: 'O número é obrigatório' })
  @IsString({ message: 'O número deve ser uma string' })
  number: string;

  @ApiProperty({
    description: 'Bairro do lead',
    example: 'Centro',
  })
  @IsNotEmpty({ message: 'O bairro é obrigatório' })
  @IsString({ message: 'O bairro deve ser uma string' })
  neighborhood: string;

  @ApiProperty({
    description: 'Cidade do lead',
    example: 'São Paulo',
  })
  @IsNotEmpty({ message: 'A cidade é obrigatória' })
  @IsString({ message: 'A cidade deve ser uma string' })
  city: string;

  @ApiProperty({
    description: 'Estado do lead',
    example: 'SP',
  })
  @IsNotEmpty({ message: 'O estado é obrigatório' })
  @IsString({ message: 'O estado deve ser uma string' })
  state: string;

  // Histórico odontológico
  @ApiProperty({
    description: 'Tratamentos anteriores do lead',
    example: ['Limpeza', 'Canal', 'Extração'],
  })
  @IsArray({ message: 'Os tratamentos anteriores devem ser um array' })
  @IsString({ each: true, message: 'Cada tratamento deve ser uma string' })
  pastTreatments: string[];

  @ApiProperty({
    description: 'Tempo desde o último procedimento',
    enum: LastProcedureTime,
    example: LastProcedureTime.ONE_YEAR,
  })
  @IsEnum(LastProcedureTime, {
    message: 'O tempo desde o último procedimento deve ser válido',
  })
  lastProcedureTime: LastProcedureTime;

  // Interesse atual
  @ApiProperty({
    description: 'Tratamentos de interesse',
    example: ['Implante', 'Limpeza'],
  })
  @IsArray({ message: 'Os tratamentos de interesse devem ser um array' })
  @IsString({ each: true, message: 'Cada tratamento deve ser uma string' })
  interestedTreatment: string[];

  @ApiProperty({
    description: 'Se deseja avaliação gratuita',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'O campo wantsFreeEvaluation deve ser um booleano' })
  wantsFreeEvaluation?: boolean;

  @ApiProperty({
    description: 'Se deseja receber promoções',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'O campo wantsPromotions deve ser um booleano' })
  wantsPromotions?: boolean;

  // Personalização
  @ApiProperty({
    description: 'Melhor horário para contato',
    example: 'Manhã',
  })
  @IsNotEmpty({ message: 'O melhor horário para contato é obrigatório' })
  @IsString({ message: 'O melhor horário para contato deve ser uma string' })
  bestContactTime: string;

  @ApiProperty({
    description: 'Como conheceu a clínica',
    example: 'Indicação de amigo',
  })
  @IsNotEmpty({ message: 'A fonte de referência é obrigatória' })
  @IsString({ message: 'A fonte de referência deve ser uma string' })
  referralSource: string;
}
