import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class SkipPatientFieldDto {
  @ApiProperty({
    description: 'ID do formulário de lead',
    example: 'uuid-do-lead-form',
  })
  @IsNotEmpty({ message: 'O ID do formulário de lead é obrigatório' })
  @IsUUID('4', {
    message: 'O ID do formulário de lead deve ser um UUID válido',
  })
  leadFormId: string;

  @ApiProperty({
    description: 'Nome do campo a ser ignorado',
    example: 'email',
  })
  @IsNotEmpty({ message: 'O nome do campo é obrigatório' })
  @IsString({ message: 'O nome do campo deve ser uma string' })
  fieldName: string;
}
