import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class ComparePatientFieldsDto {
  @ApiProperty({
    description: 'ID do formulário de lead',
    example: 'uuid-do-lead-form',
  })
  @IsNotEmpty({ message: 'O ID do formulário de lead é obrigatório' })
  @IsUUID('4', {
    message: 'O ID do formulário de lead deve ser um UUID válido',
  })
  leadFormId: string;
}
