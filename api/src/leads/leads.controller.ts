import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUI<PERSON>ipe,
  Lo<PERSON>,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { LeadsService } from './leads.service';
import { CreateLeadFormDto } from './dto/create-lead-form.dto';
import { UpdateLeadFormDto } from './dto/update-lead-form.dto';
import { UpdatePatientFieldDto } from './dto/update-patient-field.dto';
import { SkipPatientFieldDto } from './dto/skip-patient-field.dto';
import { ComparePatientFieldsDto } from './dto/compare-patient-fields.dto';
import { LeadPaginationDto } from './dto/lead-pagination.dto';
import { LeadForm } from './entities/lead-form.entity';
import {
  PaginatedResponse,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';

@ApiTags('leads')
@Controller('leads')
export class LeadsController {
  private readonly logger = new Logger(LeadsController.name);

  constructor(private readonly leadsService: LeadsService) {}

  @ApiOperation({ summary: 'Criar um novo formulário de lead' })
  @ApiResponse({
    status: 201,
    description: 'Formulário de lead criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateLeadFormDto })
  @Post()
  create(@Body() createLeadFormDto: CreateLeadFormDto): Promise<LeadForm> {
    return this.leadsService.create(createLeadFormDto);
  }

  @ApiOperation({ summary: 'Listar todos os formulários de lead' })
  @ApiResponse({
    status: 200,
    description: 'Lista de formulários de lead retornada com sucesso',
  })
  @Get()
  findAll(): Promise<LeadForm[]> {
    return this.leadsService.findAll();
  }

  @ApiOperation({
    summary: 'Listar formulários de lead com paginação e filtros',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista paginada de formulários de lead retornada com sucesso',
    type: PaginatedResponseDto,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Número da página (começando em 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Quantidade de itens por página',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Termo de busca para filtrar resultados',
  })
  @ApiQuery({
    name: 'isExistingPatient',
    required: false,
    type: Boolean,
    description: 'Filtrar por leads que são pacientes existentes',
  })
  @ApiQuery({
    name: 'hasUpdatesAvailable',
    required: false,
    type: Boolean,
    description: 'Filtrar por leads que têm atualizações disponíveis',
  })
  @ApiQuery({
    name: 'createdAt',
    required: false,
    type: String,
    description: 'Data de criação para filtrar resultados (formato YYYY-MM-DD)',
  })
  @Get('paginated')
  findAllPaginated(
    @Query() paginationDto: LeadPaginationDto,
  ): Promise<PaginatedResponse<LeadForm>> {
    return this.leadsService.findAllPaginated(paginationDto);
  }

  @ApiOperation({ summary: 'Buscar um formulário de lead pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Formulário de lead encontrado com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiParam({ name: 'id', description: 'ID do formulário de lead' })
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LeadForm> {
    return this.leadsService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar um formulário de lead' })
  @ApiResponse({
    status: 200,
    description: 'Formulário de lead atualizado com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do formulário de lead' })
  @ApiBody({ type: UpdateLeadFormDto })
  @Patch(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLeadFormDto: UpdateLeadFormDto,
  ): Promise<LeadForm> {
    return this.leadsService.update(id, updateLeadFormDto);
  }

  @ApiOperation({ summary: 'Remover um formulário de lead' })
  @ApiResponse({
    status: 200,
    description: 'Formulário de lead removido com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiParam({ name: 'id', description: 'ID do formulário de lead' })
  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.leadsService.remove(id);
  }

  @ApiOperation({ summary: 'Atualizar um campo do paciente com dados do lead' })
  @ApiResponse({
    status: 200,
    description: 'Campo do paciente atualizado com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: UpdatePatientFieldDto })
  @Post('update-patient-field')
  updatePatientField(
    @Body() updatePatientFieldDto: UpdatePatientFieldDto,
  ): Promise<LeadForm> {
    this.logger.log(
      `Updating patient field: ${JSON.stringify(updatePatientFieldDto)}`,
    );
    return this.leadsService.updatePatientField(updatePatientFieldDto);
  }

  @ApiOperation({
    summary: 'Comparar campos do lead com os campos do paciente',
  })
  @ApiResponse({ status: 200, description: 'Comparação realizada com sucesso' })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: ComparePatientFieldsDto })
  @Post('compare-patient-fields')
  comparePatientFields(
    @Body() comparePatientFieldsDto: ComparePatientFieldsDto,
  ): Promise<LeadForm> {
    this.logger.log(
      `Comparing patient fields: ${JSON.stringify(comparePatientFieldsDto)}`,
    );
    return this.leadsService.comparePatientFields(
      comparePatientFieldsDto.leadFormId,
    );
  }

  @ApiOperation({ summary: 'Ignorar um campo do paciente para não atualizar' })
  @ApiResponse({ status: 200, description: 'Campo ignorado com sucesso' })
  @ApiResponse({
    status: 404,
    description: 'Formulário de lead não encontrado',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: SkipPatientFieldDto })
  @Post('skip-patient-field')
  skipPatientField(
    @Body() skipPatientFieldDto: SkipPatientFieldDto,
  ): Promise<LeadForm> {
    this.logger.log(
      `Skipping patient field: ${JSON.stringify(skipPatientFieldDto)}`,
    );
    return this.leadsService.skipPatientField(skipPatientFieldDto);
  }
}
