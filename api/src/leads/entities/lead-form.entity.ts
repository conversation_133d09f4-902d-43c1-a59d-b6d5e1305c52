import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';

export enum LastProcedureTime {
  LESS_THAN_6_MONTHS = '<6meses',
  ONE_YEAR = '1ano',
  MORE_THAN_2_YEARS = '>2anos',
}

@Entity('lead_forms')
export class LeadForm {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único do formulário de lead' })
  id: string;

  // Dados pessoais
  @Column()
  @ApiProperty({ description: 'Nome completo do lead' })
  fullName: string;

  @Column()
  @ApiProperty({ description: 'CPF do lead' })
  cpf: string;

  @Column()
  @ApiProperty({ description: 'Telefone do lead' })
  phone: string;

  @Column()
  @ApiProperty({ description: 'Email do lead' })
  email: string;

  // Endereço
  @Column()
  @ApiProperty({ description: 'CEP do lead' })
  cep: string;

  @Column()
  @ApiProperty({ description: 'Rua do lead' })
  street: string;

  @Column()
  @ApiProperty({ description: 'Número do endereço do lead' })
  number: string;

  @Column()
  @ApiProperty({ description: 'Bairro do lead' })
  neighborhood: string;

  @Column()
  @ApiProperty({ description: 'Cidade do lead' })
  city: string;

  @Column()
  @ApiProperty({ description: 'Estado do lead' })
  state: string;

  // Histórico odontológico
  @Column('simple-array')
  @ApiProperty({ description: 'Tratamentos anteriores do lead' })
  pastTreatments: string[];

  @Column({
    type: 'enum',
    enum: LastProcedureTime,
  })
  @ApiProperty({ description: 'Tempo desde o último procedimento' })
  lastProcedureTime: LastProcedureTime;

  // Interesse atual
  @Column('simple-array')
  @ApiProperty({ description: 'Tratamentos de interesse' })
  interestedTreatment: string[];

  @Column({ nullable: true, default: true })
  @ApiProperty({ description: 'Se deseja avaliação gratuita' })
  wantsFreeEvaluation: boolean;

  @Column({ nullable: true, default: true })
  @ApiProperty({ description: 'Se deseja receber promoções' })
  wantsPromotions: boolean;

  // Personalização
  @Column()
  @ApiProperty({ description: 'Melhor horário para contato' })
  bestContactTime: string;

  @Column()
  @ApiProperty({ description: 'Como conheceu a clínica' })
  referralSource: string;

  // Campos de sistema
  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;

  // Campos de relacionamento com paciente existente
  @Column({ default: false })
  @ApiProperty({ description: 'Se é um paciente existente' })
  isExistingPatient: boolean;

  @ManyToOne(() => Patient, { nullable: true })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente relacionado ao lead', required: false })
  patient: Patient;

  @Column({ type: 'int', nullable: true })
  patientId: number | null;

  // Campos para atualização de cadastro
  @Column({ default: false })
  @ApiProperty({
    description: 'Se há atualizações disponíveis para o cadastro do paciente',
  })
  hasUpdatesAvailable: boolean;

  @Column('simple-array', { nullable: true })
  @ApiProperty({
    description: 'Campos com diferenças para atualizar no cadastro',
    required: false,
  })
  fieldsToUpdate: string[];
}
