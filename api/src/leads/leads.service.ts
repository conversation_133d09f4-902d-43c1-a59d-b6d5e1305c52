import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeadForm } from './entities/lead-form.entity';
import { CreateLeadFormDto } from './dto/create-lead-form.dto';
import { UpdateLeadFormDto } from './dto/update-lead-form.dto';
import { UpdatePatientFieldDto } from './dto/update-patient-field.dto';
import { SkipPatientFieldDto } from './dto/skip-patient-field.dto';
import { UpdatePatientDto } from '../patients/dto/update-patient.dto';
import { LeadPaginationDto } from './dto/lead-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { PatientsService } from '../patients/patients.service';
import { Patient } from '../patients/entities/patient.entity';

@Injectable()
export class LeadsService {
  private readonly logger = new Logger(LeadsService.name);

  constructor(
    @InjectRepository(LeadForm)
    private leadFormRepository: Repository<LeadForm>,
    private patientsService: PatientsService,
  ) {}

  async create(createLeadFormDto: CreateLeadFormDto): Promise<LeadForm> {
    try {
      this.logger.log(
        `Creating lead form: ${JSON.stringify(createLeadFormDto)}`,
      );

      // Normalizar CPF para comparação (remover caracteres não numéricos)
      const normalizedCpf = createLeadFormDto.cpf.replace(/\D/g, '');

      // Verificar se o CPF já existe na tabela de pacientes
      let existingPatient: Patient | null = null;
      try {
        // Buscar paciente pelo CPF normalizado
        const patients = await this.patientsService.findByCpf(normalizedCpf);
        if (patients && patients.length > 0) {
          existingPatient = patients[0];
          this.logger.log(
            `Found existing patient with ID: ${existingPatient.id}`,
          );
        } else {
          this.logger.log(
            `No existing patient found with CPF: ${normalizedCpf}`,
          );
        }
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        const errorStack = error instanceof Error ? error.stack : undefined;
        this.logger.error(
          `Error finding patient by CPF: ${errorMessage}`,
          errorStack,
        );
        // Continuar mesmo se não encontrar o paciente
      }

      // Definir valores padrão para campos booleanos quando forem nulos
      const wantsFreeEvaluation =
        createLeadFormDto.wantsFreeEvaluation === undefined
          ? true
          : createLeadFormDto.wantsFreeEvaluation;
      const wantsPromotions =
        createLeadFormDto.wantsPromotions === undefined
          ? true
          : createLeadFormDto.wantsPromotions;

      // Criar o lead form
      const leadForm = this.leadFormRepository.create({
        ...createLeadFormDto,
        cpf: normalizedCpf, // Salvar CPF normalizado
        // Definir valores para campos booleanos
        wantsFreeEvaluation,
        wantsPromotions,
        // Inicializar valores padrão
        isExistingPatient: false,
        patientId: null,
        hasUpdatesAvailable: false,
        fieldsToUpdate: [],
      });

      // Se encontrou um paciente existente
      if (existingPatient) {
        // Marcar como paciente existente
        leadForm.isExistingPatient = true;
        leadForm.patientId = existingPatient.id;

        // Fazer comparação de campos imediatamente
        this.logger.log(
          'Comparing fields for patient with ID: ' + existingPatient.id,
        );

        // Comparar campos para identificar diferenças
        const fieldsToUpdate = this.compareFields(existingPatient, leadForm);

        // Se houver campos para atualizar
        if (fieldsToUpdate.length > 0) {
          this.logger.log(`Fields to update: ${fieldsToUpdate.join(', ')}`);
          leadForm.hasUpdatesAvailable = true;
          leadForm.fieldsToUpdate = fieldsToUpdate;
        } else {
          this.logger.log('No fields to update');
          leadForm.hasUpdatesAvailable = false;
          leadForm.fieldsToUpdate = [];
        }
      } else {
        // Não é um paciente existente
        this.logger.log('Not an existing patient');
        leadForm.isExistingPatient = false;
        leadForm.patientId = null;
        leadForm.hasUpdatesAvailable = false;
        leadForm.fieldsToUpdate = [];
      }

      // Salvar o lead form
      const savedLeadForm = await this.leadFormRepository.save(leadForm);
      this.logger.log(`Lead form saved with ID: ${savedLeadForm.id}`);
      return savedLeadForm;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating lead form: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao criar formulário de lead',
      );
    }
  }

  async findAll(): Promise<LeadForm[]> {
    try {
      this.logger.log('Finding all lead forms');
      return await this.leadFormRepository.find({
        relations: ['patient'],
        order: { createdAt: 'DESC' },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding all lead forms: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar formulários de lead',
      );
    }
  }

  async findAllPaginated(
    paginationDto: LeadPaginationDto,
  ): Promise<PaginatedResponse<LeadForm>> {
    try {
      const {
        page = 1,
        limit = 6,
        search,
        isExistingPatient,
        hasUpdatesAvailable,
        createdAt,
      } = paginationDto;
      this.logger.log(
        `Finding leads with pagination: page ${page}, limit ${limit}, search: ${search}, isExistingPatient: ${isExistingPatient}, hasUpdatesAvailable: ${hasUpdatesAvailable}, createdAt: ${createdAt}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder = this.leadFormRepository
        .createQueryBuilder('lead')
        .leftJoinAndSelect('lead.patient', 'patient');

      // Aplicar filtro de busca
      if (search) {
        queryBuilder.where(
          '(lead.fullName LIKE :search OR lead.email LIKE :search OR lead.cpf LIKE :search OR lead.phone LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Aplicar filtro de paciente existente
      if (isExistingPatient !== undefined) {
        if (search) {
          queryBuilder.andWhere('lead.isExistingPatient = :isExistingPatient', {
            isExistingPatient,
          });
        } else {
          queryBuilder.where('lead.isExistingPatient = :isExistingPatient', {
            isExistingPatient,
          });
        }
      }

      // Aplicar filtro de atualizações disponíveis
      if (hasUpdatesAvailable !== undefined) {
        const whereClause =
          search || isExistingPatient !== undefined ? 'andWhere' : 'where';
        queryBuilder[whereClause](
          'lead.hasUpdatesAvailable = :hasUpdatesAvailable',
          { hasUpdatesAvailable },
        );
      }

      // Aplicar filtro de data de criação
      if (createdAt) {
        const startDate = new Date(createdAt);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(createdAt);
        endDate.setHours(23, 59, 59, 999);

        const whereClause =
          search ||
          isExistingPatient !== undefined ||
          hasUpdatesAvailable !== undefined
            ? 'andWhere'
            : 'where';
        queryBuilder[whereClause](
          'lead.createdAt BETWEEN :startDate AND :endDate',
          {
            startDate,
            endDate,
          },
        );
      }

      // Ordenação
      queryBuilder.orderBy('lead.createdAt', 'DESC');

      // Aplicar paginação
      queryBuilder.skip(skip).take(limit);

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding leads with pagination: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar leads com paginação',
      );
    }
  }

  async findOne(id: string): Promise<LeadForm> {
    try {
      this.logger.log(`Finding lead form with id: ${id}`);
      const leadForm = await this.leadFormRepository.findOne({
        where: { id },
        relations: ['patient'],
      });
      if (!leadForm) {
        throw new NotFoundException(
          `Formulário de lead com ID ${id} não encontrado`,
        );
      }

      return leadForm;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding lead form with id ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar formulário de lead',
      );
    }
  }

  /**
   * Compara os campos do lead com os campos do paciente e atualiza a lista de campos para atualizar
   * @param leadFormId ID do formulário de lead
   * @returns O formulário de lead atualizado
   */
  async comparePatientFields(leadFormId: string): Promise<LeadForm> {
    try {
      this.logger.log(
        `Comparing patient fields for lead form with id: ${leadFormId}`,
      );
      const leadForm = await this.findOne(leadFormId);

      if (!leadForm.isExistingPatient || !leadForm.patientId) {
        this.logger.log(
          `Lead form ${leadFormId} is not associated with an existing patient`,
        );
        return leadForm;
      }

      await this._comparePatientFields(leadForm);
      return await this.findOne(leadFormId);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error comparing patient fields: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao comparar campos do paciente',
      );
    }
  }

  /**
   * Método para comparar os campos do lead com os campos do paciente
   * @param patient Paciente a ser comparado
   * @param leadForm Formulário de lead a ser comparado
   * @returns Lista de campos para atualizar
   */
  private compareFields(patient: Patient, leadForm: LeadForm): string[] {
    const fieldsToUpdate: string[] = [];

    // Normalizar e comparar nome
    const normalizedPatientName = patient.name?.toLowerCase().trim() || '';
    const normalizedLeadName = leadForm.fullName.toLowerCase().trim();
    if (
      normalizedPatientName !== normalizedLeadName &&
      normalizedLeadName !== ''
    ) {
      this.logger.log(
        `Name differs - Patient: "${normalizedPatientName}", Lead: "${normalizedLeadName}"`,
      );
      fieldsToUpdate.push('name');
    }

    // Normalizar e comparar email
    const normalizedPatientEmail = patient.email?.toLowerCase().trim() || '';
    const normalizedLeadEmail = leadForm.email.toLowerCase().trim();
    if (
      normalizedPatientEmail !== normalizedLeadEmail &&
      normalizedLeadEmail !== ''
    ) {
      this.logger.log(
        `Email differs - Patient: "${normalizedPatientEmail}", Lead: "${normalizedLeadEmail}"`,
      );
      fieldsToUpdate.push('email');
    }

    // Normalizar e comparar telefone
    const normalizedPatientPhone = patient.phone?.replace(/\D/g, '') || '';
    const normalizedLeadPhone = leadForm.phone.replace(/\D/g, '');
    if (
      normalizedPatientPhone !== normalizedLeadPhone &&
      normalizedLeadPhone !== ''
    ) {
      this.logger.log(
        `Phone differs - Patient: "${normalizedPatientPhone}", Lead: "${normalizedLeadPhone}"`,
      );
      fieldsToUpdate.push('phone');
    }

    // Verificar se o WhatsApp do paciente é igual ao telefone do lead
    const normalizedPatientWhatsapp =
      patient.whatsapp?.replace(/\D/g, '') || '';
    if (
      normalizedPatientWhatsapp !== normalizedLeadPhone &&
      normalizedLeadPhone !== ''
    ) {
      this.logger.log(
        `Whatsapp differs - Patient: "${normalizedPatientWhatsapp}", Lead: "${normalizedLeadPhone}"`,
      );
      fieldsToUpdate.push('whatsapp');
    }

    // Verificar se há diferenças no endereço
    const addressDiffers =
      (patient.addressZipCode?.replace(/\D/g, '') || '') !==
        leadForm.cep.replace(/\D/g, '') ||
      (patient.addressStreet?.toLowerCase().trim() || '') !==
        leadForm.street.toLowerCase().trim() ||
      (patient.addressNumber?.trim() || '') !== leadForm.number.trim() ||
      (patient.addressNeighborhood?.toLowerCase().trim() || '') !==
        leadForm.neighborhood.toLowerCase().trim() ||
      (patient.addressCity?.toLowerCase().trim() || '') !==
        leadForm.city.toLowerCase().trim() ||
      (patient.addressState?.toUpperCase().trim() || '') !==
        leadForm.state.toUpperCase().trim();

    if (addressDiffers) {
      this.logger.log('Address differs between patient and lead');
      fieldsToUpdate.push('address');
    }

    return fieldsToUpdate;
  }

  /**
   * Método interno para comparar os campos do lead com os campos do paciente e atualizar a lista de campos para atualizar
   */
  private async _comparePatientFields(leadForm: LeadForm): Promise<void> {
    try {
      if (!leadForm.isExistingPatient || !leadForm.patientId) {
        return;
      }

      // Buscar o paciente
      const patient = await this.patientsService.findOne(leadForm.patientId);
      this.logger.log(`Comparing fields for patient with ID: ${patient.id}`);

      // Comparar campos para identificar diferenças
      const fieldsToUpdate = this.compareFields(patient, leadForm);

      // Atualizar o lead form com os campos para atualizar
      if (fieldsToUpdate.length > 0) {
        this.logger.log(`Fields to update: ${fieldsToUpdate.join(', ')}`);
        leadForm.hasUpdatesAvailable = true;
        leadForm.fieldsToUpdate = fieldsToUpdate;

        // Salvar o lead form atualizado
        await this.leadFormRepository.save(leadForm);
        this.logger.log(
          `Lead form updated with fields to update: ${fieldsToUpdate.join(', ')}`,
        );
      } else if (leadForm.hasUpdatesAvailable) {
        // Se não houver campos para atualizar, mas o lead form estiver marcado como tendo atualizações disponíveis
        this.logger.log(
          'No fields to update, but lead form has updates available. Updating lead form.',
        );
        leadForm.hasUpdatesAvailable = false;
        leadForm.fieldsToUpdate = [];

        // Salvar o lead form atualizado
        await this.leadFormRepository.save(leadForm);
        this.logger.log('Lead form updated with no fields to update');
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error comparing patient fields: ${errorMessage}`,
        errorStack,
      );
      // Não lançar exceção para não interromper o fluxo
    }
  }

  async update(
    id: string,
    updateLeadFormDto: UpdateLeadFormDto,
  ): Promise<LeadForm> {
    try {
      this.logger.log(
        `Updating lead form with id ${id}: ${JSON.stringify(
          updateLeadFormDto,
        )}`,
      );
      const leadForm = await this.findOne(id);
      const updatedLeadForm = Object.assign(leadForm, updateLeadFormDto);
      return await this.leadFormRepository.save(updatedLeadForm);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating lead form with id ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao atualizar formulário de lead',
      );
    }
  }

  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Removing lead form with id: ${id}`);
      const leadForm = await this.findOne(id);
      await this.leadFormRepository.remove(leadForm);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error removing lead form with id ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao remover formulário de lead',
      );
    }
  }

  async updatePatientField(
    updatePatientFieldDto: UpdatePatientFieldDto,
  ): Promise<LeadForm> {
    try {
      this.logger.log(
        `Updating patient field: ${JSON.stringify(updatePatientFieldDto)}`,
      );
      const { leadFormId, fieldName } = updatePatientFieldDto;

      // Buscar o lead form com relação ao paciente
      const leadForm = await this.findOne(leadFormId);

      // Verificar se o lead form está associado a um paciente
      if (!leadForm.isExistingPatient || !leadForm.patientId) {
        throw new BadRequestException(
          'Este formulário de lead não está associado a um paciente existente',
        );
      }

      // Verificar se o campo está na lista de campos para atualizar
      if (
        !leadForm.hasUpdatesAvailable ||
        !leadForm.fieldsToUpdate ||
        !leadForm.fieldsToUpdate.includes(fieldName)
      ) {
        throw new BadRequestException(
          `O campo ${fieldName} não está na lista de campos para atualizar`,
        );
      }

      // Buscar o paciente
      const patient = await this.patientsService.findOne(leadForm.patientId);
      this.logger.log(`Found patient with ID: ${patient.id}`);

      // Atualizar o campo do paciente com base no valor do lead form
      const patientUpdateFields: {
        name?: string;
        email?: string;
        phone?: string;
        whatsapp?: string;
        addressZipCode?: string;
        addressStreet?: string;
        addressNumber?: string;
        addressNeighborhood?: string;
        addressCity?: string;
        addressState?: string;
      } = {};

      switch (fieldName) {
        case 'name':
          patientUpdateFields.name = leadForm.fullName;
          this.logger.log(`Updating patient name to: ${leadForm.fullName}`);
          break;
        case 'email':
          patientUpdateFields.email = leadForm.email;
          this.logger.log(`Updating patient email to: ${leadForm.email}`);
          break;
        case 'phone':
          patientUpdateFields.phone = leadForm.phone;
          this.logger.log(`Updating patient phone to: ${leadForm.phone}`);
          break;
        case 'whatsapp':
          patientUpdateFields.whatsapp = leadForm.phone;
          this.logger.log(`Updating patient whatsapp to: ${leadForm.phone}`);
          break;
        case 'address':
          patientUpdateFields.addressZipCode = leadForm.cep;
          patientUpdateFields.addressStreet = leadForm.street;
          patientUpdateFields.addressNumber = leadForm.number;
          patientUpdateFields.addressNeighborhood = leadForm.neighborhood;
          patientUpdateFields.addressCity = leadForm.city;
          patientUpdateFields.addressState = leadForm.state;
          this.logger.log(
            `Updating patient address to: ${leadForm.street}, ${leadForm.number} - ${leadForm.neighborhood}, ${leadForm.city}/${leadForm.state}`,
          );
          break;
        default:
          throw new BadRequestException(`Campo ${fieldName} não reconhecido`);
      }

      // Atualizar o paciente
      const updateData: UpdatePatientDto = {};

      // Copiar apenas as propriedades definidas de forma type-safe

      Object.assign(updateData, patientUpdateFields);

      await this.patientsService.update(patient.id, updateData);
      this.logger.log(`Patient ${patient.id} updated successfully`);

      // Remover o campo da lista de campos para atualizar
      leadForm.fieldsToUpdate = leadForm.fieldsToUpdate.filter(
        (field) => field !== fieldName,
      );
      this.logger.log(
        `Removed ${fieldName} from fieldsToUpdate. Remaining fields: ${leadForm.fieldsToUpdate.join(', ') || 'none'}`,
      );

      // Se não houver mais campos para atualizar, marcar como não tendo atualizações disponíveis
      if (leadForm.fieldsToUpdate.length === 0) {
        leadForm.hasUpdatesAvailable = false;
        this.logger.log(
          'No more fields to update, setting hasUpdatesAvailable to false',
        );
      }

      // Salvar o lead form atualizado
      await this.leadFormRepository.save(leadForm);
      this.logger.log(`Lead form ${leadFormId} updated successfully`);

      // Buscar o lead form atualizado com a relação do paciente
      const refreshedLeadForm = await this.findOne(leadFormId);
      return refreshedLeadForm;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating patient field: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao atualizar campo do paciente',
      );
    }
  }

  async skipPatientField(
    skipPatientFieldDto: SkipPatientFieldDto,
  ): Promise<LeadForm> {
    try {
      this.logger.log(
        `Skipping patient field: ${JSON.stringify(skipPatientFieldDto)}`,
      );
      const { leadFormId, fieldName } = skipPatientFieldDto;

      // Buscar o lead form com relação ao paciente
      const leadForm = await this.findOne(leadFormId);

      // Verificar se o lead form está associado a um paciente
      if (!leadForm.isExistingPatient || !leadForm.patientId) {
        throw new BadRequestException(
          'Este formulário de lead não está associado a um paciente existente',
        );
      }

      // Verificar se o campo está na lista de campos para atualizar
      if (
        !leadForm.hasUpdatesAvailable ||
        !leadForm.fieldsToUpdate ||
        !leadForm.fieldsToUpdate.includes(fieldName)
      ) {
        throw new BadRequestException(
          `O campo ${fieldName} não está na lista de campos para atualizar`,
        );
      }

      this.logger.log(
        `Skipping field ${fieldName} for lead form ${leadFormId}`,
      );

      // Remover o campo da lista de campos para atualizar
      leadForm.fieldsToUpdate = leadForm.fieldsToUpdate.filter(
        (field) => field !== fieldName,
      );
      this.logger.log(
        `Removed ${fieldName} from fieldsToUpdate. Remaining fields: ${leadForm.fieldsToUpdate.join(', ') || 'none'}`,
      );

      // Verificar se ainda há campos para atualizar
      if (leadForm.fieldsToUpdate.length === 0) {
        leadForm.hasUpdatesAvailable = false;
        this.logger.log(
          'No more fields to update, setting hasUpdatesAvailable to false',
        );
      }

      // Salvar o lead form atualizado
      await this.leadFormRepository.save(leadForm);
      this.logger.log(`Lead form ${leadFormId} updated successfully`);

      // Buscar o lead form atualizado com a relação do paciente
      const refreshedLeadForm = await this.findOne(leadFormId);
      return refreshedLeadForm;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error skipping patient field: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao ignorar campo do paciente',
      );
    }
  }
}
