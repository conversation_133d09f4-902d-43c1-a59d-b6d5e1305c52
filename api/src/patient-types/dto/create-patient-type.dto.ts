import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsBoolean,
  Matches,
} from 'class-validator';

export class CreatePatientTypeDto {
  @ApiProperty({ example: 'Convênio', description: 'Nome do tipo de paciente' })
  @IsNotEmpty({ message: 'O nome é obrigatório' })
  @IsString({ message: 'O nome deve ser uma string' })
  nome: string;

  @ApiProperty({
    example: 'Pacientes que possuem convênio odontológico',
    description: 'Descrição do tipo de paciente',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A descrição deve ser uma string' })
  descricao?: string;

  @ApiProperty({
    example: '#FF0000',
    description: 'Cor associada ao tipo de paciente (formato hexadecimal)',
  })
  @IsNotEmpty({ message: 'A cor é obrigatória' })
  @IsString({ message: 'A cor deve ser uma string' })
  @Matches(/^#[0-9A-Fa-f]{6}$/, {
    message: 'A cor deve estar no formato hexadecimal (ex: #FF0000)',
  })
  cor: string;

  @ApiProperty({
    example: true,
    description: 'Indica se o tipo de paciente está ativo',
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'O campo ativo deve ser um booleano' })
  ativo?: boolean = true;
}
