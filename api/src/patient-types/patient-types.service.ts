import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PatientType } from './entities/patient-type.entity';
import { CreatePatientTypeDto } from './dto/create-patient-type.dto';
import { UpdatePatientTypeDto } from './dto/update-patient-type.dto';
import { formatErrorForLogging } from '../common/utils/error.utils';
import { PatientTypePaginationDto } from './dto/patient-type-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@Injectable()
export class PatientTypesService {
  private readonly logger = new Logger(PatientTypesService.name);

  constructor(
    @InjectRepository(PatientType)
    private patientTypesRepository: Repository<PatientType>,
  ) {}

  async create(
    createPatientTypeDto: CreatePatientTypeDto,
  ): Promise<PatientType> {
    try {
      this.logger.log(
        `Creating patient type: ${JSON.stringify(createPatientTypeDto)}`,
      );
      const patientType =
        this.patientTypesRepository.create(createPatientTypeDto);
      return await this.patientTypesRepository.save(patientType);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating patient type: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if ((error as { code?: string })?.code === 'ER_DUP_ENTRY') {
        throw new BadRequestException(
          'Já existe um tipo de paciente com este nome',
        );
      }
      throw new InternalServerErrorException('Erro ao criar tipo de paciente');
    }
  }

  async findAll(): Promise<PatientType[]> {
    try {
      this.logger.log('Finding all patient types');
      return await this.patientTypesRepository.find({
        order: { nome: 'ASC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding all patient types: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar tipos de paciente',
      );
    }
  }

  async findAllPaginated(
    paginationDto: PatientTypePaginationDto,
  ): Promise<PaginatedResponse<PatientType>> {
    try {
      const { page = 1, limit = 6, search, ativo } = paginationDto;
      this.logger.log(
        `Finding patient types with pagination: page ${page}, limit ${limit}, search: ${search}, ativo: ${ativo}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder =
        this.patientTypesRepository.createQueryBuilder('patientType');

      // Aplicar filtro de busca
      if (search) {
        queryBuilder.where(
          '(patientType.nome LIKE :search OR patientType.descricao LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Aplicar filtro de status ativo
      // Só filtramos por ativo=true quando o parâmetro é fornecido
      // Se o parâmetro não for fornecido, trazemos todos os registros
      if (ativo === true) {
        if (search) {
          queryBuilder.andWhere('patientType.ativo = :ativo', { ativo: true });
        } else {
          queryBuilder.where('patientType.ativo = :ativo', { ativo: true });
        }
      }

      // Ordenar por nome
      queryBuilder.orderBy('patientType.nome', 'ASC');

      // Executar a query com paginação
      const [data, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding patient types with pagination: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar tipos de paciente',
      );
    }
  }

  async findOne(id: number): Promise<PatientType> {
    try {
      this.logger.log(`Finding patient type with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de tipo de paciente inválido');
      }

      const patientType = await this.patientTypesRepository.findOne({
        where: { id },
        relations: ['patients'],
      });

      if (!patientType) {
        throw new NotFoundException(
          `Tipo de paciente com ID ${id} não encontrado`,
        );
      }

      return patientType;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding patient type with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar tipo de paciente');
    }
  }

  async update(
    id: number,
    updatePatientTypeDto: UpdatePatientTypeDto,
  ): Promise<PatientType> {
    try {
      this.logger.log(
        `Updating patient type with id: ${id}, data: ${JSON.stringify(updatePatientTypeDto)}`,
      );

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de tipo de paciente inválido');
      }

      // Verificar se o tipo de paciente existe
      const patientType = await this.findOne(id);

      // Atualizar os dados do tipo de paciente
      Object.assign(patientType, updatePatientTypeDto);

      return await this.patientTypesRepository.save(patientType);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating patient type with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      if ((error as { code?: string })?.code === 'ER_DUP_ENTRY') {
        throw new BadRequestException(
          'Já existe um tipo de paciente com este nome',
        );
      }
      throw new InternalServerErrorException(
        'Erro ao atualizar tipo de paciente',
      );
    }
  }

  async remove(id: number): Promise<void> {
    try {
      this.logger.log(`Removing patient type with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de tipo de paciente inválido');
      }

      // Verificar se o tipo de paciente existe
      const patientType = await this.findOne(id);

      // Verificar se há pacientes associados
      if (patientType.patients && patientType.patients.length > 0) {
        throw new BadRequestException(
          'Não é possível excluir o tipo de paciente pois existem pacientes associados a ele',
        );
      }

      // Remover o tipo de paciente
      await this.patientTypesRepository.remove(patientType);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing patient type with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao remover tipo de paciente',
      );
    }
  }
}
