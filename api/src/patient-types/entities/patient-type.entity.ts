import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';

@Entity('patient_types')
export class PatientType {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do tipo de paciente' })
  id: number;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Nome do tipo de paciente' })
  nome: string;

  @Column({ length: 255, nullable: true })
  @ApiProperty({
    description: 'Descrição do tipo de paciente',
    required: false,
  })
  descricao: string;

  @Column({ length: 7 }) // Formato hexadecimal: #RRGGBB
  @ApiProperty({
    description: 'Cor associada ao tipo de paciente (formato hexadecimal)',
    example: '#FF0000',
  })
  cor: string;

  @Column({ default: true })
  @ApiProperty({ description: 'Indica se o tipo de paciente está ativo' })
  ativo: boolean;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;

  @OneToMany(() => Patient, (patient) => patient.patientType)
  patients: Patient[];
}
