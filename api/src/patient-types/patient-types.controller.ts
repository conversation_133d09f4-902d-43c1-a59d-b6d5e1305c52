import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { PatientTypesService } from './patient-types.service';
import { CreatePatientTypeDto } from './dto/create-patient-type.dto';
import { UpdatePatientTypeDto } from './dto/update-patient-type.dto';
import { PatientTypePaginationDto } from './dto/patient-type-pagination.dto';
import { PatientType } from './entities/patient-type.entity';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('patient-types')
@Controller('patient-types')
export class PatientTypesController {
  constructor(private readonly patientTypesService: PatientTypesService) {}

  @ApiOperation({ summary: 'Criar um novo tipo de paciente' })
  @ApiResponse({
    status: 201,
    description: 'Tipo de paciente criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreatePatientTypeDto })
  @Post()
  create(
    @Body() createPatientTypeDto: CreatePatientTypeDto,
  ): Promise<PatientType> {
    return this.patientTypesService.create(createPatientTypeDto);
  }

  @ApiOperation({ summary: 'Listar todos os tipos de paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de tipos de paciente retornada com sucesso',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Termo de busca para filtrar tipos de paciente por nome ou descrição',
    type: String,
  })
  @ApiQuery({
    name: 'ativo',
    required: false,
    description: 'Filtrar por tipos de paciente ativos',
    type: Boolean,
  })
  @Get()
  async findAll(
    @Query() paginationDto: PatientTypePaginationDto,
  ): Promise<PaginatedResponse<PatientType> | PatientType[]> {
    // Se não houver parâmetros de paginação, retorna todos os tipos de paciente
    if (!paginationDto.page && !paginationDto.limit) {
      return this.patientTypesService.findAll();
    }

    // Se houver parâmetros de paginação, retorna os tipos de paciente paginados
    return this.patientTypesService.findAllPaginated(paginationDto);
  }

  @ApiOperation({ summary: 'Buscar um tipo de paciente pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Tipo de paciente encontrado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Tipo de paciente não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do tipo de paciente', example: 1 })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<PatientType> {
    return this.patientTypesService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar um tipo de paciente' })
  @ApiResponse({
    status: 200,
    description: 'Tipo de paciente atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Tipo de paciente não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do tipo de paciente', example: 1 })
  @ApiBody({ type: UpdatePatientTypeDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePatientTypeDto: UpdatePatientTypeDto,
  ): Promise<PatientType> {
    return this.patientTypesService.update(id, updatePatientTypeDto);
  }

  @ApiOperation({ summary: 'Remover um tipo de paciente' })
  @ApiResponse({
    status: 200,
    description: 'Tipo de paciente removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Tipo de paciente não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do tipo de paciente', example: 1 })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.patientTypesService.remove(id);
  }
}
