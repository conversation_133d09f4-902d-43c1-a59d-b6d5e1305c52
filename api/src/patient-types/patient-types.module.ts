import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PatientTypesService } from './patient-types.service';
import { PatientTypesController } from './patient-types.controller';
import { PatientType } from './entities/patient-type.entity';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [TypeOrmModule.forFeature([PatientType]), CommonModule],
  controllers: [PatientTypesController],
  providers: [PatientTypesService],
  exports: [PatientTypesService],
})
export class PatientTypesModule {}
