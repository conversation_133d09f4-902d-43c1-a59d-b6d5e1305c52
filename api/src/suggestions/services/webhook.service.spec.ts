import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { WebhookService } from './webhook.service';

describe('WebhookService', () => {
  let service: WebhookService;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let httpService: HttpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookService,
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WebhookService>(WebhookService);
    httpService = module.get<HttpService>(HttpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('formatPhoneNumber', () => {
    it('should format Brazilian phone number correctly', () => {
      expect(service.formatPhoneNumber('73991035014')).toBe('5573991035014');
    });

    it('should handle phone number with country code', () => {
      expect(service.formatPhoneNumber('5573991035014')).toBe('5573991035014');
    });

    it('should handle 10-digit phone number', () => {
      expect(service.formatPhoneNumber('**********')).toBe('55**********');
    });

    it('should handle phone number with special characters', () => {
      expect(service.formatPhoneNumber('(73) 99103-5014')).toBe(
        '5573991035014',
      );
    });

    it('should handle empty phone number', () => {
      expect(service.formatPhoneNumber('')).toBe('');
    });

    it('should handle null phone number', () => {
      expect(service.formatPhoneNumber(null as string)).toBe('');
    });
  });
});
