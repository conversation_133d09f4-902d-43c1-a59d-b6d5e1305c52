import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';

export interface WhatsAppWebhookPayload {
  id: string;
  pacienteId: number;
  paciente: string;
  contato: string;
  tratamentosRealizados: string[];
  tratamentoSugerido: string;
}

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);
  private readonly webhookUrl: string;
  private readonly webhookAuth: { username: string; password: string };

  constructor(private readonly configService: ConfigService) {
    this.webhookUrl =
      this.configService.get<string>('WEBHOOK_URL') ||
      'https://n8n.aplopes.com/webhook/odonto-suggest-procedure';
    this.webhookAuth = {
      username: this.configService.get<string>('WEBHOOK_USERNAME') || 'odonto',
      password: this.configService.get<string>('WEBHOOK_PASSWORD') || '123456',
    };
  }

  /**
   * Envia dados para o webhook do WhatsApp quando uma sugestão é aprovada
   */
  async sendSuggestionApprovalWebhook(
    payload: WhatsAppWebhookPayload,
  ): Promise<void> {
    const maxRetries = 3;
    let attempt = 0;

    this.logger.log(`🚀 INICIANDO WEBHOOK - Sugestão ID: ${payload.id}`);
    this.logger.log(`📋 PAYLOAD COMPLETO: ${JSON.stringify(payload, null, 2)}`);

    while (attempt < maxRetries) {
      attempt++;

      try {
        this.logger.log(
          `📡 TENTATIVA ${attempt}/${maxRetries} - Enviando para: ${this.webhookUrl}`,
        );
        this.logger.log(
          `🔐 AUTENTICAÇÃO: Basic Auth com usuário '${this.webhookAuth.username}'`,
        );
        this.logger.log(`⏱️ TIMEOUT: 10 segundos`);

        const startTime = Date.now();

        const response: AxiosResponse = await axios.post(
          this.webhookUrl,
          payload,
          {
            auth: this.webhookAuth,
            timeout: 10000, // 10 segundos de timeout
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'CRM-Odonto-Webhook/1.0',
            },
            validateStatus: (status) => status < 500, // Não rejeitar para status 4xx
          },
        );

        const duration = Date.now() - startTime;

        this.logger.log(`✅ WEBHOOK ENVIADO COM SUCESSO!`);
        this.logger.log(`📊 STATUS: ${response.status} ${response.statusText}`);
        this.logger.log(`⏱️ DURAÇÃO: ${duration}ms`);
        this.logger.log(
          `📤 HEADERS ENVIADOS: ${JSON.stringify(response.config.headers, null, 2)}`,
        );
        this.logger.log(
          `📥 HEADERS RECEBIDOS: ${JSON.stringify(response.headers, null, 2)}`,
        );
        this.logger.log(
          `📋 RESPOSTA COMPLETA: ${JSON.stringify(response.data, null, 2)}`,
        );

        // Se chegou aqui, o webhook foi enviado com sucesso
        return;
      } catch (error: unknown) {
        const errorConfig = (
          error as {
            config?: {
              metadata?: { startTime?: number };
              url?: string;
              method?: string;
              timeout?: number;
            };
          }
        ).config;
        const errorCode = (error as { code?: string }).code;
        const errorResponse = (
          error as {
            response?: {
              status?: number;
              statusText?: string;
              headers?: unknown;
              data?: unknown;
            };
          }
        ).response;
        const errorRequest = (error as { request?: unknown }).request;
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        const errorStack = error instanceof Error ? error.stack : undefined;

        const duration =
          Date.now() - (errorConfig?.metadata?.startTime || Date.now());

        this.logger.error(`❌ ERRO NA TENTATIVA ${attempt}/${maxRetries}`);
        this.logger.error(`⏱️ DURAÇÃO ATÉ ERRO: ${duration}ms`);

        if (errorCode) {
          this.logger.error(`🔧 CÓDIGO DO ERRO: ${errorCode}`);
        }

        if (errorResponse) {
          // Erro HTTP (4xx, 5xx)
          this.logger.error(
            `📊 STATUS HTTP: ${errorResponse.status} ${errorResponse.statusText}`,
          );
          this.logger.error(
            `📥 HEADERS DA RESPOSTA: ${JSON.stringify(errorResponse.headers, null, 2)}`,
          );
          this.logger.error(
            `📋 CORPO DA RESPOSTA: ${JSON.stringify(errorResponse.data, null, 2)}`,
          );
        } else if (errorRequest) {
          // Erro de rede/timeout
          this.logger.error(`🌐 ERRO DE REDE/TIMEOUT`);
          this.logger.error(
            `📡 REQUEST: ${JSON.stringify(
              {
                url: errorConfig?.url,
                method: errorConfig?.method,
                timeout: errorConfig?.timeout,
              },
              null,
              2,
            )}`,
          );
        } else {
          // Erro de configuração
          this.logger.error(`⚙️ ERRO DE CONFIGURAÇÃO: ${errorMessage}`);
        }

        this.logger.error(`📝 STACK TRACE: ${errorStack}`);

        // Se não é a última tentativa, aguarda antes de tentar novamente
        if (attempt < maxRetries) {
          const waitTime = attempt * 2000; // 2s, 4s, 6s
          this.logger.log(
            `⏳ AGUARDANDO ${waitTime}ms ANTES DA PRÓXIMA TENTATIVA...`,
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      }
    }

    // Se chegou aqui, todas as tentativas falharam
    this.logger.error(
      `💥 WEBHOOK FALHOU APÓS ${maxRetries} TENTATIVAS - Sugestão ID: ${payload.id}`,
    );
    this.logger.error(
      `⚠️ O webhook é uma funcionalidade auxiliar, o fluxo principal continua normalmente`,
    );
  }

  /**
   * Formata o número de telefone para o padrão brasileiro com código do país
   * Exemplo: "73991035014" -> "5573991035014"
   */
  formatPhoneNumber(phone: string): string {
    if (!phone) {
      return '';
    }

    // Remove todos os caracteres não numéricos
    const cleanPhone = phone.replace(/\D/g, '');

    // Se já tem código do país (55), retorna como está
    if (cleanPhone.startsWith('55') && cleanPhone.length >= 12) {
      return cleanPhone;
    }

    // Se tem 11 dígitos (DDD + 9 dígitos), adiciona o código do país
    if (cleanPhone.length === 11) {
      return `55${cleanPhone}`;
    }

    // Se tem 10 dígitos (DDD + 8 dígitos), adiciona 9 e código do país
    if (cleanPhone.length === 10) {
      const ddd = cleanPhone.substring(0, 2);
      const number = cleanPhone.substring(2);
      return `55${ddd}9${number}`;
    }

    // Para outros casos, tenta adicionar código do país se não tiver
    if (!cleanPhone.startsWith('55')) {
      return `55${cleanPhone}`;
    }

    return cleanPhone;
  }
}
