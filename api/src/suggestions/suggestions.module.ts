import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SuggestionsService } from './suggestions.service';
import { SuggestionsController } from './suggestions.controller';
import { Suggestion } from './entities/suggestion.entity';
import { SuggestionProcedure } from './entities/suggestion-procedure.entity';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';
import { PatientsModule } from '../patients/patients.module';
import { WebhookService } from './services/webhook.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Suggestion,
      SuggestionProcedure,
      TreatmentProcedure,
    ]),
    PatientsModule,
  ],
  controllers: [SuggestionsController],
  providers: [SuggestionsService, WebhookService],
  exports: [SuggestionsService],
})
export class SuggestionsModule {}
