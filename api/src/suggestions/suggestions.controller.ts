import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUIDPipe,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { SuggestionsService } from './suggestions.service';
import { CreateSuggestionDto } from './dto/create-suggestion.dto';
import { UpdateSuggestionDto } from './dto/update-suggestion.dto';
import { SuggestionQueryDto } from './dto/suggestion-query.dto';
import { ApprovedSuggestionResponseDto } from './dto/approved-suggestion-response.dto';
import { Suggestion } from './entities/suggestion.entity';

@ApiTags('suggestions')
@Controller('suggestions')
export class SuggestionsController {
  constructor(private readonly suggestionsService: SuggestionsService) {}

  @ApiOperation({ summary: 'Criar uma nova sugestão da IA' })
  @ApiResponse({ status: 201, description: 'Sugestão criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateSuggestionDto })
  @Post()
  create(
    @Body() createSuggestionDto: CreateSuggestionDto,
  ): Promise<Suggestion> {
    return this.suggestionsService.create(createSuggestionDto);
  }

  @ApiOperation({ summary: 'Listar todas as sugestões da IA' })
  @ApiResponse({
    status: 200,
    description: 'Lista de sugestões retornada com sucesso',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar sugestões por status',
    enum: [
      'IN_ANALYSIS',
      'PENDING_REVIEW',
      'REJECTED',
      'APPROVED',
      'WAITING_CONTACT',
    ],
  })
  @Get()
  findAll(@Query() queryDto: SuggestionQueryDto): Promise<Suggestion[]> {
    return this.suggestionsService.findAll(queryDto);
  }

  @ApiOperation({
    summary: 'Buscar todas as sugestões aprovadas com histórico completo',
  })
  @ApiResponse({
    status: 200,
    description:
      'Lista de sugestões aprovadas com informações completas do paciente e histórico retornada com sucesso',
    type: [ApprovedSuggestionResponseDto],
  })
  @Get('approved')
  findApproved(): Promise<Suggestion[]> {
    return this.suggestionsService.findApproved();
  }

  @ApiOperation({ summary: 'Buscar sugestões da IA por paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de sugestões do paciente retornada com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @Get('patient/:patientId')
  findByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<Suggestion[]> {
    return this.suggestionsService.findByPatient(patientId);
  }

  @ApiOperation({ summary: 'Buscar sugestões rejeitadas da IA por paciente' })
  @ApiResponse({
    status: 200,
    description:
      'Lista de sugestões rejeitadas do paciente retornada com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @Get('rejected/:patientId')
  findRejectedByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<Suggestion[]> {
    return this.suggestionsService.findRejectedByPatient(patientId);
  }

  @ApiOperation({ summary: 'Buscar uma sugestão da IA pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Sugestão encontrada com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Sugestão não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da sugestão' })
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Suggestion> {
    return this.suggestionsService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar uma sugestão da IA' })
  @ApiResponse({
    status: 200,
    description: 'Sugestão atualizada com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Sugestão não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da sugestão' })
  @ApiBody({ type: UpdateSuggestionDto })
  @Patch(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSuggestionDto: UpdateSuggestionDto,
  ): Promise<Suggestion> {
    return this.suggestionsService.update(id, updateSuggestionDto);
  }

  @ApiOperation({ summary: 'Remover uma sugestão da IA' })
  @ApiResponse({
    status: 200,
    description: 'Sugestão removida com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Sugestão não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da sugestão' })
  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.suggestionsService.remove(id);
  }
}
