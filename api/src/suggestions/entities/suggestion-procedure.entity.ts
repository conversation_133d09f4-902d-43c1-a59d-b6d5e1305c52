import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Suggestion } from './suggestion.entity';
import { Procedure } from '../../procedures/entities/procedure.entity';

@Entity('suggestion_procedures')
export class SuggestionProcedure {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único do procedimento sugerido' })
  id: string;

  @ManyToOne(() => Suggestion, (suggestion) => suggestion.procedures, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'suggestionId' })
  @ApiProperty({ description: 'Sugestão associada ao procedimento' })
  suggestion: Suggestion;

  @ManyToOne(() => Procedure)
  @JoinColumn({ name: 'procedureId' })
  @ApiProperty({ description: 'Procedimento odontológico sugerido' })
  procedure: Procedure;

  @Column({ type: 'date', nullable: true })
  @ApiProperty({
    description: 'Data esperada para o procedimento',
    required: false,
  })
  expectedDate: Date | null;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Observações sobre o procedimento sugerido',
    required: false,
  })
  notes: string | null;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
