import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { SuggestionStatus } from './suggestion-status.enum';
import { SuggestionProcedure } from './suggestion-procedure.entity';

@Entity('suggestions')
export class Suggestion {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da sugestão' })
  id: string;

  @Column({ name: 'patientId' })
  @ApiProperty({ description: 'ID do paciente associado à sugestão' })
  patientId: number;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado à sugestão' })
  patient: Patient;

  @Column({
    type: 'enum',
    enum: SuggestionStatus,
    default: SuggestionStatus.IN_ANALYSIS,
  })
  @ApiProperty({
    description: 'Status da sugestão',
    enum: SuggestionStatus,
    default: SuggestionStatus.IN_ANALYSIS,
  })
  status: SuggestionStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Raciocínio da IA para a sugestão',
    required: false,
  })
  iaReasoning: string | null;

  @OneToMany(() => SuggestionProcedure, (procedure) => procedure.suggestion, {
    cascade: true,
    eager: true,
  })
  @ApiProperty({
    description: 'Procedimentos sugeridos',
    type: () => [SuggestionProcedure],
  })
  procedures: SuggestionProcedure[];

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Comentário humano sobre a sugestão',
    required: false,
  })
  humanComment: string | null;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação da sugestão' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização da sugestão' })
  updatedAt: Date;
}
