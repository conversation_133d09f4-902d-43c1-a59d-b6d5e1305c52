import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Suggestion } from './entities/suggestion.entity';
import { SuggestionProcedure } from './entities/suggestion-procedure.entity';
import { SuggestionStatus } from './entities/suggestion-status.enum';
import { CreateSuggestionDto } from './dto/create-suggestion.dto';
import { UpdateSuggestionDto } from './dto/update-suggestion.dto';
import { SuggestionQueryDto } from './dto/suggestion-query.dto';
import { PatientsService } from '../patients/patients.service';
import {
  TreatmentProcedure,
  TreatmentProcedureStatus,
} from '../treatment-plans/entities/treatment-procedure.entity';
import { WebhookService } from './services/webhook.service';

@Injectable()
export class SuggestionsService {
  private readonly logger = new Logger(SuggestionsService.name);

  constructor(
    @InjectRepository(Suggestion)
    private suggestionsRepository: Repository<Suggestion>,
    @InjectRepository(SuggestionProcedure)
    private suggestionProceduresRepository: Repository<SuggestionProcedure>,
    @InjectRepository(TreatmentProcedure)
    private treatmentProceduresRepository: Repository<TreatmentProcedure>,
    private patientsService: PatientsService,
    private webhookService: WebhookService,
  ) {}

  async create(createSuggestionDto: CreateSuggestionDto): Promise<Suggestion> {
    try {
      this.logger.log(
        `Creating suggestion: ${JSON.stringify(createSuggestionDto)}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(createSuggestionDto.patientId);

      // Extrair os procedimentos do DTO
      const { procedures, ...suggestionData } = createSuggestionDto;

      // Criar a entidade de sugestão
      const suggestion = this.suggestionsRepository.create({
        ...suggestionData,
        patientId: createSuggestionDto.patientId,
      });

      // Salvar a sugestão para obter o ID
      const savedSuggestion = await this.suggestionsRepository.save(suggestion);

      // Criar e salvar os procedimentos sugeridos, se existirem
      if (procedures && procedures.length > 0) {
        const suggestionProcedures = procedures.map((proc) => {
          return this.suggestionProceduresRepository.create({
            suggestion: savedSuggestion,
            procedure: { id: proc.procedureId }, // Usar o relacionamento em vez do ID direto
            expectedDate: proc.expectedDate
              ? new Date(proc.expectedDate)
              : null,
            notes: proc.notes || null,
          });
        });

        await this.suggestionProceduresRepository.save(suggestionProcedures);
      }

      // Retornar a sugestão completa com os procedimentos
      return this.findOne(savedSuggestion.id);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating suggestion: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw new BadRequestException(
          error instanceof Error ? error.message : 'Erro desconhecido',
        );
      }
      throw new InternalServerErrorException('Erro ao criar sugestão');
    }
  }

  async findAll(queryDto?: SuggestionQueryDto): Promise<Suggestion[]> {
    try {
      this.logger.log(
        `Finding all suggestions with filters: ${JSON.stringify(queryDto)}`,
      );

      const whereCondition: Record<string, unknown> = {};

      // Aplicar filtro por status se fornecido
      if (queryDto?.status) {
        whereCondition.status = queryDto.status;
      }

      return await this.suggestionsRepository.find({
        where: whereCondition,
        relations: ['patient', 'procedures', 'procedures.procedure'],
        order: { createdAt: 'DESC' },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding all suggestions: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao buscar sugestões');
    }
  }

  async findByPatient(patientId: number): Promise<Suggestion[]> {
    try {
      this.logger.log(`Finding suggestions for patient: ${patientId}`);
      return await this.suggestionsRepository.find({
        where: { patient: { id: patientId } },
        relations: ['patient', 'procedures', 'procedures.procedure'],
        order: { createdAt: 'DESC' },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding suggestions for patient ${patientId}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar sugestões do paciente',
      );
    }
  }

  async findApproved(): Promise<Suggestion[]> {
    try {
      this.logger.log('Finding all approved suggestions');

      const approvedSuggestions = await this.suggestionsRepository.find({
        where: { status: SuggestionStatus.APPROVED },
        relations: [
          'patient',
          'patient.patientType',
          'procedures',
          'procedures.procedure',
        ],
        order: { updatedAt: 'DESC' },
      });

      // Para cada sugestão aprovada, buscar o histórico de sugestões e procedimentos do paciente
      const suggestionsWithHistory = await Promise.all(
        approvedSuggestions.map(async (suggestion) => {
          // Buscar todas as sugestões anteriores do mesmo paciente
          const patientHistory = await this.suggestionsRepository
            .createQueryBuilder('suggestion')
            .leftJoinAndSelect('suggestion.procedures', 'procedures')
            .leftJoinAndSelect('procedures.procedure', 'procedure')
            .where('suggestion.patientId = :patientId', {
              patientId: suggestion.patient.id,
            })
            .andWhere('suggestion.id != :currentId', {
              currentId: suggestion.id,
            })
            .orderBy('suggestion.createdAt', 'DESC')
            .getMany();

          // Buscar todos os procedimentos realizados (concluídos) pelo paciente
          const completedProcedures = await this.treatmentProceduresRepository
            .createQueryBuilder('treatmentProcedure')
            .leftJoinAndSelect('treatmentProcedure.procedure', 'procedure')
            .leftJoinAndSelect(
              'treatmentProcedure.professional',
              'professional',
            )
            .leftJoinAndSelect(
              'treatmentProcedure.treatmentPlan',
              'treatmentPlan',
            )
            .where('treatmentPlan.patientId = :patientId', {
              patientId: suggestion.patient.id,
            })
            .andWhere('treatmentProcedure.status = :status', {
              status: TreatmentProcedureStatus.COMPLETED,
            })
            .orderBy('treatmentProcedure.executionDate', 'DESC')
            .getMany();

          // Organizar procedimentos por plano de tratamento
          const treatmentPlansMap = new Map();
          const additionalProcedures: unknown[] = [];

          completedProcedures.forEach((procedure) => {
            if (procedure.treatmentPlan) {
              const planId = procedure.treatmentPlan.id;

              if (!treatmentPlansMap.has(planId)) {
                treatmentPlansMap.set(planId, {
                  id: procedure.treatmentPlan.id,
                  totalValue: procedure.treatmentPlan.totalValue,
                  completionPercentage:
                    procedure.treatmentPlan.completionPercentage,
                  status: procedure.treatmentPlan.status,
                  createdAt: procedure.treatmentPlan.createdAt,
                  updatedAt: procedure.treatmentPlan.updatedAt,
                  procedures: [],
                });
              }

              // Remover dados redundantes do procedimento
              const cleanProcedure = {
                id: procedure.id,
                name: procedure.name,
                value: procedure.value,
                tooth: procedure.tooth,
                executionDate: procedure.executionDate,
                status: procedure.status,
                notes: procedure.notes,
                nextVisitDetails: procedure.nextVisitDetails,
                procedure: procedure.procedure,
                professional: procedure.professional,
                createdAt: procedure.createdAt,
                updatedAt: procedure.updatedAt,
              };

              const planData = treatmentPlansMap.get(planId) as
                | { procedures: unknown[] }
                | undefined;
              if (planData && Array.isArray(planData.procedures)) {
                planData.procedures.push(cleanProcedure);
              }
            } else {
              // Procedimento sem plano de tratamento
              additionalProcedures.push({
                id: procedure.id,
                name: procedure.name,
                value: procedure.value,
                tooth: procedure.tooth,
                executionDate: procedure.executionDate,
                status: procedure.status,
                notes: procedure.notes,
                nextVisitDetails: procedure.nextVisitDetails,
                procedure: procedure.procedure,
                professional: procedure.professional,
                createdAt: procedure.createdAt,
                updatedAt: procedure.updatedAt,
              });
            }
          });

          const treatmentPlans = Array.from(treatmentPlansMap.values());

          // Adicionar o histórico como propriedades customizadas
          return {
            ...suggestion,
            patientSuggestionsHistory: patientHistory,
            patientTreatmentPlans: treatmentPlans,
            patientAdditionalProcedures: additionalProcedures,
          };
        }),
      );

      return suggestionsWithHistory;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding approved suggestions: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar sugestões aprovadas',
      );
    }
  }

  async findRejectedByPatient(patientId: number): Promise<Suggestion[]> {
    try {
      this.logger.log(`Finding rejected suggestions for patient: ${patientId}`);

      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      return await this.suggestionsRepository.find({
        where: {
          patient: { id: patientId },
          status: SuggestionStatus.REJECTED,
        },
        relations: ['patient', 'procedures', 'procedures.procedure'],
        order: { createdAt: 'DESC' },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding rejected suggestions for patient ${patientId}: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar sugestões rejeitadas do paciente',
      );
    }
  }

  async findOne(id: string): Promise<Suggestion> {
    try {
      this.logger.log(`Finding suggestion with id: ${id}`);
      const suggestion = await this.suggestionsRepository.findOne({
        where: { id },
        relations: ['patient', 'procedures', 'procedures.procedure'],
      });
      if (!suggestion) {
        throw new NotFoundException(`Sugestão com ID ${id} não encontrada`);
      }
      return suggestion;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding suggestion with id ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao buscar sugestão');
    }
  }

  async update(
    id: string,
    updateSuggestionDto: UpdateSuggestionDto,
  ): Promise<Suggestion> {
    try {
      this.logger.log(
        `Updating suggestion ${id}: ${JSON.stringify(updateSuggestionDto)}`,
      );

      // Verificar se a sugestão existe
      const existingSuggestion = await this.findOne(id);

      // Verificar se está tentando mudar para PENDING_REVIEW
      if (updateSuggestionDto.status === SuggestionStatus.PENDING_REVIEW) {
        // Verificar se tem iaReasoning
        if (
          !existingSuggestion.iaReasoning &&
          !updateSuggestionDto.iaReasoning
        ) {
          throw new BadRequestException(
            'Para mover para o status PENDING_REVIEW, o campo iaReasoning é obrigatório',
          );
        }

        // Verificar se tem procedimentos
        const hasProcedures =
          existingSuggestion.procedures &&
          existingSuggestion.procedures.length > 0;
        const willHaveProcedures =
          updateSuggestionDto.procedures &&
          updateSuggestionDto.procedures.length > 0;

        if (!hasProcedures && !willHaveProcedures) {
          throw new BadRequestException(
            'Para mover para o status PENDING_REVIEW, é necessário ter pelo menos um procedimento sugerido',
          );
        }
      }

      // Se temos procedimentos no DTO, atualizá-los
      if (updateSuggestionDto.procedures) {
        // Buscar a sugestão para usar no relacionamento
        const suggestion = await this.suggestionsRepository.findOne({
          where: { id },
        });
        if (!suggestion) {
          throw new NotFoundException(`Sugestão com ID ${id} não encontrada`);
        }

        // Remover os procedimentos existentes
        // Primeiro, encontrar os IDs dos procedimentos a serem excluídos
        const proceduresToDelete =
          await this.suggestionProceduresRepository.find({
            where: { suggestion: { id } },
          });

        if (proceduresToDelete.length > 0) {
          await this.suggestionProceduresRepository.remove(proceduresToDelete);
        }

        // Adicionar os novos procedimentos
        if (updateSuggestionDto.procedures.length > 0) {
          const suggestionProcedures = updateSuggestionDto.procedures.map(
            (proc) => {
              return this.suggestionProceduresRepository.create({
                suggestion: suggestion,
                procedure: { id: proc.procedureId }, // Usar o relacionamento em vez do ID direto
                expectedDate: proc.expectedDate
                  ? new Date(proc.expectedDate)
                  : null,
                notes: proc.notes || null,
              });
            },
          );

          await this.suggestionProceduresRepository.save(suggestionProcedures);
        }

        // Remover o campo procedures do DTO para não tentar salvá-lo na tabela de sugestões
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { procedures: _, ...suggestionData } = updateSuggestionDto;

        // Atualizar a sugestão
        await this.suggestionsRepository.update(id, suggestionData);
      } else {
        // Atualizar apenas a sugestão sem mexer nos procedimentos
        await this.suggestionsRepository.update(id, updateSuggestionDto);
      }

      // Retornar a sugestão atualizada
      const updatedSuggestion = await this.findOne(id);

      // Verificar se o status mudou para APPROVED e disparar webhook
      if (
        updateSuggestionDto.status === SuggestionStatus.APPROVED &&
        existingSuggestion.status !== SuggestionStatus.APPROVED
      ) {
        this.logger.log(
          `🎯 SUGESTÃO APROVADA! Status alterado para APPROVED - ID: ${id}`,
        );
        this.logger.log(
          `📊 Status anterior: ${existingSuggestion.status} → Status novo: ${updateSuggestionDto.status}`,
        );
        this.logger.log(
          `👤 Paciente: ${updatedSuggestion.patient.name} (ID: ${updatedSuggestion.patient.id})`,
        );

        // Disparar webhook de forma assíncrona para não bloquear a resposta
        this.sendWhatsAppWebhook(updatedSuggestion).catch((error: unknown) => {
          const errorMessage =
            error instanceof Error ? error.message : 'Erro desconhecido';
          this.logger.error(
            `💥 FALHA NO WEBHOOK - Sugestão ${id}: ${errorMessage}`,
          );
        });
      }

      return updatedSuggestion;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating suggestion ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao atualizar sugestão');
    }
  }

  /**
   * Envia webhook para WhatsApp quando uma sugestão é aprovada (status APPROVED)
   */
  private async sendWhatsAppWebhook(suggestion: Suggestion): Promise<void> {
    try {
      this.logger.log(`🔄 PREPARANDO WEBHOOK - Sugestão ${suggestion.id}`);
      this.logger.log(
        `👤 Paciente: ${suggestion.patient.name} (ID: ${suggestion.patient.id})`,
      );
      this.logger.log(
        `💭 IA Reasoning: ${suggestion.iaReasoning || 'Não informado'}`,
      );

      // Buscar procedimentos realizados pelo paciente
      this.logger.log(`🔍 BUSCANDO PROCEDIMENTOS REALIZADOS...`);
      const completedProcedures = await this.treatmentProceduresRepository
        .createQueryBuilder('treatmentProcedure')
        .leftJoinAndSelect('treatmentProcedure.procedure', 'procedure')
        .leftJoinAndSelect('treatmentProcedure.treatmentPlan', 'treatmentPlan')
        .where('treatmentPlan.patientId = :patientId', {
          patientId: suggestion.patient.id,
        })
        .andWhere('treatmentProcedure.status = :status', {
          status: TreatmentProcedureStatus.COMPLETED,
        })
        .orderBy('treatmentProcedure.executionDate', 'DESC')
        .getMany();

      this.logger.log(
        `📋 ENCONTRADOS ${completedProcedures.length} PROCEDIMENTOS REALIZADOS`,
      );

      // Extrair nomes dos procedimentos realizados
      const tratamentosRealizados = completedProcedures
        .map((proc) => proc.name || proc.procedure?.name)
        .filter(Boolean);

      if (tratamentosRealizados.length > 0) {
        this.logger.log(
          `✅ PROCEDIMENTOS REALIZADOS: ${tratamentosRealizados.join(', ')}`,
        );
      } else {
        this.logger.log(`⚠️ NENHUM PROCEDIMENTO REALIZADO ENCONTRADO`);
      }

      // Formatar número de telefone
      const telefoneOriginal =
        suggestion.patient.phone || suggestion.patient.whatsapp || '';
      this.logger.log(`📞 TELEFONE ORIGINAL: "${telefoneOriginal}"`);

      const contato = this.webhookService.formatPhoneNumber(telefoneOriginal);
      this.logger.log(`📞 TELEFONE FORMATADO: "${contato}"`);

      // Preparar payload do webhook
      const webhookPayload = {
        id: suggestion.id,
        pacienteId: suggestion.patient.id,
        paciente: suggestion.patient.name,
        contato: contato,
        tratamentosRealizados: tratamentosRealizados,
        tratamentoSugerido:
          suggestion.iaReasoning || 'Sugestão de tratamento gerada pela IA',
      };

      this.logger.log(`📦 PAYLOAD PREPARADO PARA WEBHOOK:`);
      this.logger.log(`   - ID: ${webhookPayload.id}`);
      this.logger.log(`   - Paciente ID: ${webhookPayload.pacienteId}`);
      this.logger.log(`   - Nome: ${webhookPayload.paciente}`);
      this.logger.log(`   - Contato: ${webhookPayload.contato}`);
      this.logger.log(
        `   - Tratamentos Realizados: [${webhookPayload.tratamentosRealizados.join(', ')}]`,
      );
      this.logger.log(
        `   - Tratamento Sugerido: ${webhookPayload.tratamentoSugerido}`,
      );

      // Enviar webhook
      this.logger.log(`🚀 ENVIANDO WEBHOOK...`);
      await this.webhookService.sendSuggestionApprovalWebhook(webhookPayload);

      this.logger.log(
        `🎉 WEBHOOK CONCLUÍDO COM SUCESSO - Sugestão ${suggestion.id}`,
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `💥 ERRO NO PROCESSO DE WEBHOOK - Sugestão ${suggestion.id}`,
      );
      this.logger.error(`❌ Erro: ${errorMessage}`);
      this.logger.error(`📝 Stack: ${errorStack}`);
      this.logger.error(
        `⚠️ O webhook é uma funcionalidade auxiliar, o fluxo principal continua normalmente`,
      );
      // Não relançamos o erro para não quebrar o fluxo principal
    }
  }

  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Removing suggestion with id: ${id}`);

      // Verificar se a sugestão existe
      await this.findOne(id);

      // Remover a sugestão
      await this.suggestionsRepository.delete(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error removing suggestion ${id}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao remover sugestão');
    }
  }
}
