import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { SuggestionStatus } from '../entities/suggestion-status.enum';

export class SuggestionQueryDto {
  @ApiProperty({
    description: 'Filtrar sugestões por status',
    enum: SuggestionStatus,
    required: false,
    example: SuggestionStatus.PENDING_REVIEW,
  })
  @IsOptional()
  @IsEnum(SuggestionStatus, {
    message:
      'Status inválido. Valores aceitos: IN_ANALYSIS, PENDING_REVIEW, REJECTED, APPROVED',
  })
  status?: SuggestionStatus;
}
