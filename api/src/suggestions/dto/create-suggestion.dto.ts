import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SuggestionStatus } from '../entities/suggestion-status.enum';
import { SuggestedProcedureDto } from './suggested-procedure.dto';

export class CreateSuggestionDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  @IsNumber({}, { message: 'O ID do paciente deve ser um número' })
  patientId: number;

  @ApiProperty({
    description: 'Status da sugestão',
    enum: SuggestionStatus,
    default: SuggestionStatus.IN_ANALYSIS,
  })
  @IsOptional()
  @IsEnum(SuggestionStatus, {
    message: 'Status inválido',
  })
  status?: SuggestionStatus;

  @ApiProperty({
    description: 'Raciocínio da IA para a sugestão',
    example:
      'Com base na análise dos exames e histórico do paciente, recomendo...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O raciocínio da IA deve ser uma string' })
  iaReasoning?: string;

  @ApiProperty({
    description: 'Procedimentos sugeridos',
    type: [SuggestedProcedureDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Os procedimentos sugeridos devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => SuggestedProcedureDto)
  procedures?: SuggestedProcedureDto[];

  @ApiProperty({
    description: 'Comentário humano sobre a sugestão',
    required: false,
    example: 'Concordo com a sugestão da IA',
  })
  @IsOptional()
  @IsString({ message: 'O comentário humano deve ser uma string' })
  humanComment?: string;
}
