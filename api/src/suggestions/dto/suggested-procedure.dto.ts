import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsOptional,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SuggestedProcedureDto {
  @ApiProperty({
    description: 'ID do procedimento odontológico',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do procedimento é obrigatório' })
  @IsNumber({}, { message: 'O ID do procedimento deve ser um número' })
  @Type(() => Number)
  procedureId: number;

  @ApiProperty({
    description: 'Data esperada para o procedimento',
    example: '2023-12-01',
    required: false,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'A data esperada deve estar em formato válido (YYYY-MM-DD)' },
  )
  expectedDate?: string;

  @ApiProperty({
    description: 'Observações sobre o procedimento sugerido',
    example: 'Observações sobre o procedimento',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'As observações devem ser uma string' })
  notes?: string;
}
