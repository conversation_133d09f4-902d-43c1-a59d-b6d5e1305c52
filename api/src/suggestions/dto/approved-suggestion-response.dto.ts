import { ApiProperty } from '@nestjs/swagger';
import { SuggestionStatus } from '../entities/suggestion-status.enum';

export class ProcedureInfoDto {
  @ApiProperty({ description: 'ID do procedimento' })
  id: number;

  @ApiProperty({ description: 'Nome do procedimento' })
  name: string;

  @ApiProperty({ description: 'Descrição do procedimento' })
  description: string;

  @ApiProperty({ description: 'Preço padrão do procedimento' })
  defaultPrice: number;

  @ApiProperty({ description: 'Duração estimada em minutos' })
  estimatedDuration: number;

  @ApiProperty({ description: 'Tipo do procedimento' })
  type: string;

  @ApiProperty({ description: 'Status do procedimento' })
  status: string;
}

export class SuggestionProcedureDto {
  @ApiProperty({ description: 'ID único do procedimento sugerido' })
  id: string;

  @ApiProperty({
    description: 'Informações do procedimento',
    type: ProcedureInfoDto,
  })
  procedure: ProcedureInfoDto;

  @ApiProperty({
    description: 'Data esperada para o procedimento',
    required: false,
  })
  expectedDate: Date | null;

  @ApiProperty({
    description: 'Observações sobre o procedimento sugerido',
    required: false,
  })
  notes: string | null;

  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}

export class PatientTypeDto {
  @ApiProperty({ description: 'ID do tipo de paciente' })
  id: number;

  @ApiProperty({ description: 'Nome do tipo de paciente' })
  name: string;

  @ApiProperty({ description: 'Descrição do tipo de paciente' })
  description: string;
}

export class PatientInfoDto {
  @ApiProperty({ description: 'ID do paciente' })
  id: number;

  @ApiProperty({ description: 'Nome completo do paciente' })
  name: string;

  @ApiProperty({ description: 'Data de nascimento' })
  birthDate: Date;

  @ApiProperty({ description: 'Gênero do paciente' })
  gender: string;

  @ApiProperty({ description: 'CPF do paciente' })
  cpf: string;

  @ApiProperty({ description: 'Como conheceu a clínica', required: false })
  howDidYouFindUs: string | null;

  @ApiProperty({ description: 'Data de registro' })
  registrationDate: Date;

  @ApiProperty({ description: 'Observações sobre o paciente', required: false })
  notes: string | null;

  @ApiProperty({ description: 'Email do paciente' })
  email: string;

  @ApiProperty({ description: 'Telefone do paciente' })
  phone: string;

  @ApiProperty({ description: 'WhatsApp do paciente', required: false })
  whatsapp: string | null;

  @ApiProperty({ description: 'CEP do endereço', required: false })
  addressZipCode: string | null;

  @ApiProperty({ description: 'Rua do endereço', required: false })
  addressStreet: string | null;

  @ApiProperty({ description: 'Número do endereço', required: false })
  addressNumber: string | null;

  @ApiProperty({ description: 'Bairro do endereço', required: false })
  addressNeighborhood: string | null;

  @ApiProperty({ description: 'Cidade do endereço', required: false })
  addressCity: string | null;

  @ApiProperty({ description: 'Estado do endereço', required: false })
  addressState: string | null;

  @ApiProperty({ description: 'Complemento do endereço', required: false })
  addressComplement: string | null;

  @ApiProperty({ description: 'Profissão do paciente', required: false })
  profession: string | null;

  @ApiProperty({ description: 'Número do prontuário médico', required: false })
  medicalRecordNumber: string | null;

  @ApiProperty({ description: 'Categoria do paciente' })
  category: string;

  @ApiProperty({
    description: 'Tipo de paciente',
    type: PatientTypeDto,
    required: false,
  })
  patientType: PatientTypeDto | null;

  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização' })
  updatedAt: Date;
}

export class DentistInfoDto {
  @ApiProperty({ description: 'ID do dentista' })
  id: number;

  @ApiProperty({ description: 'Nome do dentista' })
  name: string;

  @ApiProperty({ description: 'CRO do dentista' })
  cro: string;

  @ApiProperty({ description: 'Especialidade do dentista', required: false })
  specialty: string | null;
}

export class CompletedProcedureDto {
  @ApiProperty({ description: 'ID único do procedimento realizado' })
  id: number;

  @ApiProperty({ description: 'Nome do procedimento' })
  name: string;

  @ApiProperty({ description: 'Valor cobrado pelo procedimento' })
  value: number;

  @ApiProperty({ description: 'Número do dente relacionado', required: false })
  tooth: string | null;

  @ApiProperty({
    description: 'Data de execução do procedimento',
    required: false,
  })
  executionDate: Date | null;

  @ApiProperty({ description: 'Status do procedimento' })
  status: string;

  @ApiProperty({
    description: 'Observações sobre o procedimento',
    required: false,
  })
  notes: string | null;

  @ApiProperty({
    description: 'Detalhes para a próxima consulta',
    required: false,
  })
  nextVisitDetails: string | null;

  @ApiProperty({
    description: 'Informações do procedimento base',
    type: ProcedureInfoDto,
  })
  procedure: ProcedureInfoDto;

  @ApiProperty({
    description: 'Profissional responsável',
    type: DentistInfoDto,
  })
  professional: DentistInfoDto;

  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}

export class TreatmentPlanDto {
  @ApiProperty({ description: 'ID único do plano de tratamento' })
  id: number;

  @ApiProperty({ description: 'Valor total do plano de tratamento' })
  totalValue: number;

  @ApiProperty({ description: 'Percentual de conclusão do plano' })
  completionPercentage: number;

  @ApiProperty({ description: 'Status do plano de tratamento' })
  status: string;

  @ApiProperty({
    description: 'Procedimentos realizados neste plano',
    type: [CompletedProcedureDto],
  })
  procedures: CompletedProcedureDto[];

  @ApiProperty({ description: 'Data de criação do plano' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização do plano' })
  updatedAt: Date;
}

export class SuggestionHistoryDto {
  @ApiProperty({ description: 'ID único da sugestão' })
  id: string;

  @ApiProperty({ description: 'Status da sugestão', enum: SuggestionStatus })
  status: SuggestionStatus;

  @ApiProperty({
    description: 'Raciocínio da IA para a sugestão',
    required: false,
  })
  iaReasoning: string | null;

  @ApiProperty({
    description: 'Comentário humano sobre a sugestão',
    required: false,
  })
  humanComment: string | null;

  @ApiProperty({
    description: 'Procedimentos sugeridos',
    type: [SuggestionProcedureDto],
  })
  procedures: SuggestionProcedureDto[];

  @ApiProperty({ description: 'Data de criação da sugestão' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização da sugestão' })
  updatedAt: Date;
}

export class ApprovedSuggestionResponseDto {
  @ApiProperty({ description: 'ID único da sugestão aprovada' })
  id: string;

  @ApiProperty({ description: 'ID do paciente associado à sugestão' })
  patientId: number;

  @ApiProperty({
    description: 'Informações completas do paciente',
    type: PatientInfoDto,
  })
  patient: PatientInfoDto;

  @ApiProperty({ description: 'Status da sugestão', enum: SuggestionStatus })
  status: SuggestionStatus;

  @ApiProperty({
    description: 'Raciocínio da IA para a sugestão',
    required: false,
  })
  iaReasoning: string | null;

  @ApiProperty({
    description: 'Procedimentos sugeridos aprovados',
    type: [SuggestionProcedureDto],
  })
  procedures: SuggestionProcedureDto[];

  @ApiProperty({
    description: 'Comentário humano sobre a sugestão',
    required: false,
  })
  humanComment: string | null;

  @ApiProperty({
    description: 'Histórico de sugestões anteriores do paciente',
    type: [SuggestionHistoryDto],
  })
  patientSuggestionsHistory: SuggestionHistoryDto[];

  @ApiProperty({
    description:
      'Planos de tratamento do paciente com procedimentos organizados',
    type: [TreatmentPlanDto],
  })
  patientTreatmentPlans: TreatmentPlanDto[];

  @ApiProperty({
    description: 'Procedimentos realizados fora de planos de tratamento',
    type: [CompletedProcedureDto],
  })
  patientAdditionalProcedures: CompletedProcedureDto[];

  @ApiProperty({ description: 'Data de criação da sugestão' })
  createdAt: Date;

  @ApiProperty({ description: 'Data da última atualização da sugestão' })
  updatedAt: Date;
}
