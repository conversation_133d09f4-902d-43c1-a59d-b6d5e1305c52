import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SuggestionStatus } from '../entities/suggestion-status.enum';
import { SuggestedProcedureDto } from './suggested-procedure.dto';

export class UpdateSuggestionDto {
  @ApiProperty({
    description: 'Status da sugestão',
    enum: SuggestionStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(SuggestionStatus, {
    message: 'Status inválido',
  })
  status?: SuggestionStatus;

  @ApiProperty({
    description: 'Raciocínio da IA para a sugestão',
    required: false,
    example:
      'Com base na análise dos exames e histórico do paciente, recomendo...',
  })
  @IsOptional()
  @IsString({ message: 'O raciocínio da IA deve ser uma string' })
  iaReasoning?: string;

  @ApiProperty({
    description: 'Procedimentos sugeridos',
    type: [SuggestedProcedureDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Os procedimentos sugeridos devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => SuggestedProcedureDto)
  procedures?: SuggestedProcedureDto[];

  @ApiProperty({
    description: 'Comentário humano sobre a sugestão',
    required: false,
    example: 'Concordo com a sugestão da IA',
  })
  @IsOptional()
  @IsString({ message: 'O comentário humano deve ser uma string' })
  humanComment?: string;
}
