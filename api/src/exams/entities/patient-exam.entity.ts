import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';

@Entity('patient_exams')
export class PatientExam {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único do exame' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Nome do exame' })
  name: string;

  @Column()
  @ApiProperty({ description: 'URL do arquivo no MinIO' })
  fileUrl: string;

  @Column()
  @ApiProperty({ description: 'Nome original do arquivo' })
  fileName: string;

  @CreateDateColumn({ name: 'uploaded_at' })
  @ApiProperty({ description: 'Data de upload do exame' })
  uploadedAt: Date;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado ao exame' })
  patient: Patient;

  @Column()
  patientId: number;
}
