import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  ParseUUIDPipe,
  ParseIntPipe,
  BadRequestException,
  Body,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { ExamsService } from './exams.service';
import { PatientExam } from './entities/patient-exam.entity';

@ApiTags('exams')
@Controller('exams')
export class ExamsController {
  constructor(private readonly examsService: ExamsService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Fazer upload de um exame' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Arquivo PDF do exame',
        },
        patientId: { type: 'number', example: 1 },
        name: { type: 'string', example: 'Radiografia Panorâmica' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Exame enviado com sucesso',
    type: PatientExam,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadExam(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { patientId: unknown; name: unknown },
  ): Promise<PatientExam> {
    if (!file) {
      throw new BadRequestException('Arquivo não fornecido');
    }

    // Converter patientId para número
    const patientId = parseInt(String(body.patientId), 10);
    if (isNaN(patientId)) {
      throw new BadRequestException('O ID do paciente deve ser um número');
    }

    return this.examsService.uploadExam(file, patientId, String(body.name));
  }

  @Get('patients/:id/exams')
  @ApiOperation({ summary: 'Listar todos os exames de um paciente' })
  @ApiParam({ name: 'id', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de exames do paciente',
    type: [PatientExam],
  })
  async findAllByPatient(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PatientExam[]> {
    return this.examsService.findAllByPatient(id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter detalhes de um exame' })
  @ApiParam({ name: 'id', description: 'ID do exame' })
  @ApiResponse({
    status: 200,
    description: 'Detalhes do exame',
    type: PatientExam,
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<PatientExam> {
    return this.examsService.findOne(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover um exame' })
  @ApiParam({ name: 'id', description: 'ID do exame' })
  @ApiResponse({
    status: 200,
    description: 'Exame removido com sucesso',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.examsService.remove(id);
  }
}
