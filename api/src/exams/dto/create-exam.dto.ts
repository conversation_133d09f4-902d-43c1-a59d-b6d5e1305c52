import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateExamDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  patientId: number;

  @ApiProperty({
    description: 'Nome do exame',
    example: 'Radiografia Panorâmica',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}
