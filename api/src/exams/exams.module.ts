import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExamsService } from './exams.service';
import { <PERSON>amsController } from './exams.controller';
import { PatientExam } from './entities/patient-exam.entity';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsModule } from '../patients/patients.module';
import { StorageModule } from '../common/storage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PatientExam, Patient]),
    PatientsModule,
    StorageModule,
  ],
  controllers: [ExamsController],
  providers: [ExamsService],
  exports: [ExamsService],
})
export class ExamsModule {}
