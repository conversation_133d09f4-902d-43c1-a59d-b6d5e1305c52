import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Inject,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PatientExam } from './entities/patient-exam.entity';
import { Patient } from '../patients/entities/patient.entity';
import { IStorageProvider } from '../common/interfaces/storage.interface';
import { FileValidator } from '../common/validators/file.validator';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class ExamsService {
  private readonly logger = new Logger(ExamsService.name);

  constructor(
    @InjectRepository(PatientExam)
    private patientExamRepository: Repository<PatientExam>,
    @InjectRepository(Patient)
    private patientRepository: Repository<Patient>,
    @Inject('StorageProvider')
    private storageProvider: IStorageProvider,
    private fileValidator: FileValidator,
  ) {}

  async uploadExam(
    file: Express.Multer.File,
    patientId: number,
    name: string,
  ): Promise<PatientExam> {
    try {
      this.logger.log(`Uploading exam for patient ${patientId}`);

      // Validar o arquivo (apenas PDF e tamanho máximo)
      const validationError = this.fileValidator.validatePdfOnly(file);
      if (validationError) {
        throw new BadRequestException(validationError);
      }

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Fazer upload do arquivo para o MinIO
      const filePath = await this.storageProvider.uploadFile(
        file,
        `patient-exams/${patientId}`,
      );

      // Decodificar o nome do arquivo para garantir que caracteres especiais sejam exibidos corretamente
      const decodedFileName = Buffer.from(file.originalname, 'latin1').toString(
        'utf8',
      );

      // Criar a entrada no banco de dados
      const exam = this.patientExamRepository.create({
        name,
        fileUrl: filePath,
        fileName: decodedFileName,
        patientId,
      });

      await this.patientExamRepository.save(exam);

      // Gerar URL temporária para o arquivo
      exam.fileUrl = await this.storageProvider.getFileUrl(exam.fileUrl);

      return exam;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error uploading exam for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao fazer upload do exame',
      );
    }
  }

  async findAllByPatient(patientId: number): Promise<PatientExam[]> {
    try {
      this.logger.log(`Finding all exams for patient ${patientId}`);

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar todos os exames do paciente
      const exams = await this.patientExamRepository.find({
        where: { patientId },
        order: { uploadedAt: 'DESC' },
      });

      // Gerar URLs temporárias para os exames
      for (const exam of exams) {
        exam.fileUrl = await this.storageProvider.getFileUrl(exam.fileUrl);
      }

      return exams;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding exams for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao buscar os exames do paciente',
      );
    }
  }

  async findOne(id: string): Promise<PatientExam> {
    try {
      this.logger.log(`Finding exam with ID ${id}`);

      const exam = await this.patientExamRepository.findOne({
        where: { id },
      });

      if (!exam) {
        throw new NotFoundException(`Exame com ID ${id} não encontrado`);
      }

      // Gerar URL temporária para o exame
      exam.fileUrl = await this.storageProvider.getFileUrl(exam.fileUrl);

      return exam;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding exam with ID ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao buscar o exame',
      );
    }
  }

  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Removing exam with ID ${id}`);

      const exam = await this.patientExamRepository.findOne({
        where: { id },
      });

      if (!exam) {
        throw new NotFoundException(`Exame com ID ${id} não encontrado`);
      }

      // Remover o arquivo do MinIO
      await this.storageProvider.deleteFile(exam.fileUrl);

      // Remover a entrada do banco de dados
      await this.patientExamRepository.remove(exam);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing exam with ID ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao remover o exame',
      );
    }
  }
}
