import { Injectable } from '@nestjs/common';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { join } from 'path';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  createTypeOrmOptions(): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    return {
      type: 'mysql',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT as string),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      entities: [join(__dirname, '..', '**/*.entity.{ts,js}')],
      migrations: [join(__dirname, '..', './database/migrations/*.{ts,js}')],
      namingStrategy: new SnakeNamingStrategy(),
      synchronize: false, // NUNCA true em produção
      charset: 'utf8mb4',
      logging:
        process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
    };
  }
}
