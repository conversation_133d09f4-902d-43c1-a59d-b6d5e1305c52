import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsIncompleteToPatient1749835521439
  implements MigrationInterface
{
  name = 'AddIsIncompleteToPatient1749835521439';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`patient\` ADD \`is_incomplete\` tinyint NOT NULL DEFAULT 0`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`patient\` DROP COLUMN \`is_incomplete\``,
    );
  }
}
