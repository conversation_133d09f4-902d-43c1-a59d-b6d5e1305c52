import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchemas1749701225492 implements MigrationInterface {
  name = 'InitialSchemas1749701225492';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`procedures\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(100) NOT NULL, \`description\` text NULL, \`default_price\` decimal(10,2) NOT NULL DEFAULT '0.00', \`estimated_duration\` int NOT NULL DEFAULT '30', \`type\` enum ('CLINICAL', 'SURGICAL', 'AESTHETIC', 'ORTHODONTIC', 'ENDODONTIC', 'PERIODONTIC', 'PEDIATRIC', 'RADIOLOGY', 'PROSTHODONTIC') NOT NULL, \`status\` enum ('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE', \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`budget_items\` (\`id\` int NOT NULL AUTO_INCREMENT, \`budget_id\` int NOT NULL, \`procedure_id\` int NOT NULL, \`tooth\` varchar(255) NULL, \`executing_dentist_id\` int NOT NULL, \`value\` decimal(10,2) NOT NULL, \`budgetId\` int NULL, \`procedureId\` int NULL, \`executingDentistId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`budgets\` (\`id\` int NOT NULL AUTO_INCREMENT, \`patient_id\` int NOT NULL, \`dentist_id\` int NOT NULL, \`notes\` text NULL, \`status\` enum ('open', 'approved', 'cancelled') NOT NULL DEFAULT 'open', \`total_value\` decimal(10,2) NOT NULL DEFAULT '0.00', \`amount_paid\` decimal(10,2) NOT NULL DEFAULT '0.00', \`discount\` decimal(10,2) NOT NULL DEFAULT '0.00', \`discount_type\` enum ('percentage', 'fixed', 'none') NOT NULL DEFAULT 'none', \`payment_method\` enum ('to_define', 'cash', 'credit_card', 'debit_card', 'bank_slip', 'transfer', 'pix', 'check', 'multiple') NOT NULL DEFAULT 'to_define', \`installments\` int NOT NULL DEFAULT '1', \`created_by\` varchar(255) NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`patientId\` int NULL, \`dentistId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`dentists\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(100) NOT NULL, \`cro\` varchar(20) NOT NULL, \`specialty\` varchar(100) NOT NULL, \`phone\` varchar(20) NOT NULL, \`email\` varchar(100) NOT NULL, \`notes\` varchar(255) NULL, \`active\` tinyint NOT NULL DEFAULT 1, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_19d295b475f2b0b5563c9fba74\` (\`cro\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`scheduling\` (\`id\` int NOT NULL AUTO_INCREMENT, \`patient_id\` int NOT NULL, \`date\` date NOT NULL, \`time\` time NOT NULL, \`duration\` int NULL, \`end_time\` time NULL, \`dentist_id\` int NULL, \`status\` enum ('confirmed', 'unconfirmed', 'late', 'no-show', 'cancelled', 'rescheduled', 'in-progress', 'completed', 'scheduled-unconfirmed', 'scheduled-confirmed', 'unscheduled') NOT NULL DEFAULT 'unconfirmed', \`notes\` text NULL, \`cost\` decimal(10,2) NOT NULL DEFAULT '0.00', \`paid\` tinyint NOT NULL DEFAULT 0, \`treatment_id\` int NULL, \`treatment_plan_id\` int NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patientId\` int NULL, \`dentistId\` int NULL, \`treatmentPlanId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`patient_types\` (\`id\` int NOT NULL AUTO_INCREMENT, \`nome\` varchar(100) NOT NULL, \`descricao\` varchar(255) NULL, \`cor\` varchar(7) NOT NULL, \`ativo\` tinyint NOT NULL DEFAULT 1, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`receipts\` (\`id\` int NOT NULL AUTO_INCREMENT, \`date\` date NOT NULL, \`number\` varchar(255) NOT NULL, \`edited_number\` varchar(255) NULL, \`beneficiary\` varchar(255) NOT NULL, \`beneficiary_cpf\` varchar(255) NOT NULL, \`responsible\` varchar(255) NOT NULL, \`responsible_cpf\` varchar(255) NOT NULL, \`observations\` text NULL, \`professional\` varchar(255) NOT NULL, \`professional_cpf\` varchar(255) NOT NULL, \`description\` varchar(255) NOT NULL, \`value\` decimal(10,2) NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`patient_id\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`patient\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`birth_date\` date NOT NULL, \`gender\` enum ('male', 'female', 'other') NULL, \`cpf\` varchar(255) NOT NULL, \`how_did_you_find_us\` varchar(255) NULL, \`registration_date\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`notes\` text NULL, \`email\` varchar(255) NOT NULL, \`phone\` varchar(255) NOT NULL, \`whatsapp\` varchar(255) NULL, \`address_zip_code\` varchar(255) NULL, \`address_street\` varchar(255) NULL, \`address_number\` varchar(255) NULL, \`address_neighborhood\` varchar(255) NULL, \`address_city\` varchar(255) NULL, \`address_state\` varchar(255) NULL, \`address_complement\` varchar(255) NULL, \`profession\` varchar(255) NULL, \`medical_record_number\` varchar(255) NULL, \`category\` enum ('Urgente', 'Rotina', 'Follow-up') NOT NULL DEFAULT 'Rotina', \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`patientTypeId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`treatment_plans\` (\`id\` int NOT NULL AUTO_INCREMENT, \`total_value\` decimal(10,2) NOT NULL DEFAULT '0.00', \`completion_percentage\` decimal(5,2) NOT NULL DEFAULT '0.00', \`status\` enum ('open', 'completed', 'cancelled') NOT NULL DEFAULT 'open', \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`patientId\` int NULL, \`budgetId\` int NULL, \`dentistId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`treatment_procedures\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`value\` decimal(10,2) NOT NULL, \`tooth\` varchar(255) NULL, \`execution_date\` datetime NULL, \`status\` enum ('pending', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending', \`notes\` text NULL, \`next_visit_details\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`treatmentPlanId\` int NULL, \`procedureId\` int NULL, \`professionalId\` int NULL, \`appointmentId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`treatment_flow_step\` (\`id\` int NOT NULL AUTO_INCREMENT, \`from_procedure_id\` int NOT NULL, \`to_procedure_id\` int NOT NULL, \`min_days_after\` int NOT NULL, \`notes\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`employees\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(100) NOT NULL, \`email\` varchar(255) NOT NULL, \`phone\` varchar(255) NULL, \`type\` enum ('AUXILIAR_SAUDE_BUCAL', 'TECNICO_SAUDE_BUCAL', 'RECEPCIONISTA', 'SECRETARIA_ADMINISTRATIVA', 'GERENTE_CLINICA', 'FINANCEIRO', 'SERVICOS_GERAIS', 'SUPORTE_TECNICO', 'MARKETING') NOT NULL, \`birth_date\` date NULL, \`cpf\` varchar(255) NOT NULL, \`admission_date\` date NOT NULL, \`notes\` text NULL, \`is_active\` tinyint NOT NULL DEFAULT 1, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_765bc1ac8967533a04c74a9f6a\` (\`email\`), UNIQUE INDEX \`IDX_0ac9216832e4dda06946c37cb7\` (\`cpf\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`tasks\` (\`id\` varchar(36) NOT NULL, \`title\` varchar(255) NOT NULL, \`description\` text NULL, \`status\` enum ('TODO', 'IN_PROGRESS', 'WAITING', 'DONE', 'CANCELLED') NOT NULL DEFAULT 'TODO', \`priority\` enum ('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM', \`due_date\` date NULL, \`completed_at\` timestamp NULL, \`sector\` enum ('ATTENDANCE', 'CLINICAL', 'BIOSECURITY', 'RADIOLOGY', 'FINANCIAL', 'COMMERCIAL', 'MARKETING', 'HUMAN_RESOURCES', 'ADMINISTRATION', 'IT_SUPPORT', 'SUPPLIES') NOT NULL DEFAULT 'ADMINISTRATION', \`tags\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`suggestion_procedures\` (\`id\` varchar(36) NOT NULL, \`expected_date\` date NULL, \`notes\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`suggestionId\` varchar(36) NULL, \`procedureId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`suggestions\` (\`id\` varchar(36) NOT NULL, \`patientId\` int NOT NULL, \`status\` enum ('IN_ANALYSIS', 'PENDING_REVIEW', 'REJECTED', 'APPROVED') NOT NULL DEFAULT 'IN_ANALYSIS', \`ia_reasoning\` text NULL, \`human_comment\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`photo_folders\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`patient_id\` int NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patientId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`patient_photos\` (\`id\` varchar(36) NOT NULL, \`url\` varchar(255) NOT NULL, \`filename\` varchar(255) NOT NULL, \`caption\` varchar(500) NULL, \`order\` int NOT NULL DEFAULT '0', \`uploaded_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patient_id\` int NOT NULL, \`folder_id\` varchar(255) NULL, \`patientId\` int NULL, \`folderId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`lead_forms\` (\`id\` varchar(36) NOT NULL, \`full_name\` varchar(255) NOT NULL, \`cpf\` varchar(255) NOT NULL, \`phone\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, \`cep\` varchar(255) NOT NULL, \`street\` varchar(255) NOT NULL, \`number\` varchar(255) NOT NULL, \`neighborhood\` varchar(255) NOT NULL, \`city\` varchar(255) NOT NULL, \`state\` varchar(255) NOT NULL, \`past_treatments\` text NOT NULL, \`last_procedure_time\` enum ('<6meses', '1ano', '>2anos') NOT NULL, \`interested_treatment\` text NOT NULL, \`wants_free_evaluation\` tinyint NULL DEFAULT 1, \`wants_promotions\` tinyint NULL DEFAULT 1, \`best_contact_time\` varchar(255) NOT NULL, \`referral_source\` varchar(255) NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`is_existing_patient\` tinyint NOT NULL DEFAULT 0, \`patient_id\` int NULL, \`has_updates_available\` tinyint NOT NULL DEFAULT 0, \`fields_to_update\` text NULL, \`patientId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`medical_record\` (\`id\` int NOT NULL AUTO_INCREMENT, \`patient_id\` int NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`exam_file_path\` varchar(255) NULL, \`patientId\` int NULL, UNIQUE INDEX \`REL_b53c9d9d9741bac9726574f34f\` (\`patientId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`indications\` (\`id\` int NOT NULL AUTO_INCREMENT, \`date\` date NOT NULL, \`status\` enum ('pending', 'confirmed', 'rejected') NOT NULL DEFAULT 'pending', \`observation\` text NULL, \`tags\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`indicatedPatientId\` int NULL, \`referredById\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`holidays\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`date\` date NOT NULL, \`description\` text NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`patient_documents\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`file_url\` varchar(255) NOT NULL, \`file_name\` varchar(255) NOT NULL, \`uploaded_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patient_id\` int NOT NULL, \`patientId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`patient_exams\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`file_url\` varchar(255) NOT NULL, \`file_name\` varchar(255) NOT NULL, \`uploaded_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patient_id\` int NOT NULL, \`patientId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`notifications\` (\`id\` varchar(36) NOT NULL, \`title\` varchar(255) NOT NULL, \`message\` text NOT NULL, \`type\` enum ('GERAL', 'LEMBRETE DE TAREFA', 'TAREFA ATUALIZADA', 'TAREFA ATRIBUÍDA', 'TAREFA CONCLUÍDA', 'AGENDAMENTO', 'SYSTEM', 'TAREFA PRÓXIMA DO PRAZO') NOT NULL DEFAULT 'GERAL', \`read\` tinyint NOT NULL DEFAULT 0, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`employee_id\` varchar(36) NULL, \`task_id\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`dentist_schedules\` (\`id\` int NOT NULL AUTO_INCREMENT, \`dentist_id\` int NOT NULL, \`weekly_schedule\` json NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`dentistId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`dentist_exceptions\` (\`id\` int NOT NULL AUTO_INCREMENT, \`dentist_id\` int NOT NULL, \`date\` date NOT NULL, \`type\` enum ('day-off', 'custom-hours') NOT NULL, \`custom_hours\` json NULL, \`reason\` varchar(255) NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`dentistId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`anamnesis_questions\` (\`id\` int NOT NULL AUTO_INCREMENT, \`title\` varchar(255) NOT NULL, \`type\` enum ('text', 'boolean', 'options') NOT NULL DEFAULT 'text', \`order\` int NOT NULL DEFAULT '0', \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`anamnesis_answers\` (\`id\` int NOT NULL AUTO_INCREMENT, \`anamnesis_id\` varchar(255) NOT NULL, \`question_id\` int NOT NULL, \`question_text\` text NOT NULL, \`answer\` text NOT NULL, \`anamnesisId\` varchar(36) NULL, \`questionId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`anamnesis\` (\`id\` varchar(36) NOT NULL, \`patient_id\` int NOT NULL, \`employee_id\` varchar(255) NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`patientId\` int NULL, \`employeeId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`appointment_observations\` (\`id\` varchar(36) NOT NULL, \`note\` text NOT NULL, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`appointmentId\` int NULL, \`patientId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`appointment_categories\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(100) NOT NULL, \`description\` text NULL, \`color\` varchar(7) NOT NULL, \`is_active\` tinyint NOT NULL DEFAULT 1, \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` ADD CONSTRAINT \`FK_1160fb85bb3cb492ac954b491a9\` FOREIGN KEY (\`budgetId\`) REFERENCES \`budgets\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` ADD CONSTRAINT \`FK_411300df63f1aeb38b13bd1d27e\` FOREIGN KEY (\`procedureId\`) REFERENCES \`procedures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` ADD CONSTRAINT \`FK_e265019473ba9415e403f7c2d10\` FOREIGN KEY (\`executingDentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`budgets\` ADD CONSTRAINT \`FK_2d65d94d9782b976322df43c249\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`budgets\` ADD CONSTRAINT \`FK_bc237b86241f83e62d334c65be8\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_95c99eef84baad1e5c7fe58cbe7\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_0b14f7b628b3c86a8c8be6b7652\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_6520b1bc652015c430da0079752\` FOREIGN KEY (\`treatmentPlanId\`) REFERENCES \`treatment_plans\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`receipts\` ADD CONSTRAINT \`FK_1ffb599610fa0b41e084a31a904\` FOREIGN KEY (\`patient_id\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient\` ADD CONSTRAINT \`FK_779d099507dbe96cd5502203dce\` FOREIGN KEY (\`patientTypeId\`) REFERENCES \`patient_types\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` ADD CONSTRAINT \`FK_9fa6386e5779e9e7187068660fa\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` ADD CONSTRAINT \`FK_8b05ceddacc117dfa9e02020088\` FOREIGN KEY (\`budgetId\`) REFERENCES \`budgets\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` ADD CONSTRAINT \`FK_0924daa7448f8369482d3b4fc2c\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` ADD CONSTRAINT \`FK_788a585f19f6d7346cdc7cbac2e\` FOREIGN KEY (\`treatmentPlanId\`) REFERENCES \`treatment_plans\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` ADD CONSTRAINT \`FK_5dbb39a62c6d851f311d381b58a\` FOREIGN KEY (\`procedureId\`) REFERENCES \`procedures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` ADD CONSTRAINT \`FK_d40717ba12dc530863befab8187\` FOREIGN KEY (\`professionalId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` ADD CONSTRAINT \`FK_131e6a73ccb5c94c8b09ebce79b\` FOREIGN KEY (\`appointmentId\`) REFERENCES \`scheduling\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_flow_step\` ADD CONSTRAINT \`FK_6a67441584f2ff2ecba1bfebe96\` FOREIGN KEY (\`from_procedure_id\`) REFERENCES \`procedures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_flow_step\` ADD CONSTRAINT \`FK_8e08baee31ec67eaaf8cb9ac620\` FOREIGN KEY (\`to_procedure_id\`) REFERENCES \`procedures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD CONSTRAINT \`FK_af5d6d0f88d2002d9e9b0d12a4b\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE SET NULL ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestion_procedures\` ADD CONSTRAINT \`FK_c6daef41bb4bc63f2fc419832f1\` FOREIGN KEY (\`suggestionId\`) REFERENCES \`suggestions\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestion_procedures\` ADD CONSTRAINT \`FK_68c24cb4a5112bc8ab69a42fc68\` FOREIGN KEY (\`procedureId\`) REFERENCES \`procedures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestions\` ADD CONSTRAINT \`FK_dc604c8aac827f32926915ad836\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`photo_folders\` ADD CONSTRAINT \`FK_230220fe60943dff3fdc0cc9940\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` ADD CONSTRAINT \`FK_33b97db6b7df3deaf8d5879c2cd\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` ADD CONSTRAINT \`FK_3e977b4a6bb18efdee51cf15178\` FOREIGN KEY (\`folderId\`) REFERENCES \`photo_folders\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`lead_forms\` ADD CONSTRAINT \`FK_87dac7265c6525cf6e87e276619\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`medical_record\` ADD CONSTRAINT \`FK_b53c9d9d9741bac9726574f34f7\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`indications\` ADD CONSTRAINT \`FK_cd364f123891111856e6aab871f\` FOREIGN KEY (\`indicatedPatientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`indications\` ADD CONSTRAINT \`FK_f45f1f603942594ff70476ebc90\` FOREIGN KEY (\`referredById\`) REFERENCES \`patient\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_documents\` ADD CONSTRAINT \`FK_a7424418f442e66865f2b8e35f6\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_exams\` ADD CONSTRAINT \`FK_1f34f0ad5188e91f15b732bc208\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notifications\` ADD CONSTRAINT \`FK_d59afae1b9c6b8d9a17548e014f\` FOREIGN KEY (\`employee_id\`) REFERENCES \`employees\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notifications\` ADD CONSTRAINT \`FK_b4a7cd30c9f4ca1b23ef0eb6dd8\` FOREIGN KEY (\`task_id\`) REFERENCES \`tasks\`(\`id\`) ON DELETE SET NULL ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`dentist_schedules\` ADD CONSTRAINT \`FK_8562d3f108593f4ec8787ad4ef7\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`dentist_exceptions\` ADD CONSTRAINT \`FK_d0baa71d167b7973ccb76ab5108\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis_answers\` ADD CONSTRAINT \`FK_019cd841b8a0ae92ccb935570f9\` FOREIGN KEY (\`anamnesisId\`) REFERENCES \`anamnesis\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis_answers\` ADD CONSTRAINT \`FK_2cb8c9e4e766a808bd4dc976d1f\` FOREIGN KEY (\`questionId\`) REFERENCES \`anamnesis_questions\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis\` ADD CONSTRAINT \`FK_034d9e2fa49d2717754a1fa6cc6\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis\` ADD CONSTRAINT \`FK_e8ce31066571642901ed87f9229\` FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`appointment_observations\` ADD CONSTRAINT \`FK_3e4bd7b9059789099147dacb7a4\` FOREIGN KEY (\`appointmentId\`) REFERENCES \`scheduling\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`appointment_observations\` ADD CONSTRAINT \`FK_1d8677ae9a7d42d7d2346d5e9d9\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`appointment_observations\` DROP FOREIGN KEY \`FK_1d8677ae9a7d42d7d2346d5e9d9\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`appointment_observations\` DROP FOREIGN KEY \`FK_3e4bd7b9059789099147dacb7a4\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis\` DROP FOREIGN KEY \`FK_e8ce31066571642901ed87f9229\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis\` DROP FOREIGN KEY \`FK_034d9e2fa49d2717754a1fa6cc6\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis_answers\` DROP FOREIGN KEY \`FK_2cb8c9e4e766a808bd4dc976d1f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`anamnesis_answers\` DROP FOREIGN KEY \`FK_019cd841b8a0ae92ccb935570f9\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`dentist_exceptions\` DROP FOREIGN KEY \`FK_d0baa71d167b7973ccb76ab5108\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`dentist_schedules\` DROP FOREIGN KEY \`FK_8562d3f108593f4ec8787ad4ef7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`notifications\` DROP FOREIGN KEY \`FK_b4a7cd30c9f4ca1b23ef0eb6dd8\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`notifications\` DROP FOREIGN KEY \`FK_d59afae1b9c6b8d9a17548e014f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_exams\` DROP FOREIGN KEY \`FK_1f34f0ad5188e91f15b732bc208\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_documents\` DROP FOREIGN KEY \`FK_a7424418f442e66865f2b8e35f6\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`indications\` DROP FOREIGN KEY \`FK_f45f1f603942594ff70476ebc90\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`indications\` DROP FOREIGN KEY \`FK_cd364f123891111856e6aab871f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`medical_record\` DROP FOREIGN KEY \`FK_b53c9d9d9741bac9726574f34f7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`lead_forms\` DROP FOREIGN KEY \`FK_87dac7265c6525cf6e87e276619\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` DROP FOREIGN KEY \`FK_3e977b4a6bb18efdee51cf15178\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` DROP FOREIGN KEY \`FK_33b97db6b7df3deaf8d5879c2cd\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`photo_folders\` DROP FOREIGN KEY \`FK_230220fe60943dff3fdc0cc9940\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestions\` DROP FOREIGN KEY \`FK_dc604c8aac827f32926915ad836\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestion_procedures\` DROP FOREIGN KEY \`FK_68c24cb4a5112bc8ab69a42fc68\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`suggestion_procedures\` DROP FOREIGN KEY \`FK_c6daef41bb4bc63f2fc419832f1\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks\` DROP FOREIGN KEY \`FK_af5d6d0f88d2002d9e9b0d12a4b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_flow_step\` DROP FOREIGN KEY \`FK_8e08baee31ec67eaaf8cb9ac620\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_flow_step\` DROP FOREIGN KEY \`FK_6a67441584f2ff2ecba1bfebe96\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` DROP FOREIGN KEY \`FK_131e6a73ccb5c94c8b09ebce79b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` DROP FOREIGN KEY \`FK_d40717ba12dc530863befab8187\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` DROP FOREIGN KEY \`FK_5dbb39a62c6d851f311d381b58a\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_procedures\` DROP FOREIGN KEY \`FK_788a585f19f6d7346cdc7cbac2e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` DROP FOREIGN KEY \`FK_0924daa7448f8369482d3b4fc2c\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` DROP FOREIGN KEY \`FK_8b05ceddacc117dfa9e02020088\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`treatment_plans\` DROP FOREIGN KEY \`FK_9fa6386e5779e9e7187068660fa\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient\` DROP FOREIGN KEY \`FK_779d099507dbe96cd5502203dce\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`receipts\` DROP FOREIGN KEY \`FK_1ffb599610fa0b41e084a31a904\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_6520b1bc652015c430da0079752\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_0b14f7b628b3c86a8c8be6b7652\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_95c99eef84baad1e5c7fe58cbe7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`budgets\` DROP FOREIGN KEY \`FK_bc237b86241f83e62d334c65be8\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`budgets\` DROP FOREIGN KEY \`FK_2d65d94d9782b976322df43c249\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` DROP FOREIGN KEY \`FK_e265019473ba9415e403f7c2d10\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` DROP FOREIGN KEY \`FK_411300df63f1aeb38b13bd1d27e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`budget_items\` DROP FOREIGN KEY \`FK_1160fb85bb3cb492ac954b491a9\``,
    );
    await queryRunner.query(`DROP TABLE \`appointment_categories\``);
    await queryRunner.query(`DROP TABLE \`appointment_observations\``);
    await queryRunner.query(`DROP TABLE \`anamnesis\``);
    await queryRunner.query(`DROP TABLE \`anamnesis_answers\``);
    await queryRunner.query(`DROP TABLE \`anamnesis_questions\``);
    await queryRunner.query(`DROP TABLE \`dentist_exceptions\``);
    await queryRunner.query(`DROP TABLE \`dentist_schedules\``);
    await queryRunner.query(`DROP TABLE \`notifications\``);
    await queryRunner.query(`DROP TABLE \`patient_exams\``);
    await queryRunner.query(`DROP TABLE \`patient_documents\``);
    await queryRunner.query(`DROP TABLE \`holidays\``);
    await queryRunner.query(`DROP TABLE \`indications\``);
    await queryRunner.query(
      `DROP INDEX \`REL_b53c9d9d9741bac9726574f34f\` ON \`medical_record\``,
    );
    await queryRunner.query(`DROP TABLE \`medical_record\``);
    await queryRunner.query(`DROP TABLE \`lead_forms\``);
    await queryRunner.query(`DROP TABLE \`patient_photos\``);
    await queryRunner.query(`DROP TABLE \`photo_folders\``);
    await queryRunner.query(`DROP TABLE \`suggestions\``);
    await queryRunner.query(`DROP TABLE \`suggestion_procedures\``);
    await queryRunner.query(`DROP TABLE \`tasks\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_0ac9216832e4dda06946c37cb7\` ON \`employees\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_765bc1ac8967533a04c74a9f6a\` ON \`employees\``,
    );
    await queryRunner.query(`DROP TABLE \`employees\``);
    await queryRunner.query(`DROP TABLE \`treatment_flow_step\``);
    await queryRunner.query(`DROP TABLE \`treatment_procedures\``);
    await queryRunner.query(`DROP TABLE \`treatment_plans\``);
    await queryRunner.query(`DROP TABLE \`patient\``);
    await queryRunner.query(`DROP TABLE \`receipts\``);
    await queryRunner.query(`DROP TABLE \`patient_types\``);
    await queryRunner.query(`DROP TABLE \`scheduling\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_19d295b475f2b0b5563c9fba74\` ON \`dentists\``,
    );
    await queryRunner.query(`DROP TABLE \`dentists\``);
    await queryRunner.query(`DROP TABLE \`budgets\``);
    await queryRunner.query(`DROP TABLE \`budget_items\``);
    await queryRunner.query(`DROP TABLE \`procedures\``);
  }
}
