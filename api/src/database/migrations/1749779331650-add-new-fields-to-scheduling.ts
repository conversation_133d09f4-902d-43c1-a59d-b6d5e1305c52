import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNewFieldsToScheduling1749779331650
  implements MigrationInterface
{
  name = 'AddN<PERSON>FieldsToScheduling1749779331650';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_0b14f7b628b3c86a8c8be6b7652\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_6520b1bc652015c430da0079752\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_95c99eef84baad1e5c7fe58cbe7\``,
    );
    await queryRunner.query(
      `CREATE TABLE \`scheduling_procedures\` (\`scheduling_id\` int NOT NULL, \`procedure_id\` int NOT NULL, INDEX \`IDX_9b66098847a8608f3477bc387d\` (\`scheduling_id\`), INDEX \`IDX_2a6fb0bb6ba890b1cf61083271\` (\`procedure_id\`), PRIMARY KEY (\`scheduling_id\`, \`procedure_id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(`ALTER TABLE \`scheduling\` DROP COLUMN \`cost\``);
    await queryRunner.query(`ALTER TABLE \`scheduling\` DROP COLUMN \`paid\``);
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`treatment_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`treatment_plan_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`patientId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`dentistId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`treatmentPlanId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`email\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`phone\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`is_first_appointment\` tinyint NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`appointment_category_id\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`scheduledById\` varchar(36) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` CHANGE \`status\` \`status\` enum ('confirmed', 'unconfirmed', 'late', 'no-show', 'cancelled', 'rescheduled', 'in-progress', 'completed') NOT NULL DEFAULT 'unconfirmed'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` CHANGE \`patient_id\` \`patient_id\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` DROP COLUMN \`folder_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` ADD \`folder_id\` varchar(36) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_0b18819ca37f41eb54805bbffff\` FOREIGN KEY (\`patient_id\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_bdcc7ae6f2154eee79e0844ef66\` FOREIGN KEY (\`dentist_id\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_8c117b2251ccb12d3b4362936f4\` FOREIGN KEY (\`appointment_category_id\`) REFERENCES \`appointment_categories\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_e7eda27b9cf825af8fed85f0d5a\` FOREIGN KEY (\`scheduledById\`) REFERENCES \`employees\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling_procedures\` ADD CONSTRAINT \`FK_9b66098847a8608f3477bc387d5\` FOREIGN KEY (\`scheduling_id\`) REFERENCES \`scheduling\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling_procedures\` ADD CONSTRAINT \`FK_2a6fb0bb6ba890b1cf610832717\` FOREIGN KEY (\`procedure_id\`) REFERENCES \`procedures\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`scheduling_procedures\` DROP FOREIGN KEY \`FK_2a6fb0bb6ba890b1cf610832717\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling_procedures\` DROP FOREIGN KEY \`FK_9b66098847a8608f3477bc387d5\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_e7eda27b9cf825af8fed85f0d5a\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_8c117b2251ccb12d3b4362936f4\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_bdcc7ae6f2154eee79e0844ef66\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP FOREIGN KEY \`FK_0b18819ca37f41eb54805bbffff\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` DROP COLUMN \`folder_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`patient_photos\` ADD \`folder_id\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` CHANGE \`patient_id\` \`patient_id\` int NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` CHANGE \`status\` \`status\` enum ('confirmed', 'unconfirmed', 'late', 'no-show', 'cancelled', 'rescheduled', 'in-progress', 'completed', 'scheduled-unconfirmed', 'scheduled-confirmed', 'unscheduled') NOT NULL DEFAULT 'unconfirmed'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`scheduledById\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`appointment_category_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`updated_at\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` DROP COLUMN \`is_first_appointment\``,
    );
    await queryRunner.query(`ALTER TABLE \`scheduling\` DROP COLUMN \`phone\``);
    await queryRunner.query(`ALTER TABLE \`scheduling\` DROP COLUMN \`email\``);
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`treatmentPlanId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`dentistId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`patientId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`treatment_plan_id\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`treatment_id\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`paid\` tinyint NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD \`cost\` decimal NOT NULL DEFAULT '0.00'`,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_2a6fb0bb6ba890b1cf61083271\` ON \`scheduling_procedures\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_9b66098847a8608f3477bc387d\` ON \`scheduling_procedures\``,
    );
    await queryRunner.query(`DROP TABLE \`scheduling_procedures\``);
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_95c99eef84baad1e5c7fe58cbe7\` FOREIGN KEY (\`patientId\`) REFERENCES \`patient\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_6520b1bc652015c430da0079752\` FOREIGN KEY (\`treatmentPlanId\`) REFERENCES \`treatment_plans\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`scheduling\` ADD CONSTRAINT \`FK_0b14f7b628b3c86a8c8be6b7652\` FOREIGN KEY (\`dentistId\`) REFERENCES \`dentists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
