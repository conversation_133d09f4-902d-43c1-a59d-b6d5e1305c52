#!/usr/bin/env ts-node

import { SeedRunner } from './utils/seed-runner';
import { DentistsSeed } from './01-dentists.seed';
import { EmployeesSeed } from './02-employees.seed';
import { ProceduresSeed } from './03-procedures.seed';
import { PatientsSeed } from './04-patients.seed';
import { TreatmentFlowSeed } from './05-treatment-flow.seed';
import { AppointmentCategoriesSeed } from './06-appointment-categories.seed';

async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const isReset = args.includes('--reset');

  console.log('🌱 Sistema de Seeds - CRM Odontológico');
  console.log('=====================================');

  if (isReset) {
    console.log('🔄 Modo: Reset (limpar e recriar dados)');
  } else {
    console.log('📦 Modo: Executar seeds (preservar dados existentes)');
  }

  console.log('');

  const seedRunner = new SeedRunner();

  // Adicionar seeds na ordem correta (respeitando dependências)
  seedRunner.addSeed(new DentistsSeed());
  seedRunner.addSeed(new EmployeesSeed());
  seedRunner.addSeed(new ProceduresSeed());
  seedRunner.addSeed(new PatientsSeed());
  seedRunner.addSeed(new TreatmentFlowSeed());
  seedRunner.addSeed(new AppointmentCategoriesSeed());

  try {
    await seedRunner.run(isReset);

    console.log('');
    console.log('✨ Sistema de seeds executado com sucesso!');
    console.log('');
    console.log('📊 Dados criados:');
    console.log('  • Dentistas (6)');
    console.log('  • Funcionários (7)');
    console.log('  • Procedimentos (~16)');
    console.log('  • Pacientes (7)');
    console.log('  • Fluxo de tratamento (sequências de procedimentos)');
    console.log('  • Categorias de agendamento (7)');
    console.log('');
    console.log('🚀 O sistema está pronto para desenvolvimento!');

    process.exit(0);
  } catch (error) {
    console.error('');
    console.error('❌ Erro ao executar seeds:', error);
    console.error('');

    if (error instanceof Error) {
      console.error('Detalhes do erro:', error.message);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }

    process.exit(1);
  }
}

// Executar apenas se este arquivo for chamado diretamente
if (require.main === module) {
  void main();
}

export default main;
