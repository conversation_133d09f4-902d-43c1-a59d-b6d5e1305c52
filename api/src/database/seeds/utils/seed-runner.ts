import { dataConfigMigrations } from 'src/database/migrations-config';
import { DataSource } from 'typeorm';

export interface SeedInterface {
  name: string;
  run(dataSource: DataSource): Promise<void>;
  clear?(dataSource: DataSource): Promise<void>;
}

export class SeedRunner {
  private dataSource: DataSource;
  private seeds: SeedInterface[] = [];

  constructor() {
    this.dataSource = dataConfigMigrations;
  }

  addSeed(seed: SeedInterface): void {
    this.seeds.push(seed);
  }

  async run(reset: boolean = false): Promise<void> {
    try {
      console.log('🌱 Iniciando sistema de seeds...');

      if (!this.dataSource.isInitialized) {
        await this.dataSource.initialize();
        console.log('✅ Conexão com banco de dados estabelecida');
      }

      if (reset) {
        console.log('🧹 Limpando dados existentes...');
        await this.clearAllSeeds();
      }

      console.log(`📦 Executando ${this.seeds.length} seeds...`);

      for (const seed of this.seeds) {
        console.log(`  ⏳ Executando seed: ${seed.name}`);
        await seed.run(this.dataSource);
        console.log(`  ✅ Seed concluído: ${seed.name}`);
      }

      console.log('🎉 Todos os seeds foram executados com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao executar seeds:', error);
      throw error;
    } finally {
      if (this.dataSource.isInitialized) {
        await this.dataSource.destroy();
        console.log('🔌 Conexão com banco de dados encerrada');
      }
    }
  }

  private async clearAllSeeds(): Promise<void> {
    // Executar limpeza na ordem reversa para respeitar foreign keys
    const reversedSeeds = [...this.seeds].reverse();

    for (const seed of reversedSeeds) {
      if (seed.clear) {
        console.log(`  🧹 Limpando dados do seed: ${seed.name}`);
        await seed.clear(this.dataSource);
      }
    }
  }
}

export abstract class BaseSeed implements SeedInterface {
  abstract name: string;
  abstract run(dataSource: DataSource): Promise<void>;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async clear(_dataSource?: DataSource): Promise<void> {
    // Implementação padrão vazia - pode ser sobrescrita
  }

  protected async clearTable(
    dataSource: DataSource,
    tableName: string,
  ): Promise<void> {
    await dataSource.query(`DELETE FROM ${tableName}`);
    console.log(`    🗑️  Tabela ${tableName} limpa`);
  }

  protected async resetAutoIncrement(
    dataSource: DataSource,
    tableName: string,
  ): Promise<void> {
    await dataSource.query(`ALTER TABLE ${tableName} AUTO_INCREMENT = 1`);
  }
}
