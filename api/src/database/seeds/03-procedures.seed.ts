import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import { Procedure } from '../../procedures/entities/procedure.entity';
import { ProcedureType } from '../../procedures/enums/procedure-type.enum';
import { Status } from '../../procedures/enums/status.enum';

export class ProceduresSeed extends BaseSeed {
  name = 'Procedures';

  async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(Procedure);

    // Verificar se já existem dados
    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Procedimentos já existem, pulando...');
      return;
    }

    const procedures = [
      // Procedimentos Clínicos
      {
        name: 'Consulta Inicial',
        description: 'Primeira consulta com avaliação completa',
        defaultPrice: 80.0,
        estimatedDuration: 60,
        type: ProcedureType.CLINICAL,
        status: Status.ACTIVE,
      },
      {
        name: 'Limpeza Dental',
        description: 'Profilaxia e remoção de tártaro',
        defaultPrice: 120.0,
        estimatedDuration: 45,
        type: ProcedureType.CLINICAL,
        status: Status.ACTIVE,
      },
      {
        name: 'Restauração Simples',
        description: 'Restauração em resina composta',
        defaultPrice: 150.0,
        estimatedDuration: 60,
        type: ProcedureType.CLINICAL,
        status: Status.ACTIVE,
      },
      {
        name: 'Restauração Complexa',
        description: 'Restauração extensa em resina composta',
        defaultPrice: 250.0,
        estimatedDuration: 90,
        type: ProcedureType.CLINICAL,
        status: Status.ACTIVE,
      },

      // Procedimentos Endodônticos
      {
        name: 'Tratamento de Canal Unirradicular',
        description: 'Endodontia em dente com uma raiz',
        defaultPrice: 400.0,
        estimatedDuration: 120,
        type: ProcedureType.ENDODONTIC,
        status: Status.ACTIVE,
      },
      {
        name: 'Tratamento de Canal Multirradicular',
        description: 'Endodontia em dente com múltiplas raízes',
        defaultPrice: 600.0,
        estimatedDuration: 150,
        type: ProcedureType.ENDODONTIC,
        status: Status.ACTIVE,
      },

      // Procedimentos Cirúrgicos
      {
        name: 'Extração Simples',
        description: 'Extração dentária simples',
        defaultPrice: 180.0,
        estimatedDuration: 30,
        type: ProcedureType.SURGICAL,
        status: Status.ACTIVE,
      },
      {
        name: 'Extração Complexa',
        description: 'Extração cirúrgica complexa',
        defaultPrice: 350.0,
        estimatedDuration: 60,
        type: ProcedureType.SURGICAL,
        status: Status.ACTIVE,
      },
      {
        name: 'Implante Dentário',
        description: 'Colocação de implante osseointegrado',
        defaultPrice: 1200.0,
        estimatedDuration: 90,
        type: ProcedureType.SURGICAL,
        status: Status.ACTIVE,
      },

      // Procedimentos Ortodônticos
      {
        name: 'Consulta Ortodôntica',
        description: 'Avaliação ortodôntica inicial',
        defaultPrice: 100.0,
        estimatedDuration: 45,
        type: ProcedureType.ORTHODONTIC,
        status: Status.ACTIVE,
      },
      {
        name: 'Manutenção Ortodôntica',
        description: 'Ajuste mensal do aparelho',
        defaultPrice: 150.0,
        estimatedDuration: 30,
        type: ProcedureType.ORTHODONTIC,
        status: Status.ACTIVE,
      },

      // Procedimentos Estéticos
      {
        name: 'Clareamento Dental',
        description: 'Clareamento dental em consultório',
        defaultPrice: 500.0,
        estimatedDuration: 90,
        type: ProcedureType.AESTHETIC,
        status: Status.ACTIVE,
      },
      {
        name: 'Faceta de Resina',
        description: 'Faceta estética em resina composta',
        defaultPrice: 300.0,
        estimatedDuration: 120,
        type: ProcedureType.AESTHETIC,
        status: Status.ACTIVE,
      },

      // Procedimentos Periodônticos
      {
        name: 'Raspagem Subgengival',
        description: 'Tratamento periodontal básico',
        defaultPrice: 200.0,
        estimatedDuration: 60,
        type: ProcedureType.PERIODONTIC,
        status: Status.ACTIVE,
      },

      // Radiologia
      {
        name: 'Radiografia Periapical',
        description: 'Radiografia de dente específico',
        defaultPrice: 25.0,
        estimatedDuration: 10,
        type: ProcedureType.RADIOLOGY,
        status: Status.ACTIVE,
      },
      {
        name: 'Panorâmica',
        description: 'Radiografia panorâmica',
        defaultPrice: 80.0,
        estimatedDuration: 15,
        type: ProcedureType.RADIOLOGY,
        status: Status.ACTIVE,
      },
    ];

    await repository.save(procedures);
    console.log(`    🦷 Criados ${procedures.length} procedimentos`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    await this.clearTable(dataSource, 'procedures');
    await this.resetAutoIncrement(dataSource, 'procedures');
  }
}
