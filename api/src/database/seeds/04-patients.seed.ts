import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import {
  Patient,
  PatientGender,
  PatientCategory,
} from '../../patients/entities/patient.entity';

export class PatientsSeed extends BaseSeed {
  name = 'Patients';

  async run(dataSource: DataSource): Promise<void> {
    const patientRepository = dataSource.getRepository(Patient);

    // Verificar se já existem dados
    const existingCount = await patientRepository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Pacientes já existem, pulando...');
      return;
    }

    const patients = [
      {
        name: '<PERSON>',
        birthDate: new Date('1985-03-15'),
        gender: PatientGender.FEMALE,
        cpf: '123.456.789-10',
        howDidYouFindUs: 'Indicação de amigo',
        notes: 'Paciente com histórico de sensibilidade dental',
        email: '<EMAIL>',
        phone: '(11) 91234-5678',
        whatsapp: '(11) 91234-5678',
        addressZipCode: '01234-567',
        addressStreet: '<PERSON><PERSON>',
        addressNumber: '123',
        addressNeighborhood: 'Centro',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Professora',
        medicalRecordNumber: 'MR001',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'Carlos Eduardo Lima',
        birthDate: new Date('1978-07-22'),
        gender: PatientGender.MALE,
        cpf: '234.567.890-21',
        howDidYouFindUs: 'Google',
        notes: 'Paciente ansioso, necessita sedação',
        email: '<EMAIL>',
        phone: '(11) 92345-6789',
        whatsapp: '(11) 92345-6789',
        addressZipCode: '02345-678',
        addressStreet: 'Avenida Paulista',
        addressNumber: '456',
        addressNeighborhood: 'Bela Vista',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Engenheiro',
        medicalRecordNumber: 'MR002',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'Maria Fernanda Costa',
        birthDate: new Date('1992-11-08'),
        gender: PatientGender.FEMALE,
        cpf: '345.678.901-32',
        howDidYouFindUs: 'Redes sociais',
        notes: 'Interesse em tratamento estético',
        email: '<EMAIL>',
        phone: '(11) 93456-7890',
        whatsapp: '(11) 93456-7890',
        addressZipCode: '03456-789',
        addressStreet: 'Rua Augusta',
        addressNumber: '789',
        addressNeighborhood: 'Consolação',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Designer',
        medicalRecordNumber: 'MR003',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'João Pedro Oliveira',
        birthDate: new Date('2010-05-12'),
        gender: PatientGender.MALE,
        cpf: '456.789.012-43',
        howDidYouFindUs: 'Indicação médica',
        notes: 'Paciente pediátrico, primeira consulta',
        email: '<EMAIL>',
        phone: '(11) 94567-8901',
        whatsapp: '(11) 94567-8901',
        addressZipCode: '04567-890',
        addressStreet: 'Rua dos Pinheiros',
        addressNumber: '321',
        addressNeighborhood: 'Pinheiros',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Estudante',
        medicalRecordNumber: 'MR004',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'Lucia Rodrigues Mendes',
        birthDate: new Date('1965-09-25'),
        gender: PatientGender.FEMALE,
        cpf: '567.890.123-54',
        howDidYouFindUs: 'Indicação de dentista',
        notes: 'Paciente com diabetes, cuidados especiais',
        email: '<EMAIL>',
        phone: '(11) 95678-9012',
        whatsapp: '(11) 95678-9012',
        addressZipCode: '05678-901',
        addressStreet: 'Alameda Santos',
        addressNumber: '654',
        addressNeighborhood: 'Jardins',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Aposentada',
        medicalRecordNumber: 'MR005',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'Roberto Silva Ferreira',
        birthDate: new Date('1988-12-03'),
        gender: PatientGender.MALE,
        cpf: '678.901.234-65',
        howDidYouFindUs: 'Funcionário da clínica',
        notes: 'Funcionário - desconto especial',
        email: '<EMAIL>',
        phone: '(11) 96789-0123',
        whatsapp: '(11) 96789-0123',
        addressZipCode: '06789-012',
        addressStreet: 'Rua da Consolação',
        addressNumber: '987',
        addressNeighborhood: 'República',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Auxiliar Administrativo',
        medicalRecordNumber: 'MR006',
        category: PatientCategory.ROUTINE,
      },
      {
        name: 'Patricia Lima Santos',
        birthDate: new Date('1995-04-18'),
        gender: PatientGender.FEMALE,
        cpf: '789.012.345-76',
        howDidYouFindUs: 'Urgência',
        notes: 'Paciente chegou com dor intensa',
        email: '<EMAIL>',
        phone: '(11) 97890-1234',
        whatsapp: '(11) 97890-1234',
        addressZipCode: '07890-123',
        addressStreet: 'Rua Brigadeiro Luis Antonio',
        addressNumber: '147',
        addressNeighborhood: 'Liberdade',
        addressCity: 'São Paulo',
        addressState: 'SP',
        profession: 'Advogada',
        medicalRecordNumber: 'MR007',
        category: PatientCategory.URGENT,
      },
    ];

    await patientRepository.save(patients);
    console.log(`    👤 Criados ${patients.length} pacientes`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    await this.clearTable(dataSource, 'patient');
    await this.resetAutoIncrement(dataSource, 'patient');
  }
}
