import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import { AppointmentCategory } from '../../appointment-categories/entities/appointment-category.entity';

export class AppointmentCategoriesSeed extends BaseSeed {
  name = 'AppointmentCategories';

  async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(AppointmentCategory);

    // Verificar se já existem dados
    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Categorias de agendamento já existem, pulando...');
      return;
    }

    const appointmentCategories = [
      {
        name: 'Primeira Consulta',
        description: 'Primeira consulta do paciente na clínica',
        color: '#FED7AA', // Laranja claro
        isActive: true,
      },
      {
        name: 'Reativação',
        description: 'Retorno de paciente após período de inatividade',
        color: '#DDD6FE', // Roxo claro
        isActive: true,
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        description: 'Avaliação específica ou segunda opinião',
        color: '#FBCFE8', // Rosa claro
        isActive: true,
      },
      {
        name: 'Pendencia Financeira',
        description: 'Agendamento relacionado a questões financeiras',
        color: '#FECACA', // Vermelho claro
        isActive: true,
      },
      {
        name: 'Continuo',
        description: 'Continuidade de tratamento em andamento',
        color: '#BBF7D0', // Verde claro
        isActive: true,
      },
    ];

    await repository.save(appointmentCategories);
    console.log(`    📅 Criadas ${appointmentCategories.length} categorias de agendamento`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(AppointmentCategory);
    await repository.clear();
    console.log('    🧹 Categorias de agendamento removidas');
  }
}
