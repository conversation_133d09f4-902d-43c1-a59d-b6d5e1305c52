import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import { Dentist } from '../../dentists/entities/dentist.entity';

export class DentistsSeed extends BaseSeed {
  name = 'Dentists';

  async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(Dentist);

    // Verificar se já existem dados
    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Dentistas já existem, pulando...');
      return;
    }

    const dentists = [
      {
        name: 'Dr. <PERSON>',
        cro: 'CRO-SP-12345',
        specialty: 'Clínico Geral',
        phone: '(11) 99999-1111',
        email: '<EMAIL>',
        notes: 'Especialista em tratamentos gerais e preventivos',
        active: true,
      },
      {
        name: '<PERSON><PERSON><PERSON> <PERSON>',
        cro: 'CRO-SP-23456',
        specialty: 'Ortodontia',
        phone: '(11) 99999-2222',
        email: '<EMAIL>',
        notes: 'Especialista em aparelhos ortodônticos e alinhadores',
        active: true,
      },
      {
        name: '<PERSON>. <PERSON>',
        cro: 'CRO-SP-34567',
        specialty: 'Implantodontia',
        phone: '(11) 99999-3333',
        email: '<EMAIL>',
        notes: 'Especialista em implantes e cirurgias orais',
        active: true,
      },
      {
        name: 'Dra. Ana Costa',
        cro: 'CRO-SP-45678',
        specialty: 'Endodontia',
        phone: '(11) 99999-4444',
        email: '<EMAIL>',
        notes: 'Especialista em tratamentos de canal',
        active: true,
      },
      {
        name: 'Dr. Pedro Ferreira',
        cro: 'CRO-SP-56789',
        specialty: 'Periodontia',
        phone: '(11) 99999-5555',
        email: '<EMAIL>',
        notes: 'Especialista em tratamentos de gengiva e periodonto',
        active: true,
      },
      {
        name: 'Dra. Lucia Rodrigues',
        cro: 'CRO-SP-67890',
        specialty: 'Odontopediatria',
        phone: '(11) 99999-6666',
        email: '<EMAIL>',
        notes: 'Especialista em atendimento infantil',
        active: true,
      },
    ];

    await repository.save(dentists);
    console.log(`    👨‍⚕️ Criados ${dentists.length} dentistas`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    await this.clearTable(dataSource, 'dentists');
    await this.resetAutoIncrement(dataSource, 'dentists');
  }
}
