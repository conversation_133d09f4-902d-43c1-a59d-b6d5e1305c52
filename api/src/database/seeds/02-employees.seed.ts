import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import { Employee } from '../../employees/entities/employee.entity';
import { EmployeeType } from '../../employees/enums/employee-type.enum';

export class EmployeesSeed extends BaseSeed {
  name = 'Employees';

  async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(Employee);

    // Verificar se já existem dados
    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Funcionários já existem, pulando...');
      return;
    }

    const employees = [
      {
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '(11) 98888-1111',
        type: EmployeeType.RECEPCIONISTA,
        birthDate: new Date('1990-03-15'),
        cpf: '123.456.789-01',
        admissionDate: new Date('2023-01-15'),
        notes: 'Responsável pelo atendimento e agendamentos',
        isActive: true,
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '(11) 98888-2222',
        type: EmployeeType.AUXILIAR_SAUDE_BUCAL,
        birthDate: new Date('1988-07-22'),
        cpf: '234.567.890-12',
        admissionDate: new Date('2023-02-01'),
        notes: 'Auxiliar de saúde bucal experiente',
        isActive: true,
      },
      {
        name: 'Carla Mendes',
        email: '<EMAIL>',
        phone: '(11) 98888-3333',
        type: EmployeeType.TECNICO_SAUDE_BUCAL,
        birthDate: new Date('1985-11-08'),
        cpf: '345.678.901-23',
        admissionDate: new Date('2023-01-20'),
        notes: 'Técnica em saúde bucal qualificada',
        isActive: true,
      },
      {
        name: 'Patricia Lima',
        email: '<EMAIL>',
        phone: '(11) 98888-4444',
        type: EmployeeType.SECRETARIA_ADMINISTRATIVA,
        birthDate: new Date('1992-05-12'),
        cpf: '456.789.012-34',
        admissionDate: new Date('2023-03-01'),
        notes: 'Responsável pela parte administrativa',
        isActive: true,
      },
      {
        name: 'Ricardo Souza',
        email: '<EMAIL>',
        phone: '(11) 98888-5555',
        type: EmployeeType.GERENTE_CLINICA,
        birthDate: new Date('1980-09-25'),
        cpf: '567.890.123-45',
        admissionDate: new Date('2022-12-01'),
        notes: 'Gerente geral da clínica',
        isActive: true,
      },
      {
        name: 'Juliana Costa',
        email: '<EMAIL>',
        phone: '(11) 98888-6666',
        type: EmployeeType.FINANCEIRO,
        birthDate: new Date('1987-12-03'),
        cpf: '678.901.234-56',
        admissionDate: new Date('2023-02-15'),
        notes: 'Responsável pelo setor financeiro',
        isActive: true,
      },
      {
        name: 'Marcos Silva',
        email: '<EMAIL>',
        phone: '(11) 98888-7777',
        type: EmployeeType.SERVICOS_GERAIS,
        birthDate: new Date('1975-04-18'),
        cpf: '789.012.345-67',
        admissionDate: new Date('2023-01-10'),
        notes: 'Responsável pela limpeza e manutenção',
        isActive: true,
      },
    ];

    await repository.save(employees);
    console.log(`    👥 Criados ${employees.length} funcionários`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    await this.clearTable(dataSource, 'employees');
  }
}
