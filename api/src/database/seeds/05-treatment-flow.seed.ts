import { DataSource } from 'typeorm';
import { BaseSeed } from './utils/seed-runner';
import { TreatmentFlowStep } from '../../treatment-flow/entities/treatment-flow-step.entity';
import { Procedure } from '../../procedures/entities/procedure.entity';

export class TreatmentFlowSeed extends BaseSeed {
  name = 'TreatmentFlow';

  async run(dataSource: DataSource): Promise<void> {
    const treatmentFlowRepository = dataSource.getRepository(TreatmentFlowStep);
    const procedureRepository = dataSource.getRepository(Procedure);

    // Verificar se já existem dados
    const existingCount = await treatmentFlowRepository.count();
    if (existingCount > 0) {
      console.log('    ℹ️  Fluxo de tratamento já existe, pulando...');
      return;
    }

    // Buscar procedimentos necessários
    const procedures = await procedureRepository.find();

    if (procedures.length === 0) {
      console.log(
        '    ⚠️  Nenhum procedimento encontrado, pulando fluxo de tratamento...',
      );
      return;
    }

    // Mapear procedimentos por nome para facilitar a criação do fluxo
    const procedureMap = new Map<string, Procedure>();
    procedures.forEach((proc) => procedureMap.set(proc.name, proc));

    // Definir fluxos de tratamento baseados em práticas odontológicas
    const treatmentFlows: Array<{
      fromProcedureId: number;
      toProcedureId: number;
      minDaysAfter: number;
      notes: string;
    }> = [];

    // Fluxos básicos - Consulta inicial sempre primeiro
    const consultaInicial = procedureMap.get('Consulta Inicial');
    const limpezaDental = procedureMap.get('Limpeza Dental');
    const radiografiaPeriapical = procedureMap.get('Radiografia Periapical');
    const panoramica = procedureMap.get('Panorâmica');

    if (consultaInicial) {
      // Após consulta inicial, pode fazer limpeza no mesmo dia ou próximo
      if (limpezaDental) {
        treatmentFlows.push({
          fromProcedureId: consultaInicial.id,
          toProcedureId: limpezaDental.id,
          minDaysAfter: 0,
          notes:
            'Limpeza pode ser feita na mesma consulta ou agendada para o próximo dia',
        });
      }

      // Após consulta, pode fazer radiografias
      if (radiografiaPeriapical) {
        treatmentFlows.push({
          fromProcedureId: consultaInicial.id,
          toProcedureId: radiografiaPeriapical.id,
          minDaysAfter: 0,
          notes:
            'Radiografia pode ser feita na mesma consulta para diagnóstico',
        });
      }
      if (panoramica) {
        treatmentFlows.push({
          fromProcedureId: consultaInicial.id,
          toProcedureId: panoramica.id,
          minDaysAfter: 0,
          notes:
            'Panorâmica para avaliação geral pode ser feita na consulta inicial',
        });
      }
    }

    // Fluxos de restauração
    const restauracaoSimples = procedureMap.get('Restauração Simples');
    const restauracaoComplexa = procedureMap.get('Restauração Complexa');

    if (restauracaoSimples && limpezaDental) {
      treatmentFlows.push({
        fromProcedureId: limpezaDental.id,
        toProcedureId: restauracaoSimples.id,
        minDaysAfter: 1,
        notes: 'Restauração após limpeza para melhor aderência',
      });
    }

    // Fluxos endodônticos
    const tratamentoCanal = procedureMap.get(
      'Tratamento de Canal Unirradicular',
    );

    if (tratamentoCanal && radiografiaPeriapical) {
      treatmentFlows.push({
        fromProcedureId: radiografiaPeriapical.id,
        toProcedureId: tratamentoCanal.id,
        minDaysAfter: 0,
        notes:
          'Radiografia necessária para diagnóstico antes do tratamento de canal',
      });
    }

    if (tratamentoCanal && restauracaoComplexa) {
      treatmentFlows.push({
        fromProcedureId: tratamentoCanal.id,
        toProcedureId: restauracaoComplexa.id,
        minDaysAfter: 7,
        notes: 'Aguardar 1 semana após canal para fazer restauração definitiva',
      });
    }

    // Fluxos cirúrgicos
    const extracaoSimples = procedureMap.get('Extração Simples');
    const extracaoComplexa = procedureMap.get('Extração Complexa');
    const implante = procedureMap.get('Implante Dentário');

    if (extracaoSimples && implante) {
      treatmentFlows.push({
        fromProcedureId: extracaoSimples.id,
        toProcedureId: implante.id,
        minDaysAfter: 90,
        notes: 'Aguardar 3 meses para cicatrização óssea antes do implante',
      });
    }

    if (extracaoComplexa && implante) {
      treatmentFlows.push({
        fromProcedureId: extracaoComplexa.id,
        toProcedureId: implante.id,
        minDaysAfter: 120,
        notes:
          'Aguardar 4 meses para cicatrização óssea após extração complexa',
      });
    }

    // Fluxos estéticos
    const clareamento = procedureMap.get('Clareamento Dental');
    const facetaResina = procedureMap.get('Faceta de Resina');

    if (limpezaDental && clareamento) {
      treatmentFlows.push({
        fromProcedureId: limpezaDental.id,
        toProcedureId: clareamento.id,
        minDaysAfter: 7,
        notes: 'Limpeza prévia necessária para melhor resultado do clareamento',
      });
    }

    if (clareamento && facetaResina) {
      treatmentFlows.push({
        fromProcedureId: clareamento.id,
        toProcedureId: facetaResina.id,
        minDaysAfter: 14,
        notes: 'Aguardar estabilização da cor após clareamento',
      });
    }

    // Fluxos periodônticos
    const raspagemSubgengival = procedureMap.get('Raspagem Subgengival');

    if (limpezaDental && raspagemSubgengival) {
      treatmentFlows.push({
        fromProcedureId: limpezaDental.id,
        toProcedureId: raspagemSubgengival.id,
        minDaysAfter: 0,
        notes: 'Raspagem pode ser feita na sequência da limpeza se necessário',
      });
    }

    // Fluxos ortodônticos
    const consultaOrtodôntica = procedureMap.get('Consulta Ortodôntica');
    const manutencaoOrtodôntica = procedureMap.get('Manutenção Ortodôntica');

    if (consultaOrtodôntica && panoramica) {
      treatmentFlows.push({
        fromProcedureId: consultaOrtodôntica.id,
        toProcedureId: panoramica.id,
        minDaysAfter: 0,
        notes: 'Panorâmica necessária para planejamento ortodôntico',
      });
    }

    if (consultaOrtodôntica && manutencaoOrtodôntica) {
      treatmentFlows.push({
        fromProcedureId: consultaOrtodôntica.id,
        toProcedureId: manutencaoOrtodôntica.id,
        minDaysAfter: 30,
        notes: 'Primeira manutenção após instalação do aparelho',
      });
    }

    // Fluxos de retorno e controle
    if (restauracaoSimples && consultaInicial) {
      treatmentFlows.push({
        fromProcedureId: restauracaoSimples.id,
        toProcedureId: consultaInicial.id,
        minDaysAfter: 180,
        notes: 'Consulta de controle semestral após restauração',
      });
    }

    if (tratamentoCanal && radiografiaPeriapical) {
      treatmentFlows.push({
        fromProcedureId: tratamentoCanal.id,
        toProcedureId: radiografiaPeriapical.id,
        minDaysAfter: 90,
        notes: 'Radiografia de controle 3 meses após tratamento de canal',
      });
    }

    // Filtrar apenas fluxos válidos (onde ambos os procedimentos existem)
    const validFlows = treatmentFlows.filter(
      (flow) =>
        flow.fromProcedureId &&
        flow.toProcedureId &&
        flow.fromProcedureId !== flow.toProcedureId,
    );

    if (validFlows.length === 0) {
      console.log(
        '    ⚠️  Nenhum fluxo válido pode ser criado com os procedimentos disponíveis',
      );
      return;
    }

    await treatmentFlowRepository.save(validFlows);
    console.log(`    🔄 Criados ${validFlows.length} fluxos de tratamento`);
  }

  async clear(dataSource: DataSource): Promise<void> {
    await this.clearTable(dataSource, 'treatment_flow_step');
    await this.resetAutoIncrement(dataSource, 'treatment_flow_step');
  }
}
