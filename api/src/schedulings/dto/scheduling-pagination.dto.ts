import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsOptional,
  Min,
  IsString,
  IsDateString,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Transform } from 'class-transformer';

export class SchedulingPaginationDto {
  @ApiProperty({
    description: 'Númer<PERSON> da página (começando em 1)',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number;

  @ApiProperty({
    description: 'Termo de busca para filtrar por nome do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'ID do paciente para filtrar agendamentos',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  patientId?: number;

  @ApiProperty({
    description:
      'ID do tratamento para filtrar agendamentos (compatibilidade com modelo antigo)',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  treatmentId?: number;

  @ApiProperty({
    description: 'ID do plano de tratamento para filtrar agendamentos',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  treatmentPlanId?: number;

  @ApiProperty({
    description: 'ID do dentista para filtrar agendamentos',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  dentistId?: number;

  @ApiProperty({
    description: 'Status do agendamento para filtrar',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'Data inicial para filtrar agendamentos',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Data final para filtrar agendamentos',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'ID da categoria de agendamento para filtrar',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  appointmentCategoryId?: number;

  @ApiProperty({
    description: 'Se true, retorna todos os agendamentos sem paginação',
    required: false,
    type: String,
  })
  @IsOptional()
  noPagination?: string;
}
