import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  IsNumber,
  IsBoolean,
  IsArray,
  IsUUID,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SchedulingStatus } from '../entities/scheduling.entity';

export class CreateSchedulingDto {
  @ApiProperty({ example: 1, description: 'ID do paciente' })
  @IsNotEmpty()
  @IsNumber()
  patientId: number;

  @ApiProperty({
    example: '2023-06-15',
    description: 'Data do agendamento (YYYY-MM-DD)',
  })
  @IsNotEmpty()
  @IsDateString()
  date: string;

  @ApiProperty({
    example: '14:30',
    description: 'Hor<PERSON>rio do agendamento (HH:MM)',
  })
  @IsNotEmpty()
  @IsString()
  time: string;

  @ApiProperty({ example: 1, description: 'ID do dentista', required: false })
  @IsOptional()
  @IsNumber()
  dentistId?: number;

  @ApiProperty({
    enum: SchedulingStatus,
    example: 'scheduled',
    description: 'Status do agendamento',
    required: false,
  })
  @IsOptional()
  @IsEnum(SchedulingStatus)
  status?: SchedulingStatus;

  @ApiProperty({
    example: 'Paciente chegou no horário',
    description: 'Observações sobre o agendamento',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: 150.0,
    description: 'Valor do agendamento',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  cost?: number;

  @ApiProperty({
    example: true,
    description: 'Status do pagamento',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  paid?: boolean;

  @ApiProperty({
    example: 1,
    description:
      'ID do tratamento associado (compatibilidade com modelo antigo)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  treatmentId?: number;

  @ApiProperty({
    example: 1,
    description: 'ID do plano de tratamento associado',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  treatmentPlanId?: number;

  @ApiProperty({
    example: [1, 2, 3],
    description: 'IDs dos procedimentos selecionados para este agendamento',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  procedureIds?: number[];

  @ApiProperty({
    example: 60,
    description: 'Duração do agendamento em minutos',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    example: '15:30',
    description: 'Hora de término do agendamento (HH:MM)',
    required: false,
  })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do paciente para este agendamento',
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    example: '(11) 99999-9999',
    description: 'Celular do paciente para este agendamento',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    example: false,
    description: 'Indica se é a primeira consulta do paciente',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isFirstAppointment?: boolean;



  @ApiProperty({
    example: 'dff58c7d-ca92-4fec-9509-8d285e01f3d7',
    description: 'UUID do funcionário que realizou o agendamento',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  scheduledBy?: string;

  @ApiProperty({
    example: 1,
    description: 'ID da categoria de agendamento',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  appointmentCategoryId?: number;
}
