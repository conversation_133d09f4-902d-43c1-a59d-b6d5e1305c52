import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  ManyToMany,
  JoinColumn,
  Join<PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { Dentist } from '../../dentists/entities/dentist.entity';
import { Employee } from '../../employees/entities/employee.entity';
import { Procedure } from '../../procedures/entities/procedure.entity';
import { AppointmentCategory } from '../../appointment-categories/entities/appointment-category.entity';

export enum SchedulingStatus {
  CONFIRMED = 'confirmed',
  UNCONFIRMED = 'unconfirmed',
  LATE = 'late',
  NO_SHOW = 'no-show',
  CANCELLED = 'cancelled',
  RESCHEDULED = 'rescheduled',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
}

@Entity()
export class Scheduling {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Patient, (patient) => patient.schedulings, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'patient_id' })
  patient: Patient;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'time' })
  time: string;

  @Column({ type: 'int', nullable: true })
  @ApiProperty({
    description: 'Duração do agendamento em minutos',
    required: false,
  })
  duration: number | null;

  @Column({ type: 'time', nullable: true })
  @ApiProperty({
    description: 'Hora de término do agendamento (HH:MM)',
    required: false,
  })
  endTime: string | null;

  @ManyToOne(() => Dentist, (dentist) => dentist.schedulings, {
    nullable: true,
  })
  @JoinColumn({ name: 'dentist_id' })
  dentist: Dentist;

  @Column({
    type: 'enum',
    enum: SchedulingStatus,
    default: SchedulingStatus.UNCONFIRMED,
  })
  status: SchedulingStatus;

  @Column({ nullable: true, type: 'text' })
  notes: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Email do paciente para este agendamento',
    required: false,
  })
  email: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Celular do paciente para este agendamento',
    required: false,
  })
  phone: string;

  @Column({ default: false, nullable: true })
  @ApiProperty({
    description: 'Indica se é a primeira consulta do paciente',
    default: false,
    required: false,
  })
  isFirstAppointment?: boolean;

  // Relacionamento com categoria de agendamento
  @ManyToOne(() => AppointmentCategory, { nullable: true })
  @JoinColumn({ name: 'appointment_category_id' })
  @ApiProperty({
    description: 'Categoria do agendamento',
    type: () => AppointmentCategory,
    required: false,
  })
  appointmentCategory: AppointmentCategory;

  // Relacionamento many-to-many com procedimentos
  @ManyToMany(() => Procedure)
  @JoinTable({
    name: 'scheduling_procedures',
    joinColumn: { name: 'scheduling_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'procedure_id', referencedColumnName: 'id' },
  })
  @ApiProperty({
    description: 'Procedimentos associados ao agendamento',
    type: () => [Procedure],
    required: false,
  })
  procedures: Procedure[];

  // Relacionamento com funcionário que agendou
  @ManyToOne(() => Employee)
  @JoinColumn({ name: 'scheduledById' })
  @ApiProperty({
    description: 'Funcionário que realizou o agendamento',
    type: () => Employee,
    required: false,
  })
  scheduledBy: Employee;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do agendamento' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do agendamento' })
  updatedAt: Date;
}
