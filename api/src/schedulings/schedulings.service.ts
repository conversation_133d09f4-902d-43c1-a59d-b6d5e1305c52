import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateSchedulingDto } from './dto/create-scheduling.dto';
import { UpdateSchedulingDto } from './dto/update-scheduling.dto';
import { Scheduling } from './entities/scheduling.entity';
import { SchedulingPaginationDto } from './dto/scheduling-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';
import { AppointmentObservationsService } from '../appointment-observations/appointment-observations.service';

@Injectable()
export class SchedulingsService {
  private readonly logger = new Logger(SchedulingsService.name);

  constructor(
    @InjectRepository(Scheduling)
    private schedulingsRepository: Repository<Scheduling>,
    @InjectRepository(TreatmentProcedure)
    private treatmentProcedureRepository: Repository<TreatmentProcedure>,
    private appointmentObservationsService: AppointmentObservationsService,
  ) {}

  // Método para buscar procedimentos associados a um agendamento
  async findProceduresBySchedulingId(
    id: number,
  ): Promise<TreatmentProcedure[]> {
    this.logger.log(`Finding procedures for scheduling with id: ${id}`);

    // Primeiro, verificar se o agendamento existe
    const scheduling = await this.schedulingsRepository.findOne({
      where: { id },
    });

    if (!scheduling) {
      throw new NotFoundException(`Agendamento com ID ${id} não encontrado`);
    }

    // Buscar apenas os procedimentos que estão diretamente associados a este agendamento
    const procedures = await this.treatmentProcedureRepository
      .createQueryBuilder('procedure')
      .leftJoinAndSelect('procedure.procedure', 'baseProcedure')
      .leftJoinAndSelect('procedure.professional', 'professional')
      .leftJoinAndSelect('procedure.treatmentPlan', 'treatmentPlan')
      .where('procedure.appointmentId = :appointmentId', { appointmentId: id })
      .orderBy('procedure.createdAt', 'ASC')
      .getMany();

    this.logger.log(
      `Found ${procedures.length} procedures associated with scheduling ${id}`,
    );
    return procedures;
  }

  async create(createSchedulingDto: CreateSchedulingDto): Promise<Scheduling> {
    this.logger.log(
      `Creating scheduling with data: ${JSON.stringify(createSchedulingDto)}`,
    );

    // Extrair campos que precisam de tratamento especial
    const { procedureIds, scheduledBy, appointmentCategoryId, patientId, dentistId, ...schedulingData } = createSchedulingDto;

    let savedScheduling: Scheduling;

    // Garantir que a data está no formato correto
    if (schedulingData.date) {
      const dateStr = schedulingData.date;
      this.logger.log(`Original date string: ${dateStr}`);

      // Criar uma cópia do DTO para não modificar o original
      const dto = { ...schedulingData };

      try {
        // Usar a data exatamente como foi fornecida pelo cliente
        // Sem adicionar um dia para compensar o fuso horário
        if (typeof dateStr === 'string') {
          // Converter a string para um objeto Date
          const date = new Date(dateStr);
          this.logger.log(`Date object: ${date.toISOString()}`);

          // Atualizar o DTO com a data original
          // A entidade espera um objeto Date, que é o que temos aqui
          // @ts-expect-error - Ignorar erro de tipo, pois sabemos que a entidade aceita Date
          dto.date = date;
          this.logger.log(`Final date to be saved: ${dto.date}`);
        }

        const scheduling = this.schedulingsRepository.create(dto);

        // Adicionar relacionamentos obrigatórios
        if (patientId) {
          scheduling.patient = { id: patientId } as any;
        }

        if (dentistId) {
          scheduling.dentist = { id: dentistId } as any;
        }

        // Adicionar relacionamentos opcionais
        if (scheduledBy) {
          scheduling.scheduledBy = { id: scheduledBy } as any;
        }

        if (appointmentCategoryId) {
          scheduling.appointmentCategory = { id: appointmentCategoryId } as any;
        }

        savedScheduling = await this.schedulingsRepository.save(scheduling);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(`Error processing date: ${errorMessage}`);
        // Em caso de erro, usar o DTO original
        const scheduling = this.schedulingsRepository.create(schedulingData);

        // Adicionar relacionamentos obrigatórios
        if (patientId) {
          scheduling.patient = { id: patientId } as any;
        }

        if (dentistId) {
          scheduling.dentist = { id: dentistId } as any;
        }

        // Adicionar relacionamentos opcionais
        if (scheduledBy) {
          scheduling.scheduledBy = { id: scheduledBy } as any;
        }

        if (appointmentCategoryId) {
          scheduling.appointmentCategory = { id: appointmentCategoryId } as any;
        }

        savedScheduling = await this.schedulingsRepository.save(scheduling);
      }
    } else {
      this.logger.error('No date provided in DTO');
      const scheduling = this.schedulingsRepository.create(schedulingData);

      // Adicionar relacionamentos obrigatórios
      if (patientId) {
        scheduling.patient = { id: patientId } as any;
      }

      if (dentistId) {
        scheduling.dentist = { id: dentistId } as any;
      }

      // Adicionar relacionamentos opcionais
      if (scheduledBy) {
        scheduling.scheduledBy = { id: scheduledBy } as any;
      }

      if (appointmentCategoryId) {
        scheduling.appointmentCategory = { id: appointmentCategoryId } as any;
      }

      savedScheduling = await this.schedulingsRepository.save(scheduling);
    }

    // Se há procedimentos selecionados, associá-los ao agendamento
    if (procedureIds && procedureIds.length > 0) {
      try {
        await this.associateProceduresToScheduling(
          savedScheduling.id,
          procedureIds,
        );
        this.logger.log(
          `Associados ${procedureIds.length} procedimentos ao agendamento ${savedScheduling.id}`,
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(
          `Erro ao associar procedimentos ao agendamento: ${errorMessage}`,
        );
        // Não falhar a criação do agendamento se houver erro na associação de procedimentos
      }
    }

    // Se há observações, salvar no histórico de observações
    if (savedScheduling.notes && savedScheduling.notes.trim() !== '') {
      try {
        await this.appointmentObservationsService.createFromSchedulingNote(
          savedScheduling.id,
          savedScheduling.patient.id,
          savedScheduling.notes,
        );
        this.logger.log(
          `Observação salva no histórico para agendamento ${savedScheduling.id}`,
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(
          `Erro ao salvar observação no histórico: ${errorMessage}`,
        );
        // Não falhar a criação do agendamento se houver erro ao salvar a observação
      }
    }

    return savedScheduling;
  }

  // Método para associar procedimentos a um agendamento
  private async associateProceduresToScheduling(
    schedulingId: number,
    procedureIds: number[],
  ): Promise<void> {
    this.logger.log(
      `Associando procedimentos ${procedureIds.join(', ')} ao agendamento ${schedulingId}`,
    );

    for (const procedureId of procedureIds) {
      try {
        // Buscar o procedimento
        const procedure = await this.treatmentProcedureRepository.findOne({
          where: { id: procedureId },
        });

        if (procedure) {
          // Associar o agendamento ao procedimento
          procedure.appointment = { id: schedulingId } as Scheduling;
          await this.treatmentProcedureRepository.save(procedure);
          this.logger.log(
            `Procedimento ${procedureId} associado ao agendamento ${schedulingId}`,
          );
        } else {
          this.logger.warn(`Procedimento ${procedureId} não encontrado`);
        }
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(
          `Erro ao associar procedimento ${procedureId}: ${errorMessage}`,
        );
      }
    }
  }

  async findAll(): Promise<Scheduling[]> {
    this.logger.log('Finding all schedulings');
    return this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy')
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findAllWithFilters(filters: {
    search?: string;
    status?: string;
    patientId?: number;
    dentistId?: number;
    startDate?: string;
    endDate?: string;
    appointmentCategoryId?: number;
  }): Promise<Scheduling[]> {
    this.logger.log('Finding all schedulings with filters:', filters);

    const queryBuilder = this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy');

    // Aplicar filtros
    if (filters.search) {
      queryBuilder.andWhere('patient.name LIKE :search', {
        search: `%${filters.search}%`,
      });
    }

    if (filters.status) {
      queryBuilder.andWhere('scheduling.status = :status', {
        status: filters.status,
      });
    }

    if (filters.patientId) {
      queryBuilder.andWhere('patient.id = :patientId', {
        patientId: filters.patientId,
      });
    }

    if (filters.dentistId) {
      queryBuilder.andWhere('scheduling.dentistId = :dentistId', {
        dentistId: filters.dentistId,
      });
    }

    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere('scheduling.date BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    if (filters.appointmentCategoryId) {
      queryBuilder.andWhere('appointmentCategory.id = :appointmentCategoryId', {
        appointmentCategoryId: filters.appointmentCategoryId,
      });
    }

    return queryBuilder
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findByPatient(patientId: number): Promise<Scheduling[]> {
    this.logger.log(`Finding schedulings for patient with id: ${patientId}`);
    return this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy')
      .where('patient.id = :patientId', { patientId })
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findByTreatment(treatmentId: number): Promise<Scheduling[]> {
    this.logger.log(
      `Finding schedulings for treatment with id: ${treatmentId}`,
    );
    return this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .where('treatmentPlan.id = :treatmentId', { treatmentId })
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findByTreatmentPlan(treatmentPlanId: number): Promise<Scheduling[]> {
    this.logger.log(
      `Finding schedulings for treatment plan with id: ${treatmentPlanId}`,
    );
    return this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .where('treatmentPlan.id = :treatmentPlanId', {
        treatmentPlanId,
      })
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<Scheduling[]> {
    this.logger.log(`Finding schedulings between ${startDate} and ${endDate}`);
    return this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy')
      .where('scheduling.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .orderBy('scheduling.date', 'ASC')
      .addOrderBy('scheduling.time', 'ASC')
      .getMany();
  }

  async findAllPaginated(
    paginationDto: SchedulingPaginationDto,
  ): Promise<PaginatedResponse<Scheduling>> {
    const {
      page = 1,
      limit = 6,
      search,
      status,
      patientId,
      dentistId,
      startDate,
      endDate,
      appointmentCategoryId,
    } = paginationDto;

    const queryBuilder = this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy');

    // Aplicar filtros
    if (search) {
      queryBuilder.andWhere('patient.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (status) {
      queryBuilder.andWhere('scheduling.status = :status', { status });
    }

    if (patientId) {
      queryBuilder.andWhere('patient.id = :patientId', { patientId });
    }

    if (dentistId) {
      queryBuilder.andWhere('scheduling.dentistId = :dentistId', { dentistId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('scheduling.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (appointmentCategoryId) {
      queryBuilder.andWhere('appointmentCategory.id = :appointmentCategoryId', {
        appointmentCategoryId,
      });
    }

    // Contar o total de registros
    const total = await queryBuilder.getCount();

    // Aplicar paginação
    const skip = (page - 1) * limit;
    queryBuilder
      .orderBy('scheduling.date', 'DESC')
      .addOrderBy('scheduling.time', 'ASC')
      .skip(skip)
      .take(limit);

    // Obter os registros
    const data = await queryBuilder.getMany();

    // Calcular o total de páginas
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: number): Promise<Scheduling> {
    const scheduling = await this.schedulingsRepository
      .createQueryBuilder('scheduling')
      .leftJoinAndSelect('scheduling.patient', 'patient')
      .leftJoinAndSelect('patient.patientType', 'patientType')
      .leftJoinAndSelect('scheduling.dentist', 'dentist')
      .leftJoinAndSelect('scheduling.appointmentCategory', 'appointmentCategory')
      .leftJoinAndSelect('scheduling.scheduledBy', 'scheduledBy')
      .where('scheduling.id = :id', { id })
      .getOne();

    if (!scheduling) {
      throw new NotFoundException(`Agendamento com ID ${id} não encontrado`);
    }

    return scheduling;
  }

  async update(
    id: number,
    updateSchedulingDto: UpdateSchedulingDto,
  ): Promise<Scheduling> {
    this.logger.log(
      `Updating scheduling with id: ${id} and data: ${JSON.stringify(
        updateSchedulingDto,
      )}`,
    );

    // Verificar se o agendamento existe
    const scheduling = await this.schedulingsRepository.findOne({
      where: { id },
    });

    if (!scheduling) {
      throw new NotFoundException(`Agendamento com ID ${id} não encontrado`);
    }

    // Extrair campos que precisam de tratamento especial
    const { procedureIds, scheduledBy, appointmentCategoryId, patientId, dentistId, ...schedulingUpdateData } = updateSchedulingDto;

    // Log dos procedimentos se fornecidos
    if (procedureIds && procedureIds.length > 0) {
      this.logger.log(
        `Procedimentos fornecidos para atualização: ${JSON.stringify(procedureIds)}`,
      );
      // TODO: Implementar lógica para atualizar procedimentos associados ao agendamento
      // Por enquanto, apenas logamos os procedimentos
    }

    // Verificar se há uma nova observação sendo adicionada
    const hasNewNote =
      schedulingUpdateData.notes &&
      schedulingUpdateData.notes.trim() !== '' &&
      schedulingUpdateData.notes !== scheduling.notes;

    // Buscar o agendamento atual para atualização
    const currentScheduling = await this.schedulingsRepository.findOne({
      where: { id },
      relations: ['patient', 'dentist', 'scheduledBy', 'appointmentCategory'],
    });

    if (!currentScheduling) {
      throw new NotFoundException(`Agendamento com ID ${id} não encontrado`);
    }

    // Aplicar as atualizações
    Object.assign(currentScheduling, schedulingUpdateData);

    // Adicionar relacionamentos obrigatórios se fornecidos
    if (patientId) {
      currentScheduling.patient = { id: patientId } as any;
    }

    if (dentistId) {
      currentScheduling.dentist = { id: dentistId } as any;
    }

    // Adicionar relacionamentos opcionais se fornecidos
    if (scheduledBy) {
      currentScheduling.scheduledBy = { id: scheduledBy } as any;
    }

    if (appointmentCategoryId) {
      currentScheduling.appointmentCategory = { id: appointmentCategoryId } as any;
    }

    // Salvar o agendamento atualizado
    await this.schedulingsRepository.save(currentScheduling);

    // Se há uma nova observação, salvar no histórico
    if (hasNewNote) {
      try {
        // Buscar o agendamento com o relacionamento patient para obter o ID
        const schedulingWithPatient = await this.schedulingsRepository.findOne({
          where: { id },
          relations: ['patient'],
        });

        await this.appointmentObservationsService.createFromSchedulingNote(
          id,
          schedulingWithPatient!.patient.id,
          schedulingUpdateData.notes!,
        );
        this.logger.log(
          `Nova observação salva no histórico para agendamento ${id}`,
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(
          `Erro ao salvar nova observação no histórico: ${errorMessage}`,
        );
        // Não falhar a atualização do agendamento se houver erro ao salvar a observação
      }
    }

    // Retornar o agendamento atualizado
    return this.findOne(id);
  }

  async remove(id: number): Promise<{ message: string }> {
    this.logger.log(`Removing scheduling with id: ${id}`);

    // Verificar se o agendamento existe
    const scheduling = await this.schedulingsRepository.findOne({
      where: { id },
    });

    if (!scheduling) {
      throw new NotFoundException(`Agendamento com ID ${id} não encontrado`);
    }

    // Remover o agendamento
    await this.schedulingsRepository.remove(scheduling);

    return { message: 'Agendamento removido com sucesso' };
  }
}
