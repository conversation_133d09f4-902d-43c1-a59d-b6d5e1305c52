import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SchedulingsService } from './schedulings.service';
import { SchedulingsController } from './schedulings.controller';
import { Scheduling } from './entities/scheduling.entity';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';
import { AppointmentObservationsModule } from '../appointment-observations/appointment-observations.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Scheduling, TreatmentProcedure]),
    AppointmentObservationsModule,
  ],
  controllers: [SchedulingsController],
  providers: [SchedulingsService],
  exports: [SchedulingsService],
})
export class SchedulingsModule {}
