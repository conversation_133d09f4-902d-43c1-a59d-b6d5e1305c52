/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { SchedulingsService } from './schedulings.service';
import { CreateSchedulingDto } from './dto/create-scheduling.dto';
import { UpdateSchedulingDto } from './dto/update-scheduling.dto';
import { SchedulingPaginationDto } from './dto/scheduling-pagination.dto';

@ApiTags('schedulings')
@Controller('schedulings')
export class SchedulingsController {
  constructor(private readonly schedulingsService: SchedulingsService) {}

  @ApiOperation({ summary: 'Criar um novo agendamento' })
  @ApiResponse({ status: 201, description: 'Agendamento criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateSchedulingDto })
  @Post()
  create(@Body() createSchedulingDto: CreateSchedulingDto) {
    return this.schedulingsService.create(createSchedulingDto);
  }

  @ApiOperation({ summary: 'Listar todos os agendamentos' })
  @ApiResponse({
    status: 200,
    description: 'Lista de agendamentos retornada com sucesso',
  })
  @ApiQuery({
    name: 'patientId',
    required: false,
    description: 'ID do paciente para filtrar agendamentos',
    type: Number,
  })
  @ApiQuery({
    name: 'treatmentId',
    required: false,
    description:
      'ID do tratamento para filtrar agendamentos (compatibilidade com modelo antigo)',
    type: Number,
  })
  @ApiQuery({
    name: 'treatmentPlanId',
    required: false,
    description: 'ID do plano de tratamento para filtrar agendamentos',
    type: Number,
  })
  @ApiQuery({
    name: 'dentistId',
    required: false,
    description: 'ID do dentista para filtrar agendamentos',
    type: Number,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Status do agendamento para filtrar',
    type: String,
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Data inicial para filtrar agendamentos',
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'Data final para filtrar agendamentos',
    type: String,
  })
  @ApiQuery({
    name: 'appointmentCategoryId',
    required: false,
    description: 'ID da categoria de agendamento para filtrar',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar por nome do paciente',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @Get()
  async findAll(@Query() paginationDto: SchedulingPaginationDto) {
    const {
      patientId,
      treatmentId,
      treatmentPlanId,
      startDate,
      endDate,
      page,
      limit,
      noPagination,
    } = paginationDto;

    // Se noPagination=true ou não houver parâmetros de paginação, retorna todos os agendamentos
    if (
      noPagination === 'true' ||
      (page === undefined && limit === undefined)
    ) {
      // Usar o método unificado com filtros para consultas não paginadas
      const { search, status, dentistId, appointmentCategoryId } = paginationDto;

      // Se há filtros específicos de compatibilidade, usar métodos específicos
      if (treatmentId) {
        return this.schedulingsService.findByTreatment(treatmentId);
      }
      if (treatmentPlanId) {
        return this.schedulingsService.findByTreatmentPlan(treatmentPlanId);
      }

      // Para todos os outros casos (incluindo patientId), usar o método unificado
      return this.schedulingsService.findAllWithFilters({
        search,
        status,
        patientId,
        dentistId,
        startDate,
        endDate,
        appointmentCategoryId,
      });
    }

    // Se houver parâmetros de paginação, retorna os agendamentos paginados
    return this.schedulingsService.findAllPaginated(paginationDto);
  }

  @ApiOperation({ summary: 'Buscar um agendamento pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Agendamento encontrado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Agendamento não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do agendamento', example: 1 })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.schedulingsService.findOne(id);
  }

  @ApiOperation({ summary: 'Buscar procedimentos de um agendamento' })
  @ApiResponse({
    status: 200,
    description: 'Procedimentos encontrados com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Agendamento não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do agendamento', example: 1 })
  @Get(':id/procedures')
  async findProcedures(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.schedulingsService.findProceduresBySchedulingId(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      // Se não for um erro de "não encontrado", retornar um array vazio
      return [];
    }
  }

  @ApiOperation({ summary: 'Atualizar um agendamento' })
  @ApiResponse({
    status: 200,
    description: 'Agendamento atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Agendamento não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do agendamento', example: 1 })
  @ApiBody({ type: UpdateSchedulingDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSchedulingDto: UpdateSchedulingDto,
  ) {
    return this.schedulingsService.update(id, updateSchedulingDto);
  }

  @ApiOperation({ summary: 'Remover um agendamento' })
  @ApiResponse({ status: 200, description: 'Agendamento removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Agendamento não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do agendamento', example: 1 })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.schedulingsService.remove(id);
  }
}
