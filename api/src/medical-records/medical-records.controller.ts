/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Inject,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { IStorageProvider } from '../common/interfaces/storage.interface';
import { MedicalRecordsService } from './medical-records.service';
import { CreateMedicalRecordDto } from './dto/create-medical-record.dto';
import { UpdateMedicalRecordDto } from './dto/update-medical-record.dto';
import { MedicalRecordResponse } from './interfaces/complete-medical-record.interface';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

@ApiTags('medical-records')
@Controller('medical-records')
export class MedicalRecordsController {
  private readonly logger = new Logger(MedicalRecordsController.name);

  constructor(
    private readonly medicalRecordsService: MedicalRecordsService,
    @Inject('StorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo prontuário' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        patientId: { type: 'number', example: 1 },
        examFilePath: { type: 'string', example: 'exams/patient-1/exam.pdf' },
      },
      required: ['patientId'],
    },
  })
  async create(@Body() createDto: CreateMedicalRecordDto) {
    this.logger.log(
      `Creating medical record for patient: ${createDto.patientId}`,
    );

    // Se o caminho do arquivo de exame for fornecido, verificar se ele existe
    if (createDto.examFilePath) {
      try {
        const filePath = createDto.examFilePath;
        // Tentar obter a URL do arquivo para verificar se ele existe
        await this.storageProvider.getFileUrl(filePath);
      } catch {
        this.logger.error(`Exam file not found: ${createDto.examFilePath}`);
        // Não definir o caminho se o arquivo não existir
        createDto.examFilePath = undefined;
      }
    }

    return this.medicalRecordsService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os prontuários' })
  @ApiResponse({
    status: 200,
    description: 'Lista de prontuários retornada com sucesso',
  })
  findAll() {
    this.logger.log('Finding all medical records');
    return this.medicalRecordsService.findAll();
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Buscar prontuário de um paciente' })
  @ApiResponse({
    status: 200,
    description: 'Prontuário do paciente encontrado',
  })
  findByPatient(@Param('patientId', ParseIntPipe) patientId: number) {
    this.logger.log(`Finding medical record for patient: ${patientId}`);
    return this.medicalRecordsService.findByPatient(patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um prontuário pelo ID' })
  @ApiResponse({ status: 200, description: 'Prontuário encontrado' })
  @ApiResponse({ status: 404, description: 'Prontuário não encontrado' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    this.logger.log(`Finding medical record with id: ${id}`);
    return this.medicalRecordsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um prontuário' })
  @ApiResponse({
    status: 200,
    description: 'Prontuário atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Prontuário não encontrado' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateMedicalRecordDto: UpdateMedicalRecordDto,
  ) {
    this.logger.log(`Updating medical record with id: ${id}`);
    return this.medicalRecordsService.update(id, updateMedicalRecordDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover um prontuário' })
  @ApiResponse({ status: 200, description: 'Prontuário removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Prontuário não encontrado' })
  remove(@Param('id', ParseIntPipe) id: number) {
    this.logger.log(`Removing medical record with id: ${id}`);
    return this.medicalRecordsService.remove(id);
  }

  @Get('exam-file/:filePath')
  @ApiOperation({ summary: 'Obter URL temporária para arquivo de exame' })
  @ApiResponse({
    status: 200,
    description: 'URL temporária gerada com sucesso',
    schema: {
      properties: {
        url: { type: 'string' },
      },
    },
  })
  async getExamFileUrl(@Param('filePath') filePath: string) {
    this.logger.log(`Getting exam file URL for path: ${filePath}`);
    try {
      const url = await this.storageProvider.getFileUrl(filePath);
      return { url };
    } catch {
      throw new NotFoundException('Arquivo não encontrado');
    }
  }

  @Get('complete/:patientId')
  @ApiOperation({ summary: 'Obter prontuário completo de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Prontuário completo retornado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  async getCompleteMedicalRecord(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<MedicalRecordResponse> {
    this.logger.log(
      `Getting complete medical record for patient: ${patientId}`,
    );
    return this.medicalRecordsService.getCompleteMedicalRecord(patientId);
  }
}
