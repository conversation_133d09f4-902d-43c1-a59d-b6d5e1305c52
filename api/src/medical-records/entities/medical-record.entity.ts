import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from 'typeorm';
import { Patient } from '../../patients/entities/patient.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class MedicalRecord {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do prontuário' })
  id: number;

  @OneToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado ao prontuário' })
  patient: Patient;

  @Column()
  @ApiProperty({ description: 'ID do paciente' })
  patientId: number;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do prontuário' })
  createdAt: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Caminho do arquivo de exame', required: false })
  examFilePath?: string;
}
