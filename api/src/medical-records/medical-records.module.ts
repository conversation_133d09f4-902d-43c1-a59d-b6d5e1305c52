import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MedicalRecordsService } from './medical-records.service';
import { MedicalRecordsController } from './medical-records.controller';
import { MedicalRecord } from './entities/medical-record.entity';
import { PatientsModule } from '../patients/patients.module';
import { StorageModule } from '../common/storage.module';
import { Suggestion } from '../suggestions/entities/suggestion.entity';
import { SuggestionProcedure } from '../suggestions/entities/suggestion-procedure.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([MedicalRecord, Suggestion, SuggestionProcedure]),
    forwardRef(() => PatientsModule),
    StorageModule,
  ],
  controllers: [MedicalRecordsController],
  providers: [MedicalRecordsService],
  exports: [MedicalRecordsService],
})
export class MedicalRecordsModule {}
