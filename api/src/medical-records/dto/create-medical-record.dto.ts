import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateMedicalRecordDto {
  @ApiProperty({ description: 'ID do paciente', example: 1 })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  patientId: number;

  @ApiPropertyOptional({
    description: 'Caminho do arquivo de exame',
    example: 'exams/patient-1/exam.pdf',
  })
  @IsOptional()
  @IsString()
  examFilePath?: string;
}
