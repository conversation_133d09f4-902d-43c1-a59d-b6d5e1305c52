import {
  Injectable,
  NotFoundException,
  Logger,
  InternalServerErrorException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MedicalRecord } from './entities/medical-record.entity';
import { CreateMedicalRecordDto } from './dto/create-medical-record.dto';
import { UpdateMedicalRecordDto } from './dto/update-medical-record.dto';
import { PatientsService } from '../patients/patients.service';
import {
  CompleteMedicalRecord,
  MedicalRecordResponse,
} from './interfaces/complete-medical-record.interface';
import { Patient } from '../patients/entities/patient.entity';
import { Anamnesis } from '../anamnesis/entities/anamnesis.entity';
import { Budget } from '../budgets/entities/budget.entity';
import { Scheduling } from '../schedulings/entities/scheduling.entity';
import { TreatmentPlan } from '../treatment-plans/entities/treatment-plan.entity';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';
import { PatientExam } from '../exams/entities/patient-exam.entity';
import { PatientDocument } from '../documents/entities/patient-document.entity';
import { PatientPhoto } from '../photos/entities/patient-photo.entity';
import { Suggestion } from '../suggestions/entities/suggestion.entity';
import { TreatmentProcedureStatus } from '../treatment-plans/entities/treatment-procedure.entity';
import { IStorageProvider } from '../common/interfaces/storage.interface';

@Injectable()
export class MedicalRecordsService {
  private readonly logger = new Logger(MedicalRecordsService.name);

  constructor(
    @InjectRepository(MedicalRecord)
    private medicalRecordsRepository: Repository<MedicalRecord>,
    @InjectRepository(Suggestion)
    private suggestionsRepository: Repository<Suggestion>,
    @Inject(forwardRef(() => PatientsService))
    private patientsService: PatientsService,
    private dataSource: DataSource,
    @Inject('StorageProvider')
    private storageProvider: IStorageProvider,
  ) {}

  async create(
    createMedicalRecordDto: CreateMedicalRecordDto,
  ): Promise<MedicalRecord> {
    try {
      this.logger.log(
        `Creating medical record for patient: ${createMedicalRecordDto.patientId}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(createMedicalRecordDto.patientId);

      // Verificar se já existe um prontuário para este paciente
      const existingRecord = await this.medicalRecordsRepository.findOne({
        where: { patientId: createMedicalRecordDto.patientId },
      });

      if (existingRecord) {
        this.logger.log(
          `Medical record already exists for patient: ${createMedicalRecordDto.patientId}`,
        );
        return existingRecord;
      }

      const medicalRecord = this.medicalRecordsRepository.create(
        createMedicalRecordDto,
      );
      return this.medicalRecordsRepository.save(medicalRecord);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async findAll(): Promise<MedicalRecord[]> {
    try {
      this.logger.log('Finding all medical records');
      return this.medicalRecordsRepository.find({
        relations: ['patient'],
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding all medical records: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async findOne(id: number): Promise<MedicalRecord> {
    try {
      this.logger.log(`Finding medical record with id: ${id}`);
      const medicalRecord = await this.medicalRecordsRepository.findOne({
        where: { id },
        relations: ['patient'],
      });

      if (!medicalRecord) {
        throw new NotFoundException(`Prontuário com ID ${id} não encontrado`);
      }

      return medicalRecord;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async findByPatient(patientId: number): Promise<MedicalRecord> {
    try {
      this.logger.log(`Finding medical record for patient: ${patientId}`);
      const medicalRecord = await this.medicalRecordsRepository.findOne({
        where: { patientId },
        relations: ['patient'],
      });

      if (!medicalRecord) {
        throw new NotFoundException(`Prontuário não encontrado`);
      }

      return medicalRecord;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding medical record for patient: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async update(
    id: number,
    updateMedicalRecordDto: UpdateMedicalRecordDto,
  ): Promise<MedicalRecord> {
    try {
      this.logger.log(`Updating medical record with id: ${id}`);
      const medicalRecord = await this.findOne(id);

      // Atualizar os campos
      Object.assign(medicalRecord, updateMedicalRecordDto);

      return this.medicalRecordsRepository.save(medicalRecord);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    try {
      this.logger.log(`Removing medical record with id: ${id}`);
      const medicalRecord = await this.findOne(id);
      await this.medicalRecordsRepository.remove(medicalRecord);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error removing medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Obtém o prontuário completo de um paciente, agregando dados de várias entidades
   * @param patientId ID do paciente
   * @returns Prontuário completo com dados agregados
   */
  async getCompleteMedicalRecord(
    patientId: number,
  ): Promise<MedicalRecordResponse> {
    try {
      this.logger.log(
        `Getting complete medical record for patient: ${patientId}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      // Buscar ou criar o prontuário
      let medicalRecord: MedicalRecord;
      try {
        medicalRecord = await this.findByPatient(patientId);
      } catch (error) {
        if (error instanceof NotFoundException) {
          this.logger.log(
            `Medical record not found for patient ${patientId}, creating retroactively`,
          );
          medicalRecord = await this.createRetroactiveMedicalRecord(patientId);
        } else {
          throw error;
        }
      }

      // Buscar todos os dados relacionados ao paciente
      const data = await this.fetchAllPatientData(patientId);

      // Montar a resposta formatada
      return this.formatMedicalRecordResponse(medicalRecord, data);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error getting complete medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Cria um prontuário retroativo para um paciente que não possui um
   * @param patientId ID do paciente
   * @returns Prontuário criado
   */
  async createRetroactiveMedicalRecord(
    patientId: number,
  ): Promise<MedicalRecord> {
    try {
      this.logger.log(
        `Creating retroactive medical record for patient: ${patientId}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      // Criar o prontuário
      const createDto = new CreateMedicalRecordDto();
      createDto.patientId = patientId;

      return this.create(createDto);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating retroactive medical record: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Busca todos os dados relacionados a um paciente para compor o prontuário
   * @param patientId ID do paciente
   * @returns Objeto com todos os dados do paciente
   */
  private async fetchAllPatientData(
    patientId: number,
  ): Promise<CompleteMedicalRecord> {
    try {
      this.logger.log(`Fetching all data for patient: ${patientId}`);

      // Buscar o paciente com dados completos
      const patient = await this.dataSource.getRepository(Patient).findOne({
        where: { id: patientId },
        relations: ['patientType'],
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar anamneses
      const anamneses = await this.dataSource.getRepository(Anamnesis).find({
        where: { patientId },
        relations: ['employee', 'answers', 'answers.question'],
        order: { createdAt: 'DESC' },
      });

      // Buscar orçamentos
      const budgets = await this.dataSource.getRepository(Budget).find({
        where: { patientId },
        relations: ['dentist', 'items', 'items.procedure'],
        order: { createdAt: 'DESC' },
      });

      // Buscar agendamentos
      const schedulings = await this.dataSource.getRepository(Scheduling).find({
        where: { patient: { id: patientId } },
        relations: ['dentist'],
        order: { date: 'DESC', time: 'DESC' },
      });

      // Buscar planos de tratamento com procedimentos
      const treatmentPlans = await this.dataSource
        .getRepository(TreatmentPlan)
        .find({
          where: { patient: { id: patientId } },
          relations: ['dentist', 'procedures', 'procedures.professional'],
          order: { createdAt: 'DESC' },
        });

      this.logger.log(
        `Found ${treatmentPlans.length} treatment plans for patient ${patientId}`,
      );

      // Debug: verificar se os procedimentos estão sendo carregados
      for (const plan of treatmentPlans) {
        this.logger.log(
          `Treatment plan ${plan.id} has ${plan.procedures?.length || 0} procedures`,
        );
        if (plan.procedures && plan.procedures.length > 0) {
          plan.procedures.forEach((proc, index) => {
            this.logger.log(
              `  Procedure ${index + 1}: ${proc.name} (ID: ${proc.id})`,
            );
          });
        }
      }

      // Nota: O campo treatmentProcedures foi removido da entidade Scheduling
      // Os procedimentos agora são buscados através do relacionamento TreatmentProcedure.appointment
      // Por enquanto, comentando esta lógica até que seja implementada nova abordagem

      // // Carregar procedimentos para cada agendamento
      // for (const scheduling of schedulings) {
      //   try {
      //     // Buscar procedimentos associados a este agendamento
      //     const procedures = await this.dataSource.getRepository(TreatmentProcedure).find({
      //       where: { appointment: { id: scheduling.id } },
      //       relations: ['procedure', 'professional']
      //     });
      //     // @ts-expect-error - Adicionar propriedade temporária
      //     scheduling.treatmentProcedures = procedures;
      //   } catch (error) {
      //     const errorMessage =
      //       error instanceof Error ? error.message : 'Erro desconhecido';
      //     this.logger.error(
      //       `Error loading procedures for scheduling ${scheduling.id}: ${errorMessage}`,
      //     );
      //     // @ts-expect-error - Adicionar propriedade temporária
      //     scheduling.treatmentProcedures = [];
      //   }
      // }

      // Buscar procedimentos concluídos
      const completedProcedures = await this.dataSource
        .getRepository(TreatmentProcedure)
        .createQueryBuilder('procedure')
        .innerJoin('procedure.treatmentPlan', 'plan')
        .innerJoin('plan.patient', 'patient')
        .leftJoinAndSelect('procedure.professional', 'professional')
        .leftJoinAndSelect('procedure.appointment', 'appointment')
        .where('patient.id = :patientId', { patientId })
        .andWhere('procedure.status = :status', {
          status: TreatmentProcedureStatus.COMPLETED,
        })
        .orderBy('procedure.executionDate', 'DESC')
        .getMany();

      // Buscar exames
      const exams = await this.dataSource.getRepository(PatientExam).find({
        where: { patientId },
        order: { uploadedAt: 'DESC' },
      });

      // Gerar URLs temporárias para os exames
      for (const exam of exams) {
        try {
          exam.fileUrl = await this.storageProvider.getFileUrl(exam.fileUrl);
        } catch (error: unknown) {
          const errorMessage =
            error instanceof Error ? error.message : 'Erro desconhecido';
          this.logger.error(
            `Error generating URL for exam ${exam.id}: ${errorMessage}`,
          );
          // Manter a URL original se houver erro
        }
      }

      // Buscar documentos
      const documents = await this.dataSource
        .getRepository(PatientDocument)
        .find({
          where: { patientId },
          order: { uploadedAt: 'DESC' },
        });

      // Buscar fotos
      const photos = await this.dataSource.getRepository(PatientPhoto).find({
        where: { patientId },
        order: { uploadedAt: 'DESC' },
      });

      // Buscar sugestões de IA
      let suggestions: Suggestion[] = [];
      try {
        suggestions = await this.suggestionsRepository.find({
          where: { patient: { id: patientId } },
          relations: ['patient', 'procedures', 'procedures.procedure'],
          order: { createdAt: 'DESC' },
        });
        this.logger.log(
          `Found ${suggestions.length} suggestions for patient ${patientId}`,
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.error(
          `Error fetching suggestions for patient ${patientId}: ${errorMessage}`,
        );
        // Em caso de erro, manter array vazio para não quebrar o prontuário
        suggestions = [];
      }

      return {
        id: 0, // Será preenchido depois
        patientId,
        createdAt: new Date(),
        patient,
        anamneses,
        budgets,
        schedulings,
        treatmentPlans,
        completedProcedures,
        exams,
        documents,
        photos,
        suggestions,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error fetching patient data: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Formata os dados do prontuário para a resposta da API
   * @param medicalRecord Prontuário base
   * @param data Dados completos do paciente
   * @returns Resposta formatada do prontuário
   */
  private formatMedicalRecordResponse(
    medicalRecord: MedicalRecord,
    data: CompleteMedicalRecord,
  ): MedicalRecordResponse {
    try {
      const {
        patient,
        anamneses,
        budgets,
        schedulings,
        treatmentPlans,
        exams,
        documents,
        suggestions,
      } = data;

      // Formatar dados do paciente
      const formattedPatient = {
        id: patient.id,
        name: patient.name,
        birthDate: patient.birthDate,
        gender: patient.gender,
        cpf: patient.cpf,
        email: patient.email,
        phone: patient.phone,
        whatsapp: patient.whatsapp,
        address: patient.addressStreet
          ? {
              zipCode: patient.addressZipCode,
              street: patient.addressStreet,
              number: patient.addressNumber,
              neighborhood: patient.addressNeighborhood,
              city: patient.addressCity,
              state: patient.addressState,
              complement: patient.addressComplement,
            }
          : undefined,
        profession: patient.profession,
        medicalRecordNumber: patient.medicalRecordNumber,
        category: patient.category,
        registrationDate: patient.registrationDate,
        notes: patient.notes,
      };

      // Formatar anamneses
      const formattedAnamneses = anamneses.map((anamnesis) => ({
        id: anamnesis.id,
        createdAt: anamnesis.createdAt,
        employeeName: anamnesis.employee?.name,
        answers: anamnesis.answers.map((answer) => ({
          questionText: answer.questionText,
          answer: answer.answer,
        })),
      }));

      // Formatar orçamentos
      const formattedBudgets = budgets.map((budget) => ({
        id: budget.id,
        createdAt: budget.createdAt,
        totalValue: budget.totalValue,
        status: budget.status,
        dentistName: budget.dentist?.name,
        amountPaid: budget.amountPaid,
        discount: budget.discount,
        items: budget.items.map((item) => ({
          procedureName:
            item.procedure?.name || 'Procedimento não especificado',
          tooth: item.tooth,
          value: item.value,
        })),
      }));

      // Formatar agendamentos
      const formattedSchedulings = schedulings.map((scheduling) => ({
        id: scheduling.id,
        date: scheduling.date,
        time: scheduling.time,
        status: scheduling.status,
        dentistName: scheduling.dentist?.name || undefined,
        notes: scheduling.notes,
        procedures: [], // Campo treatmentProcedures foi removido da entidade Scheduling
      }));

      // Formatar planos de tratamento
      const formattedTreatmentPlans = treatmentPlans.map((plan) => {
        // Como usamos eager loading, procedures já deve ser um array
        const procedures = Array.isArray(plan.procedures)
          ? plan.procedures
          : [];
        this.logger.log(
          `Formatting treatment plan ${plan.id} with ${procedures.length} procedures`,
        );

        return {
          id: plan.id,
          createdAt: plan.createdAt,
          dentistName: plan.dentist?.name,
          totalValue: plan.totalValue,
          completionPercentage: plan.completionPercentage,
          status: plan.status as string,
          procedures: procedures.map((proc) => ({
            id: proc.id,
            name: proc.name,
            tooth: proc.tooth || undefined,
            value: proc.value,
            status: proc.status as string,
            executionDate: proc.executionDate || undefined,
            professionalName: proc.professional?.name || undefined,
            notes: proc.notes || undefined,
          })),
        };
      });

      // Formatar exames
      const formattedExams = exams.map((exam) => ({
        id: exam.id,
        name: exam.name,
        fileName: exam.fileName,
        fileUrl: exam.fileUrl,
        uploadedAt: exam.uploadedAt,
      }));

      // Formatar documentos
      const formattedDocuments = documents.map((doc) => ({
        id: doc.id,
        name: doc.name,
        fileName: doc.fileName,
        fileUrl: doc.fileUrl,
        uploadedAt: doc.uploadedAt,
      }));

      // Formatar sugestões
      const formattedSuggestions = suggestions.map((suggestion) => ({
        id: suggestion.id,
        createdAt: suggestion.createdAt,
        status: suggestion.status as string,
        iaReasoning: suggestion.iaReasoning,
        procedures: suggestion.procedures.map((proc) => ({
          procedureId: proc.procedure?.id,
          procedureName:
            proc.procedure?.name || `Procedimento ID: ${proc.procedure?.id}`,
          expectedDate: proc.expectedDate || undefined,
          notes: proc.notes || undefined,
        })),
      }));

      // Montar a resposta final
      return {
        id: medicalRecord.id,
        patientId: medicalRecord.patientId,
        createdAt: medicalRecord.createdAt,
        patient: formattedPatient,
        anamneses: formattedAnamneses,
        budgets: formattedBudgets,
        schedulings: formattedSchedulings,
        treatmentPlans: formattedTreatmentPlans,
        exams: formattedExams,
        documents: formattedDocuments,
        suggestions: formattedSuggestions,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error formatting medical record response: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao formatar dados do prontuário',
      );
    }
  }
}
