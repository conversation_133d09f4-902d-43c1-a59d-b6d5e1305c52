import { Patient } from '../../patients/entities/patient.entity';
import { Anamnesis } from '../../anamnesis/entities/anamnesis.entity';
import { Budget } from '../../budgets/entities/budget.entity';
import { Scheduling } from '../../schedulings/entities/scheduling.entity';
import { TreatmentPlan } from '../../treatment-plans/entities/treatment-plan.entity';
import { TreatmentProcedure } from '../../treatment-plans/entities/treatment-procedure.entity';
import { PatientExam } from '../../exams/entities/patient-exam.entity';
import { PatientDocument } from '../../documents/entities/patient-document.entity';
import { PatientPhoto } from '../../photos/entities/patient-photo.entity';
import { Suggestion } from '../../suggestions/entities/suggestion.entity';
// import { MedicalRecord } from '../entities/medical-record.entity';

export interface CompleteMedicalRecord {
  // Dados básicos do prontuário
  id: number;
  patientId: number;
  createdAt: Date;

  // Dados do paciente
  patient: Patient;

  // Anamneses
  anamneses: Anamnesis[];

  // Orçamentos
  budgets: Budget[];

  // Agendamentos
  schedulings: Scheduling[];

  // Planos de tratamento
  treatmentPlans: TreatmentPlan[];

  // Procedimentos realizados
  completedProcedures: TreatmentProcedure[];

  // Exames
  exams: PatientExam[];

  // Documentos
  documents: PatientDocument[];

  // Fotos
  photos: PatientPhoto[];

  // Sugestões de tratamento da IA
  suggestions: Suggestion[];
}

export interface MedicalRecordResponse {
  // Dados básicos do prontuário
  id: number;
  patientId: number;
  createdAt: Date;

  // Dados do paciente
  patient: {
    id: number;
    name: string;
    birthDate: Date;
    gender: string;
    cpf: string;
    email: string;
    phone: string;
    whatsapp?: string;
    address?: {
      zipCode?: string;
      street?: string;
      number?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
      complement?: string;
    };
    profession?: string;
    medicalRecordNumber?: string;
    category?: string;
    registrationDate: Date;
    notes?: string;
  };

  // Anamneses
  anamneses: {
    id: string;
    createdAt: Date;
    employeeName?: string;
    answers: {
      questionText: string;
      answer: string;
    }[];
  }[];

  // Orçamentos
  budgets: {
    id: number;
    createdAt: Date;
    totalValue: number;
    status: string;
    dentistName: string;
    items: {
      procedureName: string;
      tooth?: string;
      value: number;
    }[];
  }[];

  // Agendamentos
  schedulings: {
    id: number;
    date: Date;
    time: string;
    status: string;
    dentistName?: string;
    notes?: string;
    procedures?: {
      name: string;
      tooth?: string;
      status: string;
    }[];
  }[];

  // Planos de tratamento
  treatmentPlans: {
    id: number;
    createdAt: Date;
    dentistName?: string;
    totalValue: number;
    completionPercentage: number;
    status: string;
    procedures: {
      id: number;
      name: string;
      tooth?: string;
      value: number;
      status: string;
      executionDate?: Date;
      professionalName?: string;
      notes?: string;
    }[];
  }[];

  // Exames
  exams: {
    id: string;
    name: string;
    fileName: string;
    fileUrl: string;
    uploadedAt: Date;
  }[];

  // Documentos
  documents: {
    id: string;
    name: string;
    fileName: string;
    fileUrl: string;
    uploadedAt: Date;
  }[];

  // Sugestões de tratamento da IA
  suggestions: {
    id: string;
    createdAt: Date;
    status: string;
    iaReasoning: string | null;
    procedures: {
      procedureId: number;
      procedureName: string;
      expectedDate?: Date;
      notes?: string;
    }[];
  }[];
}
