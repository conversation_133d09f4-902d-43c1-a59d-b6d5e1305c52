import {
  Enti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Dentist } from '../../dentists/entities/dentist.entity';

export enum ExceptionType {
  DAY_OFF = 'day-off',
  CUSTOM_HOURS = 'custom-hours',
}

export interface CustomHours {
  start: string;
  end: string;
}

@Entity('dentist_exceptions')
export class DentistException {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único da exceção' })
  id: number;

  @ManyToOne(() => Dentist, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dentistId' })
  @ApiProperty({ description: 'Dentista associado à exceção' })
  dentist: Dentist;

  @Column()
  dentistId: number;

  @Column({ type: 'date' })
  @ApiProperty({ description: 'Data da exceção (YYYY-MM-DD)' })
  date: string;

  @Column({
    type: 'enum',
    enum: ExceptionType,
  })
  @ApiProperty({
    description: 'Tipo da exceção',
    enum: ExceptionType,
    example: ExceptionType.DAY_OFF,
  })
  type: ExceptionType;

  @Column('json', { nullable: true })
  @ApiProperty({
    description: 'Horários personalizados (apenas para tipo custom-hours)',
    example: [{ start: '08:00', end: '12:00' }],
    required: false,
  })
  customHours?: CustomHours[];

  @Column({ nullable: true, length: 255 })
  @ApiProperty({
    description: 'Motivo ou observação da exceção',
    required: false,
  })
  reason?: string;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
