import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Dentist } from '../../dentists/entities/dentist.entity';

export interface WeeklySchedule {
  [day: string]: { start: string; end: string }[];
}

@Entity('dentist_schedules')
export class DentistSchedule {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único da escala do dentista' })
  id: number;

  @ManyToOne(() => Dentist, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dentistId' })
  @ApiProperty({ description: 'Dentista associado à escala' })
  dentist: Dentist;

  @Column()
  dentistId: number;

  @Column('json')
  @ApiProperty({
    description: 'Escala semanal do dentista',
    example: {
      monday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      tuesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      wednesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      thursday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      friday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      saturday: [{ start: '08:00', end: '12:00' }],
      sunday: [],
    },
  })
  weeklySchedule: WeeklySchedule;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
