import { ApiProperty } from '@nestjs/swagger';
import { WeeklySchedule } from '../entities/dentist-schedule.entity';

export class DentistScheduleOverviewDto {
  @ApiProperty({ description: 'ID do dentista' })
  dentistId: number;

  @ApiProperty({ description: 'Nome do dentista' })
  dentistName: string;

  @ApiProperty({ description: 'Especialidade do dentista' })
  dentistSpecialty: string;

  @ApiProperty({ description: 'Indica se o dentista está ativo' })
  dentistActive: boolean;

  @ApiProperty({
    description: 'Indica se o dentista possui escala configurada',
  })
  hasSchedule: boolean;

  @ApiProperty({
    description: 'Escala semanal do dentista',
    required: false,
    example: {
      monday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      tuesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      wednesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      thursday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      friday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      saturday: [{ start: '08:00', end: '12:00' }],
      sunday: [],
    },
  })
  weeklySchedule?: WeeklySchedule;

  @ApiProperty({
    description: 'Número de exceções ativas para os próximos 30 dias',
  })
  upcomingExceptions: number;
}
