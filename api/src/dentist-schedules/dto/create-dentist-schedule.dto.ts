import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsOptional } from 'class-validator';
import { WeeklySchedule } from '../entities/dentist-schedule.entity';

class TimeSlot {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> in<PERSON> (HH:MM)', example: '08:00' })
  start: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> de fim (HH:MM)', example: '12:00' })
  end: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
class DaySchedule {
  @ApiProperty({
    description: 'Hor<PERSON>rios de segunda-feira',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  monday?: TimeSlot[];

  @ApiProperty({
    description: 'Horários de terça-feira',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  tuesday?: TimeSlot[];

  @ApiProperty({
    description: 'Hor<PERSON><PERSON><PERSON> de quarta-feira',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  wednesday?: TimeSlot[];

  @ApiProperty({
    description: 'Hor<PERSON>rios de quinta-feira',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  thursday?: TimeSlot[];

  @ApiProperty({
    description: 'Horários de sexta-feira',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  friday?: TimeSlot[];

  @ApiProperty({
    description: 'Horários de sábado',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  saturday?: TimeSlot[];

  @ApiProperty({
    description: 'Horários de domingo',
    type: [TimeSlot],
    required: false,
  })
  @IsOptional()
  sunday?: TimeSlot[];
}

export class CreateDentistScheduleDto {
  @ApiProperty({
    description: 'Escala semanal do dentista',
    example: {
      monday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      tuesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      wednesday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      thursday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      friday: [
        { start: '08:00', end: '12:00' },
        { start: '14:00', end: '18:00' },
      ],
      saturday: [{ start: '08:00', end: '12:00' }],
      sunday: [],
    },
  })
  @IsObject()
  weeklySchedule: WeeklySchedule;
}
