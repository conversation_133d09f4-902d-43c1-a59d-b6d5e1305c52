import { ApiProperty } from '@nestjs/swagger';

export class TimeSlotDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON>ício (HH:MM)', example: '08:00' })
  start: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> de fim (HH:MM)', example: '08:30' })
  end: string;

  @ApiProperty({
    description: 'Indica se o horário está disponível',
    example: true,
  })
  available: boolean;
}

export class DentistAvailabilityDto {
  @ApiProperty({ description: 'ID do dentista' })
  dentistId: number;

  @ApiProperty({ description: 'Nome do dentista' })
  dentistName: string;

  @ApiProperty({ description: 'Data consultada (YYYY-MM-DD)' })
  date: string;

  @ApiProperty({ description: 'Dia da semana', example: 'monday' })
  dayOfWeek: string;

  @ApiProperty({ description: 'Indica se o dentista trabalha neste dia' })
  isWorkingDay: boolean;

  @ApiProperty({
    description: 'Hor<PERSON>rios disponíveis no dia',
    type: [TimeSlotDto],
  })
  availableSlots: TimeSlotDto[];

  @ApiProperty({
    description: 'Exceção aplicada ao dia (se houver)',
    required: false,
  })
  exception?: {
    type: string;
    reason?: string;
    customHours?: { start: string; end: string }[];
  };
}

export class AvailableTimesDto {
  @ApiProperty({
    description: 'Lista de horários disponíveis',
    type: [String],
    example: ['08:00', '08:30', '09:00'],
  })
  availableTimes: string[];
}
