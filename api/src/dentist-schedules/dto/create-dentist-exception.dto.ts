import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  ValidateNested,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  ExceptionType,
  CustomHours,
} from '../entities/dentist-exception.entity';

class CustomHoursDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON><PERSON> de início (HH:MM)', example: '08:00' })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Horário deve estar no formato HH:MM',
  })
  start: string;

  @ApiProperty({ description: '<PERSON>r<PERSON><PERSON> de fim (HH:MM)', example: '12:00' })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: '<PERSON><PERSON><PERSON><PERSON> deve estar no formato HH:MM',
  })
  end: string;
}

export class CreateDentistExceptionDto {
  @ApiProperty({
    description: 'Data da exceção (YYYY-MM-DD)',
    example: '2024-01-15',
  })
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Data deve estar no formato YYYY-MM-DD',
  })
  date: string;

  @ApiProperty({
    description: 'Tipo da exceção',
    enum: ExceptionType,
    example: ExceptionType.DAY_OFF,
  })
  @IsEnum(ExceptionType)
  type: ExceptionType;

  @ApiProperty({
    description: 'Horários personalizados (obrigatório para tipo custom-hours)',
    type: [CustomHoursDto],
    required: false,
    example: [{ start: '08:00', end: '12:00' }],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomHoursDto)
  customHours?: CustomHours[];

  @ApiProperty({
    description: 'Motivo ou observação da exceção',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
