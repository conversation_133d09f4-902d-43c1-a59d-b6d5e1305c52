import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  MoreThanOrEqual,
  LessThanOrEqual,
  Between,
  FindOperator,
} from 'typeorm';
import { DentistSchedule } from './entities/dentist-schedule.entity';
import {
  DentistException,
  ExceptionType,
} from './entities/dentist-exception.entity';
import { Dentist } from '../dentists/entities/dentist.entity';
import { Scheduling } from '../schedulings/entities/scheduling.entity';
import { CreateDentistScheduleDto } from './dto/create-dentist-schedule.dto';

import { CreateDentistExceptionDto } from './dto/create-dentist-exception.dto';
import {
  DentistAvailabilityDto,
  TimeSlotDto,
  AvailableTimesDto,
} from './dto/dentist-availability.dto';
import { DentistScheduleOverviewDto } from './dto/schedule-overview.dto';

@Injectable()
export class DentistSchedulesService {
  private readonly logger = new Logger(DentistSchedulesService.name);

  constructor(
    @InjectRepository(DentistSchedule)
    private dentistScheduleRepository: Repository<DentistSchedule>,
    @InjectRepository(DentistException)
    private dentistExceptionRepository: Repository<DentistException>,
    @InjectRepository(Dentist)
    private dentistRepository: Repository<Dentist>,
    @InjectRepository(Scheduling)
    private schedulingRepository: Repository<Scheduling>,
  ) {}

  async createOrUpdateSchedule(
    dentistId: number,
    createScheduleDto: CreateDentistScheduleDto,
  ): Promise<DentistSchedule> {
    this.logger.log(`Creating/updating schedule for dentist ${dentistId}`);

    // Verificar se o dentista existe
    const dentist = await this.dentistRepository.findOne({
      where: { id: dentistId },
    });

    if (!dentist) {
      throw new NotFoundException(
        `Dentista com ID ${dentistId} não encontrado`,
      );
    }

    // Verificar se já existe uma escala para este dentista
    let schedule = await this.dentistScheduleRepository.findOne({
      where: { dentistId },
    });

    if (schedule) {
      // Atualizar escala existente
      schedule.weeklySchedule = createScheduleDto.weeklySchedule;
      this.logger.log(`Updating existing schedule for dentist ${dentistId}`);
    } else {
      // Criar nova escala
      schedule = this.dentistScheduleRepository.create({
        dentistId,
        weeklySchedule: createScheduleDto.weeklySchedule,
      });
      this.logger.log(`Creating new schedule for dentist ${dentistId}`);
    }

    return this.dentistScheduleRepository.save(schedule);
  }

  async getSchedule(dentistId: number): Promise<DentistSchedule> {
    this.logger.log(`Getting schedule for dentist ${dentistId}`);

    const schedule = await this.dentistScheduleRepository.findOne({
      where: { dentistId },
      relations: ['dentist'],
    });

    if (!schedule) {
      throw new NotFoundException(
        `Escala não encontrada para o dentista com ID ${dentistId}`,
      );
    }

    return schedule;
  }

  async createException(
    dentistId: number,
    createExceptionDto: CreateDentistExceptionDto,
  ): Promise<DentistException> {
    this.logger.log(
      `Creating exception for dentist ${dentistId} on ${createExceptionDto.date}`,
    );

    // Verificar se o dentista existe
    const dentist = await this.dentistRepository.findOne({
      where: { id: dentistId },
    });

    if (!dentist) {
      throw new NotFoundException(
        `Dentista com ID ${dentistId} não encontrado`,
      );
    }

    // Validar se é horário personalizado e tem customHours
    if (
      createExceptionDto.type === ExceptionType.CUSTOM_HOURS &&
      !createExceptionDto.customHours
    ) {
      throw new BadRequestException(
        'Horários personalizados são obrigatórios para exceções do tipo custom-hours',
      );
    }

    // Verificar se já existe uma exceção para esta data
    const existingException = await this.dentistExceptionRepository.findOne({
      where: { dentistId, date: createExceptionDto.date },
    });

    if (existingException) {
      throw new ConflictException(
        `Já existe uma exceção para o dentista ${dentistId} na data ${createExceptionDto.date}`,
      );
    }

    const exception = this.dentistExceptionRepository.create({
      dentistId,
      ...createExceptionDto,
    });

    return this.dentistExceptionRepository.save(exception);
  }

  async getExceptions(
    dentistId: number,
    startDate?: string,
    endDate?: string,
  ): Promise<DentistException[]> {
    this.logger.log(`Getting exceptions for dentist ${dentistId}`);

    const whereCondition: {
      dentistId: number;
      date?: string | FindOperator<string>;
    } = { dentistId };

    if (startDate && endDate) {
      whereCondition.date = Between(startDate, endDate);
    } else if (startDate) {
      whereCondition.date = MoreThanOrEqual(startDate);
    } else if (endDate) {
      whereCondition.date = LessThanOrEqual(endDate);
    }

    return this.dentistExceptionRepository.find({
      where: whereCondition,
      relations: ['dentist'],
      order: { date: 'ASC' },
    });
  }

  async deleteException(dentistId: number, exceptionId: number): Promise<void> {
    this.logger.log(
      `Deleting exception ${exceptionId} for dentist ${dentistId}`,
    );

    const exception = await this.dentistExceptionRepository.findOne({
      where: { id: exceptionId, dentistId },
    });

    if (!exception) {
      throw new NotFoundException(
        `Exceção com ID ${exceptionId} não encontrada para o dentista ${dentistId}`,
      );
    }

    await this.dentistExceptionRepository.remove(exception);
  }

  async getAvailability(
    dentistId: number,
    date: string,
  ): Promise<DentistAvailabilityDto> {
    this.logger.log(`Getting availability for dentist ${dentistId} on ${date}`);

    // Verificar se o dentista existe
    const dentist = await this.dentistRepository.findOne({
      where: { id: dentistId },
    });

    if (!dentist) {
      throw new NotFoundException(
        `Dentista com ID ${dentistId} não encontrado`,
      );
    }

    // Obter escala do dentista
    const schedule = await this.dentistScheduleRepository.findOne({
      where: { dentistId },
    });

    // Obter dia da semana
    const dayOfWeek = this.getDayOfWeek(date);

    // Verificar se há exceção para esta data
    const exception = await this.dentistExceptionRepository.findOne({
      where: { dentistId, date },
    });

    let isWorkingDay = false;
    let availableSlots: TimeSlotDto[] = [];

    if (exception) {
      if (exception.type === ExceptionType.DAY_OFF) {
        isWorkingDay = false;
      } else if (
        exception.type === ExceptionType.CUSTOM_HOURS &&
        exception.customHours
      ) {
        isWorkingDay = true;
        availableSlots = this.generateTimeSlots(exception.customHours);
      }
    } else if (schedule && schedule.weeklySchedule[dayOfWeek]) {
      const daySchedule = schedule.weeklySchedule[dayOfWeek];
      if (daySchedule && daySchedule.length > 0) {
        isWorkingDay = true;
        availableSlots = this.generateTimeSlots(daySchedule);
      }
    }

    return {
      dentistId,
      dentistName: dentist.name,
      date,
      dayOfWeek,
      isWorkingDay,
      availableSlots,
      exception: exception
        ? {
            type: exception.type,
            reason: exception.reason,
            customHours: exception.customHours,
          }
        : undefined,
    };
  }

  async getScheduleOverview(): Promise<DentistScheduleOverviewDto[]> {
    this.logger.log('Getting schedule overview for all dentists');

    const dentists = await this.dentistRepository.find({
      where: { active: true },
      order: { name: 'ASC' },
    });

    const overview: DentistScheduleOverviewDto[] = [];

    for (const dentist of dentists) {
      const schedule = await this.dentistScheduleRepository.findOne({
        where: { dentistId: dentist.id },
      });

      // Contar exceções dos próximos 30 dias
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      const upcomingExceptions = await this.dentistExceptionRepository.count({
        where: {
          dentistId: dentist.id,
          date: Between(
            new Date().toISOString().split('T')[0],
            thirtyDaysFromNow.toISOString().split('T')[0],
          ),
        },
      });

      overview.push({
        dentistId: dentist.id,
        dentistName: dentist.name,
        dentistSpecialty: dentist.specialty,
        dentistActive: dentist.active,
        hasSchedule: !!schedule,
        weeklySchedule: schedule?.weeklySchedule,
        upcomingExceptions,
      });
    }

    return overview;
  }

  async getAvailableTimes(
    dentistId: number,
    date: string,
    duration: number = 30,
    excludeAppointmentId?: number,
  ): Promise<AvailableTimesDto> {
    this.logger.log(
      `Getting available times for dentist ${dentistId} on ${date} with duration ${duration} minutes`,
    );

    // Verificar se o dentista existe
    const dentist = await this.dentistRepository.findOne({
      where: { id: dentistId },
    });

    if (!dentist) {
      throw new NotFoundException(
        `Dentista com ID ${dentistId} não encontrado`,
      );
    }

    // Obter escala do dentista
    const schedule = await this.dentistScheduleRepository.findOne({
      where: { dentistId },
    });

    // Obter dia da semana
    const dayOfWeek = this.getDayOfWeek(date);

    // Verificar se há exceção para esta data
    const exception = await this.dentistExceptionRepository.findOne({
      where: { dentistId, date },
    });

    let workingHours: { start: string; end: string }[] = [];

    if (exception) {
      if (exception.type === ExceptionType.DAY_OFF) {
        // Dentista não trabalha neste dia
        return { availableTimes: [] };
      } else if (
        exception.type === ExceptionType.CUSTOM_HOURS &&
        exception.customHours
      ) {
        workingHours = exception.customHours;
      }
    } else if (schedule && schedule.weeklySchedule[dayOfWeek]) {
      const daySchedule = schedule.weeklySchedule[dayOfWeek];
      if (daySchedule && daySchedule.length > 0) {
        workingHours = daySchedule;
      }
    }

    if (workingHours.length === 0) {
      return { availableTimes: [] };
    }

    // Buscar agendamentos existentes para este dentista nesta data
    const existingAppointments = await this.schedulingRepository.find({
      where: {
        dentist: { id: dentistId },
        date: new Date(date),
      },
    });

    // Filtrar agendamentos excluindo o que está sendo editado (se aplicável)
    const filteredAppointments = excludeAppointmentId
      ? existingAppointments.filter((apt) => apt.id !== excludeAppointmentId)
      : existingAppointments;

    // Gerar todos os slots possíveis
    const allSlots = this.generateAllTimeSlots(workingHours, duration);

    // Filtrar slots que conflitam com agendamentos existentes
    const availableSlots = allSlots.filter((slot) => {
      return !this.hasConflict(slot, filteredAppointments, duration);
    });

    return { availableTimes: availableSlots };
  }

  private getDayOfWeek(date: string): string {
    const days = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const dateObj = new Date(date + 'T00:00:00');
    return days[dateObj.getDay()];
  }

  private generateTimeSlots(
    timeRanges: { start: string; end: string }[],
  ): TimeSlotDto[] {
    const slots: TimeSlotDto[] = [];
    const slotDuration = 30; // 30 minutos por slot

    for (const range of timeRanges) {
      const startTime = this.timeToMinutes(range.start);
      const endTime = this.timeToMinutes(range.end);

      for (let time = startTime; time < endTime; time += slotDuration) {
        const slotStart = this.minutesToTime(time);
        const slotEnd = this.minutesToTime(time + slotDuration);

        slots.push({
          start: slotStart,
          end: slotEnd,
          available: true, // Por enquanto todos disponíveis, depois integrar com agendamentos
        });
      }
    }

    return slots;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  private generateAllTimeSlots(
    workingHours: { start: string; end: string }[],
    slotDuration: number,
  ): string[] {
    const slots: string[] = [];

    for (const range of workingHours) {
      const startTime = this.timeToMinutes(range.start);
      const endTime = this.timeToMinutes(range.end);

      for (
        let time = startTime;
        time <= endTime - slotDuration;
        time += slotDuration
      ) {
        const slotStart = this.minutesToTime(time);
        slots.push(slotStart);
      }
    }

    return slots.sort();
  }

  private hasConflict(
    slotTime: string,
    existingAppointments: Scheduling[],
    duration: number,
  ): boolean {
    const slotStartMinutes = this.timeToMinutes(slotTime);
    const slotEndMinutes = slotStartMinutes + duration;

    for (const appointment of existingAppointments) {
      const appointmentStartMinutes = this.timeToMinutes(appointment.time);
      const appointmentDuration = appointment.duration || 30; // Duração padrão de 30 minutos
      const appointmentEndMinutes =
        appointmentStartMinutes + appointmentDuration;

      // Verificar se há sobreposição
      if (
        (slotStartMinutes >= appointmentStartMinutes &&
          slotStartMinutes < appointmentEndMinutes) ||
        (slotEndMinutes > appointmentStartMinutes &&
          slotEndMinutes <= appointmentEndMinutes) ||
        (slotStartMinutes <= appointmentStartMinutes &&
          slotEndMinutes >= appointmentEndMinutes)
      ) {
        return true; // Há conflito
      }
    }

    return false; // Não há conflito
  }
}
