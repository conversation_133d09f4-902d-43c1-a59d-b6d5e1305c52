import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DentistSchedulesService } from './dentist-schedules.service';
import { CreateDentistScheduleDto } from './dto/create-dentist-schedule.dto';
import { UpdateDentistScheduleDto } from './dto/update-dentist-schedule.dto';
import { CreateDentistExceptionDto } from './dto/create-dentist-exception.dto';
import { DentistSchedule } from './entities/dentist-schedule.entity';
import { DentistException } from './entities/dentist-exception.entity';
import {
  DentistAvailabilityDto,
  AvailableTimesDto,
} from './dto/dentist-availability.dto';
import { DentistScheduleOverviewDto } from './dto/schedule-overview.dto';

@ApiTags('scale-of-dentists')
@Controller('dentists')
export class DentistSchedulesController {
  constructor(
    private readonly dentistSchedulesService: DentistSchedulesService,
  ) {}

  @Put(':id/schedule')
  @ApiOperation({ summary: 'Criar ou atualizar escala semanal do dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({
    status: 200,
    description: 'Escala atualizada com sucesso',
    type: DentistSchedule,
  })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  async updateSchedule(
    @Param('id', ParseIntPipe) dentistId: number,
    @Body() updateScheduleDto: UpdateDentistScheduleDto,
  ): Promise<DentistSchedule> {
    return this.dentistSchedulesService.createOrUpdateSchedule(
      dentistId,
      updateScheduleDto as CreateDentistScheduleDto,
    );
  }

  @Get(':id/schedule')
  @ApiOperation({ summary: 'Obter escala semanal do dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({
    status: 200,
    description: 'Escala encontrada',
    type: DentistSchedule,
  })
  @ApiResponse({ status: 404, description: 'Escala não encontrada' })
  async getSchedule(
    @Param('id', ParseIntPipe) dentistId: number,
  ): Promise<DentistSchedule> {
    return this.dentistSchedulesService.getSchedule(dentistId);
  }

  @Post(':id/exceptions')
  @ApiOperation({ summary: 'Criar exceção na escala do dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiResponse({
    status: 201,
    description: 'Exceção criada com sucesso',
    type: DentistException,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  @ApiResponse({ status: 409, description: 'Já existe exceção para esta data' })
  async createException(
    @Param('id', ParseIntPipe) dentistId: number,
    @Body() createExceptionDto: CreateDentistExceptionDto,
  ): Promise<DentistException> {
    return this.dentistSchedulesService.createException(
      dentistId,
      createExceptionDto,
    );
  }

  @Get(':id/exceptions')
  @ApiOperation({ summary: 'Obter exceções na escala do dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiQuery({
    name: 'startDate',
    description: 'Data inicial (YYYY-MM-DD)',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'Data final (YYYY-MM-DD)',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Exceções encontradas',
    type: [DentistException],
  })
  async getExceptions(
    @Param('id', ParseIntPipe) dentistId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<DentistException[]> {
    return this.dentistSchedulesService.getExceptions(
      dentistId,
      startDate,
      endDate,
    );
  }

  @Delete(':id/exceptions/:exceptionId')
  @ApiOperation({ summary: 'Remover exceção na escala do dentista' })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiParam({ name: 'exceptionId', description: 'ID da exceção' })
  @ApiResponse({ status: 204, description: 'Exceção removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Exceção não encontrada' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteException(
    @Param('id', ParseIntPipe) dentistId: number,
    @Param('exceptionId', ParseIntPipe) exceptionId: number,
  ): Promise<void> {
    return this.dentistSchedulesService.deleteException(dentistId, exceptionId);
  }

  @Get(':id/availability')
  @ApiOperation({
    summary: 'Verificar disponibilidade do dentista em uma data específica',
  })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiQuery({
    name: 'date',
    description: 'Data para verificar disponibilidade (YYYY-MM-DD)',
  })
  @ApiResponse({
    status: 200,
    description: 'Disponibilidade encontrada',
    type: DentistAvailabilityDto,
  })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  async getAvailability(
    @Param('id', ParseIntPipe) dentistId: number,
    @Query('date') date: string,
  ): Promise<DentistAvailabilityDto> {
    return this.dentistSchedulesService.getAvailability(dentistId, date);
  }

  @Get(':id/available-times')
  @ApiOperation({
    summary: 'Obter horários disponíveis do dentista para agendamento',
  })
  @ApiParam({ name: 'id', description: 'ID do dentista' })
  @ApiQuery({
    name: 'date',
    description: 'Data para verificar horários disponíveis (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'duration',
    description: 'Duração do agendamento em minutos (padrão: 30)',
    required: false,
  })
  @ApiQuery({
    name: 'excludeAppointmentId',
    description:
      'ID do agendamento a ser excluído da verificação (para edição)',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Horários disponíveis encontrados',
    type: AvailableTimesDto,
  })
  @ApiResponse({ status: 404, description: 'Dentista não encontrado' })
  async getAvailableTimes(
    @Param('id', ParseIntPipe) dentistId: number,
    @Query('date') date: string,
    @Query('duration') duration?: number,
    @Query('excludeAppointmentId') excludeAppointmentId?: number,
  ): Promise<AvailableTimesDto> {
    return this.dentistSchedulesService.getAvailableTimes(
      dentistId,
      date,
      duration ? Number(duration) : 30,
      excludeAppointmentId ? Number(excludeAppointmentId) : undefined,
    );
  }

  @Get('schedule/overview')
  @ApiOperation({ summary: 'Obter resumo das escalas de todos os dentistas' })
  @ApiResponse({
    status: 200,
    description: 'Resumo das escalas encontrado',
    type: [DentistScheduleOverviewDto],
  })
  async getScheduleOverview(): Promise<DentistScheduleOverviewDto[]> {
    return this.dentistSchedulesService.getScheduleOverview();
  }
}
