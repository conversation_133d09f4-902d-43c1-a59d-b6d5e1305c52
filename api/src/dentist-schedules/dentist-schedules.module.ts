import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DentistSchedulesService } from './dentist-schedules.service';
import { DentistSchedulesController } from './dentist-schedules.controller';
import { DentistSchedule } from './entities/dentist-schedule.entity';
import { DentistException } from './entities/dentist-exception.entity';
import { Dentist } from '../dentists/entities/dentist.entity';
import { Scheduling } from '../schedulings/entities/scheduling.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DentistSchedule,
      DentistException,
      Dentist,
      Scheduling,
    ]),
  ],
  controllers: [DentistSchedulesController],
  providers: [DentistSchedulesService],
  exports: [DentistSchedulesService],
})
export class DentistSchedulesModule {}
