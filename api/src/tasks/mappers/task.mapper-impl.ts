import { CreateTaskDto } from '../dto/create-task.dto';
import { TaskResponseDto } from '../dto/task-response.dto';
import { Task } from '../entities/task.entity';
import { TaskMapper } from './task.mapper';

export class TaskMapperImpl implements TaskMapper {
  toTaskResponse(task: Task): TaskResponseDto {
    const taskResponse = new TaskResponseDto();
    taskResponse.id = task.id;
    taskResponse.title = task.title;
    taskResponse.description = task.description;
    taskResponse.status = task.status;
    taskResponse.tags = task.tags;
    taskResponse.dueDate = task.dueDate;
    taskResponse.createdAt = task.createdAt;
    taskResponse.employee = task.employee;
    taskResponse.sector = task.sector;
    return taskResponse;
  }

  toTaskEntity(taskDto: CreateTaskDto): Task {
    const task = new Task();
    task.title = taskDto.title;
    task.description = taskDto.description;
    task.status = taskDto.status;
    task.priority = taskDto.priority;
    task.dueDate = new Date(taskDto.dueDate);
    task.tags = taskDto.tags || [];
    task.sector = taskDto.sector;
    return task;
  }
}
