import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TasksService } from './tasks.service';
import { TasksController } from './tasks.controller';
import { Task } from './entities/task.entity';
import { NotificationsModule } from '../notifications/notifications.module';
import { EmployeesModule } from '../employees/employees.module';
import { Employee } from '../employees/entities/employee.entity';
import { TaskMapperImpl } from './mappers/task.mapper-impl';

@Module({
  imports: [
    TypeOrmModule.forFeature([Task, Employee]),
    NotificationsModule,
    EmployeesModule,
  ],
  controllers: [TasksController],
  providers: [TasksService, TaskMapperImpl],
  exports: [TasksService],
})
export class TasksModule {}
