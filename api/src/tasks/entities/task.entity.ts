import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TaskStatus } from '../enums/task-status.enum';
import { TaskPriority } from '../enums/task-priority.enum';
import { Employee } from '../../employees/entities/employee.entity';
import { Sector } from '../enums/task-sector.enum';

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da tarefa' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Título da tarefa' })
  title: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Descrição da tarefa', required: false })
  description: string;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.TODO,
  })
  @ApiProperty({
    description: 'Status da tarefa',
    enum: TaskStatus,
    default: TaskStatus.TODO,
  })
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM,
  })
  @ApiProperty({
    description: 'Prioridade da tarefa',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM,
  })
  priority: TaskPriority;

  @Column({ type: 'date', nullable: true })
  @ApiProperty({
    description: 'Data limite para conclusão da tarefa',
    required: false,
  })
  dueDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({
    description: 'Data de conclusão da tarefa',
    required: false,
  })
  completedAt: Date | null;

  @ManyToOne(() => Employee, {
    nullable: true,
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  @ApiProperty({
    description: 'Funcionário responsável pela tarefa',
    required: false,
  })
  employee: Employee;

  @Column({
    type: 'enum',
    enum: Sector,
    default: Sector.ADMINISTRATION,
  })
  @ApiProperty({
    description: 'Setor responsável',
    enum: Sector,
    default: Sector.ADMINISTRATION,
  })
  sector: Sector;

  @Column('simple-array', { nullable: true })
  @ApiProperty({
    description: 'Tags associadas à tarefa',
    required: false,
    type: [String],
  })
  tags: string[];

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
