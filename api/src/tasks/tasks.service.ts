import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Task } from './entities/task.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { TaskStatus } from './enums/task-status.enum';
import { TaskPriority } from './enums/task-priority.enum';
import { NotificationsService } from '../notifications/notifications.service';
import { Employee } from '../employees/entities/employee.entity';
import { TaskMapperImpl } from './mappers/task.mapper-impl';
import { UpdateTaskDto } from './dto/update-task.dto';
import { Sector } from './enums/task-sector.enum';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private tasksRepository: Repository<Task>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    private notificationsService: NotificationsService,
    private taskImpl: TaskMapperImpl,
  ) {}

  async create(createTaskDto: CreateTaskDto): Promise<Task> {
    const task = this.taskImpl.toTaskEntity(createTaskDto);
    const employee = await this.employeeRepository.findOneBy({
      id: createTaskDto.employeeId,
    });

    //Essa condição é sempre verdade, mas é por questão de erro no eslint
    if (employee) {
      task.employee = employee;
    }

    const savedTask = await this.tasksRepository.save(task);
    if (savedTask.employee) {
      await this.notificationsService.createTaskAssignedNotification(savedTask);
    }

    return task;
  }

  async findAll(
    status?: TaskStatus,
    priority?: TaskPriority,
    employeeId?: string,
    search?: string,
    tags?: string,
    sector?: Sector,
  ): Promise<Task[]> {
    const queryBuilder = this.tasksRepository
      .createQueryBuilder('task')
      .leftJoinAndSelect('task.employee', 'employee');

    // Aplicar filtros se fornecidos
    if (status) {
      queryBuilder.andWhere('task.status = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('task.priority = :priority', { priority });
    }

    if (employeeId) {
      queryBuilder.andWhere('employee.id = :employeeId', { employeeId });
    }

    if (search) {
      // Buscar por título, nome do funcionário ou tags
      queryBuilder.andWhere(
        '(task.title LIKE :search OR employee.name LIKE :search OR task.tags LIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (tags && tags !== search) {
      // Buscar tarefas que contenham qualquer uma das tags fornecidas
      const tagList = tags.split(',').map((tag) => tag.trim());

      // Para cada tag, adicionar uma condição OR
      tagList.forEach((tag, index) => {
        queryBuilder.orWhere(`task.tags LIKE :tag${index}`, {
          [`tag${index}`]: `%${tag}%`,
        });
      });
    }

    if (sector) {
      queryBuilder.andWhere('task.sector = :sector', {
        sector: sector as string,
      });
    }

    // Ordenar por data de criação (mais recente primeiro)
    queryBuilder.orderBy('task.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { id },
      relations: ['employee'],
    });

    if (!task) {
      throw new NotFoundException(`Tarefa com ID ${id} não encontrada`);
    }

    return task;
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task> {
    const task = await this.findOne(id);
    const oldEmployee = task.employee;
    // Atualiza os atributos da tarefa
    this.applyTaskUpdates(task, updateTaskDto);

    // Verifica a mudança de responsável e trata a notificação
    await this.handleAssigneeChange(task, updateTaskDto);

    const updatedTask = await this.tasksRepository.save(task);

    // envia notificações
    await this.handleNotificationUpdate(updatedTask, oldEmployee);

    return updatedTask;
  }

  async remove(id: string): Promise<void> {
    const task = await this.findOne(id);
    await this.tasksRepository.remove(task).then(() => {
      // Não precisamos fazer nada com o resultado aqui
      return;
    });
  }

  // Método para aplicar as atualizações de atributos da tarefa
  private applyTaskUpdates(task: Task, updateTaskDto: UpdateTaskDto): void {
    //alguns atributos ficam como undefined, mas no banco de dados continua normal
    const data = task.completedAt;
    Object.assign(task, updateTaskDto);
    task.completedAt = data;

    // Verifica e atualiza o status da tarefa
    this.updateTaskStatus(task, updateTaskDto);
  }

  // Método para tratar a mudança de responsável
  private async handleAssigneeChange(
    task: Task,
    updateTaskDto: UpdateTaskDto,
  ): Promise<void> {
    const newAssignee =
      updateTaskDto.employeeId && updateTaskDto.employeeId !== task.employee.id;

    if (newAssignee) {
      const employee = await this.employeeRepository.findOneBy({
        id: updateTaskDto.employeeId,
      });
      if (employee) {
        await this.notificationsService.createTaskReassignmentNotification(
          task,
        );
        task.employee = employee;
        await this.notificationsService.createTaskAssignedNotification(task);
      }
    }
  }

  // Método para atualizar o status da tarefa
  private updateTaskStatus(task: Task, updateTaskDto: UpdateTaskDto): void {
    if (updateTaskDto.status === TaskStatus.DONE && !task.completedAt) {
      task.completedAt = new Date();
    } else if (task.completedAt) {
      task.completedAt = null;
    }
  }

  // Método para envio de notificações
  private async handleNotificationUpdate(
    updatedTask: Task,
    employee: Employee,
  ): Promise<void> {
    if (updatedTask.status === TaskStatus.DONE) {
      await this.notificationsService.createTaskCompletedNotification(
        updatedTask,
      );
    } else if (updatedTask.status === TaskStatus.CANCELLED) {
      await this.notificationsService.createTaskCancelledNotification(
        updatedTask,
      );
    } else if (updatedTask.employee === employee) {
      await this.notificationsService.updateTaskNotification(updatedTask);
    }
  }
}
