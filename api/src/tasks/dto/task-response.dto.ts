import { Employee } from '../../employees/entities/employee.entity';
import { TaskPriority } from '../enums/task-priority.enum';
import { TaskStatus } from '../enums/task-status.enum';
import { Sector } from '../enums/task-sector.enum';

export class TaskResponseDto {
  id: string;
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  sector: Sector;
  dueDate?: Date;
  employee: Employee;
  tags?: string[];
  completedAt?: Date;
  createdAt: Date;
}
