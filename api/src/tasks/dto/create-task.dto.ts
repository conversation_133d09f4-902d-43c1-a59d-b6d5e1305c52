import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsArray,
  IsDate,
  IsUUID,
} from 'class-validator';
import { TaskStatus } from '../enums/task-status.enum';
import { TaskPriority } from '../enums/task-priority.enum';
import { Type } from 'class-transformer';
import { Sector } from '../enums/task-sector.enum';

export class CreateTaskDto {
  @ApiProperty({ description: 'Título da tarefa' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Descrição da tarefa', required: false })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Status da tarefa',
    enum: TaskStatus,
    default: TaskStatus.TODO,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaskStatus)
  status: TaskStatus;

  @ApiProperty({
    description: 'Prioridade da tarefa',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority: TaskPriority;

  @ApiProperty({
    description: 'Data limite para conclusão da tarefa',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dueDate: string;

  @ApiProperty({
    description: 'ID do funcionário responsável pela tarefa',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  employeeId: string;

  @ApiProperty({
    description: 'Setor responsável',
    enum: Sector,
    default: Sector.ADMINISTRATION,
    required: true,
  })
  @IsEnum(Sector)
  sector: Sector;

  @ApiProperty({
    description: 'Tags associadas à tarefa',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Data de conclusão da tarefa',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  completedAt?: Date;
}
