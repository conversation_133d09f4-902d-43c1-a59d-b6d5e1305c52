import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsEnum,
  IsOptional,
  Min,
  IsString,
} from 'class-validator';
import { TreatmentProcedureStatus } from '../entities/treatment-procedure.entity';

export class CreateTreatmentProcedureWithPlanDto {
  @ApiProperty({ description: 'ID do paciente' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  patientId: number;

  @ApiProperty({ description: 'ID do procedimento' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do procedimento é obrigatório' })
  procedureId: number;

  @ApiProperty({
    description: 'ID do dentista responsável pelo plano',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  dentistId?: number;

  @ApiProperty({ description: 'Nome do procedimento' })
  @IsNotEmpty({ message: 'O nome do procedimento é obrigatório' })
  name: string;

  @ApiProperty({ description: 'Valor do procedimento' })
  @IsNumber()
  @Min(0, { message: 'O valor não pode ser negativo' })
  @IsNotEmpty({ message: 'O valor é obrigatório' })
  value: number;

  @ApiProperty({
    description: 'Número do dente relacionado ao procedimento',
    required: false,
  })
  @IsOptional()
  tooth?: string;

  @ApiProperty({
    description: 'Data de execução do procedimento',
    required: false,
  })
  @IsOptional()
  executionDate?: Date;

  @ApiProperty({ description: 'ID do profissional responsável' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do profissional é obrigatório' })
  professionalId: number;

  @ApiProperty({
    description: 'ID do agendamento relacionado (se houver)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  appointmentId?: number;

  @ApiProperty({
    description: 'Status do procedimento',
    enum: TreatmentProcedureStatus,
    default: TreatmentProcedureStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(TreatmentProcedureStatus)
  status?: TreatmentProcedureStatus;

  @ApiProperty({
    description: 'Observações sobre o procedimento',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Detalhes para a próxima consulta',
    required: false,
  })
  @IsOptional()
  @IsString()
  nextVisitDetails?: string;
}
