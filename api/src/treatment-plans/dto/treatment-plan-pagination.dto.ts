import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNumber, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { TreatmentPlanStatus } from '../entities/treatment-plan.entity';

export class TreatmentPlanPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Filtrar por status do plano de tratamento',
    enum: TreatmentPlanStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(TreatmentPlanStatus)
  status?: TreatmentPlanStatus;

  @ApiProperty({
    description: 'Filtrar por ID do paciente',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  patientId?: number;

  @ApiProperty({
    description: 'Filtrar por ID do orçamento',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  budgetId?: number;

  @ApiProperty({
    description: 'Se true, retorna todos os registros sem paginação',
    required: false,
  })
  @IsOptional()
  @IsString()
  noPagination?: string;

  // Não precisamos redeclarar o campo search, pois ele já existe na classe pai
}
