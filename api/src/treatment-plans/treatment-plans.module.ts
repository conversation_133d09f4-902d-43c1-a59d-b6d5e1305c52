import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TreatmentPlansService } from './treatment-plans.service';
import { TreatmentPlansController } from './treatment-plans.controller';
import { TreatmentPlan } from './entities/treatment-plan.entity';
import { TreatmentProcedure } from './entities/treatment-procedure.entity';
import { PatientsModule } from '../patients/patients.module';
import { BudgetsModule } from '../budgets/budgets.module';
import { ProceduresModule } from '../procedures/procedures.module';
import { DentistsModule } from '../dentists/dentists.module';
import { SchedulingsModule } from '../schedulings/schedulings.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TreatmentPlan, TreatmentProcedure]),
    PatientsModule,
    forwardRef(() => BudgetsModule),
    ProceduresModule,
    DentistsModule,
    SchedulingsModule,
  ],
  controllers: [TreatmentPlansController],
  providers: [TreatmentPlansService],
  exports: [TreatmentPlansService],
})
export class TreatmentPlansModule {}
