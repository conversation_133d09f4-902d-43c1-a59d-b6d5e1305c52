import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TreatmentPlan } from './treatment-plan.entity';
import { Procedure } from '../../procedures/entities/procedure.entity';
import { Scheduling } from '../../schedulings/entities/scheduling.entity';
import { Dentist } from '../../dentists/entities/dentist.entity';

export enum TreatmentProcedureStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('treatment_procedures')
export class TreatmentProcedure {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do procedimento do tratamento' })
  id: number;

  @ManyToOne(() => TreatmentPlan, (plan) => plan.procedures, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'treatmentPlanId' })
  @ApiProperty({
    description: 'Plano de tratamento associado',
    type: () => TreatmentPlan,
  })
  treatmentPlan: TreatmentPlan;

  @ManyToOne(() => Procedure)
  @JoinColumn({ name: 'procedureId' })
  @ApiProperty({ description: 'Procedimento associado', type: () => Procedure })
  procedure: Procedure;

  @Column()
  @ApiProperty({ description: 'Nome do procedimento' })
  name: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Valor do procedimento' })
  value: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @ApiProperty({
    description: 'Número do dente relacionado ao procedimento',
    required: false,
  })
  tooth: string | null;

  @Column({ type: 'datetime', nullable: true })
  @ApiProperty({
    description: 'Data de execução do procedimento',
    required: false,
  })
  executionDate: Date | null;

  @ManyToOne(() => Dentist)
  @JoinColumn({ name: 'professionalId' })
  @ApiProperty({ description: 'Profissional responsável', type: () => Dentist })
  professional: Dentist;

  @ManyToOne(() => Scheduling, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'appointmentId' })
  @ApiProperty({
    description: 'Agendamento relacionado (se houver)',
    required: false,
    type: () => Scheduling,
  })
  appointment: Scheduling | null;

  @Column({
    type: 'enum',
    enum: TreatmentProcedureStatus,
    default: TreatmentProcedureStatus.PENDING,
  })
  @ApiProperty({
    description: 'Status do procedimento',
    enum: TreatmentProcedureStatus,
    default: TreatmentProcedureStatus.PENDING,
  })
  status: TreatmentProcedureStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Observações sobre o procedimento',
    required: false,
  })
  notes: string | null;

  @Column({ type: 'text', nullable: true, name: 'next_visit_details' })
  @ApiProperty({
    description: 'Detalhes para a próxima consulta',
    required: false,
  })
  nextVisitDetails: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
