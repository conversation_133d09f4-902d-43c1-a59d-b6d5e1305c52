import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { Budget } from '../../budgets/entities/budget.entity';
import { TreatmentProcedure } from './treatment-procedure.entity';
import { Dentist } from '../../dentists/entities/dentist.entity';

export enum TreatmentPlanStatus {
  OPEN = 'open',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('treatment_plans')
export class TreatmentPlan {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do plano de tratamento' })
  id: number;

  @ManyToOne(() => Patient, (patient) => patient.treatmentPlans, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({
    description: 'Paciente associado ao plano',
    type: () => Patient,
  })
  patient: Patient;

  @ManyToOne(() => Budget, (budget) => budget.treatmentPlans, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'budgetId' })
  @ApiProperty({
    description: 'Orçamento relacionado (se houver)',
    required: false,
    type: () => Budget,
  })
  budget: Budget | null;

  @ManyToOne(() => Dentist, { nullable: true })
  @JoinColumn({ name: 'dentistId' })
  @ApiProperty({
    description: 'Dentista responsável pelo plano',
    required: false,
    type: () => Dentist,
  })
  dentist: Dentist | null;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Valor total do plano de tratamento' })
  totalValue: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  @ApiProperty({
    description: 'Porcentagem de conclusão do plano de tratamento',
  })
  completionPercentage: number;

  @Column({
    type: 'enum',
    enum: TreatmentPlanStatus,
    default: TreatmentPlanStatus.OPEN,
  })
  @ApiProperty({
    description: 'Status do plano de tratamento',
    enum: TreatmentPlanStatus,
    default: TreatmentPlanStatus.OPEN,
  })
  status: TreatmentPlanStatus;

  @OneToMany(() => TreatmentProcedure, (procedure) => procedure.treatmentPlan, {
    cascade: true,
    eager: false,
  })
  @ApiProperty({
    description: 'Procedimentos do plano de tratamento',
    type: () => [TreatmentProcedure],
  })
  procedures: TreatmentProcedure[];

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do plano de tratamento' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({
    description: 'Data da última atualização do plano de tratamento',
  })
  updatedAt: Date;
}
