import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { TreatmentPlansService } from './treatment-plans.service';
import { CreateTreatmentPlanDto } from './dto/create-treatment-plan.dto';
import { UpdateTreatmentPlanDto } from './dto/update-treatment-plan.dto';
import { UpdateTreatmentProcedureDto } from './dto/update-treatment-procedure.dto';
import { CreateTreatmentProcedureWithPlanDto } from './dto/create-treatment-procedure-with-plan.dto';
import { TreatmentPlan } from './entities/treatment-plan.entity';
import { TreatmentProcedure } from './entities/treatment-procedure.entity';
import { TreatmentPlanPaginationDto } from './dto/treatment-plan-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('treatment-plans')
@Controller('treatment-plans')
export class TreatmentPlansController {
  constructor(private readonly treatmentPlansService: TreatmentPlansService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo plano de tratamento' })
  @ApiResponse({
    status: 201,
    description: 'Plano de tratamento criado com sucesso',
    type: TreatmentPlan,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createTreatmentPlanDto: CreateTreatmentPlanDto,
  ): Promise<TreatmentPlan> {
    return this.treatmentPlansService.create(createTreatmentPlanDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os planos de tratamento' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar planos de tratamento',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status do plano de tratamento',
    enum: ['open', 'completed', 'cancelled'],
  })
  @ApiQuery({
    name: 'patientId',
    required: false,
    description: 'Filtrar por ID do paciente',
    type: Number,
  })
  @ApiQuery({
    name: 'budgetId',
    required: false,
    description: 'Filtrar por ID do orçamento',
    type: Number,
  })
  @ApiQuery({
    name: 'noPagination',
    required: false,
    description: 'Se true, retorna todos os registros sem paginação',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de planos de tratamento retornada com sucesso',
  })
  findAll(
    @Query() paginationDto: TreatmentPlanPaginationDto,
  ): Promise<PaginatedResponse<TreatmentPlan>> {
    return this.treatmentPlansService.findAllPaginated(paginationDto);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Listar planos de tratamento de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description:
      'Lista de planos de tratamento do paciente retornada com sucesso',
    type: [TreatmentPlan],
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  findByPatient(
    @Param('patientId') patientId: string,
  ): Promise<TreatmentPlan[]> {
    return this.treatmentPlansService.findByPatient(+patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um plano de tratamento pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do plano de tratamento' })
  @ApiResponse({
    status: 200,
    description: 'Plano de tratamento encontrado',
    type: TreatmentPlan,
  })
  @ApiResponse({
    status: 404,
    description: 'Plano de tratamento não encontrado',
  })
  findOne(@Param('id') id: string): Promise<TreatmentPlan> {
    return this.treatmentPlansService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um plano de tratamento' })
  @ApiParam({ name: 'id', description: 'ID do plano de tratamento' })
  @ApiResponse({
    status: 200,
    description: 'Plano de tratamento atualizado com sucesso',
    type: TreatmentPlan,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({
    status: 404,
    description: 'Plano de tratamento não encontrado',
  })
  update(
    @Param('id') id: string,
    @Body() updateTreatmentPlanDto: UpdateTreatmentPlanDto,
  ): Promise<TreatmentPlan> {
    return this.treatmentPlansService.update(+id, updateTreatmentPlanDto);
  }

  @Patch('procedures/:id')
  @ApiOperation({ summary: 'Atualizar um procedimento do plano de tratamento' })
  @ApiParam({ name: 'id', description: 'ID do procedimento' })
  @ApiResponse({
    status: 200,
    description: 'Procedimento atualizado com sucesso',
    type: TreatmentProcedure,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Procedimento não encontrado' })
  updateProcedure(
    @Param('id') id: string,
    @Body() updateTreatmentProcedureDto: UpdateTreatmentProcedureDto,
  ): Promise<TreatmentProcedure> {
    return this.treatmentPlansService.updateProcedure(
      +id,
      updateTreatmentProcedureDto,
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um plano de tratamento' })
  @ApiParam({ name: 'id', description: 'ID do plano de tratamento' })
  @ApiResponse({
    status: 204,
    description: 'Plano de tratamento removido com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Plano de tratamento não encontrado',
  })
  remove(@Param('id') id: string): Promise<void> {
    return this.treatmentPlansService.remove(+id);
  }

  @Delete('procedures/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um procedimento do plano de tratamento' })
  @ApiParam({ name: 'id', description: 'ID do procedimento' })
  @ApiResponse({
    status: 204,
    description: 'Procedimento removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Procedimento não encontrado' })
  removeProcedure(@Param('id') id: string): Promise<void> {
    return this.treatmentPlansService.removeProcedure(+id);
  }

  @Post('procedures/with-plan')
  @ApiOperation({
    summary: 'Criar um procedimento com plano de tratamento automático',
  })
  @ApiResponse({
    status: 201,
    description: 'Procedimento criado com plano automático com sucesso',
    type: TreatmentProcedure,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  createProcedureWithPlan(
    @Body() createDto: CreateTreatmentProcedureWithPlanDto,
  ): Promise<TreatmentProcedure> {
    return this.treatmentPlansService.createProcedureWithPlan(createDto);
  }
}
