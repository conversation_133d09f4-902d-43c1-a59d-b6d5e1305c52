import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import {
  TreatmentPlan,
  TreatmentPlanStatus,
} from './entities/treatment-plan.entity';
import {
  TreatmentProcedure,
  TreatmentProcedureStatus,
} from './entities/treatment-procedure.entity';
import { CreateTreatmentPlanDto } from './dto/create-treatment-plan.dto';
import { UpdateTreatmentPlanDto } from './dto/update-treatment-plan.dto';
import { UpdateTreatmentProcedureDto } from './dto/update-treatment-procedure.dto';
import { TreatmentPlanPaginationDto } from './dto/treatment-plan-pagination.dto';
import { CreateTreatmentProcedureWithPlanDto } from './dto/create-treatment-procedure-with-plan.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { PatientsService } from '../patients/patients.service';
import { Budget, BudgetStatus } from '../budgets/entities/budget.entity';
import { ProceduresService } from '../procedures/procedures.service';
import { DentistsService } from '../dentists/dentists.service';
import { SchedulingsService } from '../schedulings/schedulings.service';

@Injectable()
export class TreatmentPlansService {
  private readonly logger = new Logger(TreatmentPlansService.name);

  constructor(
    @InjectRepository(TreatmentPlan)
    private treatmentPlanRepository: Repository<TreatmentPlan>,
    @InjectRepository(TreatmentProcedure)
    private treatmentProcedureRepository: Repository<TreatmentProcedure>,
    private patientsService: PatientsService,
    private proceduresService: ProceduresService,
    private dentistsService: DentistsService,
    private schedulingsService: SchedulingsService,
    private dataSource: DataSource,
  ) {}

  async create(
    createTreatmentPlanDto: CreateTreatmentPlanDto,
  ): Promise<TreatmentPlan> {
    try {
      this.logger.log(
        `Creating treatment plan: ${JSON.stringify(createTreatmentPlanDto)}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(createTreatmentPlanDto.patientId);

      // Verificar se os procedimentos e profissionais existem
      for (const procedure of createTreatmentPlanDto.procedures) {
        await this.proceduresService.findOne(procedure.procedureId);
        await this.dentistsService.findOne(procedure.professionalId);

        // Verificar se o agendamento existe (se fornecido)
        if (procedure.appointmentId) {
          await this.schedulingsService.findOne(procedure.appointmentId);
        }
      }

      // Iniciar transação
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Criar o plano de tratamento
        const treatmentPlan = new TreatmentPlan();

        // Buscar o paciente
        const patient = await this.patientsService.findOne(
          createTreatmentPlanDto.patientId,
        );
        treatmentPlan.patient = patient;

        // Buscar o orçamento (se houver)
        if (createTreatmentPlanDto.budgetId) {
          const budget = await this.dataSource
            .getRepository(Budget)
            .findOne({ where: { id: createTreatmentPlanDto.budgetId } });
          treatmentPlan.budget = budget;
        }

        treatmentPlan.totalValue = createTreatmentPlanDto.totalValue;
        treatmentPlan.completionPercentage = 0; // Inicialmente 0%
        treatmentPlan.status =
          createTreatmentPlanDto.status || TreatmentPlanStatus.OPEN;

        // Salvar o plano de tratamento
        const savedTreatmentPlan =
          await queryRunner.manager.save(treatmentPlan);

        // Criar os procedimentos do plano de tratamento
        for (const procedureDto of createTreatmentPlanDto.procedures) {
          const procedureEntity = await this.proceduresService.findOne(
            procedureDto.procedureId,
          );
          const professional = await this.dentistsService.findOne(
            procedureDto.professionalId,
          );

          const treatmentProcedure = new TreatmentProcedure();
          treatmentProcedure.treatmentPlan = savedTreatmentPlan;
          treatmentProcedure.procedure = procedureEntity;
          treatmentProcedure.name = procedureDto.name;
          treatmentProcedure.value = procedureDto.value;
          treatmentProcedure.tooth = procedureDto.tooth || null;
          treatmentProcedure.executionDate = procedureDto.executionDate || null;
          treatmentProcedure.professional = professional;

          if (procedureDto.appointmentId) {
            const appointment = await this.schedulingsService.findOne(
              procedureDto.appointmentId,
            );
            treatmentProcedure.appointment = appointment;
          }

          treatmentProcedure.status =
            procedureDto.status || TreatmentProcedureStatus.PENDING;

          await queryRunner.manager.save(treatmentProcedure);
        }

        // Os procedimentos já foram salvos individualmente

        // Calcular a porcentagem de conclusão
        await this.updateCompletionPercentage(
          savedTreatmentPlan.id,
          queryRunner,
        );

        // Commit da transação
        await queryRunner.commitTransaction();

        // Retornar o plano de tratamento completo
        return this.findOne(savedTreatmentPlan.id);
      } catch (error) {
        // Rollback em caso de erro
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Liberar o queryRunner
        await queryRunner.release();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating treatment plan: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao criar plano de tratamento',
      );
    }
  }

  async createFromBudget(budget: Budget): Promise<TreatmentPlan> {
    // Iniciar transação
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Creating treatment plan from budget: ${budget.id}`);

      // Verificar se o orçamento está aprovado
      if (budget.status !== BudgetStatus.APPROVED) {
        throw new BadRequestException(
          'Apenas orçamentos aprovados podem gerar planos de tratamento',
        );
      }

      // Verificar se já existe um plano de tratamento para este orçamento
      const existingPlan = await queryRunner.manager.findOne(TreatmentPlan, {
        where: { budget: { id: budget.id } },
      });

      if (existingPlan) {
        throw new BadRequestException(
          `Já existe um plano de tratamento para o orçamento ${budget.id}`,
        );
      }

      // Verificar se o orçamento tem itens
      if (!budget.items || budget.items.length === 0) {
        throw new BadRequestException(
          `O orçamento ${budget.id} não possui itens para criar um plano de tratamento`,
        );
      }

      this.logger.log(
        `Orçamento ${budget.id} tem ${budget.items.length} itens`,
      );

      // Buscar todos os procedimentos e dentistas necessários
      const procedurePromises = budget.items.map(async (item) => {
        if (!item.procedure) {
          this.logger.log(
            `Buscando procedimento ${item.procedureId} para o item ${item.id}`,
          );
          try {
            const procedure = await this.proceduresService.findOne(
              item.procedureId,
            );
            return {
              ...item,
              procedure,
            };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Erro desconhecido';
            this.logger.error(
              `Erro ao buscar procedimento ${item.procedureId}: ${errorMessage}`,
            );
            throw new BadRequestException(
              `Procedimento ${item.procedureId} não encontrado`,
            );
          }
        }
        return item;
      });

      // Aguardar todas as promessas de busca de procedimentos
      const itemsWithProcedures = await Promise.all(procedurePromises);

      // Criar o plano de tratamento
      const treatmentPlan = new TreatmentPlan();

      // Buscar o paciente
      const patient = await this.patientsService.findOne(budget.patientId);
      treatmentPlan.patient = patient;

      // Associar ao orçamento
      treatmentPlan.budget = budget;

      // Definir valores
      treatmentPlan.totalValue = budget.totalValue;
      treatmentPlan.completionPercentage = 0;
      treatmentPlan.status = TreatmentPlanStatus.OPEN;

      // Salvar o plano de tratamento
      const savedTreatmentPlan = await queryRunner.manager.save(treatmentPlan);

      this.logger.log(
        `Plano de tratamento criado com ID ${savedTreatmentPlan.id}`,
      );

      // Criar os procedimentos do plano de tratamento
      for (const item of itemsWithProcedures) {
        if (!item.procedure) {
          throw new BadRequestException(
            `Procedimento não encontrado para o item ${item.id}`,
          );
        }

        const professional = await this.dentistsService.findOne(
          item.executingDentistId,
        );

        const treatmentProcedure = new TreatmentProcedure();
        treatmentProcedure.treatmentPlan = savedTreatmentPlan;
        treatmentProcedure.procedure = item.procedure;
        treatmentProcedure.name = item.procedure.name;
        treatmentProcedure.value = item.value;
        treatmentProcedure.tooth = item.tooth || null;
        treatmentProcedure.professional = professional;
        treatmentProcedure.status = TreatmentProcedureStatus.PENDING;

        await queryRunner.manager.save(treatmentProcedure);
        this.logger.log(
          `Procedimento ${treatmentProcedure.name} adicionado ao plano de tratamento`,
        );
      }

      // Commit da transação
      await queryRunner.commitTransaction();

      this.logger.log(
        `Plano de tratamento criado com sucesso para o orçamento ${budget.id}`,
      );

      // Retornar o plano de tratamento completo
      return this.findOne(savedTreatmentPlan.id);
    } catch (error) {
      // Rollback em caso de erro
      await queryRunner.rollbackTransaction();

      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating treatment plan from budget: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao criar plano de tratamento a partir do orçamento',
      );
    } finally {
      // Liberar o queryRunner
      await queryRunner.release();
    }
  }

  async findAll(): Promise<TreatmentPlan[]> {
    return this.treatmentPlanRepository.find({
      relations: [
        'patient',
        'budget',
        'procedures',
        'procedures.procedure',
        'procedures.professional',
        'procedures.appointment',
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllPaginated(
    paginationDto: TreatmentPlanPaginationDto,
  ): Promise<PaginatedResponse<TreatmentPlan>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        patientId,
        budgetId,
        noPagination,
      } = paginationDto;
      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder = this.treatmentPlanRepository
        .createQueryBuilder('treatmentPlan')
        .leftJoinAndSelect('treatmentPlan.patient', 'patient')
        .leftJoinAndSelect('treatmentPlan.budget', 'budget')
        .leftJoinAndSelect('budget.dentist', 'dentist')
        .leftJoinAndSelect('treatmentPlan.procedures', 'procedures')
        .leftJoinAndSelect('procedures.procedure', 'procedure')
        .leftJoinAndSelect('procedures.professional', 'professional')
        .leftJoinAndSelect('procedures.appointment', 'appointment');

      // Aplicar filtros
      if (patientId) {
        queryBuilder.andWhere('treatmentPlan.patientId = :patientId', {
          patientId,
        });
      }

      if (budgetId) {
        queryBuilder.andWhere('treatmentPlan.budgetId = :budgetId', {
          budgetId,
        });
      }

      if (status) {
        queryBuilder.andWhere('treatmentPlan.status = :status', { status });
      }

      if (search) {
        queryBuilder.andWhere(
          '(patient.name LIKE :search OR procedure.name LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Aplicar paginação apenas se noPagination não for 'true'
      if (noPagination !== 'true') {
        queryBuilder.skip(skip).take(limit);
      }

      // Ordenação
      queryBuilder.orderBy('treatmentPlan.createdAt', 'DESC');

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding treatment plans: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar planos de tratamento',
      );
    }
  }

  async findByPatient(patientId: number): Promise<TreatmentPlan[]> {
    try {
      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      return this.treatmentPlanRepository.find({
        where: { patient: { id: patientId } },
        relations: [
          'patient',
          'budget',
          'budget.dentist',
          'dentist',
          'procedures',
          'procedures.procedure',
          'procedures.professional',
          'procedures.appointment',
        ],
        order: { createdAt: 'DESC' },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding treatment plans by patient: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar planos de tratamento do paciente',
      );
    }
  }

  async findOne(id: number): Promise<TreatmentPlan> {
    const treatmentPlan = await this.treatmentPlanRepository.findOne({
      where: { id },
      relations: [
        'patient',
        'budget',
        'budget.dentist',
        'dentist',
        'procedures',
        'procedures.procedure',
        'procedures.professional',
        'procedures.appointment',
      ],
    });

    if (!treatmentPlan) {
      throw new NotFoundException(
        `Plano de tratamento com ID ${id} não encontrado`,
      );
    }

    return treatmentPlan;
  }

  async update(
    id: number,
    updateTreatmentPlanDto: UpdateTreatmentPlanDto,
  ): Promise<TreatmentPlan> {
    try {
      // Verificar se o plano de tratamento existe
      const treatmentPlan = await this.findOne(id);

      // Verificar se o plano de tratamento está cancelado
      if (treatmentPlan.status === TreatmentPlanStatus.CANCELLED) {
        throw new BadRequestException(
          'Não é possível editar um plano de tratamento cancelado.',
        );
      }

      // Iniciar transação
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Atualizar os campos básicos do plano de tratamento
        if (updateTreatmentPlanDto.patientId) {
          const patient = await this.patientsService.findOne(
            updateTreatmentPlanDto.patientId,
          );
          treatmentPlan.patient = patient;
        }

        if (updateTreatmentPlanDto.budgetId !== undefined) {
          if (updateTreatmentPlanDto.budgetId) {
            const budget = await this.dataSource
              .getRepository(Budget)
              .findOne({ where: { id: updateTreatmentPlanDto.budgetId } });
            treatmentPlan.budget = budget;
          } else {
            treatmentPlan.budget = null;
          }
        }

        if (updateTreatmentPlanDto.totalValue !== undefined) {
          treatmentPlan.totalValue = updateTreatmentPlanDto.totalValue;
        }

        if (updateTreatmentPlanDto.status) {
          treatmentPlan.status = updateTreatmentPlanDto.status;
        }

        // Salvar as alterações no plano de tratamento
        await queryRunner.manager.save(treatmentPlan);

        // Se houver procedimentos para atualizar
        if (updateTreatmentPlanDto.procedures) {
          // Remover os procedimentos existentes
          this.logger.log(`Removendo procedimentos existentes do plano ${id}`);

          // Usar a relação correta para remover os procedimentos
          const proceduresToDelete = await queryRunner.manager.find(
            TreatmentProcedure,
            {
              where: { treatmentPlan: { id } },
              relations: ['treatmentPlan'],
            },
          );

          if (proceduresToDelete.length > 0) {
            this.logger.log(
              `Encontrados ${proceduresToDelete.length} procedimentos para remover`,
            );
            await queryRunner.manager.remove(proceduresToDelete);
          } else {
            this.logger.log(`Nenhum procedimento encontrado para remover`);
          }

          // Verificar se os procedimentos e profissionais existem
          for (const procedure of updateTreatmentPlanDto.procedures) {
            await this.proceduresService.findOne(procedure.procedureId);
            await this.dentistsService.findOne(procedure.professionalId);

            // Verificar se o agendamento existe (se fornecido)
            if (procedure.appointmentId) {
              await this.schedulingsService.findOne(procedure.appointmentId);
            }
          }

          // Criar os novos procedimentos
          const treatmentProcedures = updateTreatmentPlanDto.procedures.map(
            (procedureDto) =>
              this.treatmentProcedureRepository.create({
                treatmentPlan: { id },
                procedure: { id: procedureDto.procedureId },
                name: procedureDto.name,
                value: procedureDto.value,
                tooth: procedureDto.tooth,
                executionDate: procedureDto.executionDate,
                professional: { id: procedureDto.professionalId },
                appointment: procedureDto.appointmentId
                  ? { id: procedureDto.appointmentId }
                  : null,
                status: procedureDto.status || TreatmentProcedureStatus.PENDING,
              }),
          );

          // Salvar os novos procedimentos
          await queryRunner.manager.save(treatmentProcedures);
        }

        // Calcular a porcentagem de conclusão
        await this.updateCompletionPercentage(id, queryRunner);

        // Verificar se todos os procedimentos estão concluídos
        await this.checkIfCompleted(id, queryRunner);

        // Commit da transação
        await queryRunner.commitTransaction();

        // Retornar o plano de tratamento atualizado
        return this.findOne(id);
      } catch (error) {
        // Rollback em caso de erro
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Liberar o queryRunner
        await queryRunner.release();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating treatment plan: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao atualizar plano de tratamento',
      );
    }
  }

  async updateProcedure(
    id: number,
    updateDto: UpdateTreatmentProcedureDto,
  ): Promise<TreatmentProcedure> {
    try {
      this.logger.log(`Atualizando procedimento ${id} com dados:`, {
        ...updateDto,
        notes:
          updateDto.notes !== undefined ? updateDto.notes : 'não fornecido',
        nextVisitDetails:
          updateDto.nextVisitDetails !== undefined
            ? updateDto.nextVisitDetails
            : 'não fornecido',
      });

      // Verificar se o procedimento existe
      const procedure = await this.treatmentProcedureRepository.findOne({
        where: { id },
        relations: ['treatmentPlan', 'professional'],
      });

      if (!procedure) {
        throw new NotFoundException(`Procedimento com ID ${id} não encontrado`);
      }

      this.logger.log(`Procedimento encontrado:`, {
        id: procedure.id,
        name: procedure.name,
        status: procedure.status,
        professionalId: procedure.professional?.id,
      });

      // Verificar se o plano de tratamento está cancelado
      if (procedure.treatmentPlan.status === TreatmentPlanStatus.CANCELLED) {
        throw new BadRequestException(
          'Não é possível editar procedimentos de um plano de tratamento cancelado.',
        );
      }

      // Iniciar transação
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Atualizar os campos do procedimento
        if (updateDto.procedureId) {
          const procedureEntity = await this.proceduresService.findOne(
            updateDto.procedureId,
          );
          procedure.procedure = procedureEntity;
        }

        if (updateDto.name) {
          procedure.name = updateDto.name;
        }

        if (updateDto.value !== undefined) {
          procedure.value = updateDto.value;
        }

        if (updateDto.tooth !== undefined) {
          procedure.tooth = updateDto.tooth;
        }

        if (updateDto.executionDate !== undefined) {
          procedure.executionDate = updateDto.executionDate;
        }

        if (updateDto.professionalId !== undefined) {
          // Verificar se o status do procedimento permite a alteração do dentista
          if (procedure.status !== TreatmentProcedureStatus.PENDING) {
            this.logger.warn(
              `Tentativa de alterar o dentista do procedimento ${procedure.id} com status ${procedure.status}. ` +
                `Dentista atual: ${procedure.professional?.id} (${procedure.professional?.name}), ` +
                `Novo dentista solicitado: ${updateDto.professionalId}`,
            );
            throw new BadRequestException(
              'Não é possível alterar o dentista de um procedimento que já foi executado ou agendado. Apenas procedimentos com status "Pendente" podem ter o dentista alterado.',
            );
          }

          this.logger.log(
            `Atualizando dentista do procedimento para ID: ${updateDto.professionalId}`,
          );
          const professional = await this.dentistsService.findOne(
            updateDto.professionalId,
          );
          procedure.professional = professional;
        }

        if (updateDto.appointmentId !== undefined) {
          if (updateDto.appointmentId) {
            const appointment = await this.schedulingsService.findOne(
              updateDto.appointmentId,
            );
            procedure.appointment = appointment;
          } else {
            procedure.appointment = null;
          }
        }

        if (updateDto.status) {
          procedure.status = updateDto.status;
        }

        // Atualizar observações se fornecidas
        if (updateDto.notes !== undefined) {
          this.logger.log(
            `Atualizando observações do procedimento: ${updateDto.notes}`,
          );
          procedure.notes = updateDto.notes;
        }

        // Atualizar detalhes da próxima consulta se fornecidos
        if (updateDto.nextVisitDetails !== undefined) {
          this.logger.log(
            `Atualizando detalhes da próxima consulta: ${updateDto.nextVisitDetails}`,
          );
          procedure.nextVisitDetails = updateDto.nextVisitDetails;
        }

        // Salvar as alterações no procedimento
        await queryRunner.manager.save(procedure);

        // Calcular a porcentagem de conclusão do plano de tratamento
        await this.updateCompletionPercentage(
          procedure.treatmentPlan.id,
          queryRunner,
        );

        // Verificar se todos os procedimentos estão concluídos
        await this.checkIfCompleted(procedure.treatmentPlan.id, queryRunner);

        // Commit da transação
        await queryRunner.commitTransaction();

        // Retornar o procedimento atualizado
        const updatedProcedure =
          await this.treatmentProcedureRepository.findOne({
            where: { id },
            relations: [
              'treatmentPlan',
              'procedure',
              'professional',
              'appointment',
            ],
          });

        if (!updatedProcedure) {
          throw new NotFoundException(
            `Procedimento com ID ${id} não encontrado após atualização`,
          );
        }

        this.logger.log(`Procedimento atualizado com sucesso:`, {
          id: updatedProcedure.id,
          name: updatedProcedure.name,
          status: updatedProcedure.status,
          professionalId: updatedProcedure.professional?.id,
          notes: updatedProcedure.notes,
          nextVisitDetails: updatedProcedure.nextVisitDetails,
        });

        return updatedProcedure;
      } catch (error) {
        // Rollback em caso de erro
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Liberar o queryRunner
        await queryRunner.release();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating treatment procedure: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao atualizar procedimento do plano de tratamento',
      );
    }
  }

  async remove(id: number): Promise<void> {
    const treatmentPlan = await this.findOne(id);

    // Verificar se o plano de tratamento está concluído
    if (treatmentPlan.status === TreatmentPlanStatus.COMPLETED) {
      throw new BadRequestException(
        'Não é possível excluir um plano de tratamento concluído. Considere cancelá-lo.',
      );
    }

    await this.treatmentPlanRepository.remove(treatmentPlan);
  }

  async removeProcedure(id: number): Promise<void> {
    try {
      // Verificar se o procedimento existe
      const procedure = await this.treatmentProcedureRepository.findOne({
        where: { id },
        relations: ['treatmentPlan'],
      });

      if (!procedure) {
        throw new NotFoundException(`Procedimento com ID ${id} não encontrado`);
      }

      // Verificar se o plano de tratamento está cancelado
      if (procedure.treatmentPlan.status === TreatmentPlanStatus.CANCELLED) {
        throw new BadRequestException(
          'Não é possível remover procedimentos de um plano de tratamento cancelado.',
        );
      }

      // Iniciar transação
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Remover o procedimento
        await queryRunner.manager.remove(procedure);

        // Calcular a porcentagem de conclusão do plano de tratamento
        await this.updateCompletionPercentage(
          procedure.treatmentPlan.id,
          queryRunner,
        );

        // Verificar se todos os procedimentos estão concluídos
        await this.checkIfCompleted(procedure.treatmentPlan.id, queryRunner);

        // Commit da transação
        await queryRunner.commitTransaction();
      } catch (error) {
        // Rollback em caso de erro
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Liberar o queryRunner
        await queryRunner.release();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error removing treatment procedure: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao remover procedimento do plano de tratamento',
      );
    }
  }

  private async updateCompletionPercentage(
    treatmentPlanId: number,
    queryRunner: QueryRunner,
  ): Promise<void> {
    try {
      this.logger.log(
        `Atualizando porcentagem de conclusão para o plano ${treatmentPlanId}`,
      );

      // Buscar todos os procedimentos do plano de tratamento usando a relação correta
      const procedures = await queryRunner.manager.find(TreatmentProcedure, {
        where: {
          treatmentPlan: { id: treatmentPlanId },
        },
        relations: ['treatmentPlan'],
      });

      this.logger.log(
        `Encontrados ${procedures.length} procedimentos para o plano ${treatmentPlanId}`,
      );

      if (procedures.length === 0) {
        return;
      }

      // Contar procedimentos concluídos
      const completedProcedures = procedures.filter(
        (procedure: TreatmentProcedure) =>
          procedure.status === TreatmentProcedureStatus.COMPLETED,
      ).length;

      this.logger.log(
        `${completedProcedures} procedimentos concluídos de ${procedures.length} total`,
      );

      // Calcular a porcentagem de conclusão
      const completionPercentage =
        (completedProcedures / procedures.length) * 100;
      this.logger.log(
        `Porcentagem de conclusão calculada: ${completionPercentage}%`,
      );

      // Atualizar a porcentagem de conclusão no plano de tratamento
      await queryRunner.manager.update(
        TreatmentPlan,
        { id: treatmentPlanId },
        { completionPercentage },
      );

      this.logger.log(
        `Porcentagem de conclusão atualizada para o plano ${treatmentPlanId}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Erro ao atualizar porcentagem de conclusão: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  async createProcedureWithPlan(
    createDto: CreateTreatmentProcedureWithPlanDto,
  ): Promise<TreatmentProcedure> {
    try {
      this.logger.log(
        `Creating procedure with auto plan: ${JSON.stringify(createDto)}`,
      );

      // Verificar se o paciente existe
      await this.patientsService.findOne(createDto.patientId);

      // Verificar se o procedimento existe
      await this.proceduresService.findOne(createDto.procedureId);

      // Verificar se o profissional existe
      await this.dentistsService.findOne(createDto.professionalId);

      // Verificar se o agendamento existe (se fornecido)
      if (createDto.appointmentId) {
        await this.schedulingsService.findOne(createDto.appointmentId);
      }

      // Iniciar transação
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Criar o plano de tratamento
        const treatmentPlan = new TreatmentPlan();

        // Buscar o paciente
        const patient = await this.patientsService.findOne(createDto.patientId);
        treatmentPlan.patient = patient;

        // Buscar o dentista (usando o dentistId se fornecido, ou o professionalId caso contrário)
        const dentistId = createDto.dentistId || createDto.professionalId;
        const dentist = await this.dentistsService.findOne(dentistId);
        treatmentPlan.dentist = dentist;

        // Não associar a nenhum orçamento
        treatmentPlan.budget = null;

        // Definir valores
        treatmentPlan.totalValue = createDto.value;
        treatmentPlan.completionPercentage = 0;
        treatmentPlan.status = TreatmentPlanStatus.OPEN;

        // Salvar o plano de tratamento
        const savedTreatmentPlan =
          await queryRunner.manager.save(treatmentPlan);

        this.logger.log(
          `Plano de tratamento criado com ID ${savedTreatmentPlan.id}`,
        );

        // Criar o procedimento
        const procedureEntity = await this.proceduresService.findOne(
          createDto.procedureId,
        );
        const professional = await this.dentistsService.findOne(
          createDto.professionalId,
        );

        const treatmentProcedure = new TreatmentProcedure();
        treatmentProcedure.treatmentPlan = savedTreatmentPlan;
        treatmentProcedure.procedure = procedureEntity;
        treatmentProcedure.name = createDto.name;
        treatmentProcedure.value = createDto.value;
        treatmentProcedure.tooth = createDto.tooth || null;
        treatmentProcedure.executionDate = createDto.executionDate || null;
        treatmentProcedure.professional = professional;
        treatmentProcedure.status =
          createDto.status || TreatmentProcedureStatus.PENDING;
        treatmentProcedure.notes = createDto.notes || null;
        treatmentProcedure.nextVisitDetails =
          createDto.nextVisitDetails || null;

        // Se houver agendamento, associar
        if (createDto.appointmentId) {
          const appointment = await this.schedulingsService.findOne(
            createDto.appointmentId,
          );
          treatmentProcedure.appointment = appointment;
        }

        // Salvar o procedimento
        const savedProcedure =
          await queryRunner.manager.save(treatmentProcedure);

        this.logger.log(
          `Procedimento ${savedProcedure.name} criado com ID ${savedProcedure.id}`,
        );

        // Commit da transação
        await queryRunner.commitTransaction();

        // Retornar o procedimento criado
        const result = await this.treatmentProcedureRepository.findOne({
          where: { id: savedProcedure.id },
          relations: [
            'treatmentPlan',
            'procedure',
            'professional',
            'appointment',
          ],
        });

        if (!result) {
          throw new NotFoundException(
            `Procedimento com ID ${savedProcedure.id} não encontrado após criação`,
          );
        }

        return result;
      } catch (error) {
        // Rollback em caso de erro
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Liberar o queryRunner
        await queryRunner.release();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating procedure with auto plan: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao criar procedimento com plano automático',
      );
    }
  }

  private async checkIfCompleted(
    treatmentPlanId: number,
    queryRunner: QueryRunner,
  ): Promise<void> {
    try {
      this.logger.log(
        `Verificando se o plano ${treatmentPlanId} está concluído`,
      );

      // Buscar o plano de tratamento atual para verificar seu status
      let treatmentPlan: TreatmentPlan | null = null;
      try {
        // Primeiro tentar buscar usando o método findOne do serviço para garantir todas as relações
        treatmentPlan = await this.findOne(treatmentPlanId);
      } catch (error: unknown) {
        // Se falhar, tentar buscar diretamente pelo queryRunner
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        this.logger.warn(
          `Erro ao buscar plano ${treatmentPlanId} usando findOne: ${errorMessage}. Tentando com queryRunner...`,
        );
        treatmentPlan = await queryRunner.manager.findOne(TreatmentPlan, {
          where: { id: treatmentPlanId },
        });
      }

      if (!treatmentPlan) {
        this.logger.warn(
          `Plano de tratamento ${treatmentPlanId} não encontrado`,
        );
        return;
      }

      // Buscar todos os procedimentos do plano de tratamento usando a relação correta
      const procedures = await queryRunner.manager.find(TreatmentProcedure, {
        where: {
          treatmentPlan: { id: treatmentPlanId },
        },
        relations: ['treatmentPlan'],
      });

      this.logger.log(
        `Encontrados ${procedures.length} procedimentos para o plano ${treatmentPlanId}`,
      );

      if (procedures.length === 0) {
        return;
      }

      // Verificar se todos os procedimentos estão concluídos
      const allCompleted = procedures.every(
        (procedure: TreatmentProcedure) =>
          procedure.status === TreatmentProcedureStatus.COMPLETED,
      );

      this.logger.log(
        `Todos os procedimentos estão concluídos? ${allCompleted}`,
      );

      // Verificar se há pelo menos um procedimento não cancelado e não concluído
      const hasActiveIncomplete = procedures.some(
        (procedure: TreatmentProcedure) =>
          procedure.status !== TreatmentProcedureStatus.COMPLETED &&
          procedure.status !== TreatmentProcedureStatus.CANCELLED,
      );

      this.logger.log(
        `Há procedimentos ativos não concluídos? ${hasActiveIncomplete}`,
      );

      // Se todos estão concluídos, marcar o plano como concluído
      if (allCompleted) {
        // Atualizar o status do plano de tratamento para concluído
        await queryRunner.manager.update(
          TreatmentPlan,
          { id: treatmentPlanId },
          { status: TreatmentPlanStatus.COMPLETED },
        );

        this.logger.log(`Plano ${treatmentPlanId} marcado como concluído`);
      }
      // Se o plano está concluído mas há procedimentos ativos não concluídos, reabrir o plano
      else if (
        treatmentPlan.status === TreatmentPlanStatus.COMPLETED &&
        hasActiveIncomplete
      ) {
        // Atualizar o status do plano de tratamento para aberto
        await queryRunner.manager.update(
          TreatmentPlan,
          { id: treatmentPlanId },
          { status: TreatmentPlanStatus.OPEN },
        );

        this.logger.log(
          `Plano ${treatmentPlanId} reaberto porque há procedimentos não concluídos`,
        );
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Erro ao verificar conclusão do plano: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }
}
