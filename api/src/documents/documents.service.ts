import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Inject,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PatientDocument } from './entities/patient-document.entity';
import { Patient } from '../patients/entities/patient.entity';
import { IStorageProvider } from '../common/interfaces/storage.interface';
import { FileValidator } from '../common/validators/file.validator';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class DocumentsService {
  private readonly logger = new Logger(DocumentsService.name);

  constructor(
    @InjectRepository(PatientDocument)
    private patientDocumentRepository: Repository<PatientDocument>,
    @InjectRepository(Patient)
    private patientRepository: Repository<Patient>,
    @Inject('StorageProvider')
    private storageProvider: IStorageProvider,
    private fileValidator: FileValidator,
  ) {}

  async uploadDocument(
    file: Express.Multer.File,
    patientId: number,
    name: string,
  ): Promise<PatientDocument> {
    try {
      this.logger.log(`Uploading document for patient ${patientId}`);

      // Validar o arquivo (PDF, JPG, JPEG, PNG e tamanho máximo)
      const validationError = this.fileValidator.validateDocumentFile(file);
      if (validationError) {
        throw new BadRequestException(validationError);
      }

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Fazer upload do arquivo para o MinIO
      const filePath = await this.storageProvider.uploadFile(
        file,
        `patient-documents/${patientId}`,
      );

      // Decodificar o nome do arquivo para garantir que caracteres especiais sejam exibidos corretamente
      const decodedFileName = Buffer.from(file.originalname, 'latin1').toString(
        'utf8',
      );

      // Criar a entrada no banco de dados
      const document = this.patientDocumentRepository.create({
        name,
        fileUrl: filePath,
        fileName: decodedFileName,
        patientId,
      });

      await this.patientDocumentRepository.save(document);

      // Gerar URL temporária para o arquivo
      document.fileUrl = await this.storageProvider.getFileUrl(
        document.fileUrl,
      );

      return document;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error uploading document for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao fazer upload do documento',
      );
    }
  }

  async findAllByPatient(patientId: number): Promise<PatientDocument[]> {
    try {
      this.logger.log(`Finding all documents for patient ${patientId}`);

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar todos os documentos do paciente
      const documents = await this.patientDocumentRepository.find({
        where: { patientId },
        order: { uploadedAt: 'DESC' },
      });

      // Gerar URLs temporárias para os documentos
      for (const document of documents) {
        document.fileUrl = await this.storageProvider.getFileUrl(
          document.fileUrl,
        );
      }

      return documents;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding documents for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao buscar os documentos do paciente',
      );
    }
  }

  async findOne(id: string): Promise<PatientDocument> {
    try {
      this.logger.log(`Finding document with ID ${id}`);

      const document = await this.patientDocumentRepository.findOne({
        where: { id },
      });

      if (!document) {
        throw new NotFoundException(`Documento com ID ${id} não encontrado`);
      }

      // Gerar URL temporária para o documento
      document.fileUrl = await this.storageProvider.getFileUrl(
        document.fileUrl,
      );

      return document;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding document with ID ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao buscar o documento',
      );
    }
  }

  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Removing document with ID ${id}`);

      const document = await this.patientDocumentRepository.findOne({
        where: { id },
      });

      if (!document) {
        throw new NotFoundException(`Documento com ID ${id} não encontrado`);
      }

      // Remover o arquivo do MinIO
      await this.storageProvider.deleteFile(document.fileUrl);

      // Remover a entrada do banco de dados
      await this.patientDocumentRepository.remove(document);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing document with ID ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Ocorreu um erro ao remover o documento',
      );
    }
  }
}
