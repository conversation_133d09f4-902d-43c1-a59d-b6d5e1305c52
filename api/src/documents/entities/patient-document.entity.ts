import {
  <PERSON><PERSON><PERSON>,
  <PERSON>um<PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';

@Entity('patient_documents')
export class PatientDocument {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único do documento' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Nome do documento' })
  name: string;

  @Column()
  @ApiProperty({ description: 'URL do arquivo no MinIO' })
  fileUrl: string;

  @Column()
  @ApiProperty({ description: 'Nome original do arquivo' })
  fileName: string;

  @CreateDateColumn({ name: 'uploaded_at' })
  @ApiProperty({ description: 'Data de upload do documento' })
  uploadedAt: Date;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado ao documento' })
  patient: Patient;

  @Column()
  patientId: number;
}
