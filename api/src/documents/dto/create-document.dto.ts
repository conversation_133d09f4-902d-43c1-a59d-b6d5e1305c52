import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateDocumentDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  patientId: number;

  @ApiProperty({
    description: 'Nome do documento',
    example: 'Carteira de Identidade',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}
