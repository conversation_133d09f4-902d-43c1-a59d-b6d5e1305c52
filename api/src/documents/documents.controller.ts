import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  ParseUUIDPipe,
  ParseIntPipe,
  BadRequestException,
  Body,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { DocumentsService } from './documents.service';
import { PatientDocument } from './entities/patient-document.entity';

@ApiTags('documents')
@Controller('documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Fazer upload de um documento' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Arquivo do documento (PDF, JPG, JPEG, PNG)',
        },
        patientId: { type: 'number', example: 1 },
        name: { type: 'string', example: 'Carteira de Identidade' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Documento enviado com sucesso',
    type: PatientDocument,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { patientId: unknown; name: unknown },
  ): Promise<PatientDocument> {
    if (!file) {
      throw new BadRequestException('Arquivo não fornecido');
    }

    // Converter patientId para número
    const patientId = parseInt(String(body.patientId), 10);
    if (isNaN(patientId)) {
      throw new BadRequestException('O ID do paciente deve ser um número');
    }

    return this.documentsService.uploadDocument(
      file,
      patientId,
      String(body.name),
    );
  }

  @Get('patients/:id/documents')
  @ApiOperation({ summary: 'Listar todos os documentos de um paciente' })
  @ApiParam({ name: 'id', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de documentos do paciente',
    type: [PatientDocument],
  })
  async findAllByPatient(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PatientDocument[]> {
    return this.documentsService.findAllByPatient(id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter detalhes de um documento' })
  @ApiParam({ name: 'id', description: 'ID do documento' })
  @ApiResponse({
    status: 200,
    description: 'Detalhes do documento',
    type: PatientDocument,
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PatientDocument> {
    return this.documentsService.findOne(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover um documento' })
  @ApiParam({ name: 'id', description: 'ID do documento' })
  @ApiResponse({
    status: 200,
    description: 'Documento removido com sucesso',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.documentsService.remove(id);
  }
}
