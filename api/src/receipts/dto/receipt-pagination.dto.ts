import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from '../../common/dto/pagination.dto';

export class ReceiptPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Filtrar por ID do paciente',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  patientId?: number;
}
