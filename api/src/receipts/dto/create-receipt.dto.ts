import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsN<PERSON>ber,
  IsOptional,
  IsNotEmpty,
  IsDateString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateReceiptDto {
  @ApiProperty({ description: 'ID do paciente' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  patientId: number;

  @ApiProperty({ description: 'Data do recibo (YYYY-MM-DD)' })
  @IsNotEmpty()
  @IsDateString()
  date: string;

  @ApiProperty({ description: 'Número do recibo' })
  @IsNotEmpty()
  @IsString()
  number: string;

  @ApiProperty({ description: 'Número editado do recibo', required: false })
  @IsOptional()
  @IsString()
  editedNumber?: string;

  @ApiProperty({ description: 'Nome do beneficiário' })
  @IsNotEmpty()
  @IsString()
  beneficiary: string;

  @ApiProperty({ description: 'CPF do beneficiário' })
  @IsNotEmpty()
  @IsString()
  beneficiaryCpf: string;

  @ApiProperty({ description: 'Nome do responsável' })
  @IsNotEmpty()
  @IsString()
  responsible: string;

  @ApiProperty({ description: 'CPF do responsável' })
  @IsNotEmpty()
  @IsString()
  responsibleCpf: string;

  @ApiProperty({ description: 'Observações sobre o recibo', required: false })
  @IsOptional()
  @IsString()
  observations?: string;

  @ApiProperty({ description: 'Nome do profissional' })
  @IsNotEmpty()
  @IsString()
  professional: string;

  @ApiProperty({ description: 'CPF do profissional' })
  @IsNotEmpty()
  @IsString()
  professionalCpf: string;

  @ApiProperty({ description: 'Descrição do serviço' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ description: 'Valor do recibo' })
  @IsNumber()
  @Min(0, { message: 'O valor não pode ser negativo' })
  @Type(() => Number)
  value: number;
}
