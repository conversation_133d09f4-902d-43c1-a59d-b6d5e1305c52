import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';

@Entity('receipts')
export class Receipt {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do recibo' })
  id: number;

  @Column({ type: 'date' })
  @ApiProperty({ description: 'Data do recibo' })
  date: Date;

  @Column()
  @ApiProperty({ description: 'Número do recibo' })
  number: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Número editado do recibo', required: false })
  editedNumber: string;

  @Column()
  @ApiProperty({ description: 'Nome do beneficiário' })
  beneficiary: string;

  @Column()
  @ApiProperty({ description: 'CPF do beneficiário' })
  beneficiaryCpf: string;

  @Column()
  @ApiProperty({ description: 'Nome do responsável' })
  responsible: string;

  @Column()
  @ApiProperty({ description: 'CPF do responsável' })
  responsibleCpf: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'Observações sobre o recibo', required: false })
  observations: string;

  @Column()
  @ApiProperty({ description: 'Nome do profissional' })
  professional: string;

  @Column()
  @ApiProperty({ description: 'CPF do profissional' })
  professionalCpf: string;

  @Column()
  @ApiProperty({ description: 'Descrição do serviço' })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Valor do recibo' })
  value: number;

  @ManyToOne(() => Patient, (patient) => patient.receipts, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  patient: Patient;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do recibo' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do recibo' })
  updatedAt: Date;
}
