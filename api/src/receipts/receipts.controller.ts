import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ReceiptsService } from './receipts.service';
import { CreateReceiptDto } from './dto/create-receipt.dto';
import { UpdateReceiptDto } from './dto/update-receipt.dto';
import { Receipt } from './entities/receipt.entity';
import { ReceiptPaginationDto } from './dto/receipt-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('receipts')
@Controller('receipts')
export class ReceiptsController {
  constructor(private readonly receiptsService: ReceiptsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo recibo' })
  @ApiResponse({
    status: 201,
    description: 'Recibo criado com sucesso',
    type: Receipt,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createReceiptDto: CreateReceiptDto): Promise<Receipt> {
    return this.receiptsService.create(createReceiptDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os recibos' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar recibos',
    type: String,
  })
  @ApiQuery({
    name: 'patientId',
    required: false,
    description: 'Filtrar por ID do paciente',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de recibos retornada com sucesso',
  })
  findAll(
    @Query() paginationDto: ReceiptPaginationDto,
  ): Promise<PaginatedResponse<Receipt>> {
    return this.receiptsService.findAllPaginated(paginationDto);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Listar recibos de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de recibos do paciente retornada com sucesso',
    type: [Receipt],
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  findByPatient(@Param('patientId') patientId: string): Promise<Receipt[]> {
    return this.receiptsService.findByPatient(+patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um recibo pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do recibo' })
  @ApiResponse({
    status: 200,
    description: 'Recibo encontrado',
    type: Receipt,
  })
  @ApiResponse({ status: 404, description: 'Recibo não encontrado' })
  findOne(@Param('id') id: string): Promise<Receipt> {
    return this.receiptsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um recibo' })
  @ApiParam({ name: 'id', description: 'ID do recibo' })
  @ApiResponse({
    status: 200,
    description: 'Recibo atualizado com sucesso',
    type: Receipt,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Recibo não encontrado' })
  update(
    @Param('id') id: string,
    @Body() updateReceiptDto: UpdateReceiptDto,
  ): Promise<Receipt> {
    return this.receiptsService.update(+id, updateReceiptDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um recibo' })
  @ApiParam({ name: 'id', description: 'ID do recibo' })
  @ApiResponse({ status: 204, description: 'Recibo removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Recibo não encontrado' })
  remove(@Param('id') id: string): Promise<void> {
    return this.receiptsService.remove(+id);
  }
}
