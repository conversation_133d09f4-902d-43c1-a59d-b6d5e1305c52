import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Receipt } from './entities/receipt.entity';
import { CreateReceiptDto } from './dto/create-receipt.dto';
import { UpdateReceiptDto } from './dto/update-receipt.dto';
import { ReceiptPaginationDto } from './dto/receipt-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { PatientsService } from '../patients/patients.service';

@Injectable()
export class ReceiptsService {
  private readonly logger = new Logger(ReceiptsService.name);

  constructor(
    @InjectRepository(Receipt)
    private readonly receiptsRepository: Repository<Receipt>,
    private readonly patientsService: PatientsService,
  ) {}

  async create(createReceiptDto: CreateReceiptDto): Promise<Receipt> {
    try {
      this.logger.log(`Creating receipt: ${JSON.stringify(createReceiptDto)}`);

      // Verificar se o paciente existe
      const patient = await this.patientsService.findOne(
        createReceiptDto.patientId,
      );
      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${createReceiptDto.patientId} não encontrado`,
        );
      }

      // Criar a instância do recibo
      const receipt = this.receiptsRepository.create({
        ...createReceiptDto,
        patient,
      });

      // Salvar o recibo
      const savedReceipt = await this.receiptsRepository.save(receipt);

      // Retornar o recibo salvo
      return this.findOne(savedReceipt.id);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error creating receipt: ${errorMessage}`, errorStack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao criar recibo');
    }
  }

  async findAllPaginated(
    paginationDto: ReceiptPaginationDto,
  ): Promise<PaginatedResponse<Receipt>> {
    try {
      const { page = 1, limit = 10, search, patientId } = paginationDto;
      const skip = (page - 1) * limit;

      // Construir a query base
      const queryBuilder = this.receiptsRepository
        .createQueryBuilder('receipt')
        .leftJoinAndSelect('receipt.patient', 'patient');

      // Adicionar filtros
      if (search) {
        queryBuilder.andWhere(
          '(receipt.number LIKE :search OR receipt.beneficiary LIKE :search OR receipt.professional LIKE :search)',
          { search: `%${search}%` },
        );
      }

      if (patientId) {
        queryBuilder.andWhere('receipt.patient.id = :patientId', { patientId });
      }

      // Contar o total de itens
      const total = await queryBuilder.getCount();

      // Obter os itens paginados
      const items = await queryBuilder
        .orderBy('receipt.date', 'DESC')
        .skip(skip)
        .take(limit)
        .getMany();

      return {
        data: items,
        total,
        page,
        limit,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error finding receipts: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException('Erro ao buscar recibos');
    }
  }

  async findByPatient(patientId: number): Promise<Receipt[]> {
    try {
      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      // Buscar os recibos do paciente
      const receipts = await this.receiptsRepository.find({
        where: { patient: { id: patientId } },
        relations: ['patient'],
        order: { date: 'DESC' },
      });

      return receipts;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding receipts by patient: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar recibos do paciente',
      );
    }
  }

  async findOne(id: number): Promise<Receipt> {
    try {
      this.logger.log(`Finding receipt with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de recibo inválido');
      }

      // Buscar o recibo
      const receipt = await this.receiptsRepository.findOne({
        where: { id },
        relations: ['patient'],
      });

      if (!receipt) {
        throw new NotFoundException(`Recibo com ID ${id} não encontrado`);
      }

      return receipt;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error finding receipt: ${errorMessage}`, errorStack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar recibo');
    }
  }

  async update(
    id: number,
    updateReceiptDto: UpdateReceiptDto,
  ): Promise<Receipt> {
    try {
      this.logger.log(`Updating receipt with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de recibo inválido');
      }

      // Verificar se o recibo existe
      await this.findOne(id);

      // Verificar se o paciente existe, se o ID do paciente foi fornecido
      if (updateReceiptDto.patientId) {
        await this.patientsService.findOne(updateReceiptDto.patientId);
      }

      // Atualizar o recibo
      await this.receiptsRepository.update(id, updateReceiptDto);

      // Retornar o recibo atualizado
      return this.findOne(id);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error updating receipt: ${errorMessage}`, errorStack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao atualizar recibo');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      this.logger.log(`Removing receipt with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de recibo inválido');
      }

      // Verificar se o recibo existe
      const receipt = await this.findOne(id);

      // Remover o recibo
      await this.receiptsRepository.remove(receipt);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error removing receipt: ${errorMessage}`, errorStack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao remover recibo');
    }
  }
}
