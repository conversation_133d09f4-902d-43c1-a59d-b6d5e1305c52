import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import {
  Scheduling,
  SchedulingStatus,
} from '../schedulings/entities/scheduling.entity';
import { TreatmentPlan } from '../treatment-plans/entities/treatment-plan.entity';
import {
  TreatmentProcedure,
  TreatmentProcedureStatus,
} from '../treatment-plans/entities/treatment-procedure.entity';

export interface FinancialReport {
  // Procedimentos de Tratamento
  totalProcedures: number;
  completedProcedures: number;
  inProgressProcedures: number;
  pendingProcedures: number;
  cancelledProcedures: number;
  procedureRevenue: number;
  procedurePendingRevenue: number;
  procedureReceivedRevenue: number;

  // Agendamentos
  totalSchedulings: number;
  completedSchedulings: number;
  confirmedSchedulings: number;
  scheduledSchedulings: number;
  cancelledSchedulings: number;
  schedulingRevenue: number;
  schedulingPendingRevenue: number;
  schedulingReceivedRevenue: number;

  // Total
  totalRevenue: number;
  totalPendingRevenue: number;
  totalReceivedRevenue: number;
}

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(
    @InjectRepository(TreatmentPlan)
    private treatmentPlansRepository: Repository<TreatmentPlan>,
    @InjectRepository(TreatmentProcedure)
    private treatmentProceduresRepository: Repository<TreatmentProcedure>,
    @InjectRepository(Scheduling)
    private schedulingsRepository: Repository<Scheduling>,
  ) {}

  async getFinancialReport(
    startDate?: string,
    endDate?: string,
    patientId?: number,
    status?: string,
    paymentStatus?: string,
    type?: string,
  ): Promise<FinancialReport> {
    // Construir condições de filtro
    const procedureWhere: Record<string, unknown> = {};
    const schedulingWhere: Record<string, unknown> = {};

    if (patientId) {
      // Para procedimentos, precisamos filtrar pelo plano de tratamento
      procedureWhere.patientId = patientId;
      schedulingWhere.patientId = patientId;
    }

    if (status) {
      procedureWhere.status = status;

      // Mapear status para agendamentos
      if (status === String(TreatmentProcedureStatus.IN_PROGRESS)) {
        schedulingWhere.status = 'confirmed';
      } else {
        schedulingWhere.status = String(status);
      }
    }

    if (startDate && endDate) {
      procedureWhere.createdAt = Between(
        new Date(startDate),
        new Date(endDate),
      );
      schedulingWhere.date = Between(new Date(startDate), new Date(endDate));
    } else if (startDate) {
      procedureWhere.createdAt = MoreThanOrEqual(new Date(startDate));
      schedulingWhere.date = MoreThanOrEqual(new Date(startDate));
    } else if (endDate) {
      procedureWhere.createdAt = LessThanOrEqual(new Date(endDate));
      schedulingWhere.date = LessThanOrEqual(new Date(endDate));
    }

    // Buscar procedimentos e agendamentos com base no tipo
    let procedures: TreatmentProcedure[] = [];
    let schedulings: Scheduling[] = [];

    if (!type || type === 'procedure') {
      // Usar QueryBuilder para ter mais controle sobre a consulta
      const queryBuilder = this.treatmentProceduresRepository
        .createQueryBuilder('procedure')
        .leftJoinAndSelect('procedure.treatmentPlan', 'treatmentPlan')
        .leftJoinAndSelect('treatmentPlan.patient', 'patient')
        .leftJoinAndSelect('patient.patientType', 'patientType');

      // Adicionar condições de filtro
      if (patientId) {
        queryBuilder.andWhere('patient.id = :patientId', { patientId });
      }

      if (status) {
        queryBuilder.andWhere('procedure.status = :status', { status });
      }

      if (startDate && endDate) {
        queryBuilder.andWhere(
          'procedure.createdAt BETWEEN :startDate AND :endDate',
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          },
        );
      } else if (startDate) {
        queryBuilder.andWhere('procedure.createdAt >= :startDate', {
          startDate: new Date(startDate),
        });
      } else if (endDate) {
        queryBuilder.andWhere('procedure.createdAt <= :endDate', {
          endDate: new Date(endDate),
        });
      }

      procedures = await queryBuilder.getMany();
    }

    if (!type || type === 'scheduling') {
      // Buscar agendamentos sem procedimento associado (avulsos)
      this.logger.log(
        'Buscando agendamentos avulsos (sem procedimento associado)',
      );

      // Construir a consulta base
      let queryBuilder = this.schedulingsRepository
        .createQueryBuilder('scheduling')
        .leftJoinAndSelect('scheduling.patient', 'patient')
        .leftJoinAndSelect('patient.patientType', 'patientType')
        .leftJoin(
          'treatment_procedures',
          'treatmentProcedures',
          'treatmentProcedures.appointmentId = scheduling.id',
        )
        .where('treatmentProcedures.id IS NULL'); // Garantir que não existam procedimentos associados

      // Adicionar condições de filtro
      if (patientId) {
        queryBuilder = queryBuilder.andWhere('patient.id = :patientId', {
          patientId,
        });
      }

      if (status) {
        queryBuilder = queryBuilder.andWhere('scheduling.status = :status', {
          status,
        });
      }

      if (paymentStatus) {
        const isPaid = paymentStatus === 'paid';
        queryBuilder = queryBuilder.andWhere('scheduling.paid = :isPaid', {
          isPaid,
        });
      }

      if (startDate && endDate) {
        queryBuilder = queryBuilder.andWhere(
          'scheduling.date BETWEEN :startDate AND :endDate',
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          },
        );
      } else if (startDate) {
        queryBuilder = queryBuilder.andWhere('scheduling.date >= :startDate', {
          startDate: new Date(startDate),
        });
      } else if (endDate) {
        queryBuilder = queryBuilder.andWhere('scheduling.date <= :endDate', {
          endDate: new Date(endDate),
        });
      }

      // Executar a consulta
      schedulings = await queryBuilder.getMany();
    }

    // Calcular estatísticas de procedimentos
    const procedureRevenue = procedures
      .filter((p) => p.status !== TreatmentProcedureStatus.CANCELLED)
      .reduce((sum, p) => sum + (p.value || 0), 0);

    const procedurePendingRevenue = procedures
      .filter(
        (p) =>
          p.status !== TreatmentProcedureStatus.CANCELLED &&
          p.status !== TreatmentProcedureStatus.COMPLETED,
      )
      .reduce((sum, p) => sum + (p.value || 0), 0);

    const procedureReceivedRevenue = procedures
      .filter((p) => p.status === TreatmentProcedureStatus.COMPLETED)
      .reduce((sum, p) => sum + (p.value || 0), 0);

    // Calcular estatísticas de agendamentos
    // Nota: Os campos cost e paid foram removidos da entidade Scheduling
    // Por enquanto, definindo valores zerados até que seja implementada nova lógica
    const schedulingRevenue = 0;
    const schedulingPendingRevenue = 0;
    const schedulingReceivedRevenue = 0;

    return {
      // Procedimentos
      totalProcedures: procedures.length,
      completedProcedures: procedures.filter(
        (p) => p.status === TreatmentProcedureStatus.COMPLETED,
      ).length,
      inProgressProcedures: procedures.filter(
        (p) => p.status === TreatmentProcedureStatus.IN_PROGRESS,
      ).length,
      pendingProcedures: procedures.filter(
        (p) => p.status === TreatmentProcedureStatus.PENDING,
      ).length,
      cancelledProcedures: procedures.filter(
        (p) => p.status === TreatmentProcedureStatus.CANCELLED,
      ).length,
      procedureRevenue,
      procedurePendingRevenue,
      procedureReceivedRevenue,

      // Agendamentos
      totalSchedulings: schedulings.length,
      completedSchedulings: schedulings.filter(
        (a) => a.status === SchedulingStatus.COMPLETED,
      ).length,
      confirmedSchedulings: schedulings.filter(
        (a) => a.status === SchedulingStatus.CONFIRMED,
      ).length,
      scheduledSchedulings: schedulings.filter(
        (a) => a.status === SchedulingStatus.UNCONFIRMED,
      ).length,
      cancelledSchedulings: schedulings.filter(
        (a) => a.status === SchedulingStatus.CANCELLED,
      ).length,
      schedulingRevenue,
      schedulingPendingRevenue,
      schedulingReceivedRevenue,

      // Total
      totalRevenue: procedureRevenue + schedulingRevenue,
      totalPendingRevenue: procedurePendingRevenue + schedulingPendingRevenue,
      totalReceivedRevenue:
        procedureReceivedRevenue + schedulingReceivedRevenue,
    };
  }
}
