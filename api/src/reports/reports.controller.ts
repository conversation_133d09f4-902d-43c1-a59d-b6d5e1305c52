import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ReportsService } from './reports.service';

@ApiTags('reports')
@Controller('reports')
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @ApiOperation({ summary: 'Obter relatório financeiro' })
  @ApiResponse({
    status: 200,
    description: 'Relatório financeiro obtido com sucesso',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Data inicial (YYYY-MM-DD)',
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'Data final (YYYY-MM-DD)',
    type: String,
  })
  @ApiQuery({
    name: 'patientId',
    required: false,
    description: 'ID do paciente',
    type: Number,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Status (scheduled, in-progress, completed, cancelled)',
    type: String,
  })
  @ApiQuery({
    name: 'paymentStatus',
    required: false,
    description: 'Status de pagamento (paid, pending)',
    type: String,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Tipo (treatment, attendance)',
    type: String,
  })
  @Get('financial')
  async getFinancialReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('patientId') patientId?: string,
    @Query('status') status?: string,
    @Query('paymentStatus') paymentStatus?: string,
    @Query('type') type?: string,
  ): Promise<unknown> {
    return this.reportsService.getFinancialReport(
      startDate,
      endDate,
      patientId ? +patientId : undefined,
      status,
      paymentStatus,
      type,
    );
  }
}
