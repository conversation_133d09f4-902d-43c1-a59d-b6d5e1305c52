import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';
import { SchedulingsModule } from '../schedulings/schedulings.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Scheduling } from '../schedulings/entities/scheduling.entity';
import { TreatmentPlan } from '../treatment-plans/entities/treatment-plan.entity';
import { TreatmentProcedure } from '../treatment-plans/entities/treatment-procedure.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TreatmentPlan, TreatmentProcedure, Scheduling]),
    SchedulingsModule,
  ],
  controllers: [ReportsController],
  providers: [ReportsService],
  exports: [ReportsService],
})
export class ReportsModule {}
