export interface PatientSuggestion {
  id: string;
  status: string;
  iaReasoning: string | null;
  humanComment: string | null;
  createdAt: string;
}

export interface PatientWithConcludedTreatment {
  id: number;
  name: string;
  cpf: string;
  medicalRecordUrl: string;
  treatmentConcludedAt: string; // Data de conclusão do tratamento
  suggestions: PatientSuggestion[]; // Sugestões vinculadas ao paciente
}
