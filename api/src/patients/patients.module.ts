import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PatientsService } from './patients.service';
import { PatientsController } from './patients.controller';
import { Patient } from './entities/patient.entity';
import { CommonModule } from '../common/common.module';
import { IndicationsModule } from '../indications/indications.module';
import { MedicalRecordsModule } from '../medical-records/medical-records.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Patient]),
    CommonModule,
    forwardRef(() => IndicationsModule),
    forwardRef(() => MedicalRecordsModule),
  ],
  controllers: [PatientsController],
  providers: [PatientsService],
  exports: [PatientsService],
})
export class PatientsModule {}
