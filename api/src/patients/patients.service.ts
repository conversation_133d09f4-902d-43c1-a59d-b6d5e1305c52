import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePatientDto } from './dto/create-patient.dto';
import { UpdatePatientDto } from './dto/update-patient.dto';
import { ConcludedTreatmentQueryDto } from './dto/concluded-treatment-query.dto';
import { CreateExternalPatientDto } from './dto/create-external-patient.dto';
import { Patient } from './entities/patient.entity';
import { PatientType } from '../patient-types/entities/patient-type.entity';
import { ValidationService } from '../common/services/validation.service';
import { PaginationDto, PaginatedResponse } from '../common/dto/pagination.dto';
import { IndicationsService } from '../indications/indications.service';
import { MedicalRecordsService } from '../medical-records/medical-records.service';
import { CreateMedicalRecordDto } from '../medical-records/dto/create-medical-record.dto';
import { PatientWithConcludedTreatment } from './interfaces/patient-with-concluded-treatment.interface';
import { TreatmentPlanStatus } from '../treatment-plans/entities/treatment-plan.entity';

@Injectable()
export class PatientsService {
  private readonly logger = new Logger(PatientsService.name);

  constructor(
    @InjectRepository(Patient)
    private patientsRepository: Repository<Patient>,
    private validationService: ValidationService,
    @Inject(forwardRef(() => IndicationsService))
    private indicationsService: IndicationsService,
    @Inject(forwardRef(() => MedicalRecordsService))
    private medicalRecordsService: MedicalRecordsService,
  ) {}

  async create(createPatientDto: CreatePatientDto): Promise<Patient> {
    try {
      this.logger.log(`Creating patient: ${JSON.stringify(createPatientDto)}`);

      // Validar e formatar a data de nascimento
      if (createPatientDto.birthDate) {
        const formattedDate = this.validationService.validateAndFormatDate(
          createPatientDto.birthDate,
          'Data de nascimento',
        );
        if (formattedDate) {
          createPatientDto.birthDate = formattedDate;
        } else {
          createPatientDto.birthDate = '';
        }
      }

      // Criar a instância do paciente
      const patient = this.patientsRepository.create(createPatientDto);

      // Log específico para o patientTypeId
      this.logger.log(
        `Patient type ID in create DTO: ${createPatientDto.patientTypeId}`,
      );

      // Verificar se o patientTypeId está definido no DTO
      if (createPatientDto.patientTypeId !== undefined) {
        const patientTypeId =
          createPatientDto.patientTypeId === null
            ? null
            : Number(createPatientDto.patientTypeId);

        // Se temos um patientTypeId, buscamos o tipo de paciente correspondente
        if (patientTypeId !== null) {
          try {
            const patientType = await this.patientsRepository.manager.findOne(
              PatientType,
              {
                where: { id: patientTypeId },
              },
            );

            if (patientType) {
              this.logger.log(
                `Found patient type for ID ${patientTypeId}: ${patientType.nome}`,
              );
              patient.patientType = patientType;
            } else {
              this.logger.warn(
                `Patient type with ID ${patientTypeId} not found`,
              );
              patient.patientType = null;
            }
          } catch (error: unknown) {
            const errorMessage =
              error instanceof Error ? error.message : 'Erro desconhecido';
            this.logger.error(`Error loading patient type: ${errorMessage}`);
            patient.patientType = null;
          }
        }
      }

      // Salvar o paciente com o relacionamento
      const savedPatient = await this.patientsRepository.save(patient);

      // Buscar o paciente salvo com todas as relações para garantir que tudo foi salvo corretamente
      const refreshedPatient = await this.patientsRepository.findOne({
        where: { id: savedPatient.id },
        relations: ['patientType'],
      });

      // Log do paciente após salvar
      if (refreshedPatient?.patientType) {
        this.logger.log(
          `Patient created with type: ${JSON.stringify({
            id: refreshedPatient.patientType.id,
            nome: refreshedPatient.patientType.nome,
          })}`,
        );
      } else {
        this.logger.log('Patient created without a patient type');
      }

      // Verificar se o paciente foi indicado por outro paciente
      if (createPatientDto.referredById) {
        try {
          this.logger.log(
            `Patient was referred by ID: ${createPatientDto.referredById}`,
          );

          // Criar uma indicação
          await this.indicationsService.createFromPatientRegistration(
            savedPatient.id,
            createPatientDto.referredById,
          );

          this.logger.log(`Indication created successfully`);
        } catch (error: unknown) {
          const errorMessage =
            error instanceof Error ? error.message : 'Erro desconhecido';
          const errorStack = error instanceof Error ? error.stack : undefined;
          this.logger.error(
            `Error creating indication: ${errorMessage}`,
            errorStack,
          );
          // Não interrompemos o fluxo se a criação da indicação falhar
        }
      }

      // Criar prontuário automaticamente para o paciente
      try {
        this.logger.log(
          `Creating medical record for patient: ${savedPatient.id}`,
        );
        const createMedicalRecordDto = new CreateMedicalRecordDto();
        createMedicalRecordDto.patientId = savedPatient.id;
        await this.medicalRecordsService.create(createMedicalRecordDto);
        this.logger.log(
          `Medical record created successfully for patient: ${savedPatient.id}`,
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Erro desconhecido';
        const errorStack = error instanceof Error ? error.stack : undefined;
        this.logger.error(
          `Error creating medical record for patient: ${errorMessage}`,
          errorStack,
        );
        // Não interrompemos o fluxo se a criação do prontuário falhar
      }

      return refreshedPatient || savedPatient;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      const errorCode = (error as { code?: string }).code;

      this.logger.error(`Error creating patient: ${errorMessage}`, errorStack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (errorCode === 'ER_DUP_ENTRY') {
        throw new BadRequestException(
          'Já existe um paciente com este CPF ou email',
        );
      }
      throw new InternalServerErrorException('Erro ao criar paciente');
    }
  }

  async createFromExternal(
    createExternalPatientDto: CreateExternalPatientDto,
  ): Promise<Patient> {
    try {
      this.logger.log(
        `Creating external patient: ${JSON.stringify(createExternalPatientDto)}`,
      );

      // Criar o paciente com valores padrão para campos obrigatórios
      const patient = this.patientsRepository.create({
        name: createExternalPatientDto.name,
        phone: createExternalPatientDto.phone,
        birthDate: new Date('1900-01-01'),
        cpf: '000.000.000-00',
        email: '<EMAIL>', // Será atualizado após salvar
        isIncomplete: true,
      });

      // Salvar o paciente para obter o ID
      const savedPatient = await this.patientsRepository.save(patient);

      // Atualizar o email com o ID real
      savedPatient.email = `temp_${savedPatient.id}@temp.com`;
      await this.patientsRepository.save(savedPatient);

      this.logger.log(
        `External patient created successfully with id: ${savedPatient.id}`,
      );

      return savedPatient;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      const errorCode = (error as { code?: string }).code;

      this.logger.error(
        `Error creating external patient: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (errorCode === 'ER_DUP_ENTRY') {
        throw new BadRequestException(
          'Erro ao criar paciente: dados duplicados',
        );
      }
      throw new InternalServerErrorException('Erro ao criar paciente externo');
    }
  }

  async findAll(
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<Patient>> {
    try {
      const { page = 1, limit = 10, search, category } = paginationDto;
      this.logger.log(
        `Finding patients with pagination: page ${page}, limit ${limit}, search: ${search}, category: ${category}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder =
        this.patientsRepository.createQueryBuilder('patient');

      // Aplicar filtro de busca
      if (search) {
        queryBuilder.where(
          '(patient.name LIKE :search OR patient.email LIKE :search OR patient.phone LIKE :search OR patient.cpf LIKE :search OR DATE_FORMAT(patient.birthDate, "%d/%m/%Y") LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Aplicar filtro de categoria
      if (category) {
        if (search) {
          queryBuilder.andWhere('patient.category = :category', { category });
        } else {
          queryBuilder.where('patient.category = :category', { category });
        }
      }

      // Aplicar paginação
      queryBuilder.skip(skip).take(limit);

      // Ordenação
      queryBuilder.orderBy('patient.name', 'ASC');

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      // Limpar valores padrão para pacientes incompletos
      const processedData = data.map((patient) =>
        this.clearDefaultValuesIfIncomplete(patient),
      );

      return {
        data: processedData,
        total,
        page,
        limit,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding patients with pagination: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Erro ao buscar pacientes');
    }
  }

  async findOne(id: number): Promise<Patient> {
    try {
      this.logger.log(`Finding patient with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de paciente inválido');
      }

      // Buscar o paciente com todas as relações
      const patient = await this.patientsRepository.findOne({
        where: { id },
        relations: ['schedulings', 'treatmentPlans', 'patientType'],
      });

      // Log para depuração da relação patientType
      if (patient) {
        const patientTypeId = patient.patientType?.id;
        this.logger.log(
          `Patient found with id ${id}, patientType: ${patientTypeId ? patientTypeId : 'null'}`,
        );

        // Verificar se temos a relação patientType carregada
        if (patient.patientType) {
          this.logger.log(
            `Patient type relation loaded: id=${patient.patientType.id}, nome=${patient.patientType.nome}`,
          );
        } else {
          this.logger.log(`No patient type relation for patient ${id}`);
        }
      }

      if (!patient) {
        throw new NotFoundException(`Paciente com ID ${id} não encontrado`);
      }

      // Limpar valores padrão se o paciente for incompleto
      return this.clearDefaultValuesIfIncomplete(patient);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding patient with id ${id}: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar paciente');
    }
  }

  async update(
    id: number,
    updatePatientDto: UpdatePatientDto,
  ): Promise<Patient> {
    try {
      this.logger.log(
        `Updating patient with id: ${id}, data: ${JSON.stringify(updatePatientDto)}`,
      );

      // Log específico para o patientTypeId
      this.logger.log(
        `Patient type ID being updated: ${updatePatientDto.patientTypeId}`,
      );

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de paciente inválido');
      }

      // Validar e formatar a data de nascimento
      if (updatePatientDto.birthDate) {
        const formattedDate = this.validationService.validateAndFormatDate(
          updatePatientDto.birthDate,
          'Data de nascimento',
        );
        if (formattedDate) {
          updatePatientDto.birthDate = formattedDate;
        } else {
          updatePatientDto.birthDate = '';
        }
      }

      // Verificar se o paciente existe
      const patient = await this.findOne(id);

      // Log do paciente antes da atualização
      this.logger.log(
        `Patient before update: ${JSON.stringify({
          id: patient.id,
          name: patient.name,
          patientTypeId: patient.patientType?.id,
        })}`,
      );

      // Atualizar os dados do paciente
      Object.assign(patient, updatePatientDto);

      // Verificar explicitamente se patientTypeId está definido no DTO
      // Fazemos isso após o Object.assign para garantir que nosso valor explícito não seja sobrescrito
      if (updatePatientDto.patientTypeId !== undefined) {
        this.logger.log(
          `Explicitly setting patientTypeId to: ${updatePatientDto.patientTypeId}`,
        );

        // Agora que atualizamos a definição de tipo para permitir null, podemos atribuir diretamente
        const newPatientTypeId =
          updatePatientDto.patientTypeId === null
            ? null
            : Number(updatePatientDto.patientTypeId);

        // Se temos um novo patientTypeId, buscamos o tipo de paciente correspondente
        if (newPatientTypeId !== null) {
          try {
            const patientType = await this.patientsRepository.manager.findOne(
              PatientType,
              {
                where: { id: newPatientTypeId },
              },
            );

            if (patientType) {
              this.logger.log(
                `Found patient type for ID ${newPatientTypeId}: ${patientType.nome}`,
              );
              patient.patientType = patientType;
            } else {
              this.logger.warn(
                `Patient type with ID ${newPatientTypeId} not found`,
              );
              patient.patientType = null;
            }
          } catch (error: unknown) {
            const errorMessage =
              error instanceof Error ? error.message : 'Erro desconhecido';
            this.logger.error(`Error loading patient type: ${errorMessage}`);
            patient.patientType = null;
          }
        } else {
          // Se o patientTypeId é null, limpamos a relação
          patient.patientType = null;
          this.logger.log(`Set patientType to null`);
        }
      }

      // Log do paciente após a atualização
      this.logger.log(
        `Patient after update: ${JSON.stringify({
          id: patient.id,
          name: patient.name,
          patientTypeId: patient.patientType?.id,
        })}`,
      );

      // Salvar o paciente atualizado
      // Usamos o método save do repositório para garantir que as relações sejam atualizadas corretamente
      await this.patientsRepository.save(patient);

      // Não podemos usar clear() pois ele tenta fazer TRUNCATE na tabela
      // Em vez disso, vamos buscar o paciente diretamente do banco de dados
      // com uma nova consulta para garantir dados frescos

      // Buscar o paciente atualizado com todas as relações
      // Usamos uma nova consulta para evitar o cache do TypeORM
      const refreshedPatient = await this.patientsRepository
        .createQueryBuilder('patient')
        .leftJoinAndSelect('patient.patientType', 'patientType')
        .leftJoinAndSelect('patient.schedulings', 'schedulings')
        .leftJoinAndSelect('patient.treatmentPlans', 'treatmentPlans')
        .where('patient.id = :id', { id })
        .getOne();

      if (!refreshedPatient) {
        throw new NotFoundException(
          `Paciente com ID ${id} não encontrado após atualização`,
        );
      }

      // Verificar se temos a relação patientType carregada corretamente
      if (refreshedPatient.patientType) {
        this.logger.log(
          `Patient type relation loaded correctly: id=${refreshedPatient.patientType.id}, nome=${refreshedPatient.patientType.nome}`,
        );
      }

      // Log do paciente após salvar
      this.logger.log(
        `Patient after save: ${JSON.stringify({
          id: refreshedPatient.id,
          name: refreshedPatient.name,
          patientTypeId: refreshedPatient.patientType?.id,
        })}`,
      );

      // Log adicional para verificar a relação patientType
      if (refreshedPatient.patientType) {
        this.logger.log(
          `Patient type relation in response: ${JSON.stringify({
            id: refreshedPatient.patientType.id,
            nome: refreshedPatient.patientType.nome,
          })}`,
        );
      } else {
        this.logger.log('No patient type relation in response');
      }

      // Limpar valores padrão se o paciente for incompleto
      return this.clearDefaultValuesIfIncomplete(refreshedPatient);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      const errorCode = (error as { code?: string }).code;

      this.logger.error(
        `Error updating patient with id ${id}: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      if (errorCode === 'ER_DUP_ENTRY') {
        throw new BadRequestException(
          'Já existe um paciente com este CPF ou email',
        );
      }
      throw new InternalServerErrorException('Erro ao atualizar paciente');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      this.logger.log(`Removing patient with id: ${id}`);

      // Validar o ID
      if (!id || isNaN(id) || id <= 0) {
        throw new BadRequestException('ID de paciente inválido');
      }

      // Verificar se o paciente existe
      const patient = await this.findOne(id);

      // Remover o paciente
      await this.patientsRepository.remove(patient);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      const errorCode = (error as { code?: string }).code;

      this.logger.error(
        `Error removing patient with id ${id}: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      if (errorCode === 'ER_ROW_IS_REFERENCED') {
        throw new BadRequestException(
          'Não é possível excluir o paciente pois ele possui agendamentos ou tratamentos associados',
        );
      }
      throw new InternalServerErrorException('Erro ao remover paciente');
    }
  }

  async findByCpf(cpf: string): Promise<Patient[]> {
    try {
      this.logger.log(`Finding patients with CPF: ${cpf}`);

      // Normalizar o CPF (remover caracteres não numéricos)
      const normalizedCpf = cpf.replace(/\D/g, '');

      // Usar uma consulta SQL que remove caracteres não numéricos do CPF no banco de dados
      const patients = await this.patientsRepository
        .createQueryBuilder('patient')
        .leftJoinAndSelect('patient.patientType', 'patientType')
        .where(
          'REPLACE(REPLACE(REPLACE(patient.cpf, ".", ""), "-", ""), "/", "") = :normalizedCpf',
          {
            normalizedCpf,
          },
        )
        .getMany();

      this.logger.log(
        `Found ${patients.length} patients with normalized CPF: ${normalizedCpf}`,
      );

      // Limpar valores padrão para pacientes incompletos
      const processedPatients = patients.map((patient) =>
        this.clearDefaultValuesIfIncomplete(patient),
      );

      return processedPatients;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding patients by CPF ${cpf}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar pacientes por CPF',
      );
    }
  }

  async findWithConcludedTreatment(
    queryDto: ConcludedTreatmentQueryDto,
  ): Promise<PatientWithConcludedTreatment[]> {
    try {
      // Garantir que days seja um número e tenha um valor padrão
      const days = queryDto.days || 30;
      this.logger.log(
        `Finding patients with treatment concluded in the last ${days} days`,
      );

      // Calcular a data limite (hoje - X dias)
      const limitDate = new Date();
      limitDate.setDate(limitDate.getDate() - days);

      // Formatar a data para o formato do MySQL (YYYY-MM-DD)
      const formattedLimitDate = limitDate.toISOString().split('T')[0];

      this.logger.log(`Limit date: ${formattedLimitDate}`);

      // Buscar pacientes com planos de tratamento concluídos após a data limite
      const result = await this.patientsRepository
        .createQueryBuilder('patient')
        .select([
          'patient.id as id',
          'patient.name as name',
          'patient.cpf as cpf',
          'MAX(treatmentPlan.updatedAt) as treatmentConcludedAt', // Data de conclusão do tratamento (última atualização)
        ])
        .innerJoin('patient.treatmentPlans', 'treatmentPlan')
        .where('treatmentPlan.status = :status', {
          status: TreatmentPlanStatus.COMPLETED,
        })
        .andWhere('treatmentPlan.updatedAt >= :limitDate', {
          limitDate: formattedLimitDate,
        })
        .groupBy('patient.id')
        .getRawMany();

      this.logger.log(
        `Found ${result.length} patients with concluded treatment`,
      );

      // Buscar sugestões para todos os pacientes encontrados
      const patientIds = result.map((patient: { id: number }) => patient.id);
      const suggestionsMap = new Map<
        number,
        Array<{
          id: string;
          status: string;
          iaReasoning: string;
          humanComment: string;
          createdAt: string;
        }>
      >();

      if (patientIds.length > 0) {
        const suggestions = await this.patientsRepository
          .createQueryBuilder('patient')
          .select([
            'patient.id as patientId',
            'suggestion.id as suggestionId',
            'suggestion.status as suggestionStatus',
            'suggestion.iaReasoning as suggestionIaReasoning',
            'suggestion.humanComment as suggestionHumanComment',
            'suggestion.created_at as suggestionCreatedAt',
          ])
          .leftJoin(
            'suggestions',
            'suggestion',
            'suggestion.patientId = patient.id',
          )
          .where('patient.id IN (:...patientIds)', { patientIds })
          .andWhere('suggestion.id IS NOT NULL') // Apenas pacientes com sugestões
          .orderBy('suggestion.created_at', 'DESC')
          .getRawMany();

        // Agrupar sugestões por paciente
        suggestions.forEach(
          (row: {
            patientId: number;
            suggestionId: string;
            suggestionStatus: string;
            suggestionIaReasoning: string;
            suggestionHumanComment: string;
            suggestionCreatedAt: Date;
          }) => {
            if (!suggestionsMap.has(row.patientId)) {
              suggestionsMap.set(row.patientId, []);
            }
            const patientSuggestions = suggestionsMap.get(row.patientId);
            if (patientSuggestions) {
              patientSuggestions.push({
                id: row.suggestionId,
                status: row.suggestionStatus,
                iaReasoning: row.suggestionIaReasoning,
                humanComment: row.suggestionHumanComment,
                createdAt: row.suggestionCreatedAt.toISOString(),
              });
            }
          },
        );
      }

      // Mapear os resultados para o formato esperado
      return result.map(
        (patient: {
          id: number;
          name: string;
          cpf: string;
          treatmentConcludedAt: Date;
        }) => ({
          id: patient.id,
          name: patient.name,
          cpf: patient.cpf,
          medicalRecordUrl: `/medical-records/complete/${patient.id}`,
          treatmentConcludedAt: patient.treatmentConcludedAt.toISOString(),
          suggestions: suggestionsMap.get(patient.id) || [],
        }),
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding patients with concluded treatment: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar pacientes com tratamento concluído',
      );
    }
  }

  /**
   * Limpa valores padrão de pacientes incompletos
   * Retorna campos vazios quando são valores padrão para facilitar edição no frontend
   */
  private clearDefaultValuesIfIncomplete(patient: Patient): Patient {
    if (!patient.isIncomplete) {
      return patient;
    }

    // Criar uma cópia do paciente para não modificar o original
    const processedPatient = { ...patient };

    // Limpar CPF se for valor padrão
    if (processedPatient.cpf === '000.000.000-00') {
      processedPatient.cpf = '';
    }

    // Limpar email se for valor padrão (temporário)
    if (
      processedPatient.email &&
      processedPatient.email.includes('temp_') &&
      processedPatient.email.includes('@temp.com')
    ) {
      processedPatient.email = '';
    }

    // Limpar data de nascimento se for valor padrão
    if (processedPatient.birthDate) {
      const birthDateStr = new Date(processedPatient.birthDate)
        .toISOString()
        .split('T')[0];
      if (birthDateStr === '1900-01-01') {
        // Para campos de data, definimos como null para que seja serializado como null no JSON
        processedPatient.birthDate = null as any;
      }
    }

    return processedPatient;
  }
}
