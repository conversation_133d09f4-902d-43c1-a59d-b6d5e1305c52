import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  Inject,
  forwardRef,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { PatientsService } from './patients.service';
import { CreatePatientDto } from './dto/create-patient.dto';
import { UpdatePatientDto } from './dto/update-patient.dto';
import { ConcludedTreatmentQueryDto } from './dto/concluded-treatment-query.dto';
import { CreateExternalPatientDto } from './dto/create-external-patient.dto';
import { PaginationDto, PaginatedResponse } from '../common/dto/pagination.dto';
import { Patient } from './entities/patient.entity';
import { PatientWithConcludedTreatment } from './interfaces/patient-with-concluded-treatment.interface';
import { MedicalRecordsService } from '../medical-records/medical-records.service';
import { MedicalRecordResponse } from '../medical-records/interfaces/complete-medical-record.interface';

@ApiTags('patients')
@Controller('patients')
export class PatientsController {
  private readonly logger = new Logger(PatientsController.name);

  constructor(
    private readonly patientsService: PatientsService,
    @Inject(forwardRef(() => MedicalRecordsService))
    private readonly medicalRecordsService: MedicalRecordsService,
  ) {}

  @ApiOperation({ summary: 'Criar um novo paciente' })
  @ApiResponse({ status: 201, description: 'Paciente criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreatePatientDto })
  @Post()
  create(@Body() createPatientDto: CreatePatientDto): Promise<Patient> {
    return this.patientsService.create(createPatientDto);
  }

  @ApiOperation({
    summary: 'Criar um paciente via sistema externo (apenas nome e telefone)',
  })
  @ApiResponse({
    status: 201,
    description: 'Paciente externo criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateExternalPatientDto })
  @Post('external')
  createFromExternal(
    @Body() createExternalPatientDto: CreateExternalPatientDto,
  ): Promise<Patient> {
    return this.patientsService.createFromExternal(createExternalPatientDto);
  }

  @ApiOperation({ summary: 'Listar todos os pacientes' })
  @ApiResponse({
    status: 200,
    description: 'Lista de pacientes retornada com sucesso',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Termo de busca para filtrar pacientes por nome, email, telefone, CPF ou data de nascimento',
    type: String,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    description:
      'Categoria para filtrar pacientes (Urgente, Rotina, Follow-up)',
    type: String,
  })
  @Get()
  async findAll(
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<Patient>> {
    return this.patientsService.findAll(paginationDto);
  }

  @ApiOperation({
    summary:
      'Listar pacientes com plano de tratamento concluído nos últimos X dias',
  })
  @ApiResponse({
    status: 200,
    description:
      'Lista de pacientes com tratamento concluído retornada com sucesso',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'João Silva' },
          cpf: { type: 'string', example: '123.456.789-00' },
          medicalRecordUrl: {
            type: 'string',
            example: '/medical-records/complete/1',
          },
          treatmentConcludedAt: {
            type: 'string',
            format: 'date-time',
            example: '2025-05-20T15:30:00.000Z',
          },
          suggestions: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'uuid-string' },
                status: { type: 'string', example: 'IN_ANALYSIS' },
                iaReasoning: {
                  type: 'string',
                  nullable: true,
                  example:
                    'Paciente necessita de limpeza devido ao acúmulo de tártaro',
                },
                humanComment: {
                  type: 'string',
                  nullable: true,
                  example: 'Aprovado pelo dentista responsável',
                },
                createdAt: {
                  type: 'string',
                  format: 'date-time',
                  example: '2025-01-20T10:30:00.000Z',
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'days',
    required: false,
    type: Number,
    description:
      'Número de dias para buscar pacientes com tratamento concluído',
    example: 30,
  })
  @Get('with-concluded-treatment')
  async findWithConcludedTreatment(
    @Query('days') daysParam?: string,
  ): Promise<PatientWithConcludedTreatment[]> {
    // Converter o parâmetro para número e usar valor padrão se necessário
    let days = 30;
    if (daysParam) {
      const parsedDays = parseInt(daysParam, 10);
      if (!isNaN(parsedDays) && parsedDays > 0) {
        days = parsedDays;
      }
    }

    this.logger.log(
      `Finding patients with concluded treatment in the last ${days} days`,
    );

    // Criar o DTO manualmente
    const queryDto: ConcludedTreatmentQueryDto = { days };
    return this.patientsService.findWithConcludedTreatment(queryDto);
  }

  @ApiOperation({ summary: 'Buscar um paciente pelo ID' })
  @ApiResponse({ status: 200, description: 'Paciente encontrado com sucesso' })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do paciente', example: 1 })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Patient> {
    return this.patientsService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar um paciente' })
  @ApiResponse({ status: 200, description: 'Paciente atualizado com sucesso' })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do paciente', example: 1 })
  @ApiBody({ type: UpdatePatientDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePatientDto: UpdatePatientDto,
  ): Promise<Patient> {
    return this.patientsService.update(id, updatePatientDto);
  }

  @ApiOperation({ summary: 'Remover um paciente' })
  @ApiResponse({ status: 200, description: 'Paciente removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do paciente', example: 1 })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    this.logger.log(`Removing patient with id: ${id}`);
    return this.patientsService.remove(id);
  }

  @ApiOperation({ summary: 'Obter prontuário completo do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Prontuário completo retornado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do paciente', example: 1 })
  @Get(':id/medical-record')
  async getMedicalRecord(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<MedicalRecordResponse> {
    this.logger.log(`Getting medical record for patient: ${id}`);
    // Verificar se o paciente existe
    await this.patientsService.findOne(id);
    // Obter o prontuário completo
    return this.medicalRecordsService.getCompleteMedicalRecord(id);
  }
}
