import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class ConcludedTreatmentQueryDto {
  @ApiProperty({
    description:
      'Número de dias para buscar pacientes com tratamento concluído',
    example: 30,
    default: 30,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1, { message: 'O número de dias deve ser maior que zero' })
  days?: number = 30;
}
