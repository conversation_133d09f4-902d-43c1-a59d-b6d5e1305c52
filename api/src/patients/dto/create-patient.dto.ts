import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsString,
  IsDateString,
  IsEnum,
  IsNumber,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PatientCategory, PatientGender } from '../entities/patient.entity';
import { Type } from 'class-transformer';

export class CreatePatientDto {
  // Dados Cadastrais
  @ApiProperty({
    example: '<PERSON>',
    description: 'Nome completo do paciente',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: '1985-06-15',
    description: 'Data de nascimento do paciente (YYYY-MM-DD)',
  })
  @IsNotEmpty()
  @IsDateString()
  birthDate: string;

  @ApiProperty({
    example: 'male',
    description: 'Gênero do paciente',
    enum: PatientGender,
    required: false,
  })
  @IsOptional()
  @IsEnum(PatientGender)
  gender?: PatientGender;

  @ApiProperty({
    example: '123.456.789-00',
    description: 'CPF do paciente',
  })
  @IsNotEmpty()
  @IsString()
  cpf: string;

  @ApiProperty({
    example: 'Google',
    description: 'Como o paciente conheceu a clínica',
    required: false,
  })
  @IsOptional()
  @IsString()
  howDidYouFindUs?: string;

  @ApiProperty({
    example: 'Paciente com histórico de problemas periodontais',
    description: 'Observações sobre o paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  // Contato
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do paciente',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '(11) 98765-4321',
    description: 'Telefone do paciente',
  })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({
    example: '(11) 98765-4321',
    description: 'WhatsApp do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  whatsapp?: string;

  @ApiProperty({
    example: '01234-567',
    description: 'CEP do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressZipCode?: string;

  @ApiProperty({
    example: 'Rua das Flores',
    description: 'Rua do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressStreet?: string;

  @ApiProperty({
    example: '123',
    description: 'Número do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressNumber?: string;

  @ApiProperty({
    example: 'Jardim Primavera',
    description: 'Bairro do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressNeighborhood?: string;

  @ApiProperty({
    example: 'São Paulo',
    description: 'Cidade do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressCity?: string;

  @ApiProperty({
    example: 'SP',
    description: 'Estado do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressState?: string;

  @ApiProperty({
    example: 'Apto 101',
    description: 'Complemento do endereço do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressComplement?: string;

  // Dados Complementares
  @ApiProperty({
    example: 'Engenheiro',
    description: 'Profissão do paciente',
    required: false,
  })
  @IsOptional()
  @IsString()
  profession?: string;

  @ApiProperty({
    example: '12345',
    description: 'Número do prontuário médico',
    required: false,
  })
  @IsOptional()
  @IsString()
  medicalRecordNumber?: string;

  @ApiProperty({
    example: 'Rotina',
    description: 'Categoria do paciente',
    enum: PatientCategory,
    default: PatientCategory.ROUTINE,
  })
  @IsOptional()
  @IsEnum(PatientCategory)
  category?: PatientCategory;

  @ApiProperty({
    example: 1,
    description: 'ID do tipo de paciente',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'O ID do tipo de paciente deve ser um número' })
  patientTypeId?: number | null;

  @ApiProperty({
    example: 1,
    description: 'ID do paciente que fez a indicação',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    {},
    { message: 'O ID do paciente que fez a indicação deve ser um número' },
  )
  referredById?: number | null;
}
