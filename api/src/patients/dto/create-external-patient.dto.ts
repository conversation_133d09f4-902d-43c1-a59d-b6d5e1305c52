import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateExternalPatientDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Nome completo do paciente',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '(11) 98765-4321',
    description: 'Telefone do paciente',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;
}
