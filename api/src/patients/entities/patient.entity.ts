import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Scheduling } from '../../schedulings/entities/scheduling.entity';
import { PatientType } from '../../patient-types/entities/patient-type.entity';
import { Budget } from '../../budgets/entities/budget.entity';
import { TreatmentPlan } from '../../treatment-plans/entities/treatment-plan.entity';
import { Receipt } from '../../receipts/entities/receipt.entity';

export enum PatientCategory {
  URGENT = 'Urgente',
  ROUTINE = 'Rotina',
  FOLLOW_UP = 'Follow-up',
}

export enum PatientGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

@Entity()
export class Patient {
  @PrimaryGeneratedColumn()
  id: number;

  // Dados Cadastrais
  @Column()
  name: string;

  @Column({ type: 'date' })
  birthDate: Date;

  @Column({
    type: 'enum',
    enum: PatientGender,
    nullable: true,
  })
  gender: PatientGender;

  @Column()
  cpf: string;

  @Column({ nullable: true })
  howDidYouFindUs: string;

  @CreateDateColumn()
  registrationDate: Date;

  @Column({ nullable: true, type: 'text' })
  notes: string;

  // Contato
  @Column()
  email: string;

  @Column()
  phone: string;

  @Column({ nullable: true })
  whatsapp: string;

  @Column({ nullable: true })
  addressZipCode: string;

  @Column({ nullable: true })
  addressStreet: string;

  @Column({ nullable: true })
  addressNumber: string;

  @Column({ nullable: true })
  addressNeighborhood: string;

  @Column({ nullable: true })
  addressCity: string;

  @Column({ nullable: true })
  addressState: string;

  @Column({ nullable: true })
  addressComplement: string;

  // Dados Complementares
  @Column({ nullable: true })
  profession: string;

  @Column({ nullable: true })
  medicalRecordNumber: string;

  @Column({
    type: 'enum',
    enum: PatientCategory,
    default: PatientCategory.ROUTINE,
  })
  category: PatientCategory;

  @Column({ default: false })
  isIncomplete: boolean;

  // Campos de sistema
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relacionamentos
  @OneToMany(() => Scheduling, (scheduling) => scheduling.patient)
  schedulings: Scheduling[];

  // Relacionamento com tratamentos removido - agora usa TreatmentPlan e TreatmentProcedure

  @OneToMany(() => Budget, (budget) => budget.patient)
  budgets: Budget[];

  @ManyToOne(() => PatientType, (patientType) => patientType.patients, {
    nullable: true,
  })
  @JoinColumn({ name: 'patientTypeId' })
  patientType: PatientType | null;

  @OneToMany(() => TreatmentPlan, (treatmentPlan) => treatmentPlan.patient, {
    lazy: true,
  })
  treatmentPlans: Promise<TreatmentPlan[]>;

  @OneToMany(() => Receipt, (receipt) => receipt.patient)
  receipts: Receipt[];
}
