import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Anamnesis } from './entities/anamnesis.entity';
import { AnamnesisQuestion } from './entities/anamnesis-question.entity';
import { AnamnesisAnswer } from './entities/anamnesis-answer.entity';
import { CreateAnamnesisDto } from './dto/create-anamnesis.dto';
import { UpdateAnamnesisDto } from './dto/update-anamnesis.dto';
import { CreateAnamnesisQuestionDto } from './dto/create-anamnesis-question.dto';
import { UpdateAnamnesisQuestionDto } from './dto/update-anamnesis-question.dto';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class AnamnesisService {
  private readonly logger = new Logger(AnamnesisService.name);

  constructor(
    @InjectRepository(Anamnesis)
    private anamnesisRepository: Repository<Anamnesis>,
    @InjectRepository(AnamnesisQuestion)
    private anamnesisQuestionRepository: Repository<AnamnesisQuestion>,
    @InjectRepository(AnamnesisAnswer)
    private anamnesisAnswerRepository: Repository<AnamnesisAnswer>,
  ) {}

  // Métodos para AnamnesisQuestion
  async findAllQuestions(): Promise<AnamnesisQuestion[]> {
    try {
      this.logger.log('Finding all anamnesis questions');
      return await this.anamnesisQuestionRepository.find({
        order: { order: 'ASC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding anamnesis questions: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar perguntas de anamnese',
      );
    }
  }

  async findOneQuestion(id: number): Promise<AnamnesisQuestion> {
    try {
      this.logger.log(`Finding anamnesis question with id: ${id}`);
      const question = await this.anamnesisQuestionRepository.findOne({
        where: { id },
      });
      if (!question) {
        throw new NotFoundException(
          `Pergunta de anamnese com ID ${id} não encontrada`,
        );
      }
      return question;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding anamnesis question with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar pergunta de anamnese',
      );
    }
  }

  async createQuestion(
    createAnamnesisQuestionDto: CreateAnamnesisQuestionDto,
  ): Promise<AnamnesisQuestion> {
    try {
      this.logger.log(
        `Creating anamnesis question: ${JSON.stringify(
          createAnamnesisQuestionDto,
        )}`,
      );
      const question = this.anamnesisQuestionRepository.create(
        createAnamnesisQuestionDto,
      );
      return await this.anamnesisQuestionRepository.save(question);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating anamnesis question: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao criar pergunta de anamnese',
      );
    }
  }

  async updateQuestion(
    id: number,
    updateAnamnesisQuestionDto: UpdateAnamnesisQuestionDto,
  ): Promise<AnamnesisQuestion> {
    try {
      this.logger.log(
        `Updating anamnesis question with id: ${id}, data: ${JSON.stringify(
          updateAnamnesisQuestionDto,
        )}`,
      );
      const question = await this.findOneQuestion(id);
      Object.assign(question, updateAnamnesisQuestionDto);
      return await this.anamnesisQuestionRepository.save(question);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating anamnesis question with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao atualizar pergunta de anamnese',
      );
    }
  }

  async removeQuestion(id: number): Promise<void> {
    try {
      this.logger.log(`Removing anamnesis question with id: ${id}`);
      const question = await this.findOneQuestion(id);
      await this.anamnesisQuestionRepository.remove(question);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing anamnesis question with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao remover pergunta de anamnese',
      );
    }
  }

  // Métodos para Anamnesis
  async create(createAnamnesisDto: CreateAnamnesisDto): Promise<Anamnesis> {
    try {
      this.logger.log(
        `Creating anamnesis: ${JSON.stringify(createAnamnesisDto)}`,
      );

      // Verificar se todas as perguntas existem e obter os textos das perguntas
      const questionsMap = new Map<number, string>();
      for (const answer of createAnamnesisDto.answers) {
        const question = await this.findOneQuestion(answer.questionId);
        questionsMap.set(answer.questionId, question.title);
      }

      // Criar a anamnese
      const anamnesis = this.anamnesisRepository.create({
        patientId: createAnamnesisDto.patientId,
        employeeId: createAnamnesisDto.employeeId,
      });

      // Salvar a anamnese para obter o ID
      const savedAnamnesis = await this.anamnesisRepository.save(anamnesis);

      // Criar as respostas com o texto da pergunta
      const answers = createAnamnesisDto.answers.map((answerDto) =>
        this.anamnesisAnswerRepository.create({
          anamnesisId: savedAnamnesis.id,
          questionId: answerDto.questionId,
          questionText:
            questionsMap.get(answerDto.questionId) || 'Pergunta não encontrada',
          answer: answerDto.answer,
        }),
      );

      // Salvar as respostas
      await this.anamnesisAnswerRepository.save(answers);

      // Retornar a anamnese com as respostas
      return this.findOne(savedAnamnesis.id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new BadRequestException(error.message);
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating anamnesis: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao criar anamnese');
    }
  }

  async findAll(): Promise<Anamnesis[]> {
    try {
      this.logger.log('Finding all anamnesis');
      return await this.anamnesisRepository.find({
        relations: ['patient', 'employee', 'answers', 'answers.question'],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding all anamnesis: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao buscar anamneses');
    }
  }

  async findByPatient(patientId: number): Promise<Anamnesis[]> {
    try {
      this.logger.log(`Finding anamnesis for patient with id: ${patientId}`);
      return await this.anamnesisRepository.find({
        where: { patientId },
        relations: ['patient', 'employee', 'answers', 'answers.question'],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding anamnesis for patient with id ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar anamneses do paciente',
      );
    }
  }

  async findOne(id: string): Promise<Anamnesis> {
    try {
      this.logger.log(`Finding anamnesis with id: ${id}`);
      const anamnesis = await this.anamnesisRepository.findOne({
        where: { id },
        relations: ['patient', 'employee', 'answers', 'answers.question'],
      });
      if (!anamnesis) {
        throw new NotFoundException(`Anamnese com ID ${id} não encontrada`);
      }
      return anamnesis;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding anamnesis with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao buscar anamnese');
    }
  }

  async update(
    id: string,
    updateAnamnesisDto: UpdateAnamnesisDto,
  ): Promise<Anamnesis> {
    try {
      this.logger.log(
        `Updating anamnesis with id: ${id}, data: ${JSON.stringify(
          updateAnamnesisDto,
        )}`,
      );

      // Verificar se a anamnese existe
      const anamnesis = await this.findOne(id);

      // Atualizar os campos da anamnese
      if (updateAnamnesisDto.patientId) {
        anamnesis.patientId = updateAnamnesisDto.patientId;
      }
      if (updateAnamnesisDto.employeeId !== undefined) {
        anamnesis.employeeId = updateAnamnesisDto.employeeId;
      }

      // Salvar a anamnese
      await this.anamnesisRepository.save(anamnesis);

      // Atualizar as respostas se fornecidas
      if (updateAnamnesisDto.answers && updateAnamnesisDto.answers.length > 0) {
        // Remover as respostas existentes
        await this.anamnesisAnswerRepository.delete({ anamnesisId: id });

        // Verificar se todas as perguntas existem e obter os textos das perguntas
        const questionsMap = new Map<number, string>();
        for (const answer of updateAnamnesisDto.answers) {
          const question = await this.findOneQuestion(answer.questionId);
          questionsMap.set(answer.questionId, question.title);
        }

        // Criar as novas respostas com o texto da pergunta
        const answers = updateAnamnesisDto.answers.map((answerDto) =>
          this.anamnesisAnswerRepository.create({
            anamnesisId: id,
            questionId: answerDto.questionId,
            questionText:
              questionsMap.get(answerDto.questionId) ||
              'Pergunta não encontrada',
            answer: answerDto.answer,
          }),
        );

        // Salvar as novas respostas
        await this.anamnesisAnswerRepository.save(answers);
      }

      // Retornar a anamnese atualizada
      return this.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating anamnesis with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao atualizar anamnese');
    }
  }

  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Removing anamnesis with id: ${id}`);
      const anamnesis = await this.findOne(id);
      await this.anamnesisRepository.remove(anamnesis);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing anamnesis with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao remover anamnese');
    }
  }
}
