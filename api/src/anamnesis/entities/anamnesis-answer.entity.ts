import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Anamnesis } from './anamnesis.entity';
import { AnamnesisQuestion } from './anamnesis-question.entity';

@Entity('anamnesis_answers')
export class AnamnesisAnswer {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único da resposta de anamnese' })
  id: number;

  @ManyToOne(() => Anamnesis, (anamnesis) => anamnesis.answers, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'anamnesisId' })
  anamnesis: Anamnesis;

  @Column()
  anamnesisId: string;

  @ManyToOne(() => AnamnesisQuestion, (question) => question.answers, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'questionId' })
  question: AnamnesisQuestion;

  @Column()
  questionId: number;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Texto da pergunta no momento da resposta' })
  questionText: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Resposta à pergunta' })
  answer: string;
}
