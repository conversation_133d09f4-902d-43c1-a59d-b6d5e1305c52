import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { Employee } from '../../employees/entities/employee.entity';
import { AnamnesisAnswer } from './anamnesis-answer.entity';

@Entity('anamnesis')
export class Anamnesis {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da anamnese' })
  id: string;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado à anamnese' })
  patient: Patient;

  @Column()
  patientId: number;

  @ManyToOne(() => Employee, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'employeeId' })
  @ApiProperty({ description: 'Funcionário que realizou a anamnese' })
  employee: Employee;

  @Column({ nullable: true })
  employeeId: string;

  @OneToMany(() => AnamnesisAnswer, (answer) => answer.anamnesis, {
    cascade: true,
    eager: true,
  })
  @ApiProperty({ description: 'Respostas da anamnese' })
  answers: AnamnesisAnswer[];

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação da anamnese' })
  createdAt: Date;
}
