import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { AnamnesisAnswer } from './anamnesis-answer.entity';

export enum AnamnesisQuestionType {
  TEXT = 'text',
  BOOLEAN = 'boolean',
  OPTIONS = 'options',
}

@Entity('anamnesis_questions')
export class AnamnesisQuestion {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único da pergunta de anamnese' })
  id: number;

  @Column({ length: 255 })
  @ApiProperty({ description: 'Título/texto da pergunta' })
  title: string;

  @Column({
    type: 'enum',
    enum: AnamnesisQuestionType,
    default: AnamnesisQuestionType.TEXT,
  })
  @ApiProperty({
    description: 'Tipo da pergunta (texto, booleano, opções)',
    enum: AnamnesisQuestionType,
  })
  type: AnamnesisQuestionType;

  @Column({ type: 'int', default: 0 })
  @ApiProperty({ description: 'Ordem de exibição da pergunta' })
  order: number;

  @OneToMany(() => AnamnesisAnswer, (answer) => answer.question)
  answers: AnamnesisAnswer[];

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
