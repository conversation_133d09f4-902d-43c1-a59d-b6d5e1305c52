import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { AnamnesisService } from './anamnesis.service';
import { CreateAnamnesisDto } from './dto/create-anamnesis.dto';
import { UpdateAnamnesisDto } from './dto/update-anamnesis.dto';
import { CreateAnamnesisQuestionDto } from './dto/create-anamnesis-question.dto';
import { UpdateAnamnesisQuestionDto } from './dto/update-anamnesis-question.dto';
import { Anamnesis } from './entities/anamnesis.entity';
import { AnamnesisQuestion } from './entities/anamnesis-question.entity';

@ApiTags('anamnesis')
@Controller('anamnesis')
export class AnamnesisController {
  constructor(private readonly anamnesisService: AnamnesisService) {}

  @ApiOperation({ summary: 'Criar uma nova anamnese' })
  @ApiResponse({ status: 201, description: 'Anamnese criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateAnamnesisDto })
  @Post()
  create(@Body() createAnamnesisDto: CreateAnamnesisDto): Promise<Anamnesis> {
    return this.anamnesisService.create(createAnamnesisDto);
  }

  @ApiOperation({ summary: 'Listar todas as anamneses' })
  @ApiResponse({
    status: 200,
    description: 'Lista de anamneses retornada com sucesso',
  })
  @Get()
  findAll(): Promise<Anamnesis[]> {
    return this.anamnesisService.findAll();
  }

  @ApiOperation({ summary: 'Buscar uma anamnese pelo ID' })
  @ApiResponse({ status: 200, description: 'Anamnese encontrada com sucesso' })
  @ApiResponse({ status: 404, description: 'Anamnese não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da anamnese' })
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Anamnesis> {
    return this.anamnesisService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar uma anamnese' })
  @ApiResponse({ status: 200, description: 'Anamnese atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Anamnese não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID da anamnese' })
  @ApiBody({ type: UpdateAnamnesisDto })
  @Patch(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAnamnesisDto: UpdateAnamnesisDto,
  ): Promise<Anamnesis> {
    return this.anamnesisService.update(id, updateAnamnesisDto);
  }

  @ApiOperation({ summary: 'Remover uma anamnese' })
  @ApiResponse({ status: 200, description: 'Anamnese removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Anamnese não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da anamnese' })
  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.anamnesisService.remove(id);
  }

  @ApiOperation({ summary: 'Listar anamneses de um paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de anamneses do paciente retornada com sucesso',
  })
  @ApiParam({ name: 'id', description: 'ID do paciente' })
  @Get('patients/:id')
  findByPatient(@Param('id', ParseIntPipe) id: number): Promise<Anamnesis[]> {
    return this.anamnesisService.findByPatient(id);
  }
}

@ApiTags('anamnesis-questions')
@Controller('anamnesis-questions')
export class AnamnesisQuestionsController {
  constructor(private readonly anamnesisService: AnamnesisService) {}

  @ApiOperation({ summary: 'Criar uma nova pergunta de anamnese' })
  @ApiResponse({
    status: 201,
    description: 'Pergunta de anamnese criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateAnamnesisQuestionDto })
  @Post()
  create(
    @Body() createAnamnesisQuestionDto: CreateAnamnesisQuestionDto,
  ): Promise<AnamnesisQuestion> {
    return this.anamnesisService.createQuestion(createAnamnesisQuestionDto);
  }

  @ApiOperation({ summary: 'Listar todas as perguntas de anamnese' })
  @ApiResponse({
    status: 200,
    description: 'Lista de perguntas de anamnese retornada com sucesso',
  })
  @Get()
  findAll(): Promise<AnamnesisQuestion[]> {
    return this.anamnesisService.findAllQuestions();
  }

  @ApiOperation({ summary: 'Buscar uma pergunta de anamnese pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Pergunta de anamnese encontrada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Pergunta de anamnese não encontrada',
  })
  @ApiParam({ name: 'id', description: 'ID da pergunta de anamnese' })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<AnamnesisQuestion> {
    return this.anamnesisService.findOneQuestion(id);
  }

  @ApiOperation({ summary: 'Atualizar uma pergunta de anamnese' })
  @ApiResponse({
    status: 200,
    description: 'Pergunta de anamnese atualizada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Pergunta de anamnese não encontrada',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID da pergunta de anamnese' })
  @ApiBody({ type: UpdateAnamnesisQuestionDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAnamnesisQuestionDto: UpdateAnamnesisQuestionDto,
  ): Promise<AnamnesisQuestion> {
    return this.anamnesisService.updateQuestion(id, updateAnamnesisQuestionDto);
  }

  @ApiOperation({ summary: 'Remover uma pergunta de anamnese' })
  @ApiResponse({
    status: 200,
    description: 'Pergunta de anamnese removida com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Pergunta de anamnese não encontrada',
  })
  @ApiParam({ name: 'id', description: 'ID da pergunta de anamnese' })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.anamnesisService.removeQuestion(id);
  }
}
