import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateAnamnesisAnswerDto {
  @ApiProperty({
    description: 'ID da pergunta',
    example: 1,
  })
  @IsNumber({}, { message: 'O ID da pergunta deve ser um número' })
  questionId: number;

  @ApiProperty({
    description: 'Texto da pergunta no momento da resposta',
    example: 'Está em tratamento médico atualmente?',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O texto da pergunta deve ser uma string' })
  questionText?: string;

  @ApiProperty({
    description: 'Resposta à pergunta',
    example: 'Sim',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A resposta deve ser uma string' })
  answer: string;
}
