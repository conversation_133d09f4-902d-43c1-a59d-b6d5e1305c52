import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsString, Min } from 'class-validator';
import { AnamnesisQuestionType } from '../entities/anamnesis-question.entity';

export class CreateAnamnesisQuestionDto {
  @ApiProperty({
    description: 'Título/texto da pergunta',
    example: 'Está em tratamento médico atualmente?',
  })
  @IsNotEmpty({ message: 'O título da pergunta é obrigatório' })
  @IsString({ message: 'O título deve ser uma string' })
  title: string;

  @ApiProperty({
    description: 'Tipo da pergunta',
    enum: AnamnesisQuestionType,
    example: AnamnesisQuestionType.BOOLEAN,
  })
  @IsNotEmpty({ message: 'O tipo da pergunta é obrigatório' })
  @IsEnum(AnamnesisQuestionType, {
    message: 'O tipo deve ser text, boolean ou options',
  })
  type: AnamnesisQuestionType;

  @ApiProperty({
    description: 'Ordem de exibição da pergunta',
    example: 1,
  })
  @IsInt({ message: 'A ordem deve ser um número inteiro' })
  @Min(0, { message: 'A ordem deve ser maior ou igual a zero' })
  order: number;
}
