import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CreateAnamnesisAnswerDto } from './create-anamnesis-answer.dto';

export class CreateAnamnesisDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  @IsNumber({}, { message: 'O ID do paciente deve ser um número' })
  patientId: number;

  @ApiProperty({
    description: 'ID do funcionário que realizou a anamnese',
    example: 'uuid-do-funcionario',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O ID do funcionário deve ser uma string' })
  employeeId?: string;

  @ApiProperty({
    description: 'Respostas da anamnese',
    type: [CreateAnamnesisAnswerDto],
  })
  @IsArray({ message: 'As respostas devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => CreateAnamnesisAnswerDto)
  answers: CreateAnamnesisAnswerDto[];
}
