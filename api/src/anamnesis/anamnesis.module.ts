import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnamnesisService } from './anamnesis.service';
import {
  AnamnesisController,
  AnamnesisQuestionsController,
} from './anamnesis.controller';
import { Anamnesis } from './entities/anamnesis.entity';
import { AnamnesisQuestion } from './entities/anamnesis-question.entity';
import { AnamnesisAnswer } from './entities/anamnesis-answer.entity';
import { PatientsModule } from '../patients/patients.module';
import { EmployeesModule } from '../employees/employees.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Anamnesis, AnamnesisQuestion, AnamnesisAnswer]),
    PatientsModule,
    EmployeesModule,
  ],
  controllers: [AnamnesisController, AnamnesisQuestionsController],
  providers: [AnamnesisService],
  exports: [AnamnesisService],
})
export class AnamnesisModule {}
