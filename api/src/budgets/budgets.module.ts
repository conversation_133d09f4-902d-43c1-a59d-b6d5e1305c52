import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BudgetsService } from './budgets.service';
import { BudgetsController } from './budgets.controller';
import { Budget } from './entities/budget.entity';
import { BudgetItem } from './entities/budget-item.entity';
import { PatientsModule } from '../patients/patients.module';
import { DentistsModule } from '../dentists/dentists.module';
import { ProceduresModule } from '../procedures/procedures.module';
import { TreatmentPlansModule } from '../treatment-plans/treatment-plans.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Budget, BudgetItem]),
    PatientsModule,
    DentistsModule,
    ProceduresModule,
    forwardRef(() => TreatmentPlansModule),
  ],
  controllers: [BudgetsController],
  providers: [BudgetsService],
  exports: [BudgetsService],
})
export class BudgetsModule {}
