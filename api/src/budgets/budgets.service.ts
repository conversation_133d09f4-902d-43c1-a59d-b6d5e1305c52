import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Budget, BudgetStatus, DiscountType } from './entities/budget.entity';
import { BudgetItem } from './entities/budget-item.entity';
import { CreateBudgetDto } from './dto/create-budget.dto';
import { UpdateBudgetDto } from './dto/update-budget.dto';
import { BudgetPaginationDto } from './dto/budget-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { PatientsService } from '../patients/patients.service';
import { DentistsService } from '../dentists/dentists.service';
import { ProceduresService } from '../procedures/procedures.service';
import { TreatmentPlansService } from '../treatment-plans/treatment-plans.service';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class BudgetsService {
  private readonly logger = new Logger(BudgetsService.name);

  constructor(
    @InjectRepository(Budget)
    private budgetsRepository: Repository<Budget>,
    @InjectRepository(BudgetItem)
    private budgetItemsRepository: Repository<BudgetItem>,
    private patientsService: PatientsService,
    private dentistsService: DentistsService,
    private proceduresService: ProceduresService,
    @Inject(forwardRef(() => TreatmentPlansService))
    private treatmentPlansService: TreatmentPlansService,
  ) {}

  async create(createBudgetDto: CreateBudgetDto): Promise<Budget> {
    try {
      this.logger.log(`Creating budget: ${JSON.stringify(createBudgetDto)}`);

      // Verificar se o paciente existe
      await this.patientsService.findOne(createBudgetDto.patientId);

      // Verificar se o dentista existe
      await this.dentistsService.findOne(createBudgetDto.dentistId);

      // Verificar se os procedimentos existem
      for (const item of createBudgetDto.items) {
        await this.proceduresService.findOne(item.procedureId);
        await this.dentistsService.findOne(item.executingDentistId);
      }

      // Criar o orçamento
      const budget = this.budgetsRepository.create({
        patientId: createBudgetDto.patientId,
        dentistId: createBudgetDto.dentistId,
        notes: createBudgetDto.notes,
        status: createBudgetDto.status || BudgetStatus.OPEN,
        totalValue: createBudgetDto.totalValue,
        amountPaid: createBudgetDto.amountPaid,
        discount: createBudgetDto.discount || 0,
        discountType: createBudgetDto.discountType || DiscountType.NONE,
        paymentMethod: createBudgetDto.paymentMethod,
        createdBy: createBudgetDto.createdBy,
        installments: createBudgetDto.installments || 1,
      });

      // Salvar o orçamento para obter o ID
      const savedBudget = await this.budgetsRepository.save(budget);

      // Criar os itens do orçamento
      const budgetItems = createBudgetDto.items.map((item) =>
        this.budgetItemsRepository.create({
          budgetId: savedBudget.id,
          procedureId: item.procedureId,
          tooth: item.tooth,
          executingDentistId: item.executingDentistId,
          value: item.value,
        }),
      );

      // Salvar os itens do orçamento
      await this.budgetItemsRepository.save(budgetItems);

      // Retornar o orçamento completo
      return this.findOne(savedBudget.id);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating budget: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao criar orçamento');
    }
  }

  async findAll(): Promise<Budget[]> {
    return this.budgetsRepository.find({
      relations: [
        'patient',
        'dentist',
        'items',
        'items.procedure',
        'items.executingDentist',
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllPaginated(
    paginationDto: BudgetPaginationDto,
  ): Promise<PaginatedResponse<Budget>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        patientId,
        dentistId,
      } = paginationDto;
      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder = this.budgetsRepository
        .createQueryBuilder('budget')
        .leftJoinAndSelect('budget.patient', 'patient')
        .leftJoinAndSelect('budget.dentist', 'dentist')
        .leftJoinAndSelect('budget.items', 'items')
        .leftJoinAndSelect('items.procedure', 'procedure')
        .leftJoinAndSelect('items.executingDentist', 'executingDentist');

      // Aplicar filtros
      if (patientId) {
        queryBuilder.andWhere('budget.patientId = :patientId', { patientId });
      }

      if (dentistId) {
        queryBuilder.andWhere('budget.dentistId = :dentistId', { dentistId });
      }

      if (status) {
        queryBuilder.andWhere('budget.status = :status', { status });
      }

      if (search) {
        queryBuilder.andWhere(
          '(patient.name LIKE :search OR dentist.name LIKE :search OR budget.notes LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Aplicar paginação
      queryBuilder.skip(skip).take(limit);

      // Ordenação
      queryBuilder.orderBy('budget.createdAt', 'DESC');

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding budgets: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao buscar orçamentos');
    }
  }

  async findByPatient(patientId: number): Promise<Budget[]> {
    try {
      // Verificar se o paciente existe
      await this.patientsService.findOne(patientId);

      return this.budgetsRepository.find({
        where: { patientId },
        relations: [
          'patient',
          'dentist',
          'items',
          'items.procedure',
          'items.executingDentist',
        ],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding budgets by patient: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar orçamentos do paciente',
      );
    }
  }

  async findOne(id: number): Promise<Budget> {
    const budget = await this.budgetsRepository.findOne({
      where: { id },
      relations: [
        'patient',
        'dentist',
        'items',
        'items.procedure',
        'items.executingDentist',
      ],
    });

    if (!budget) {
      throw new NotFoundException(`Orçamento com ID ${id} não encontrado`);
    }

    // Garantir que todos os itens tenham seus procedimentos carregados
    if (budget.items && budget.items.length > 0) {
      for (const item of budget.items) {
        if (!item.procedure) {
          this.logger.warn(
            `Item ${item.id} do orçamento ${id} não tem procedimento associado`,
          );
        }
      }
    }

    return budget;
  }

  async update(id: number, updateBudgetDto: UpdateBudgetDto): Promise<Budget> {
    try {
      // Verificar se o orçamento existe
      const budget = await this.findOne(id);

      // Verificar se o orçamento está aprovado ou cancelado
      if (
        budget.status === BudgetStatus.APPROVED &&
        updateBudgetDto.status !== BudgetStatus.CANCELLED
      ) {
        throw new BadRequestException(
          'Não é possível editar um orçamento aprovado. Apenas cancelar.',
        );
      }

      if (budget.status === BudgetStatus.CANCELLED) {
        throw new BadRequestException(
          'Não é possível editar um orçamento cancelado.',
        );
      }

      // Verificar se o paciente existe (se fornecido)
      if (updateBudgetDto.patientId) {
        await this.patientsService.findOne(updateBudgetDto.patientId);
      }

      // Verificar se o dentista existe (se fornecido)
      if (updateBudgetDto.dentistId) {
        await this.dentistsService.findOne(updateBudgetDto.dentistId);
      }

      // Verificar se o orçamento está sendo aprovado
      const isBeingApproved =
        budget.status !== BudgetStatus.APPROVED &&
        updateBudgetDto.status === BudgetStatus.APPROVED;

      // Atualizar os campos básicos do orçamento
      if (updateBudgetDto.patientId)
        budget.patientId = updateBudgetDto.patientId;
      if (updateBudgetDto.dentistId)
        budget.dentistId = updateBudgetDto.dentistId;
      if (updateBudgetDto.notes !== undefined)
        budget.notes = updateBudgetDto.notes;
      if (updateBudgetDto.status) budget.status = updateBudgetDto.status;
      if (updateBudgetDto.totalValue !== undefined)
        budget.totalValue = updateBudgetDto.totalValue;
      if (updateBudgetDto.discount !== undefined)
        budget.discount = updateBudgetDto.discount;
      if (updateBudgetDto.discountType !== undefined)
        budget.discountType = updateBudgetDto.discountType;
      if (updateBudgetDto.paymentMethod)
        budget.paymentMethod = updateBudgetDto.paymentMethod;
      if (updateBudgetDto.installments)
        budget.installments = updateBudgetDto.installments;
      if (updateBudgetDto.amountPaid)
        budget.amountPaid = updateBudgetDto.amountPaid;

      // Salvar as alterações no orçamento
      await this.budgetsRepository.save(budget);

      // Se houver itens para atualizar
      if (updateBudgetDto.items) {
        // Remover os itens existentes
        await this.budgetItemsRepository.delete({ budgetId: id });

        // Verificar se os procedimentos e dentistas existem
        for (const item of updateBudgetDto.items) {
          await this.proceduresService.findOne(item.procedureId);
          await this.dentistsService.findOne(item.executingDentistId);
        }

        // Criar os novos itens
        const budgetItems = updateBudgetDto.items.map((item) =>
          this.budgetItemsRepository.create({
            budgetId: id,
            procedureId: item.procedureId,
            tooth: item.tooth,
            executingDentistId: item.executingDentistId,
            value: item.value,
          }),
        );

        // Salvar os novos itens
        await this.budgetItemsRepository.save(budgetItems);
      }

      // Se o orçamento foi aprovado, criar um plano de tratamento
      if (isBeingApproved) {
        try {
          // Buscar o orçamento completo com todos os relacionamentos
          const completeBudget = await this.budgetsRepository.findOne({
            where: { id },
            relations: [
              'patient',
              'dentist',
              'items',
              'items.procedure',
              'items.executingDentist',
            ],
          });

          if (!completeBudget) {
            throw new NotFoundException(
              `Orçamento com ID ${id} não encontrado`,
            );
          }

          this.logger.log(
            `Orçamento ${id} aprovado. Criando plano de tratamento com ${completeBudget.items?.length || 0} itens.`,
          );

          // Verificar se o orçamento tem itens
          if (!completeBudget.items || completeBudget.items.length === 0) {
            throw new BadRequestException(
              `O orçamento ${id} não possui itens para criar um plano de tratamento`,
            );
          }

          // Verificar se todos os itens têm procedimentos associados
          for (const item of completeBudget.items) {
            if (!item.procedure) {
              this.logger.warn(
                `Item ${item.id} do orçamento ${id} não tem procedimento associado`,
              );
            }
          }

          // Criar o plano de tratamento
          const treatmentPlan =
            await this.treatmentPlansService.createFromBudget(completeBudget);

          this.logger.log(
            `Plano de tratamento criado com sucesso para o orçamento ${id}: ID do plano ${treatmentPlan.id}`,
          );
        } catch (error) {
          const errorInfo = formatErrorForLogging(error);
          this.logger.error(
            `Erro ao criar plano de tratamento a partir do orçamento ${id}: ${errorInfo.message}`,
            errorInfo.stack,
          );

          // Se for um erro de validação ou não encontrado, lançar para impedir a aprovação do orçamento
          if (
            error instanceof BadRequestException ||
            error instanceof NotFoundException
          ) {
            throw error;
          }

          // Para outros tipos de erro, não impedir a aprovação do orçamento
          this.logger.warn(
            `A aprovação do orçamento ${id} continuará, mas o plano de tratamento não foi criado.`,
          );
        }
      }

      // Retornar o orçamento atualizado
      return this.findOne(id);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating budget: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao atualizar orçamento');
    }
  }

  async remove(id: number): Promise<void> {
    const budget = await this.findOne(id);

    // Verificar se o orçamento está aprovado
    if (budget.status === BudgetStatus.APPROVED) {
      throw new BadRequestException(
        'Não é possível excluir um orçamento aprovado. Considere cancelá-lo.',
      );
    }

    await this.budgetsRepository.remove(budget);
  }
}
