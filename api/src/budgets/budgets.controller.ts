import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { BudgetsService } from './budgets.service';
import { CreateBudgetDto } from './dto/create-budget.dto';
import { UpdateBudgetDto } from './dto/update-budget.dto';
import { Budget } from './entities/budget.entity';
import { BudgetPaginationDto } from './dto/budget-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('budgets')
@Controller('budgets')
export class BudgetsController {
  constructor(private readonly budgetsService: BudgetsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo orçamento' })
  @ApiResponse({
    status: 201,
    description: 'Orçamento criado com sucesso',
    type: Budget,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(@Body() createBudgetDto: CreateBudgetDto): Promise<Budget> {
    return this.budgetsService.create(createBudgetDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os orçamentos' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar orçamentos',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status do orçamento',
    enum: ['open', 'approved', 'cancelled'],
  })
  @ApiQuery({
    name: 'patientId',
    required: false,
    description: 'Filtrar por ID do paciente',
    type: Number,
  })
  @ApiQuery({
    name: 'dentistId',
    required: false,
    description: 'Filtrar por ID do dentista',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de orçamentos retornada com sucesso',
  })
  findAll(
    @Query() paginationDto: BudgetPaginationDto,
  ): Promise<PaginatedResponse<Budget>> {
    return this.budgetsService.findAllPaginated(paginationDto);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Listar orçamentos de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de orçamentos do paciente retornada com sucesso',
    type: [Budget],
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  findByPatient(@Param('patientId') patientId: string): Promise<Budget[]> {
    return this.budgetsService.findByPatient(+patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um orçamento pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do orçamento' })
  @ApiResponse({
    status: 200,
    description: 'Orçamento encontrado',
    type: Budget,
  })
  @ApiResponse({ status: 404, description: 'Orçamento não encontrado' })
  findOne(@Param('id') id: string): Promise<Budget> {
    return this.budgetsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um orçamento' })
  @ApiParam({ name: 'id', description: 'ID do orçamento' })
  @ApiResponse({
    status: 200,
    description: 'Orçamento atualizado com sucesso',
    type: Budget,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Orçamento não encontrado' })
  update(
    @Param('id') id: string,
    @Body() updateBudgetDto: UpdateBudgetDto,
  ): Promise<Budget> {
    return this.budgetsService.update(+id, updateBudgetDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um orçamento' })
  @ApiParam({ name: 'id', description: 'ID do orçamento' })
  @ApiResponse({ status: 204, description: 'Orçamento removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Orçamento não encontrado' })
  remove(@Param('id') id: string): Promise<void> {
    return this.budgetsService.remove(+id);
  }
}
