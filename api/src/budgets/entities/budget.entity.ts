import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { Dentist } from '../../dentists/entities/dentist.entity';
import { BudgetItem } from './budget-item.entity';
import { TreatmentPlan } from '../../treatment-plans/entities/treatment-plan.entity';

export enum BudgetStatus {
  OPEN = 'open',
  APPROVED = 'approved',
  CANCELLED = 'cancelled',
}

export enum DiscountType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  NONE = 'none',
}

export enum PaymentMethod {
  TO_DEFINE = 'to_define',
  CASH = 'cash',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_SLIP = 'bank_slip',
  TRANSFER = 'transfer',
  PIX = 'pix',
  CHECK = 'check',
  MULTIPLE = 'multiple',
}

@Entity('budgets')
export class Budget {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do orçamento' })
  id: number;

  @ManyToOne(() => Patient, (patient) => patient.budgets, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'patientId' })
  patient: Patient;

  @Column()
  patientId: number;

  @ManyToOne(() => Dentist, (dentist) => dentist.budgets)
  @JoinColumn({ name: 'dentistId' })
  dentist: Dentist;

  @Column()
  dentistId: number;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Observações sobre o orçamento',
    required: false,
  })
  notes: string;

  @Column({
    type: 'enum',
    enum: BudgetStatus,
    default: BudgetStatus.OPEN,
  })
  @ApiProperty({
    description: 'Status do orçamento',
    enum: BudgetStatus,
    default: BudgetStatus.OPEN,
  })
  status: BudgetStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Valor total do orçamento' })
  totalValue: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Valor pago' })
  amountPaid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @ApiProperty({ description: 'Valor do desconto aplicado' })
  discount: number;

  @Column({
    type: 'enum',
    enum: DiscountType,
    default: DiscountType.NONE,
  })
  @ApiProperty({
    description: 'Tipo de desconto aplicado',
    enum: DiscountType,
    default: DiscountType.NONE,
  })
  discountType: DiscountType;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.TO_DEFINE,
  })
  @ApiProperty({
    description: 'Método de pagamento',
    enum: PaymentMethod,
    default: PaymentMethod.TO_DEFINE,
  })
  paymentMethod: PaymentMethod;

  @Column({ default: 1 })
  @ApiProperty({
    description: 'Número de parcelas para pagamento',
    default: 1,
    minimum: 1,
    maximum: 12,
  })
  installments: number;

  @OneToMany(() => BudgetItem, (budgetItem) => budgetItem.budget, {
    cascade: true,
  })
  items: BudgetItem[];

  @Column({ name: 'created_by', nullable: true })
  @ApiProperty({
    description: 'Usuário que criou o orçamento',
    required: false,
  })
  createdBy: string;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do orçamento' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do orçamento' })
  updatedAt: Date;

  @OneToMany(() => TreatmentPlan, (treatmentPlan) => treatmentPlan.budget)
  @ApiProperty({
    description: 'Planos de tratamento associados a este orçamento',
    type: () => [TreatmentPlan],
  })
  treatmentPlans: TreatmentPlan[];
}
