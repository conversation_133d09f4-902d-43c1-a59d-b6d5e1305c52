import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Budget } from './budget.entity';
import { Procedure } from '../../procedures/entities/procedure.entity';
import { Dentist } from '../../dentists/entities/dentist.entity';

@Entity('budget_items')
export class BudgetItem {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único do item do orçamento' })
  id: number;

  @ManyToOne(() => Budget, (budget) => budget.items, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'budgetId' })
  budget: Budget;

  @Column()
  budgetId: number;

  @ManyToOne(() => Procedure)
  @JoinColumn({ name: 'procedureId' })
  procedure: Procedure;

  @Column()
  procedureId: number;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Número do dente relacionado ao procedimento',
    required: false,
  })
  tooth: string;

  @ManyToOne(() => Dentist)
  @JoinColumn({ name: 'executingDentistId' })
  executingDentist: Dentist;

  @Column()
  executingDentistId: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @ApiProperty({ description: 'Valor do procedimento neste orçamento' })
  value: number;
}
