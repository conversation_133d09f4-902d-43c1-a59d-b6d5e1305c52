import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { BudgetStatus } from '../entities/budget.entity';

export class BudgetPaginationDto {
  @ApiProperty({
    description: 'Página atual (começando em 1)',
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(String(value)))
  page?: number = 1;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(String(value)))
  limit?: number = 10;

  @ApiProperty({
    description: 'Termo de busca para filtrar orçamentos',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filtrar por status do orçamento',
    required: false,
    enum: BudgetStatus,
  })
  @IsOptional()
  @IsEnum(BudgetStatus)
  status?: BudgetStatus;

  @ApiProperty({
    description: 'Filtrar por ID do paciente',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(String(value)))
  patientId?: number;

  @ApiProperty({
    description: 'Filtrar por ID do dentista',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(String(value)))
  dentistId?: number;
}
