import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  BudgetStatus,
  DiscountType,
  PaymentMethod,
} from '../entities/budget.entity';

export class CreateBudgetItemDto {
  @ApiProperty({ description: 'ID do procedimento' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do procedimento é obrigatório' })
  procedureId: number;

  @ApiProperty({
    description: 'Número do dente relacionado ao procedimento',
    required: false,
  })
  @IsOptional()
  @IsString()
  tooth?: string;

  @ApiProperty({ description: 'ID do dentista que executará o procedimento' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do dentista é obrigatório' })
  executingDentistId: number;

  @ApiProperty({ description: 'Valor do procedimento neste orçamento' })
  @IsNumber()
  @Min(0, { message: 'O valor não pode ser negativo' })
  @IsNotEmpty({ message: 'O valor é obrigatório' })
  value: number;
}

export class CreateBudgetDto {
  @ApiProperty({ description: 'ID do paciente' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  patientId: number;

  @ApiProperty({ description: 'ID do dentista responsável pelo orçamento' })
  @IsNumber()
  @IsNotEmpty({ message: 'O ID do dentista é obrigatório' })
  dentistId: number;

  @ApiProperty({
    description: 'Observações sobre o orçamento',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Status do orçamento',
    enum: BudgetStatus,
    default: BudgetStatus.OPEN,
  })
  @IsOptional()
  @IsEnum(BudgetStatus)
  status?: BudgetStatus;

  @ApiProperty({ description: 'Valor total do orçamento' })
  @IsNumber()
  @Min(0, { message: 'O valor total não pode ser negativo' })
  totalValue: number;

  @ApiProperty({ description: 'Valor pago' })
  @IsNumber()
  @Min(0, { message: 'O valor total não pode ser negativo' })
  amountPaid: number;

  @ApiProperty({ description: 'Valor do desconto aplicado', default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'O desconto não pode ser negativo' })
  discount?: number = 0;

  @ApiProperty({
    description: 'Tipo de desconto aplicado',
    enum: DiscountType,
    default: DiscountType.NONE,
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType = DiscountType.NONE;

  @ApiProperty({
    description: 'Método de pagamento',
    enum: PaymentMethod,
    default: PaymentMethod.TO_DEFINE,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod = PaymentMethod.TO_DEFINE;

  @ApiProperty({
    description: 'Número de parcelas para pagamento',
    default: 1,
    minimum: 1,
    maximum: 12,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'O número de parcelas deve ser pelo menos 1' })
  @Max(12, { message: 'O número máximo de parcelas é 12' })
  installments?: number = 1;

  @ApiProperty({
    description: 'Itens do orçamento',
    type: [CreateBudgetItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBudgetItemDto)
  items: CreateBudgetItemDto[];

  @ApiProperty({
    description: 'Usuário que criou o orçamento',
    required: false,
  })
  @IsOptional()
  @IsString()
  createdBy?: string;
}
