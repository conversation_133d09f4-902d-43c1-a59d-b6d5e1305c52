import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateBudgetDto } from './create-budget.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { BudgetStatus } from '../entities/budget.entity';

export class UpdateBudgetDto extends PartialType(CreateBudgetDto) {
  @ApiProperty({
    description: 'Status do orçamento',
    enum: BudgetStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(BudgetStatus)
  status?: BudgetStatus;
}
