import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, IsString, Min } from 'class-validator';

export class CreateTreatmentFlowStepDto {
  @ApiProperty({ description: 'ID do procedimento de origem' })
  @IsNotEmpty({ message: 'O procedimento de origem é obrigatório' })
  @IsInt({
    message: 'O ID do procedimento de origem deve ser um número inteiro',
  })
  fromProcedureId: number;

  @ApiProperty({ description: 'ID do procedimento de destino' })
  @IsNotEmpty({ message: 'O procedimento de destino é obrigatório' })
  @IsInt({
    message: 'O ID do procedimento de destino deve ser um número inteiro',
  })
  toProcedureId: number;

  @ApiProperty({
    description: 'Número mínimo de dias após o procedimento de origem',
  })
  @IsNotEmpty({ message: 'O número mínimo de dias é obrigatório' })
  @IsInt({ message: 'O número mínimo de dias deve ser um número inteiro' })
  @Min(0, { message: 'O número mínimo de dias não pode ser negativo' })
  minDaysAfter: number;

  @ApiProperty({
    description: 'Observações sobre o passo do fluxo',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'As observações devem ser um texto' })
  notes?: string;
}
