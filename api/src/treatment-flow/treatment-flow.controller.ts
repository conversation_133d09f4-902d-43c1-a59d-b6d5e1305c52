import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { TreatmentFlowService } from './treatment-flow.service';
import { CreateTreatmentFlowStepDto } from './dto/create-treatment-flow-step.dto';
import { UpdateTreatmentFlowStepDto } from './dto/update-treatment-flow-step.dto';
import { TreatmentFlowStep } from './entities/treatment-flow-step.entity';

@ApiTags('treatment-flow')
@Controller('treatment-flow')
export class TreatmentFlowController {
  constructor(private readonly treatmentFlowService: TreatmentFlowService) {}

  @ApiOperation({ summary: 'Criar um novo passo do fluxo de tratamento' })
  @ApiResponse({
    status: 201,
    description: 'Passo do fluxo criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateTreatmentFlowStepDto })
  @Post()
  create(
    @Body() createTreatmentFlowStepDto: CreateTreatmentFlowStepDto,
  ): Promise<TreatmentFlowStep> {
    return this.treatmentFlowService.create(createTreatmentFlowStepDto);
  }

  @ApiOperation({ summary: 'Listar todos os passos do fluxo de tratamento' })
  @ApiResponse({
    status: 200,
    description: 'Lista de passos do fluxo retornada com sucesso',
  })
  @Get()
  findAll(): Promise<TreatmentFlowStep[]> {
    return this.treatmentFlowService.findAll();
  }

  @ApiOperation({ summary: 'Buscar um passo do fluxo de tratamento pelo ID' })
  @ApiResponse({
    status: 200,
    description: 'Passo do fluxo encontrado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Passo do fluxo não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do passo do fluxo' })
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<TreatmentFlowStep> {
    return this.treatmentFlowService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar um passo do fluxo de tratamento' })
  @ApiResponse({
    status: 200,
    description: 'Passo do fluxo atualizado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Passo do fluxo não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiParam({ name: 'id', description: 'ID do passo do fluxo' })
  @ApiBody({ type: UpdateTreatmentFlowStepDto })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTreatmentFlowStepDto: UpdateTreatmentFlowStepDto,
  ): Promise<TreatmentFlowStep> {
    return this.treatmentFlowService.update(id, updateTreatmentFlowStepDto);
  }

  @ApiOperation({ summary: 'Remover um passo do fluxo de tratamento' })
  @ApiResponse({
    status: 200,
    description: 'Passo do fluxo removido com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Passo do fluxo não encontrado' })
  @ApiParam({ name: 'id', description: 'ID do passo do fluxo' })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.treatmentFlowService.remove(id);
  }
}
