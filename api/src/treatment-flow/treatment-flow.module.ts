import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TreatmentFlowService } from './treatment-flow.service';
import { TreatmentFlowController } from './treatment-flow.controller';
import { TreatmentFlowStep } from './entities/treatment-flow-step.entity';
import { ProceduresModule } from '../procedures/procedures.module';

@Module({
  imports: [TypeOrmModule.forFeature([TreatmentFlowStep]), ProceduresModule],
  controllers: [TreatmentFlowController],
  providers: [TreatmentFlowService],
  exports: [TreatmentFlowService],
})
export class TreatmentFlowModule {}
