import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Procedure } from '../../procedures/entities/procedure.entity';

@Entity('treatment_flow_step')
export class TreatmentFlowStep {
  @PrimaryGeneratedColumn('increment')
  @ApiProperty({ description: 'ID único do passo do fluxo de tratamento' })
  id: number;

  @Column({ name: 'from_procedure_id' })
  @ApiProperty({ description: 'ID do procedimento de origem' })
  fromProcedureId: number;

  @ManyToOne(() => Procedure)
  @JoinColumn({ name: 'from_procedure_id' })
  @ApiProperty({ description: 'Procedimento de origem', type: () => Procedure })
  fromProcedure: Procedure;

  @Column({ name: 'to_procedure_id' })
  @ApiProperty({ description: 'ID do procedimento de destino' })
  toProcedureId: number;

  @ManyToOne(() => Procedure)
  @JoinColumn({ name: 'to_procedure_id' })
  @ApiProperty({
    description: 'Procedimento de destino',
    type: () => Procedure,
  })
  toProcedure: Procedure;

  @Column({ type: 'int' })
  @ApiProperty({
    description: 'Número mínimo de dias após o procedimento de origem',
  })
  minDaysAfter: number;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Observações sobre o passo do fluxo',
    required: false,
  })
  notes: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
