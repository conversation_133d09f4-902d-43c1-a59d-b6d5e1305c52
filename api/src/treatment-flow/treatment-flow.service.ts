import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TreatmentFlowStep } from './entities/treatment-flow-step.entity';
import { CreateTreatmentFlowStepDto } from './dto/create-treatment-flow-step.dto';
import { UpdateTreatmentFlowStepDto } from './dto/update-treatment-flow-step.dto';
import { ProceduresService } from '../procedures/procedures.service';

@Injectable()
export class TreatmentFlowService {
  private readonly logger = new Logger(TreatmentFlowService.name);

  constructor(
    @InjectRepository(TreatmentFlowStep)
    private treatmentFlowStepRepository: Repository<TreatmentFlowStep>,
    private proceduresService: ProceduresService,
  ) {}

  async create(
    createTreatmentFlowStepDto: CreateTreatmentFlowStepDto,
  ): Promise<TreatmentFlowStep> {
    try {
      this.logger.log(
        `Creating treatment flow step: ${JSON.stringify(createTreatmentFlowStepDto)}`,
      );

      // Verificar se os procedimentos existem
      await this.validateProcedures(
        createTreatmentFlowStepDto.fromProcedureId,
        createTreatmentFlowStepDto.toProcedureId,
      );

      // Verificar se já existe um fluxo com os mesmos procedimentos
      const existingFlow = await this.treatmentFlowStepRepository
        .createQueryBuilder('flowStep')
        .where('flowStep.fromProcedureId = :fromProcedureId', {
          fromProcedureId: createTreatmentFlowStepDto.fromProcedureId,
        })
        .andWhere('flowStep.toProcedureId = :toProcedureId', {
          toProcedureId: createTreatmentFlowStepDto.toProcedureId,
        })
        .getOne();

      if (existingFlow) {
        throw new BadRequestException(
          'Já existe um fluxo configurado entre esses procedimentos',
        );
      }

      // Criar a entidade
      const treatmentFlowStep = this.treatmentFlowStepRepository.create(
        createTreatmentFlowStepDto,
      );

      // Salvar no banco de dados
      const savedStep =
        await this.treatmentFlowStepRepository.save(treatmentFlowStep);

      // Buscar novamente com as relações para garantir consistência
      const result = await this.treatmentFlowStepRepository.findOne({
        where: { id: savedStep.id },
        relations: ['fromProcedure', 'toProcedure'],
      });

      if (!result) {
        throw new NotFoundException(
          `Etapa de fluxo com ID ${savedStep.id} não encontrada após criação`,
        );
      }

      return result;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      // Log detalhado para facilitar a depuração
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error creating treatment flow step: ${errorMessage}`,
        errorStack,
      );
      this.logger.error(
        `Create data: ${JSON.stringify(createTreatmentFlowStepDto)}`,
      );

      // Verificar se é um erro de SQL e fornecer mais detalhes
      const errorCode = (error as { code?: string }).code;
      const sqlMessage = (error as { sqlMessage?: string }).sqlMessage;

      if (
        errorCode &&
        (errorCode.startsWith('ER_') || errorCode.includes('SQL'))
      ) {
        this.logger.error(
          `SQL Error: ${errorCode} - ${sqlMessage || errorMessage}`,
        );
        throw new InternalServerErrorException(
          `Erro de banco de dados ao criar passo do fluxo de tratamento: ${errorCode}`,
        );
      }

      throw new InternalServerErrorException(
        'Erro ao criar passo do fluxo de tratamento',
      );
    }
  }

  async findAll(): Promise<TreatmentFlowStep[]> {
    try {
      return await this.treatmentFlowStepRepository.find({
        relations: ['fromProcedure', 'toProcedure'],
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding treatment flow steps: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar passos do fluxo de tratamento',
      );
    }
  }

  async findOne(id: number): Promise<TreatmentFlowStep> {
    try {
      const treatmentFlowStep = await this.treatmentFlowStepRepository.findOne({
        where: { id },
        relations: ['fromProcedure', 'toProcedure'],
      });

      if (!treatmentFlowStep) {
        throw new NotFoundException(
          `Passo do fluxo de tratamento com ID ${id} não encontrado`,
        );
      }

      return treatmentFlowStep;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Log detalhado para facilitar a depuração
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error finding treatment flow step ID ${id}: ${errorMessage}`,
        errorStack,
      );

      // Verificar se é um erro de SQL e fornecer mais detalhes
      const errorCode = (error as { code?: string }).code;
      const sqlMessage = (error as { sqlMessage?: string }).sqlMessage;

      if (
        errorCode &&
        (errorCode.startsWith('ER_') || errorCode.includes('SQL'))
      ) {
        this.logger.error(
          `SQL Error: ${errorCode} - ${sqlMessage || errorMessage}`,
        );
        throw new InternalServerErrorException(
          `Erro de banco de dados ao buscar passo do fluxo de tratamento: ${errorCode}`,
        );
      }

      throw new InternalServerErrorException(
        'Erro ao buscar passo do fluxo de tratamento',
      );
    }
  }

  async update(
    id: number,
    updateTreatmentFlowStepDto: UpdateTreatmentFlowStepDto,
  ): Promise<TreatmentFlowStep> {
    try {
      // Buscar a etapa do fluxo existente sem relações para evitar problemas de cache
      const existingStep = await this.treatmentFlowStepRepository.findOne({
        where: { id },
      });

      if (!existingStep) {
        throw new NotFoundException(
          `Etapa de fluxo com ID ${id} não encontrada`,
        );
      }

      // Determinar os IDs dos procedimentos a serem usados
      const fromProcedureId =
        updateTreatmentFlowStepDto.fromProcedureId !== undefined
          ? updateTreatmentFlowStepDto.fromProcedureId
          : existingStep.fromProcedureId;

      const toProcedureId =
        updateTreatmentFlowStepDto.toProcedureId !== undefined
          ? updateTreatmentFlowStepDto.toProcedureId
          : existingStep.toProcedureId;

      // Verificar se os procedimentos existem
      await this.validateProcedures(fromProcedureId, toProcedureId);

      // Verificar se já existe outro fluxo com os mesmos procedimentos
      const existingFlow = await this.treatmentFlowStepRepository
        .createQueryBuilder('flowStep')
        .where('flowStep.fromProcedureId = :fromProcedureId', {
          fromProcedureId,
        })
        .andWhere('flowStep.toProcedureId = :toProcedureId', { toProcedureId })
        .andWhere('flowStep.id != :id', { id })
        .getOne();

      if (existingFlow) {
        throw new BadRequestException(
          'Já existe um fluxo configurado entre esses procedimentos',
        );
      }

      // Determinar os valores a serem atualizados
      const minDaysAfter =
        updateTreatmentFlowStepDto.minDaysAfter !== undefined
          ? updateTreatmentFlowStepDto.minDaysAfter
          : existingStep.minDaysAfter;

      const notes =
        updateTreatmentFlowStepDto.notes !== undefined
          ? updateTreatmentFlowStepDto.notes
          : existingStep.notes;

      // Log dos valores que serão atualizados
      this.logger
        .log(`Atualizando etapa de fluxo ${id} com os seguintes valores:
        fromProcedureId: ${fromProcedureId} (original: ${existingStep.fromProcedureId})
        toProcedureId: ${toProcedureId} (original: ${existingStep.toProcedureId})
        minDaysAfter: ${minDaysAfter} (original: ${existingStep.minDaysAfter})
        notes: ${notes || '(vazio)'} (original: ${existingStep.notes || '(vazio)'})`);

      // Usar SQL nativo para garantir que os dados sejam atualizados corretamente
      await this.treatmentFlowStepRepository.query(
        `UPDATE treatment_flow_step
         SET from_procedure_id = ?,
             to_procedure_id = ?,
             minDaysAfter = ?,
             notes = ?,
             updatedAt = NOW()
         WHERE id = ?`,
        [fromProcedureId, toProcedureId, minDaysAfter, notes, id],
      );

      // Limpar o cache do EntityManager para garantir que as relações sejam recarregadas
      // Não precisamos limpar o cache, pois estamos usando SQL nativo

      // Buscar a etapa atualizada com suas relações
      const result = await this.treatmentFlowStepRepository
        .createQueryBuilder('flowStep')
        .leftJoinAndSelect('flowStep.fromProcedure', 'fromProcedure')
        .leftJoinAndSelect('flowStep.toProcedure', 'toProcedure')
        .where('flowStep.id = :id', { id })
        .getOne();

      if (!result) {
        throw new NotFoundException(
          `Etapa de fluxo com ID ${id} não encontrada após atualização`,
        );
      }

      // Verificar se as relações foram carregadas corretamente
      if (result.fromProcedureId !== result.fromProcedure?.id) {
        this.logger.warn(
          `Inconsistência detectada: fromProcedureId (${result.fromProcedureId}) != fromProcedure.id (${result.fromProcedure?.id})`,
        );
      }

      if (result.toProcedureId !== result.toProcedure?.id) {
        this.logger.warn(
          `Inconsistência detectada: toProcedureId (${result.toProcedureId}) != toProcedure.id (${result.toProcedure?.id})`,
        );
      }

      // Log para depuração
      this.logger.log(`Etapa de fluxo atualizada com sucesso:
        ID: ${result.id}
        fromProcedureId: ${result.fromProcedureId}
        toProcedureId: ${result.toProcedureId}
        fromProcedure.id: ${result.fromProcedure?.id}
        toProcedure.id: ${result.toProcedure?.id}
        minDaysAfter: ${result.minDaysAfter}
        notes: ${result.notes || '(vazio)'}`);

      return result;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      // Log detalhado para facilitar a depuração
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating treatment flow step ID ${id}: ${errorMessage}`,
        errorStack,
      );
      this.logger.error(
        `Update data: ${JSON.stringify(updateTreatmentFlowStepDto)}`,
      );

      // Verificar se é um erro de SQL e fornecer mais detalhes
      const errorCode = (error as { code?: string }).code;
      const sqlMessage = (error as { sqlMessage?: string }).sqlMessage;

      if (
        errorCode &&
        (errorCode.startsWith('ER_') || errorCode.includes('SQL'))
      ) {
        this.logger.error(
          `SQL Error: ${errorCode} - ${sqlMessage || errorMessage}`,
        );
        throw new InternalServerErrorException(
          `Erro de banco de dados ao atualizar passo do fluxo de tratamento: ${errorCode}`,
        );
      }

      // Tentar uma abordagem alternativa se a primeira falhar
      try {
        this.logger.log('Tentando abordagem alternativa para atualização...');

        // Primeiro, buscar a etapa atual novamente para garantir que temos os dados corretos
        const currentStep = await this.treatmentFlowStepRepository.findOne({
          where: { id },
        });

        if (!currentStep) {
          throw new NotFoundException(
            `Etapa de fluxo com ID ${id} não encontrada`,
          );
        }

        // Atualizar diretamente no banco de dados usando SQL nativo
        await this.treatmentFlowStepRepository.query(
          `UPDATE treatment_flow_step
           SET from_procedure_id = ?, to_procedure_id = ?, minDaysAfter = ?, notes = ?, updatedAt = NOW()
           WHERE id = ?`,
          [
            updateTreatmentFlowStepDto.fromProcedureId ||
              currentStep.fromProcedureId,
            updateTreatmentFlowStepDto.toProcedureId ||
              currentStep.toProcedureId,
            updateTreatmentFlowStepDto.minDaysAfter !== undefined
              ? updateTreatmentFlowStepDto.minDaysAfter
              : currentStep.minDaysAfter,
            updateTreatmentFlowStepDto.notes !== undefined
              ? updateTreatmentFlowStepDto.notes
              : currentStep.notes,
            id,
          ],
        );

        // Buscar a etapa atualizada
        const updatedStep = await this.treatmentFlowStepRepository
          .createQueryBuilder('flowStep')
          .leftJoinAndSelect('flowStep.fromProcedure', 'fromProcedure')
          .leftJoinAndSelect('flowStep.toProcedure', 'toProcedure')
          .where('flowStep.id = :id', { id })
          .getOne();

        if (!updatedStep) {
          throw new NotFoundException(
            `Etapa de fluxo com ID ${id} não encontrada após atualização alternativa`,
          );
        }

        this.logger.log('Atualização alternativa bem-sucedida');
        return updatedStep;
      } catch (fallbackError: unknown) {
        const fallbackErrorMessage =
          fallbackError instanceof Error
            ? fallbackError.message
            : 'Erro desconhecido';
        const fallbackErrorStack =
          fallbackError instanceof Error ? fallbackError.stack : undefined;
        this.logger.error(
          `Fallback approach also failed: ${fallbackErrorMessage}`,
          fallbackErrorStack,
        );
        throw new InternalServerErrorException(
          'Erro ao atualizar passo do fluxo de tratamento. Todas as tentativas falharam.',
        );
      }
    }
  }

  async remove(id: number): Promise<void> {
    try {
      const treatmentFlowStep = await this.findOne(id);
      await this.treatmentFlowStepRepository.remove(treatmentFlowStep);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Log detalhado para facilitar a depuração
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error removing treatment flow step ID ${id}: ${errorMessage}`,
        errorStack,
      );

      // Verificar se é um erro de SQL e fornecer mais detalhes
      const errorCode = (error as { code?: string }).code;
      const sqlMessage = (error as { sqlMessage?: string }).sqlMessage;

      if (
        errorCode &&
        (errorCode.startsWith('ER_') || errorCode.includes('SQL'))
      ) {
        this.logger.error(
          `SQL Error: ${errorCode} - ${sqlMessage || errorMessage}`,
        );
        throw new InternalServerErrorException(
          `Erro de banco de dados ao remover passo do fluxo de tratamento: ${errorCode}`,
        );
      }

      throw new InternalServerErrorException(
        'Erro ao remover passo do fluxo de tratamento',
      );
    }
  }

  private async validateProcedures(
    fromProcedureId: number,
    toProcedureId: number,
  ): Promise<void> {
    try {
      // Verificar se o procedimento de origem existe
      await this.proceduresService.findOne(fromProcedureId);

      // Verificar se o procedimento de destino existe
      await this.proceduresService.findOne(toProcedureId);

      // Verificar se os procedimentos são diferentes
      if (fromProcedureId === toProcedureId) {
        throw new BadRequestException(
          'Os procedimentos de origem e destino não podem ser iguais',
        );
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(
          `Procedimento com ID ${error.message.includes(fromProcedureId.toString()) ? fromProcedureId : toProcedureId} não encontrado`,
        );
      }
      throw error;
    }
  }
}
