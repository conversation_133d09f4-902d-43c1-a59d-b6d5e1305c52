import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppointmentObservation } from './entities/appointment-observation.entity';
import { CreateAppointmentObservationDto } from './dto/create-appointment-observation.dto';

@Injectable()
export class AppointmentObservationsService {
  private readonly logger = new Logger(AppointmentObservationsService.name);

  constructor(
    @InjectRepository(AppointmentObservation)
    private appointmentObservationsRepository: Repository<AppointmentObservation>,
  ) {}

  async create(
    createAppointmentObservationDto: CreateAppointmentObservationDto,
  ): Promise<AppointmentObservation> {
    this.logger.log(
      `Creating appointment observation for appointment ${createAppointmentObservationDto.appointmentId}`,
    );

    const observation = this.appointmentObservationsRepository.create({
      appointment: { id: createAppointmentObservationDto.appointmentId } as {
        id: number;
      },
      patient: { id: createAppointmentObservationDto.patientId } as {
        id: number;
      },
      note: createAppointmentObservationDto.note,
    });

    return this.appointmentObservationsRepository.save(observation);
  }

  async findByPatient(patientId: number): Promise<AppointmentObservation[]> {
    this.logger.log(
      `Finding appointment observations for patient ${patientId}`,
    );

    return this.appointmentObservationsRepository
      .createQueryBuilder('observation')
      .leftJoinAndSelect('observation.appointment', 'appointment')
      .leftJoinAndSelect('observation.patient', 'patient')
      .where('patient.id = :patientId', { patientId })
      .orderBy('observation.createdAt', 'DESC')
      .getMany();
  }

  async createFromSchedulingNote(
    appointmentId: number,
    patientId: number,
    note: string,
  ): Promise<AppointmentObservation | null> {
    // Só criar observação se a nota não estiver vazia
    if (!note || note.trim() === '') {
      return null;
    }

    this.logger.log(
      `Creating observation from scheduling note for appointment ${appointmentId}`,
    );

    const observation = this.appointmentObservationsRepository.create({
      appointment: { id: appointmentId } as { id: number },
      patient: { id: patientId } as { id: number },
      note: note.trim(),
    });

    return this.appointmentObservationsRepository.save(observation);
  }
}
