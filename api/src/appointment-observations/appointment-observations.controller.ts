import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { AppointmentObservationsService } from './appointment-observations.service';
import { CreateAppointmentObservationDto } from './dto/create-appointment-observation.dto';
import { AppointmentObservation } from './entities/appointment-observation.entity';

@ApiTags('appointment-observations')
@Controller('appointment-observations')
export class AppointmentObservationsController {
  constructor(
    private readonly appointmentObservationsService: AppointmentObservationsService,
  ) {}

  @ApiOperation({ summary: 'Criar uma nova observação de agendamento' })
  @ApiResponse({
    status: 201,
    description: 'Observação criada com sucesso',
    type: AppointmentObservation,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiBody({ type: CreateAppointmentObservationDto })
  @Post()
  create(
    @Body() createAppointmentObservationDto: CreateAppointmentObservationDto,
  ): Promise<AppointmentObservation> {
    return this.appointmentObservationsService.create(
      createAppointmentObservationDto,
    );
  }
}

@ApiTags('patients')
@Controller('patients')
export class PatientsAppointmentObservationsController {
  constructor(
    private readonly appointmentObservationsService: AppointmentObservationsService,
  ) {}

  @ApiOperation({
    summary: 'Buscar observações de agendamentos de um paciente',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de observações retornada com sucesso',
    type: [AppointmentObservation],
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente', example: 1 })
  @Get(':patientId/appointment-observations')
  findByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<AppointmentObservation[]> {
    return this.appointmentObservationsService.findByPatient(patientId);
  }
}
