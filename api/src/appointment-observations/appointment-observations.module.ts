import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppointmentObservationsService } from './appointment-observations.service';
import {
  AppointmentObservationsController,
  PatientsAppointmentObservationsController,
} from './appointment-observations.controller';
import { AppointmentObservation } from './entities/appointment-observation.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AppointmentObservation])],
  controllers: [
    AppointmentObservationsController,
    PatientsAppointmentObservationsController,
  ],
  providers: [AppointmentObservationsService],
  exports: [AppointmentObservationsService],
})
export class AppointmentObservationsModule {}
