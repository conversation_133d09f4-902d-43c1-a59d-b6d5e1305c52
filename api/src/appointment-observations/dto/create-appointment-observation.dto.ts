import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAppointmentObservationDto {
  @ApiProperty({ example: 1, description: 'ID do agendamento' })
  @IsNotEmpty()
  @IsNumber()
  appointmentId: number;

  @ApiProperty({ example: 1, description: 'ID do paciente' })
  @IsNotEmpty()
  @IsNumber()
  patientId: number;

  @ApiProperty({
    example: 'Paciente chegou no horário e relatou dor no dente 16',
    description: 'Texto da observação',
  })
  @IsNotEmpty()
  @IsString()
  note: string;
}
