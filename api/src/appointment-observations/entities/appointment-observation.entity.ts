import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { Scheduling } from '../../schedulings/entities/scheduling.entity';

@Entity('appointment_observations')
export class AppointmentObservation {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da observação' })
  id: string;

  @ManyToOne(() => Scheduling, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'appointmentId' })
  @ApiProperty({ description: 'Agendamento associado à observação' })
  appointment: Scheduling;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado à observação' })
  patient: Patient;

  @Column({ type: 'text' })
  @ApiProperty({ description: 'Texto da observação' })
  note: string;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data e hora da criação da observação' })
  createdAt: Date;
}
