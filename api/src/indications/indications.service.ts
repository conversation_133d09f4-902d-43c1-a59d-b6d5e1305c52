import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Indication } from './entities/indication.entity';
import { CreateIndicationDto } from './dto/create-indication.dto';
import { UpdateIndicationDto } from './dto/update-indication.dto';
import { IndicationPaginationDto } from './dto/indication-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { Patient } from '../patients/entities/patient.entity';
import { IndicationStatus } from './enums/indication-status.enum';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class IndicationsService {
  private readonly logger = new Logger(IndicationsService.name);

  constructor(
    @InjectRepository(Indication)
    private indicationsRepository: Repository<Indication>,
    @InjectRepository(Patient)
    private patientsRepository: Repository<Patient>,
  ) {}

  async create(createIndicationDto: CreateIndicationDto): Promise<Indication> {
    try {
      this.logger.log(
        `Creating indication: ${JSON.stringify(createIndicationDto)}`,
      );

      // Verificar se o paciente indicado existe
      const indicatedPatient = await this.patientsRepository.findOne({
        where: { id: createIndicationDto.indicatedPatientId },
      });

      if (!indicatedPatient) {
        throw new NotFoundException(
          `Paciente indicado com ID ${createIndicationDto.indicatedPatientId} não encontrado`,
        );
      }

      // Verificar se o paciente que indicou existe (se fornecido)
      let referredBy: Patient | null = null;
      if (createIndicationDto.referredById) {
        const foundPatient = await this.patientsRepository.findOne({
          where: { id: createIndicationDto.referredById },
        });

        if (!foundPatient) {
          throw new NotFoundException(
            `Paciente que indicou com ID ${createIndicationDto.referredById} não encontrado`,
          );
        }

        referredBy = foundPatient;
      }

      // Criar a indicação
      const indication = this.indicationsRepository.create({
        date: new Date(createIndicationDto.date),
        status: createIndicationDto.status,
        observation: createIndicationDto.observation,
        tags: createIndicationDto.tags,
        indicatedPatient,
        referredBy,
      });

      return await this.indicationsRepository.save(indication);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating indication: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  async findAll(
    paginationDto: IndicationPaginationDto,
  ): Promise<PaginatedResponse<Indication>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        indicatedPatientId,
        referredById,
        tag,
      } = paginationDto;
      this.logger.log(
        `Finding indications with pagination: page ${page}, limit ${limit}, search: ${search}, status: ${status}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder = this.indicationsRepository
        .createQueryBuilder('indication')
        .leftJoinAndSelect('indication.indicatedPatient', 'indicatedPatient')
        .leftJoinAndSelect('indication.referredBy', 'referredBy');

      // Aplicar filtro de busca
      if (search) {
        queryBuilder.andWhere(
          '(indicatedPatient.name LIKE :search OR referredBy.name LIKE :search OR indication.observation LIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Filtrar por status
      if (status) {
        queryBuilder.andWhere('indication.status = :status', { status });
      }

      // Filtrar por paciente indicado
      if (indicatedPatientId) {
        queryBuilder.andWhere(
          'indication.indicatedPatientId = :indicatedPatientId',
          { indicatedPatientId },
        );
      }

      // Filtrar por paciente que indicou
      if (referredById) {
        queryBuilder.andWhere('indication.referredById = :referredById', {
          referredById,
        });
      }

      // Filtrar por tag
      if (tag) {
        queryBuilder.andWhere('indication.tags LIKE :tag', { tag: `%${tag}%` });
      }

      // Ordenar por data de criação (mais recentes primeiro)
      queryBuilder.orderBy('indication.createdAt', 'DESC');

      // Executar a query com paginação
      const [indications, total] = await queryBuilder
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      return {
        data: indications,
        total,
        page,
        limit,
      };
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding indications: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  async findOne(id: number): Promise<Indication> {
    try {
      this.logger.log(`Finding indication with id: ${id}`);

      const indication = await this.indicationsRepository.findOne({
        where: { id },
        relations: ['indicatedPatient', 'referredBy'],
      });

      if (!indication) {
        throw new NotFoundException(`Indicação com ID ${id} não encontrada`);
      }

      return indication;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding indication: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  async findByPatient(patientId: number): Promise<Indication[]> {
    try {
      this.logger.log(`Finding indications for patient with id: ${patientId}`);

      // Verificar se o paciente existe
      const patient = await this.patientsRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar indicações onde o paciente foi indicado ou indicou alguém
      const indications = await this.indicationsRepository
        .createQueryBuilder('indication')
        .leftJoinAndSelect('indication.indicatedPatient', 'indicatedPatient')
        .leftJoinAndSelect('indication.referredBy', 'referredBy')
        .where('indication.indicatedPatientId = :patientId', { patientId })
        .orWhere('indication.referredById = :patientId', { patientId })
        .orderBy('indication.createdAt', 'DESC')
        .getMany();

      return indications;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding indications by patient: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  async update(
    id: number,
    updateIndicationDto: UpdateIndicationDto,
  ): Promise<Indication> {
    try {
      this.logger.log(`Updating indication with id: ${id}`);

      // Verificar se a indicação existe
      const indication = await this.findOne(id);

      // Atualizar os campos
      if (updateIndicationDto.date) {
        indication.date = new Date(updateIndicationDto.date);
      }

      if (updateIndicationDto.status) {
        indication.status = updateIndicationDto.status;
      }

      if (updateIndicationDto.observation !== undefined) {
        indication.observation = updateIndicationDto.observation;
      }

      if (updateIndicationDto.tags !== undefined) {
        indication.tags = updateIndicationDto.tags;
      }

      // Atualizar relacionamentos se necessário
      if (updateIndicationDto.indicatedPatientId) {
        const indicatedPatient = await this.patientsRepository.findOne({
          where: { id: updateIndicationDto.indicatedPatientId },
        });

        if (!indicatedPatient) {
          throw new NotFoundException(
            `Paciente indicado com ID ${updateIndicationDto.indicatedPatientId} não encontrado`,
          );
        }

        indication.indicatedPatient = indicatedPatient;
      }

      if (updateIndicationDto.referredById !== undefined) {
        if (updateIndicationDto.referredById === null) {
          indication.referredBy = null;
        } else {
          const foundPatient = await this.patientsRepository.findOne({
            where: { id: updateIndicationDto.referredById },
          });

          if (!foundPatient) {
            throw new NotFoundException(
              `Paciente que indicou com ID ${updateIndicationDto.referredById} não encontrado`,
            );
          }

          indication.referredBy = foundPatient;
        }
      }

      return await this.indicationsRepository.save(indication);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating indication: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  async createFromPatientRegistration(
    indicatedPatientId: number,
    referredById: number,
  ): Promise<Indication> {
    try {
      this.logger.log(
        `Creating indication from patient registration: indicatedPatientId=${indicatedPatientId}, referredById=${referredById}`,
      );

      // Verificar se os pacientes existem
      const indicatedPatient = await this.patientsRepository.findOne({
        where: { id: indicatedPatientId },
      });

      if (!indicatedPatient) {
        throw new NotFoundException(
          `Paciente indicado com ID ${indicatedPatientId} não encontrado`,
        );
      }

      const foundReferredBy = await this.patientsRepository.findOne({
        where: { id: referredById },
      });

      if (!foundReferredBy) {
        throw new NotFoundException(
          `Paciente que indicou com ID ${referredById} não encontrado`,
        );
      }

      // Criar a indicação com valores padrão
      const indication = this.indicationsRepository.create({
        date: new Date(),
        status: IndicationStatus.CONFIRMED, // Alterado para CONFIRMED
        observation: 'Indicação confirmada durante o cadastro do paciente',
        tags: ['cadastro'],
        indicatedPatient,
        referredBy: foundReferredBy,
      });

      return await this.indicationsRepository.save(indication);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating indication from patient registration: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }
}
