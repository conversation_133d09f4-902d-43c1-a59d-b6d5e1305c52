import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { IndicationsService } from './indications.service';
import { CreateIndicationDto } from './dto/create-indication.dto';
import { UpdateIndicationDto } from './dto/update-indication.dto';
import { Indication } from './entities/indication.entity';
import { IndicationPaginationDto } from './dto/indication-pagination.dto';
import { PaginatedResponse } from '../common/dto/pagination.dto';

@ApiTags('indications')
@Controller('indications')
export class IndicationsController {
  constructor(private readonly indicationsService: IndicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar uma nova indicação' })
  @ApiResponse({
    status: 201,
    description: 'Indicação criada com sucesso',
    type: Indication,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createIndicationDto: CreateIndicationDto,
  ): Promise<Indication> {
    return this.indicationsService.create(createIndicationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas as indicações' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca para filtrar indicações',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status da indicação',
    enum: ['pending', 'confirmed', 'rejected'],
  })
  @ApiQuery({
    name: 'indicatedPatientId',
    required: false,
    description: 'Filtrar por ID do paciente indicado',
    type: Number,
  })
  @ApiQuery({
    name: 'referredById',
    required: false,
    description: 'Filtrar por ID do paciente que fez a indicação',
    type: Number,
  })
  @ApiQuery({
    name: 'tag',
    required: false,
    description: 'Filtrar por etiqueta',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de indicações retornada com sucesso',
  })
  findAll(
    @Query() paginationDto: IndicationPaginationDto,
  ): Promise<PaginatedResponse<Indication>> {
    return this.indicationsService.findAll(paginationDto);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Listar indicações de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Lista de indicações do paciente retornada com sucesso',
    type: [Indication],
  })
  @ApiResponse({ status: 404, description: 'Paciente não encontrado' })
  findByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<Indication[]> {
    return this.indicationsService.findByPatient(patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma indicação pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da indicação' })
  @ApiResponse({
    status: 200,
    description: 'Indicação encontrada',
    type: Indication,
  })
  @ApiResponse({ status: 404, description: 'Indicação não encontrada' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Indication> {
    return this.indicationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar uma indicação' })
  @ApiParam({ name: 'id', description: 'ID da indicação' })
  @ApiResponse({
    status: 200,
    description: 'Indicação atualizada com sucesso',
    type: Indication,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Indicação não encontrada' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateIndicationDto: UpdateIndicationDto,
  ): Promise<Indication> {
    return this.indicationsService.update(id, updateIndicationDto);
  }
}
