import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { IndicationStatus } from '../enums/indication-status.enum';

@Entity('indications')
export class Indication {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: 'ID único da indicação' })
  id: number;

  @Column({ type: 'date' })
  @ApiProperty({ description: 'Data da indicação' })
  date: Date;

  @Column({
    type: 'enum',
    enum: IndicationStatus,
    default: IndicationStatus.PENDING,
  })
  @ApiProperty({
    description: 'Status da indicação',
    enum: IndicationStatus,
    default: IndicationStatus.PENDING,
  })
  status: IndicationStatus;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Observações sobre a indicação',
    required: false,
  })
  observation: string;

  @Column('simple-array', { nullable: true })
  @ApiProperty({
    description: 'Etiquetas associadas à indicação',
    required: false,
    type: [String],
  })
  tags: string[];

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'indicatedPatientId' })
  @ApiProperty({ description: 'Paciente que foi indicado' })
  indicatedPatient: Patient;

  @ManyToOne(() => Patient, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'referredById' })
  @ApiProperty({ description: 'Paciente que fez a indicação' })
  referredBy: Patient | null;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
