import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IndicationsService } from './indications.service';
import { IndicationsController } from './indications.controller';
import { Indication } from './entities/indication.entity';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsModule } from '../patients/patients.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Indication, Patient]),
    forwardRef(() => PatientsModule),
  ],
  controllers: [IndicationsController],
  providers: [IndicationsService],
  exports: [IndicationsService],
})
export class IndicationsModule {}
