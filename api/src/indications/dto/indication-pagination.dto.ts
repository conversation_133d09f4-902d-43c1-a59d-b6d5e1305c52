import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNumber, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { IndicationStatus } from '../enums/indication-status.enum';

export class IndicationPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Filtrar por status da indicação',
    enum: IndicationStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(IndicationStatus)
  status?: IndicationStatus;

  @ApiProperty({
    description: 'Filtrar por ID do paciente indicado',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  indicatedPatientId?: number;

  @ApiProperty({
    description: 'Filtrar por ID do paciente que fez a indicação',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  referredById?: number;

  @ApiProperty({
    description: 'Filtrar por etiqueta',
    required: false,
  })
  @IsOptional()
  @IsString()
  tag?: string;
}
