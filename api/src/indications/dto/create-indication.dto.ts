import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsEnum,
  IsString,
  IsOptional,
  IsArray,
  IsDateString,
} from 'class-validator';
import { IndicationStatus } from '../enums/indication-status.enum';

export class CreateIndicationDto {
  @ApiProperty({ description: 'Data da indicação' })
  @IsNotEmpty()
  @IsDateString()
  date: string;

  @ApiProperty({
    description: 'Status da indicação',
    enum: IndicationStatus,
    default: IndicationStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(IndicationStatus)
  status?: IndicationStatus;

  @ApiProperty({
    description: 'Observações sobre a indicação',
    required: false,
  })
  @IsOptional()
  @IsString()
  observation?: string;

  @ApiProperty({
    description: 'Etiquetas associadas à indicação',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({ description: 'ID do paciente que foi indicado' })
  @IsNotEmpty()
  @IsNumber()
  indicatedPatientId: number;

  @ApiProperty({
    description: 'ID do paciente que fez a indicação',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  referredById?: number;
}
