import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDateString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateHolidayDto {
  @ApiProperty({
    description: 'Nome do feriado',
    example: 'Natal',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Data do feriado no formato YYYY-MM-DD',
    example: '2024-12-25',
  })
  @IsDateString()
  @IsNotEmpty()
  date: string;

  @ApiPropertyOptional({
    description: 'Descrição opcional do feriado',
    example: 'Feriado nacional de Natal',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
