import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { HolidaysService } from './holidays.service';
import { CreateHolidayDto } from './dto/create-holiday.dto';
import { UpdateHolidayDto } from './dto/update-holiday.dto';
import { Holiday } from './entities/holiday.entity';

@ApiTags('holidays')
@Controller('holidays')
export class HolidaysController {
  constructor(private readonly holidaysService: HolidaysService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo feriado' })
  @ApiResponse({
    status: 201,
    description: 'Feriado criado com sucesso',
    type: Holiday,
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe um feriado cadastrado para esta data',
  })
  create(@Body() createHolidayDto: CreateHolidayDto): Promise<Holiday> {
    return this.holidaysService.create(createHolidayDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os feriados' })
  @ApiQuery({
    name: 'year',
    required: false,
    description: 'Filtrar feriados por ano',
    example: 2024,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de feriados',
    type: [Holiday],
  })
  findAll(@Query('year') year?: string): Promise<Holiday[]> {
    const yearNumber = year ? parseInt(year, 10) : undefined;
    return this.holidaysService.findAll(yearNumber);
  }

  @Get('upcoming')
  @ApiOperation({ summary: 'Listar próximos feriados' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Número máximo de feriados a retornar',
    example: 5,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista dos próximos feriados',
    type: [Holiday],
  })
  getUpcoming(@Query('limit') limit?: string): Promise<Holiday[]> {
    const limitNumber = limit ? parseInt(limit, 10) : undefined;
    return this.holidaysService.getUpcomingHolidays(limitNumber);
  }

  @Get('check/:date')
  @ApiOperation({ summary: 'Verificar se uma data é feriado' })
  @ApiParam({
    name: 'date',
    description: 'Data no formato YYYY-MM-DD',
    example: '2024-12-25',
  })
  @ApiResponse({
    status: 200,
    description: 'Retorna true se a data for feriado, false caso contrário',
    schema: { type: 'boolean' },
  })
  isHoliday(@Param('date') date: string): Promise<boolean> {
    return this.holidaysService.isHoliday(date);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar feriado por ID' })
  @ApiParam({
    name: 'id',
    description: 'ID do feriado',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Feriado encontrado',
    type: Holiday,
  })
  @ApiResponse({
    status: 404,
    description: 'Feriado não encontrado',
  })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Holiday> {
    return this.holidaysService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar feriado' })
  @ApiParam({
    name: 'id',
    description: 'ID do feriado',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Feriado atualizado com sucesso',
    type: Holiday,
  })
  @ApiResponse({
    status: 404,
    description: 'Feriado não encontrado',
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe um feriado cadastrado para esta data',
  })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateHolidayDto: UpdateHolidayDto,
  ): Promise<Holiday> {
    return this.holidaysService.update(id, updateHolidayDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover feriado' })
  @ApiParam({
    name: 'id',
    description: 'ID do feriado',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Feriado removido com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Feriado não encontrado',
  })
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.holidaysService.remove(id);
  }
}
