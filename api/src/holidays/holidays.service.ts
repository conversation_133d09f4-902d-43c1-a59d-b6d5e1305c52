import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Holiday } from './entities/holiday.entity';
import { CreateHolidayDto } from './dto/create-holiday.dto';
import { UpdateHolidayDto } from './dto/update-holiday.dto';

@Injectable()
export class HolidaysService {
  constructor(
    @InjectRepository(Holiday)
    private holidayRepository: Repository<Holiday>,
  ) {}

  async create(createHolidayDto: CreateHolidayDto): Promise<Holiday> {
    // Verificar se já existe um feriado na mesma data
    const existingHoliday = await this.holidayRepository.findOne({
      where: { date: createHolidayDto.date },
    });

    if (existingHoliday) {
      throw new ConflictException(
        `Já existe um feriado cadastrado para a data ${createHolidayDto.date}`,
      );
    }

    const holiday = this.holidayRepository.create(createHolidayDto);
    return this.holidayRepository.save(holiday);
  }

  async findAll(year?: number): Promise<Holiday[]> {
    if (year) {
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;

      return this.holidayRepository.find({
        where: {
          date: Between(startDate, endDate),
        },
        order: { date: 'ASC' },
      });
    }

    return this.holidayRepository.find({
      order: { date: 'ASC' },
    });
  }

  async findOne(id: number): Promise<Holiday> {
    const holiday = await this.holidayRepository.findOne({
      where: { id },
    });

    if (!holiday) {
      throw new NotFoundException(`Feriado com ID ${id} não encontrado`);
    }

    return holiday;
  }

  async update(
    id: number,
    updateHolidayDto: UpdateHolidayDto,
  ): Promise<Holiday> {
    const holiday = await this.findOne(id);

    // Se a data está sendo alterada, verificar se não conflita com outro feriado
    if (updateHolidayDto.date && updateHolidayDto.date !== holiday.date) {
      const existingHoliday = await this.holidayRepository.findOne({
        where: { date: updateHolidayDto.date },
      });

      if (existingHoliday && existingHoliday.id !== id) {
        throw new ConflictException(
          `Já existe um feriado cadastrado para a data ${updateHolidayDto.date}`,
        );
      }
    }

    Object.assign(holiday, updateHolidayDto);
    return this.holidayRepository.save(holiday);
  }

  async remove(id: number): Promise<void> {
    const holiday = await this.findOne(id);
    await this.holidayRepository.remove(holiday);
  }

  async isHoliday(date: string): Promise<boolean> {
    const holiday = await this.holidayRepository.findOne({
      where: { date },
    });

    return !!holiday;
  }

  async getHolidayByDate(date: string): Promise<Holiday | null> {
    return this.holidayRepository.findOne({
      where: { date },
    });
  }

  async getUpcomingHolidays(limit: number = 5): Promise<Holiday[]> {
    const today = new Date().toISOString().split('T')[0];

    return this.holidayRepository.find({
      where: {
        date: Between(today, '9999-12-31'),
      },
      order: { date: 'ASC' },
      take: limit,
    });
  }
}
