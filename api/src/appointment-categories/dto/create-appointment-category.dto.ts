import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsNotEmpty, 
  IsOptional, 
  IsBoolean, 
  MaxLength,
  Matches
} from 'class-validator';

export class CreateAppointmentCategoryDto {
  @ApiProperty({
    description: 'Nome da categoria de agendamento',
    example: 'Avalia<PERSON>',
    maxLength: 100
  })
  @IsString({ message: 'O nome deve ser uma string' })
  @IsNotEmpty({ message: 'O nome é obrigatório' })
  @MaxLength(100, { message: 'O nome deve ter no máximo 100 caracteres' })
  name: string;

  @ApiProperty({
    description: 'Descrição da categoria de agendamento',
    example: 'Primeira consulta para avaliação do paciente',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'A descrição deve ser uma string' })
  description?: string;

  @ApiProperty({
    description: 'Código hexadecimal da cor da categoria (deve ser uma cor válida do sistema)',
    example: '#3B82F6',
    pattern: '^#[0-9A-Fa-f]{6}$'
  })
  @IsString({ message: 'A cor deve ser uma string' })
  @IsNotEmpty({ message: 'A cor é obrigatória' })
  @Matches(/^#[0-9A-Fa-f]{6}$/, { 
    message: 'A cor deve estar no formato hexadecimal válido (ex: #3B82F6)' 
  })
  color: string;

  @ApiProperty({
    description: 'Indica se a categoria está ativa',
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean({ message: 'O status ativo deve ser um valor booleano' })
  isActive?: boolean;
}
