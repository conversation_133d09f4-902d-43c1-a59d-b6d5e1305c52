import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsNumber, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class FilterAppointmentCategoryDto {
  @ApiProperty({
    description: 'Filtrar por nome da categoria (busca parcial)',
    required: false,
    example: 'Avaliação'
  })
  @IsOptional()
  @IsString({ message: 'O nome deve ser uma string' })
  name?: string;

  @ApiProperty({
    description: 'Filtrar por status ativo/inativo',
    required: false,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'O status ativo deve ser um valor booleano' })
  isActive?: boolean;

  @ApiProperty({
    description: 'Número da página para paginação',
    required: false,
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'A página deve ser um número' })
  @Min(1, { message: 'A página deve ser maior que 0' })
  page?: number;

  @ApiProperty({
    description: 'Número de itens por página',
    required: false,
    minimum: 1,
    maximum: 100,
    default: 10
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'O limite deve ser um número' })
  @Min(1, { message: 'O limite deve ser maior que 0' })
  limit?: number;
}
