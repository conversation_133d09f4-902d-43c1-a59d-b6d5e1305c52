import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AppointmentCategoriesService } from './appointment-categories.service';
import { CreateAppointmentCategoryDto } from './dto/create-appointment-category.dto';
import { UpdateAppointmentCategoryDto } from './dto/update-appointment-category.dto';
import { FilterAppointmentCategoryDto } from './dto/filter-appointment-category.dto';
import { AppointmentCategory } from './entities/appointment-category.entity';

@ApiTags('appointment-categories')
@Controller('appointment-categories')
export class AppointmentCategoriesController {
  constructor(
    private readonly appointmentCategoriesService: AppointmentCategoriesService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Criar uma nova categoria de agendamento',
    description:
      'Cria uma nova categoria de agendamento com nome, descrição, cor e status.',
  })
  @ApiResponse({
    status: 201,
    description: 'Categoria de agendamento criada com sucesso',
    type: AppointmentCategory,
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos ou cor em formato incorreto',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflito - já existe uma categoria com este nome',
  })
  @ApiBody({ type: CreateAppointmentCategoryDto })
  create(
    @Body() createAppointmentCategoryDto: CreateAppointmentCategoryDto,
  ): Promise<AppointmentCategory> {
    return this.appointmentCategoriesService.create(
      createAppointmentCategoryDto,
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Listar categorias de agendamento',
    description:
      'Lista todas as categorias de agendamento com filtros opcionais e paginação.',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filtrar por nome da categoria (busca parcial)',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    type: Boolean,
    description: 'Filtrar por status ativo/inativo',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Número da página para paginação',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Número de itens por página (máximo 100)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de categorias de agendamento retornada com sucesso',
  })
  findAll(@Query() filterDto: FilterAppointmentCategoryDto) {
    return this.appointmentCategoriesService.findAll(filterDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Buscar categoria de agendamento por ID',
    description: 'Retorna uma categoria de agendamento específica pelo ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID da categoria de agendamento',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Categoria de agendamento encontrada com sucesso',
    type: AppointmentCategory,
  })
  @ApiResponse({
    status: 404,
    description: 'Categoria de agendamento não encontrada',
  })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<AppointmentCategory> {
    return this.appointmentCategoriesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Atualizar categoria de agendamento',
    description: 'Atualiza uma categoria de agendamento existente.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID da categoria de agendamento',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Categoria de agendamento atualizada com sucesso',
    type: AppointmentCategory,
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos ou cor em formato incorreto',
  })
  @ApiResponse({
    status: 404,
    description: 'Categoria de agendamento não encontrada',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflito - já existe uma categoria com este nome',
  })
  @ApiBody({ type: UpdateAppointmentCategoryDto })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAppointmentCategoryDto: UpdateAppointmentCategoryDto,
  ): Promise<AppointmentCategory> {
    return this.appointmentCategoriesService.update(
      id,
      updateAppointmentCategoryDto,
    );
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Excluir categoria de agendamento',
    description: 'Remove uma categoria de agendamento do sistema.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID da categoria de agendamento',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Categoria de agendamento excluída com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Categoria de agendamento não encontrada',
  })
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.appointmentCategoriesService.remove(id);
  }
}
