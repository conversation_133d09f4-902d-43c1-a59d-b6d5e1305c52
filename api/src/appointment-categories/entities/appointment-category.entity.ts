import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('appointment_categories')
export class AppointmentCategory {
  @PrimaryGeneratedColumn('increment')
  @ApiProperty({ description: 'ID único da categoria de agendamento' })
  id: number;

  @Column({ length: 100 })
  @ApiProperty({
    description: 'Nome da categoria de agendamento',
    example: 'Avaliação',
  })
  name: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: 'Descrição da categoria de agendamento',
    example: 'Primeira consulta para avaliação do paciente',
    required: false,
  })
  description: string;

  @Column({ length: 7 })
  @ApiProperty({
    description: 'Código hexadecimal da cor da categoria',
    example: '#3B82F6',
  })
  color: string;

  @Column({ default: true })
  @ApiProperty({
    description: 'Indica se a categoria está ativa',
    default: true,
  })
  isActive: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
