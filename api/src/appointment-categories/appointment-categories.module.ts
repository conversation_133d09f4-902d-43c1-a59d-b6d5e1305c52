import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppointmentCategoriesService } from './appointment-categories.service';
import { AppointmentCategoriesController } from './appointment-categories.controller';
import { AppointmentCategory } from './entities/appointment-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AppointmentCategory])],
  controllers: [AppointmentCategoriesController],
  providers: [AppointmentCategoriesService],
  exports: [AppointmentCategoriesService],
})
export class AppointmentCategoriesModule {}
