import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppointmentCategory } from './entities/appointment-category.entity';
import { CreateAppointmentCategoryDto } from './dto/create-appointment-category.dto';
import { UpdateAppointmentCategoryDto } from './dto/update-appointment-category.dto';
import { FilterAppointmentCategoryDto } from './dto/filter-appointment-category.dto';

@Injectable()
export class AppointmentCategoriesService {
  private readonly logger = new Logger(AppointmentCategoriesService.name);

  constructor(
    @InjectRepository(AppointmentCategory)
    private appointmentCategoriesRepository: Repository<AppointmentCategory>,
  ) {}

  async create(
    createAppointmentCategoryDto: CreateAppointmentCategoryDto,
  ): Promise<AppointmentCategory> {
    this.logger.log(
      `Creating appointment category: ${createAppointmentCategoryDto.name}`,
    );

    // Verificar se já existe uma categoria com o mesmo nome
    const existingCategory = await this.appointmentCategoriesRepository.findOne(
      {
        where: { name: createAppointmentCategoryDto.name },
      },
    );

    if (existingCategory) {
      throw new ConflictException(
        `Já existe uma categoria de agendamento com o nome "${createAppointmentCategoryDto.name}"`,
      );
    }

    // Validar se a cor está no formato correto
    if (!this.isValidHexColor(createAppointmentCategoryDto.color)) {
      throw new BadRequestException(
        'A cor deve estar no formato hexadecimal válido (ex: #3B82F6)',
      );
    }

    const appointmentCategory = this.appointmentCategoriesRepository.create({
      ...createAppointmentCategoryDto,
      isActive: createAppointmentCategoryDto.isActive ?? true,
    });

    const savedCategory =
      await this.appointmentCategoriesRepository.save(appointmentCategory);
    this.logger.log(
      `Appointment category created with ID: ${savedCategory.id}`,
    );

    return savedCategory;
  }

  async findAll(filterDto?: FilterAppointmentCategoryDto): Promise<{
    data: AppointmentCategory[];
    total: number;
    page?: number;
    limit?: number;
    totalPages?: number;
  }> {
    this.logger.log(
      'Finding all appointment categories with filters:',
      filterDto,
    );

    const queryBuilder =
      this.appointmentCategoriesRepository.createQueryBuilder('category');

    // Aplicar filtros
    if (filterDto?.name) {
      queryBuilder.andWhere('category.name LIKE :name', {
        name: `%${filterDto.name}%`,
      });
    }

    if (filterDto?.isActive !== undefined) {
      queryBuilder.andWhere('category.isActive = :isActive', {
        isActive: filterDto.isActive,
      });
    }

    // Ordenar por nome
    queryBuilder.orderBy('category.name', 'ASC');

    // Aplicar paginação se fornecida
    if (filterDto?.page && filterDto?.limit) {
      const page = filterDto.page;
      const limit = Math.min(filterDto.limit, 100); // Máximo 100 itens por página
      const skip = (page - 1) * limit;

      queryBuilder.skip(skip).take(limit);

      const [data, total] = await queryBuilder.getManyAndCount();
      const totalPages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    }

    // Sem paginação - retornar todos
    const data = await queryBuilder.getMany();
    return {
      data,
      total: data.length,
    };
  }

  async findOne(id: number): Promise<AppointmentCategory> {
    this.logger.log(`Finding appointment category with ID: ${id}`);

    const appointmentCategory =
      await this.appointmentCategoriesRepository.findOne({
        where: { id },
      });

    if (!appointmentCategory) {
      throw new NotFoundException(
        `Categoria de agendamento com ID ${id} não encontrada`,
      );
    }

    return appointmentCategory;
  }

  async update(
    id: number,
    updateAppointmentCategoryDto: UpdateAppointmentCategoryDto,
  ): Promise<AppointmentCategory> {
    this.logger.log(`Updating appointment category with ID: ${id}`);

    const appointmentCategory = await this.findOne(id);

    // Verificar se o novo nome já existe (se fornecido)
    if (
      updateAppointmentCategoryDto.name &&
      updateAppointmentCategoryDto.name !== appointmentCategory.name
    ) {
      const existingCategory =
        await this.appointmentCategoriesRepository.findOne({
          where: { name: updateAppointmentCategoryDto.name },
        });

      if (existingCategory) {
        throw new ConflictException(
          `Já existe uma categoria de agendamento com o nome "${updateAppointmentCategoryDto.name}"`,
        );
      }
    }

    // Validar cor se fornecida
    if (
      updateAppointmentCategoryDto.color &&
      !this.isValidHexColor(updateAppointmentCategoryDto.color)
    ) {
      throw new BadRequestException(
        'A cor deve estar no formato hexadecimal válido (ex: #3B82F6)',
      );
    }

    Object.assign(appointmentCategory, updateAppointmentCategoryDto);
    const updatedCategory =
      await this.appointmentCategoriesRepository.save(appointmentCategory);

    this.logger.log(`Appointment category updated with ID: ${id}`);
    return updatedCategory;
  }

  async remove(id: number): Promise<void> {
    this.logger.log(`Removing appointment category with ID: ${id}`);

    const appointmentCategory = await this.findOne(id);

    await this.appointmentCategoriesRepository.remove(appointmentCategory);
    this.logger.log(`Appointment category removed with ID: ${id}`);
  }

  private isValidHexColor(color: string): boolean {
    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;
    return hexColorRegex.test(color);
  }
}
