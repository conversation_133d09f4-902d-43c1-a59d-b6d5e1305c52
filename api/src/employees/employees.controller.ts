import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { EmployeesService } from './employees.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { Employee } from './entities/employee.entity';
import { EmployeeType } from './enums/employee-type.enum';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { EmployeePaginationDto } from './dto/employee-pagination.dto';

@ApiTags('employees')
@Controller('employees')
export class EmployeesController {
  constructor(private readonly employeesService: EmployeesService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo funcionário' })
  @ApiResponse({
    status: 201,
    description: 'Funcionário criado com sucesso',
    type: Employee,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({
    status: 409,
    description: 'Conflito - Email ou CPF já existe',
  })
  create(@Body() createEmployeeDto: CreateEmployeeDto): Promise<Employee> {
    return this.employeesService.create(createEmployeeDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os funcionários' })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Termo de busca por nome ou email',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: EmployeeType,
    description: 'Filtro pelo tipo de funcionário',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Número da página (começando em 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Quantidade de itens por página',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de funcionários retornada com sucesso',
  })
  async findAll(
    @Query() paginationDto: EmployeePaginationDto,
  ): Promise<PaginatedResponse<Employee> | Employee[]> {
    const { search, type, page, limit } = paginationDto;

    // Se não houver parâmetros de paginação, retorna todos os funcionários (para compatibilidade)
    if (!page && !limit) {
      return this.employeesService.findAll(search, type);
    }

    // Se houver parâmetros de paginação, retorna os funcionários paginados
    return this.employeesService.findAllPaginated(paginationDto, search, type);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um funcionário pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do funcionário' })
  @ApiResponse({
    status: 200,
    description: 'Funcionário encontrado',
    type: Employee,
  })
  @ApiResponse({ status: 404, description: 'Funcionário não encontrado' })
  findOne(@Param('id') id: string): Promise<Employee> {
    return this.employeesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um funcionário' })
  @ApiParam({ name: 'id', description: 'ID do funcionário' })
  @ApiResponse({
    status: 200,
    description: 'Funcionário atualizado com sucesso',
    type: Employee,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Funcionário não encontrado' })
  @ApiResponse({
    status: 409,
    description: 'Conflito - Email ou CPF já existe',
  })
  update(
    @Param('id') id: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
  ): Promise<Employee> {
    return this.employeesService.update(id, updateEmployeeDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um funcionário' })
  @ApiParam({ name: 'id', description: 'ID do funcionário' })
  @ApiResponse({ status: 204, description: 'Funcionário removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Funcionário não encontrado' })
  remove(@Param('id') id: string): Promise<void> {
    return this.employeesService.remove(id);
  }
}
