import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, Min, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { EmployeeType } from '../enums/employee-type.enum';

export class EmployeePaginationDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> da página (começando em 1)',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Quantidade de itens por página',
    default: 6,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 6;

  @ApiProperty({
    description: 'Termo de busca para filtrar resultados',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Tipo de funcionário para filtrar resultados',
    required: false,
    enum: EmployeeType,
  })
  @IsOptional()
  @IsEnum(EmployeeType)
  type?: EmployeeType;
}
