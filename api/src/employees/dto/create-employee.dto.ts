import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsEnum,
  IsDateString,
  IsBoolean,
  IsOptional,
  IsNotEmpty,
} from 'class-validator';
import { EmployeeType } from '../enums/employee-type.enum';

export class CreateEmployeeDto {
  @ApiProperty({ description: 'Nome completo do funcionário' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Email do funcionário' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Telefone do funcionário', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Tipo/Cargo do funcionário',
    enum: EmployeeType,
  })
  @IsNotEmpty()
  @IsEnum(EmployeeType)
  type: EmployeeType;

  @ApiProperty({
    description: 'Data de nascimento do funcionário',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @ApiProperty({ description: 'CPF do funcionário' })
  @IsNotEmpty()
  @IsString()
  cpf: string;

  @ApiProperty({ description: 'Data de admissão do funcionário' })
  @IsNotEmpty()
  @IsDateString()
  admissionDate: string;

  @ApiProperty({
    description: 'Observações sobre o funcionário',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'Indica se o funcionário está ativo',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
