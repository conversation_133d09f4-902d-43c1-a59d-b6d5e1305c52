import {
  Injectable,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { Employee } from './entities/employee.entity';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { EmployeeType } from './enums/employee-type.enum';
import { PaginatedResponse } from '../common/dto/pagination.dto';
import { EmployeePaginationDto } from './dto/employee-pagination.dto';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class EmployeesService {
  private readonly logger = new Logger(EmployeesService.name);

  constructor(
    @InjectRepository(Employee)
    private employeesRepository: Repository<Employee>,
  ) {}

  async create(createEmployeeDto: CreateEmployeeDto): Promise<Employee> {
    // Verificar se já existe um funcionário com o mesmo email
    const existingEmployeeByEmail = await this.employeesRepository.findOne({
      where: { email: createEmployeeDto.email },
    });

    if (existingEmployeeByEmail) {
      throw new ConflictException(
        `Já existe um funcionário com o email ${createEmployeeDto.email}`,
      );
    }

    // Verificar se já existe um funcionário com o mesmo CPF
    const existingEmployeeByCpf = await this.employeesRepository.findOne({
      where: { cpf: createEmployeeDto.cpf },
    });

    if (existingEmployeeByCpf) {
      throw new ConflictException(
        `Já existe um funcionário com o CPF ${createEmployeeDto.cpf}`,
      );
    }

    const employee = this.employeesRepository.create(createEmployeeDto);
    return this.employeesRepository.save(employee);
  }

  async findAll(search?: string, type?: EmployeeType): Promise<Employee[]> {
    this.logger.log(
      `Finding all employees with search: ${search}, type: ${type}`,
    );
    const where: FindOptionsWhere<Employee> = {};

    if (search) {
      where.name = Like(`%${search}%`);
      // Não é possível usar OR diretamente no where, então vamos fazer uma consulta personalizada
      return this.employeesRepository
        .createQueryBuilder('employee')
        .where('employee.name LIKE :search OR employee.email LIKE :search', {
          search: `%${search}%`,
        })
        .andWhere(type ? 'employee.type = :type' : '1=1', { type })
        .orderBy('employee.name', 'ASC')
        .getMany();
    }

    if (type) {
      where.type = type;
    }

    return this.employeesRepository.find({
      where,
      order: { name: 'ASC' },
    });
  }

  async findAllPaginated(
    paginationDto: EmployeePaginationDto,
    search?: string,
    type?: EmployeeType,
  ): Promise<PaginatedResponse<Employee>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;

      // Usar os valores do DTO se fornecidos, caso contrário, usar os parâmetros
      const searchTerm = paginationDto.search || search;
      const employeeType = paginationDto.type || type;

      this.logger.log(
        `Finding employees with pagination: page ${page}, limit ${limit}, search: ${searchTerm}, type: ${employeeType}`,
      );

      const skip = (page - 1) * limit;

      // Construir a query com filtros
      const queryBuilder =
        this.employeesRepository.createQueryBuilder('employee');

      // Aplicar filtro de busca
      if (searchTerm) {
        queryBuilder.where(
          '(employee.name LIKE :search OR employee.email LIKE :search)',
          { search: `%${searchTerm}%` },
        );
      }

      // Aplicar filtro de tipo
      if (employeeType) {
        if (searchTerm) {
          queryBuilder.andWhere('employee.type = :type', {
            type: employeeType,
          });
        } else {
          queryBuilder.where('employee.type = :type', { type: employeeType });
        }
      }

      // Aplicar paginação
      queryBuilder.skip(skip).take(limit);

      // Ordenação
      queryBuilder.orderBy('employee.name', 'ASC');

      // Executar a query
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding employees with pagination: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw new InternalServerErrorException('Erro ao buscar funcionários');
    }
  }

  async findOne(id: string): Promise<Employee> {
    const employee = await this.employeesRepository.findOne({
      where: { id },
    });

    if (!employee) {
      throw new NotFoundException(`Funcionário com ID ${id} não encontrado`);
    }

    return employee;
  }

  async update(
    id: string,
    updateEmployeeDto: UpdateEmployeeDto,
  ): Promise<Employee> {
    // Verificar se o funcionário existe
    const employee = await this.findOne(id);

    // Verificar se está tentando atualizar o email para um que já existe
    if (updateEmployeeDto.email && updateEmployeeDto.email !== employee.email) {
      const existingEmployee = await this.employeesRepository.findOne({
        where: { email: updateEmployeeDto.email },
      });

      if (existingEmployee) {
        throw new ConflictException(
          `Já existe um funcionário com o email ${updateEmployeeDto.email}`,
        );
      }
    }

    // Verificar se está tentando atualizar o CPF para um que já existe
    if (updateEmployeeDto.cpf && updateEmployeeDto.cpf !== employee.cpf) {
      const existingEmployee = await this.employeesRepository.findOne({
        where: { cpf: updateEmployeeDto.cpf },
      });

      if (existingEmployee) {
        throw new ConflictException(
          `Já existe um funcionário com o CPF ${updateEmployeeDto.cpf}`,
        );
      }
    }

    await this.employeesRepository.update(id, updateEmployeeDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const employee = await this.findOne(id);
    await this.employeesRepository.remove(employee);
  }
}
