import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { EmployeeType } from '../enums/employee-type.enum';

@Entity('employees')
export class Employee {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único do funcionário' })
  id: string;

  @Column({ length: 100 })
  @ApiProperty({ description: 'Nome completo do funcionário' })
  name: string;

  @Column({ unique: true })
  @ApiProperty({ description: 'Email do funcionário' })
  email: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Telefone do funcionário', required: false })
  phone: string;

  @Column({
    type: 'enum',
    enum: EmployeeType,
  })
  @ApiProperty({
    description: 'Tipo/Cargo do funcionário',
    enum: EmployeeType,
  })
  type: EmployeeType;

  @Column({ type: 'date', nullable: true })
  @ApiProperty({
    description: 'Data de nascimento do funcionário',
    required: false,
  })
  birthDate: Date;

  @Column({ unique: true })
  @ApiProperty({ description: 'CPF do funcionário' })
  cpf: string;

  @Column({ type: 'date', name: 'admission_date' })
  @ApiProperty({ description: 'Data de admissão do funcionário' })
  admissionDate: Date;

  @Column({ nullable: true, type: 'text' })
  @ApiProperty({
    description: 'Observações sobre o funcionário',
    required: false,
  })
  notes: string;

  @Column({ default: true, name: 'is_active' })
  @ApiProperty({ description: 'Indica se o funcionário está ativo' })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação do registro' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @ApiProperty({ description: 'Data da última atualização do registro' })
  updatedAt: Date;
}
