import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PhotosService } from './photos.service';
import { PhotosController, PhotoFoldersController } from './photos.controller';
import { PatientPhoto } from './entities/patient-photo.entity';
import { PhotoFolder } from './entities/photo-folder.entity';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsModule } from '../patients/patients.module';
import { StorageModule } from '../common/storage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PatientPhoto, PhotoFolder, Patient]),
    PatientsModule,
    StorageModule,
  ],
  controllers: [PhotosController, PhotoFoldersController],
  providers: [PhotosService],
  exports: [PhotosService],
})
export class PhotosModule {}
