import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber } from 'class-validator';

export class UpdatePhotoDto {
  @ApiProperty({
    description: 'Legenda da foto',
    example: 'Radiografia panorâmica atualizada',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A legenda deve ser uma string' })
  caption?: string;

  @ApiProperty({
    description: 'Ordem da foto',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'A ordem deve ser um número' })
  order?: number;
}
