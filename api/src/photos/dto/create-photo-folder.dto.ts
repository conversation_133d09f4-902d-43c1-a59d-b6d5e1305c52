import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreatePhotoFolderDto {
  @ApiProperty({
    description: 'Nome da pasta',
    example: 'Radiografias',
  })
  @IsNotEmpty({ message: 'O nome da pasta é obrigatório' })
  @IsString({ message: 'O nome da pasta deve ser uma string' })
  name: string;

  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  @IsNumber({}, { message: 'O ID do paciente deve ser um número' })
  patientId: number;
}
