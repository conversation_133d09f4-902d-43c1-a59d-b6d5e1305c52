import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class MovePhotoDto {
  @ApiProperty({
    description: 'ID da pasta de destino (null para remover da pasta atual)',
    example: 'uuid-da-pasta',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'O ID da pasta deve ser uma string' })
  folderId: string | null;
}
