import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class PhotoUploadItem {
  @ApiProperty({
    description: 'Legenda da foto',
    example: 'Radiografia panorâmica',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A legenda deve ser uma string' })
  caption?: string;

  @ApiProperty({
    description: 'Ordem da foto',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'A ordem deve ser um número' })
  order?: number;
}

export class MultipleUploadPhotoDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  @IsNumber({}, { message: 'O ID do paciente deve ser um número' })
  patientId: number;

  @ApiProperty({
    description: 'ID da pasta (opcional)',
    example: 'uuid-da-pasta',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O ID da pasta deve ser uma string' })
  folderId?: string;

  @ApiProperty({
    description: 'Informações das fotos (legendas e ordem)',
    type: [PhotoUploadItem],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'As informações das fotos devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => PhotoUploadItem)
  photosInfo?: PhotoUploadItem[];
}
