import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PhotoOrderItem {
  @ApiProperty({
    description: 'ID da foto',
    example: 'uuid-da-foto',
  })
  @IsNotEmpty({ message: 'O ID da foto é obrigatório' })
  photoId: string;

  @ApiProperty({
    description: 'Nova ordem da foto',
    example: 1,
  })
  @IsNotEmpty({ message: 'A ordem é obrigatória' })
  order: number;
}

export class ReorderPhotosDto {
  @ApiProperty({
    description: 'Array com as novas ordens das fotos',
    type: [PhotoOrderItem],
  })
  @IsNotEmpty({ message: 'A lista de fotos é obrigatória' })
  @IsArray({ message: 'A lista de fotos deve ser um array' })
  @ValidateNested({ each: true })
  @Type(() => PhotoOrderItem)
  photos: PhotoOrderItem[];
}
