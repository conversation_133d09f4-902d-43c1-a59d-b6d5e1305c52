import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CameraPhotoDto {
  @ApiProperty({
    description: 'ID do paciente',
    example: 1,
  })
  @IsNotEmpty({ message: 'O ID do paciente é obrigatório' })
  @IsNumber({}, { message: 'O ID do paciente deve ser um número' })
  patientId: number;

  @ApiProperty({
    description: 'ID da pasta (opcional)',
    example: 'uuid-da-pasta',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O ID da pasta deve ser uma string' })
  folderId?: string;

  @ApiProperty({
    description: 'Imagem em formato base64',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
  })
  @IsNotEmpty({ message: 'A imagem é obrigatória' })
  @IsString({ message: 'A imagem deve ser uma string em formato base64' })
  imageBase64: string;

  @ApiProperty({
    description: 'Legenda da foto',
    example: 'Foto frontal',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A legenda deve ser uma string' })
  caption?: string;

  @ApiProperty({
    description: 'Ordem da foto',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'A ordem deve ser um número' })
  order?: number;
}
