import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  ParseUUIDPipe,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { PhotosService } from './photos.service';
import { CreatePhotoFolderDto } from './dto/create-photo-folder.dto';
import { UpdatePhotoFolderDto } from './dto/update-photo-folder.dto';
import { MovePhotoDto } from './dto/move-photo.dto';
import { CameraPhotoDto } from './dto/camera-photo.dto';
import { MultipleCameraPhotoDto } from './dto/multiple-camera-photo.dto';
import { UpdatePhotoDto } from './dto/update-photo.dto';
import { ReorderPhotosDto } from './dto/reorder-photos.dto';
import { PatientPhoto } from './entities/patient-photo.entity';
import { PhotoFolder } from './entities/photo-folder.entity';

@ApiTags('photos')
@Controller('photos')
export class PhotosController {
  constructor(private readonly photosService: PhotosService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Fazer upload de uma foto' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Arquivo de imagem',
        },
        patientId: { type: 'number', example: 1 },
        folderId: { type: 'string', example: 'uuid-da-pasta' },
        caption: { type: 'string', example: 'Radiografia panorâmica' },
        order: { type: 'number', example: 1 },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Foto enviada com sucesso',
    type: PatientPhoto,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadPhoto(
    @UploadedFile() file: Express.Multer.File,
    @Body()
    body: {
      patientId: string;
      folderId?: string;
      caption?: string;
      order?: string;
    },
  ): Promise<PatientPhoto> {
    // Converter patientId para número
    const patientId = parseInt(body.patientId, 10);
    if (isNaN(patientId)) {
      throw new BadRequestException('O ID do paciente deve ser um número');
    }

    // Converter order para número se fornecido
    const order = body.order ? parseInt(body.order, 10) : undefined;

    return this.photosService.uploadPhoto(
      file,
      patientId,
      body.folderId,
      body.caption,
      order,
    );
  }

  @Post('camera')
  @ApiOperation({ summary: 'Enviar foto da câmera' })
  @ApiResponse({
    status: 201,
    description: 'Foto da câmera enviada com sucesso',
    type: PatientPhoto,
  })
  async uploadCameraPhoto(
    @Body() cameraPhotoDto: CameraPhotoDto,
  ): Promise<PatientPhoto> {
    // Garantir que patientId é um número
    if (typeof cameraPhotoDto.patientId !== 'number') {
      const patientId = parseInt(String(cameraPhotoDto.patientId), 10);
      if (isNaN(patientId)) {
        throw new BadRequestException('O ID do paciente deve ser um número');
      }
      cameraPhotoDto.patientId = patientId;
    }

    return this.photosService.uploadCameraPhoto(
      cameraPhotoDto.imageBase64,
      cameraPhotoDto.patientId,
      cameraPhotoDto.folderId,
      cameraPhotoDto.caption,
      cameraPhotoDto.order,
    );
  }

  @Post('upload-multiple')
  @ApiOperation({ summary: 'Fazer upload de múltiplas fotos' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Arquivos de imagem',
        },
        patientId: { type: 'number', example: 1 },
        folderId: { type: 'string', example: 'uuid-da-pasta' },
        photosInfo: {
          type: 'string',
          description: 'JSON com informações das fotos (legendas e ordem)',
          example:
            '[{"caption":"Foto 1","order":1},{"caption":"Foto 2","order":2}]',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Fotos enviadas com sucesso',
    type: [PatientPhoto],
  })
  @UseInterceptors(FilesInterceptor('files', 10))
  async uploadMultiplePhotos(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: { patientId: string; folderId?: string; photosInfo?: string },
  ): Promise<PatientPhoto[]> {
    // Converter patientId para número
    const patientId = parseInt(body.patientId, 10);
    if (isNaN(patientId)) {
      throw new BadRequestException('O ID do paciente deve ser um número');
    }

    // Parse das informações das fotos se fornecidas
    let photosInfo: Array<{ caption?: string; order?: number }> = [];
    if (body.photosInfo) {
      try {
        photosInfo = JSON.parse(body.photosInfo) as Array<{
          caption?: string;
          order?: number;
        }>;
      } catch {
        throw new BadRequestException(
          'Formato inválido para informações das fotos',
        );
      }
    }

    return this.photosService.uploadMultiplePhotos(
      files,
      patientId,
      body.folderId,
      photosInfo,
    );
  }

  @Post('camera-multiple')
  @ApiOperation({ summary: 'Enviar múltiplas fotos da câmera' })
  @ApiResponse({
    status: 201,
    description: 'Fotos da câmera enviadas com sucesso',
    type: [PatientPhoto],
  })
  async uploadMultipleCameraPhotos(
    @Body() multipleCameraPhotoDto: MultipleCameraPhotoDto,
  ): Promise<PatientPhoto[]> {
    // Garantir que patientId é um número
    if (typeof multipleCameraPhotoDto.patientId !== 'number') {
      const patientId = parseInt(String(multipleCameraPhotoDto.patientId), 10);
      if (isNaN(patientId)) {
        throw new BadRequestException('O ID do paciente deve ser um número');
      }
      multipleCameraPhotoDto.patientId = patientId;
    }

    return this.photosService.uploadMultipleCameraPhotos(
      multipleCameraPhotoDto,
    );
  }

  @Get('patients/:patientId')
  @ApiOperation({ summary: 'Listar todas as fotos de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Fotos do paciente retornadas com sucesso',
    schema: {
      properties: {
        folders: {
          type: 'array',
          items: { $ref: '#/components/schemas/PhotoFolder' },
        },
        unfiled: {
          type: 'array',
          items: { $ref: '#/components/schemas/PatientPhoto' },
        },
      },
    },
  })
  findAllPhotosByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<{
    folders: PhotoFolder[];
    unfiled: PatientPhoto[];
  }> {
    return this.photosService.findAllPhotosByPatient(patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma foto pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da foto' })
  @ApiResponse({
    status: 200,
    description: 'Foto encontrada',
    type: PatientPhoto,
  })
  findOnePhoto(@Param('id', ParseUUIDPipe) id: string): Promise<PatientPhoto> {
    return this.photosService.findOnePhoto(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar informações de uma foto' })
  @ApiParam({ name: 'id', description: 'ID da foto' })
  @ApiResponse({
    status: 200,
    description: 'Foto atualizada com sucesso',
    type: PatientPhoto,
  })
  updatePhoto(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePhotoDto: UpdatePhotoDto,
  ): Promise<PatientPhoto> {
    return this.photosService.updatePhoto(id, updatePhotoDto);
  }

  @Patch(':id/move')
  @ApiOperation({ summary: 'Mover uma foto para outra pasta' })
  @ApiParam({ name: 'id', description: 'ID da foto' })
  @ApiResponse({
    status: 200,
    description: 'Foto movida com sucesso',
    type: PatientPhoto,
  })
  movePhoto(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() movePhotoDto: MovePhotoDto,
  ): Promise<PatientPhoto> {
    return this.photosService.movePhoto(id, movePhotoDto);
  }

  @Post('reorder')
  @ApiOperation({ summary: 'Reordenar fotos' })
  @ApiResponse({
    status: 200,
    description: 'Fotos reordenadas com sucesso',
    type: [PatientPhoto],
  })
  reorderPhotos(
    @Body() reorderPhotosDto: ReorderPhotosDto,
  ): Promise<PatientPhoto[]> {
    return this.photosService.reorderPhotos(reorderPhotosDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover uma foto' })
  @ApiParam({ name: 'id', description: 'ID da foto' })
  @ApiResponse({
    status: 200,
    description: 'Foto removida com sucesso',
  })
  removePhoto(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.photosService.removePhoto(id);
  }
}

@ApiTags('photo-folders')
@Controller('photo-folders')
export class PhotoFoldersController {
  constructor(private readonly photosService: PhotosService) {}

  @Post()
  @ApiOperation({ summary: 'Criar uma nova pasta de fotos' })
  @ApiResponse({
    status: 201,
    description: 'Pasta criada com sucesso',
    type: PhotoFolder,
  })
  createFolder(
    @Body() createPhotoFolderDto: CreatePhotoFolderDto,
  ): Promise<PhotoFolder> {
    return this.photosService.createFolder(createPhotoFolderDto);
  }

  @Get('patients/:patientId')
  @ApiOperation({ summary: 'Listar todas as pastas de um paciente' })
  @ApiParam({ name: 'patientId', description: 'ID do paciente' })
  @ApiResponse({
    status: 200,
    description: 'Pastas do paciente retornadas com sucesso',
    type: [PhotoFolder],
  })
  findAllFoldersByPatient(
    @Param('patientId', ParseIntPipe) patientId: number,
  ): Promise<PhotoFolder[]> {
    return this.photosService.findAllFoldersByPatient(patientId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma pasta pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da pasta' })
  @ApiResponse({
    status: 200,
    description: 'Pasta encontrada',
    type: PhotoFolder,
  })
  findOneFolder(@Param('id', ParseUUIDPipe) id: string): Promise<PhotoFolder> {
    return this.photosService.findOneFolder(id);
  }

  @Get(':id/photos')
  @ApiOperation({ summary: 'Listar fotos de uma pasta' })
  @ApiParam({ name: 'id', description: 'ID da pasta' })
  @ApiResponse({
    status: 200,
    description: 'Fotos da pasta retornadas com sucesso',
    type: [PatientPhoto],
  })
  findPhotosByFolder(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PatientPhoto[]> {
    return this.photosService.findPhotosByFolder(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar uma pasta' })
  @ApiParam({ name: 'id', description: 'ID da pasta' })
  @ApiResponse({
    status: 200,
    description: 'Pasta atualizada com sucesso',
    type: PhotoFolder,
  })
  updateFolder(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePhotoFolderDto: UpdatePhotoFolderDto,
  ): Promise<PhotoFolder> {
    return this.photosService.updateFolder(id, updatePhotoFolderDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remover uma pasta' })
  @ApiParam({ name: 'id', description: 'ID da pasta' })
  @ApiResponse({
    status: 200,
    description: 'Pasta removida com sucesso',
  })
  removeFolder(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.photosService.removeFolder(id);
  }
}
