import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { PatientPhoto } from './patient-photo.entity';

@Entity('photo_folders')
export class PhotoFolder {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da pasta de fotos' })
  id: string;

  @Column({ length: 255 })
  @ApiProperty({ description: 'Nome da pasta' })
  name: string;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado à pasta' })
  patient: Patient;

  @Column()
  patientId: number;

  @OneToMany(() => PatientPhoto, (photo) => photo.folder)
  photos: PatientPhoto[];

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação da pasta' })
  createdAt: Date;
}
