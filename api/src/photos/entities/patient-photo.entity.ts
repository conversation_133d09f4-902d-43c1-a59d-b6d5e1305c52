import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Patient } from '../../patients/entities/patient.entity';
import { PhotoFolder } from './photo-folder.entity';

@Entity('patient_photos')
export class PatientPhoto {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da foto' })
  id: string;

  @Column({ length: 255 })
  @ApiProperty({ description: 'URL da foto' })
  url: string;

  @Column({ length: 255 })
  @ApiProperty({ description: 'Nome do arquivo' })
  filename: string;

  @Column({ length: 500, nullable: true })
  @ApiProperty({ description: 'Legenda da foto', required: false })
  caption: string;

  @Column({ default: 0 })
  @ApiProperty({ description: 'Ordem de exibição da foto' })
  order: number;

  @CreateDateColumn({ name: 'uploaded_at' })
  @ApiProperty({ description: 'Data de upload da foto' })
  uploadedAt: Date;

  @ManyToOne(() => Patient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'patientId' })
  @ApiProperty({ description: 'Paciente associado à foto' })
  patient: Patient;

  @Column()
  patientId: number;

  @ManyToOne(() => PhotoFolder, (folder) => folder.photos, { nullable: true })
  @JoinColumn({ name: 'folderId' })
  @ApiProperty({ description: 'Pasta que contém a foto', required: false })
  folder: PhotoFolder;

  @Column({ type: 'varchar', length: 36, nullable: true })
  folderId: string | null;
}
