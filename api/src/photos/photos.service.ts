/* eslint-disable @typescript-eslint/explicit-function-return-type */
import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Inject,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { PatientPhoto } from './entities/patient-photo.entity';
import { PhotoFolder } from './entities/photo-folder.entity';
import { Patient } from '../patients/entities/patient.entity';
import { CreatePhotoFolderDto } from './dto/create-photo-folder.dto';
import { UpdatePhotoFolderDto } from './dto/update-photo-folder.dto';
import { MovePhotoDto } from './dto/move-photo.dto';
import { IStorageProvider } from '../common/interfaces/storage.interface';
import { Readable } from 'stream';
import { formatErrorForLogging } from '../common/utils/error.utils';

@Injectable()
export class PhotosService {
  private readonly logger = new Logger(PhotosService.name);

  constructor(
    @InjectRepository(PatientPhoto)
    private patientPhotoRepository: Repository<PatientPhoto>,
    @InjectRepository(PhotoFolder)
    private photoFolderRepository: Repository<PhotoFolder>,
    @InjectRepository(Patient)
    private patientRepository: Repository<Patient>,
    @Inject('StorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  // Métodos para fotos
  async uploadPhoto(
    file: Express.Multer.File,
    patientId: number,
    folderId?: string,
    caption?: string,
    order?: number,
  ): Promise<PatientPhoto> {
    try {
      this.logger.log(
        `Uploading photo for patient ${patientId}${
          folderId ? ` to folder ${folderId}` : ''
        }`,
      );

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Verificar se a pasta existe (se fornecida)
      let folder: PhotoFolder | null = null;
      if (folderId) {
        folder = await this.photoFolderRepository.findOne({
          where: { id: folderId, patientId },
        });

        if (!folder) {
          throw new NotFoundException(
            `Pasta com ID ${folderId} não encontrada para o paciente ${patientId}`,
          );
        }
      }

      // Fazer upload do arquivo para o MinIO
      const filePath = await this.storageProvider.uploadFile(
        file,
        `patient-photos/${patientId}`,
      );

      // Decodificar o nome do arquivo para garantir que caracteres especiais sejam exibidos corretamente
      const decodedFileName = Buffer.from(file.originalname, 'latin1').toString(
        'utf8',
      );

      // Determinar a ordem se não fornecida
      let finalOrder = order;
      if (finalOrder === undefined) {
        const maxOrder = (await this.patientPhotoRepository
          .createQueryBuilder('photo')
          .select('MAX(photo.order)', 'maxOrder')
          .where('photo.patientId = :patientId', { patientId })
          .andWhere(
            folderId ? 'photo.folderId = :folderId' : 'photo.folderId IS NULL',
            { folderId },
          )
          .getRawOne()) as { maxOrder: number | null };
        finalOrder = (maxOrder?.maxOrder || 0) + 1;
      }

      // Criar a entrada no banco de dados
      const photo = this.patientPhotoRepository.create({
        url: filePath,
        filename: decodedFileName,
        caption,
        order: finalOrder,
        patientId,
        folderId: folder?.id || null,
      });

      // Salvar a foto
      const savedPhoto = await this.patientPhotoRepository.save(photo);

      // Gerar URL temporária para acesso à foto
      const url = await this.storageProvider.getFileUrl(filePath);
      savedPhoto.url = url;

      return savedPhoto;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error uploading photo: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao fazer upload da foto');
    }
  }

  async uploadCameraPhoto(
    imageBase64: string,
    patientId: number,
    folderId?: string,
    caption?: string,
    order?: number,
  ): Promise<PatientPhoto> {
    try {
      this.logger.log(
        `Uploading camera photo for patient ${patientId}${
          folderId ? ` to folder ${folderId}` : ''
        }`,
      );

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Verificar se a pasta existe (se fornecida)
      let folder: PhotoFolder | null = null;
      if (folderId) {
        folder = await this.photoFolderRepository.findOne({
          where: { id: folderId, patientId },
        });

        if (!folder) {
          throw new NotFoundException(
            `Pasta com ID ${folderId} não encontrada para o paciente ${patientId}`,
          );
        }
      }

      // Extrair o tipo de imagem e os dados do base64
      const matches = imageBase64.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        throw new BadRequestException('Formato de imagem base64 inválido');
      }

      const type = matches[1];
      const data = Buffer.from(matches[2], 'base64');

      // Criar um objeto de arquivo para o MinIO
      const file: Express.Multer.File = {
        buffer: data,
        originalname: `camera_${Date.now()}.${type.split('/')[1] || 'jpg'}`,
        mimetype: type,
        size: data.length,
        fieldname: 'file',
        encoding: '7bit',
        destination: '',
        filename: '',
        path: '',
        stream: new Readable({
          read() {
            this.push(data);
            this.push(null);
          },
        }),
      };

      // Fazer upload do arquivo para o MinIO
      const filePath = await this.storageProvider.uploadFile(
        file,
        `patient-photos/${patientId}`,
      );

      // Decodificar o nome do arquivo para garantir que caracteres especiais sejam exibidos corretamente
      const decodedFileName = Buffer.from(file.originalname, 'latin1').toString(
        'utf8',
      );

      // Determinar a ordem se não fornecida
      let finalOrder = order;
      if (finalOrder === undefined) {
        const maxOrder = (await this.patientPhotoRepository
          .createQueryBuilder('photo')
          .select('MAX(photo.order)', 'maxOrder')
          .where('photo.patientId = :patientId', { patientId })
          .andWhere(
            folderId ? 'photo.folderId = :folderId' : 'photo.folderId IS NULL',
            { folderId },
          )
          .getRawOne()) as { maxOrder: number | null };
        finalOrder = (maxOrder?.maxOrder || 0) + 1;
      }

      // Criar a entrada no banco de dados
      const photo = this.patientPhotoRepository.create({
        url: filePath,
        filename: decodedFileName,
        caption,
        order: finalOrder,
        patientId,
        folderId: folder?.id || null,
      });

      // Salvar a foto
      const savedPhoto = await this.patientPhotoRepository.save(photo);

      // Gerar URL temporária para acesso à foto
      const url = await this.storageProvider.getFileUrl(filePath);
      savedPhoto.url = url;

      return savedPhoto;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error uploading camera photo: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao fazer upload da foto da câmera',
      );
    }
  }

  async findAllPhotosByPatient(patientId: number): Promise<{
    folders: PhotoFolder[];
    unfiled: PatientPhoto[];
  }> {
    try {
      this.logger.log(`Finding all photos for patient ${patientId}`);

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar todas as pastas do paciente
      const folders = await this.photoFolderRepository.find({
        where: { patientId },
        order: { name: 'ASC' },
      });

      // Buscar todas as fotos do paciente que não estão em pastas
      const unfiled = await this.patientPhotoRepository.find({
        where: {
          patientId,
          folderId: IsNull(),
        },
        order: { order: 'ASC', uploadedAt: 'DESC' },
      });

      // Gerar URLs temporárias para as fotos sem pasta
      for (const photo of unfiled) {
        photo.url = await this.storageProvider.getFileUrl(photo.url);
      }

      // Retornar pastas e fotos sem pasta
      return { folders, unfiled };
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding photos for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar fotos do paciente',
      );
    }
  }

  async findPhotosByFolder(folderId: string): Promise<PatientPhoto[]> {
    try {
      this.logger.log(`Finding photos for folder ${folderId}`);

      // Verificar se a pasta existe
      const folder = await this.photoFolderRepository.findOne({
        where: { id: folderId },
      });

      if (!folder) {
        throw new NotFoundException(`Pasta com ID ${folderId} não encontrada`);
      }

      // Buscar todas as fotos da pasta
      const photos = await this.patientPhotoRepository.find({
        where: { folderId },
        order: { order: 'ASC', uploadedAt: 'DESC' },
      });

      // Gerar URLs temporárias para as fotos
      for (const photo of photos) {
        photo.url = await this.storageProvider.getFileUrl(photo.url);
      }

      return photos;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding photos for folder ${folderId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar fotos da pasta');
    }
  }

  async findOnePhoto(id: string): Promise<PatientPhoto> {
    try {
      this.logger.log(`Finding photo with id ${id}`);

      const photo = await this.patientPhotoRepository.findOne({
        where: { id },
        relations: ['folder'],
      });

      if (!photo) {
        throw new NotFoundException(`Foto com ID ${id} não encontrada`);
      }

      // Gerar URL temporária para a foto
      photo.url = await this.storageProvider.getFileUrl(photo.url);

      return photo;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding photo with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar foto');
    }
  }

  async movePhoto(
    id: string,
    movePhotoDto: MovePhotoDto,
  ): Promise<PatientPhoto> {
    try {
      this.logger.log(
        `Moving photo ${id} to folder ${movePhotoDto.folderId || 'none'}`,
      );

      // Verificar se a foto existe
      const photo = await this.patientPhotoRepository.findOne({
        where: { id },
        relations: ['patient'],
      });

      if (!photo) {
        throw new NotFoundException(`Foto com ID ${id} não encontrada`);
      }

      // Verificar se a pasta existe (se fornecida)
      if (movePhotoDto.folderId) {
        const folder = await this.photoFolderRepository.findOne({
          where: { id: movePhotoDto.folderId, patientId: photo.patientId },
        });

        if (!folder) {
          throw new NotFoundException(
            `Pasta com ID ${movePhotoDto.folderId} não encontrada para o paciente ${photo.patientId}`,
          );
        }
      }

      // Atualizar a pasta da foto
      photo.folderId = movePhotoDto.folderId;

      // Salvar a foto
      const updatedPhoto = await this.patientPhotoRepository.save(photo);

      // Gerar URL temporária para a foto
      updatedPhoto.url = await this.storageProvider.getFileUrl(
        updatedPhoto.url,
      );

      return updatedPhoto;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error moving photo ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao mover foto');
    }
  }

  async removePhoto(id: string): Promise<void> {
    try {
      this.logger.log(`Removing photo with id ${id}`);

      // Verificar se a foto existe
      const photo = await this.patientPhotoRepository.findOne({
        where: { id },
      });

      if (!photo) {
        throw new NotFoundException(`Foto com ID ${id} não encontrada`);
      }

      // Remover o arquivo do MinIO
      await this.storageProvider.deleteFile(photo.url);

      // Remover a foto do banco de dados
      await this.patientPhotoRepository.remove(photo);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing photo ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao remover foto');
    }
  }

  // Métodos para pastas
  async createFolder(
    createPhotoFolderDto: CreatePhotoFolderDto,
  ): Promise<PhotoFolder> {
    try {
      this.logger.log(
        `Creating folder for patient ${createPhotoFolderDto.patientId}`,
      );

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: createPhotoFolderDto.patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${createPhotoFolderDto.patientId} não encontrado`,
        );
      }

      // Criar a pasta
      const folder = this.photoFolderRepository.create(createPhotoFolderDto);

      // Salvar a pasta
      return await this.photoFolderRepository.save(folder);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error creating folder: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao criar pasta');
    }
  }

  async findAllFoldersByPatient(patientId: number): Promise<PhotoFolder[]> {
    try {
      this.logger.log(`Finding all folders for patient ${patientId}`);

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Buscar todas as pastas do paciente
      return await this.photoFolderRepository.find({
        where: { patientId },
        order: { name: 'ASC' },
      });
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding folders for patient ${patientId}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao buscar pastas do paciente',
      );
    }
  }

  async findOneFolder(id: string): Promise<PhotoFolder> {
    try {
      this.logger.log(`Finding folder with id ${id}`);

      const folder = await this.photoFolderRepository.findOne({
        where: { id },
      });

      if (!folder) {
        throw new NotFoundException(`Pasta com ID ${id} não encontrada`);
      }

      return folder;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error finding folder with id ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar pasta');
    }
  }

  async updateFolder(
    id: string,
    updatePhotoFolderDto: UpdatePhotoFolderDto,
  ): Promise<PhotoFolder> {
    try {
      this.logger.log(`Updating folder with id ${id}`);

      // Verificar se a pasta existe
      const folder = await this.photoFolderRepository.findOne({
        where: { id },
      });

      if (!folder) {
        throw new NotFoundException(`Pasta com ID ${id} não encontrada`);
      }

      // Atualizar a pasta
      this.photoFolderRepository.merge(folder, updatePhotoFolderDto);

      // Salvar a pasta
      return await this.photoFolderRepository.save(folder);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error updating folder ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao atualizar pasta');
    }
  }

  async removeFolder(id: string): Promise<void> {
    try {
      this.logger.log(`Removing folder with id ${id}`);

      // Verificar se a pasta existe
      const folder = await this.photoFolderRepository.findOne({
        where: { id },
        relations: ['photos'],
      });

      if (!folder) {
        throw new NotFoundException(`Pasta com ID ${id} não encontrada`);
      }

      // Atualizar as fotos da pasta para não terem pasta
      if (folder.photos && folder.photos.length > 0) {
        await this.patientPhotoRepository.update(
          { folderId: id },
          { folderId: null },
        );
      }

      // Remover a pasta
      await this.photoFolderRepository.remove(folder);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `Error removing folder ${id}: ${errorInfo.message}`,
        errorInfo.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao remover pasta');
    }
  }

  // Novos métodos para upload múltiplo e funcionalidades avançadas
  async uploadMultiplePhotos(
    files: Express.Multer.File[],
    patientId: number,
    folderId?: string,
    photosInfo?: Array<{ caption?: string; order?: number }>,
  ): Promise<PatientPhoto[]> {
    try {
      this.logger.log(
        `Uploading ${files.length} photos for patient ${patientId}${
          folderId ? ` to folder ${folderId}` : ''
        }`,
      );

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Verificar se a pasta existe (se fornecida)
      let folder: PhotoFolder | null = null;
      if (folderId) {
        folder = await this.photoFolderRepository.findOne({
          where: { id: folderId, patientId },
        });

        if (!folder) {
          throw new NotFoundException(
            `Pasta com ID ${folderId} não encontrada para o paciente ${patientId}`,
          );
        }
      }

      // Obter a próxima ordem disponível
      const maxOrder = (await this.patientPhotoRepository
        .createQueryBuilder('photo')
        .select('MAX(photo.order)', 'maxOrder')
        .where('photo.patientId = :patientId', { patientId })
        .andWhere(
          folderId ? 'photo.folderId = :folderId' : 'photo.folderId IS NULL',
          { folderId },
        )
        .getRawOne()) as { maxOrder: number | null };
      let nextOrder = (maxOrder?.maxOrder || 0) + 1;

      const savedPhotos: PatientPhoto[] = [];

      // Processar cada arquivo
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const photoInfo = photosInfo?.[i] || {};

        // Fazer upload do arquivo para o MinIO
        const filePath = await this.storageProvider.uploadFile(
          file,
          `patient-photos/${patientId}`,
        );

        // Decodificar o nome do arquivo
        const decodedFileName = Buffer.from(
          file.originalname,
          'latin1',
        ).toString('utf8');

        // Determinar a ordem
        const finalOrder =
          photoInfo.order !== undefined ? photoInfo.order : nextOrder++;

        // Criar a entrada no banco de dados
        const photo = this.patientPhotoRepository.create({
          url: filePath,
          filename: decodedFileName,
          caption: photoInfo.caption,
          order: finalOrder,
          patientId,
          folderId: folder?.id || null,
        });

        // Salvar a foto
        const savedPhoto = await this.patientPhotoRepository.save(photo);

        // Gerar URL temporária para acesso à foto
        const url = await this.storageProvider.getFileUrl(filePath);
        savedPhoto.url = url;

        savedPhotos.push(savedPhoto);
      }

      return savedPhotos;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error uploading multiple photos: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao fazer upload das fotos');
    }
  }

  async uploadMultipleCameraPhotos(multipleCameraPhotoDto: {
    patientId: number;
    folderId?: string;
    photos: Array<{ imageBase64: string; caption?: string; order?: number }>;
  }): Promise<PatientPhoto[]> {
    try {
      const { patientId, folderId, photos } = multipleCameraPhotoDto;

      this.logger.log(
        `Uploading ${photos.length} camera photos for patient ${patientId}${
          folderId ? ` to folder ${folderId}` : ''
        }`,
      );

      // Verificar se o paciente existe
      const patient = await this.patientRepository.findOne({
        where: { id: patientId },
      });

      if (!patient) {
        throw new NotFoundException(
          `Paciente com ID ${patientId} não encontrado`,
        );
      }

      // Verificar se a pasta existe (se fornecida)
      let folder: PhotoFolder | null = null;
      if (folderId) {
        folder = await this.photoFolderRepository.findOne({
          where: { id: folderId, patientId },
        });

        if (!folder) {
          throw new NotFoundException(
            `Pasta com ID ${folderId} não encontrada para o paciente ${patientId}`,
          );
        }
      }

      // Obter a próxima ordem disponível
      const maxOrder = (await this.patientPhotoRepository
        .createQueryBuilder('photo')
        .select('MAX(photo.order)', 'maxOrder')
        .where('photo.patientId = :patientId', { patientId })
        .andWhere(
          folderId ? 'photo.folderId = :folderId' : 'photo.folderId IS NULL',
          { folderId },
        )
        .getRawOne()) as { maxOrder: number | null };
      let nextOrder = (maxOrder?.maxOrder || 0) + 1;

      const savedPhotos: PatientPhoto[] = [];

      // Processar cada foto
      for (const photoData of photos) {
        // Extrair o tipo de imagem e os dados do base64
        const matches = photoData.imageBase64.match(
          /^data:([A-Za-z-+/]+);base64,(.+)$/,
        );
        if (!matches || matches.length !== 3) {
          throw new BadRequestException('Formato de imagem base64 inválido');
        }

        const type = matches[1];
        const data = Buffer.from(matches[2], 'base64');

        // Criar um objeto de arquivo para o MinIO
        const file: Express.Multer.File = {
          buffer: data,
          originalname: `camera_${Date.now()}_${Math.random().toString(36).substring(2, 11)}.${type.split('/')[1] || 'jpg'}`,
          mimetype: type,
          size: data.length,
          fieldname: 'file',
          encoding: '7bit',
          destination: '',
          filename: '',
          path: '',
          stream: new Readable({
            read() {
              this.push(data);
              this.push(null);
            },
          }),
        };

        // Fazer upload do arquivo para o MinIO
        const filePath = await this.storageProvider.uploadFile(
          file,
          `patient-photos/${patientId}`,
        );

        // Decodificar o nome do arquivo
        const decodedFileName = Buffer.from(
          file.originalname,
          'latin1',
        ).toString('utf8');

        // Determinar a ordem
        const finalOrder =
          photoData.order !== undefined ? photoData.order : nextOrder++;

        // Criar a entrada no banco de dados
        const photo = this.patientPhotoRepository.create({
          url: filePath,
          filename: decodedFileName,
          caption: photoData.caption,
          order: finalOrder,
          patientId,
          folderId: folder?.id || null,
        });

        // Salvar a foto
        const savedPhoto = await this.patientPhotoRepository.save(photo);

        // Gerar URL temporária para acesso à foto
        const url = await this.storageProvider.getFileUrl(filePath);
        savedPhoto.url = url;

        savedPhotos.push(savedPhoto);
      }

      return savedPhotos;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error uploading multiple camera photos: ${errorMessage}`,
        errorStack,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Erro ao fazer upload das fotos da câmera',
      );
    }
  }

  async updatePhoto(
    id: string,
    updatePhotoDto: { caption?: string; order?: number },
  ): Promise<PatientPhoto> {
    try {
      this.logger.log(`Updating photo ${id}`);

      // Verificar se a foto existe
      const photo = await this.patientPhotoRepository.findOne({
        where: { id },
      });

      if (!photo) {
        throw new NotFoundException(`Foto com ID ${id} não encontrada`);
      }

      // Atualizar os campos fornecidos
      if (updatePhotoDto.caption !== undefined) {
        photo.caption = updatePhotoDto.caption;
      }
      if (updatePhotoDto.order !== undefined) {
        photo.order = updatePhotoDto.order;
      }

      // Salvar a foto atualizada
      const updatedPhoto = await this.patientPhotoRepository.save(photo);

      // Gerar URL temporária para a foto
      updatedPhoto.url = await this.storageProvider.getFileUrl(
        updatedPhoto.url,
      );

      return updatedPhoto;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating photo ${id}: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao atualizar foto');
    }
  }

  async reorderPhotos(reorderPhotosDto: {
    photos: Array<{ photoId: string; order: number }>;
  }): Promise<PatientPhoto[]> {
    try {
      this.logger.log(`Reordering ${reorderPhotosDto.photos.length} photos`);

      const updatedPhotos: PatientPhoto[] = [];

      // Atualizar a ordem de cada foto
      for (const photoOrder of reorderPhotosDto.photos) {
        const photo = await this.patientPhotoRepository.findOne({
          where: { id: photoOrder.photoId },
        });

        if (!photo) {
          throw new NotFoundException(
            `Foto com ID ${photoOrder.photoId} não encontrada`,
          );
        }

        photo.order = photoOrder.order;
        const updatedPhoto = await this.patientPhotoRepository.save(photo);

        // Gerar URL temporária para a foto
        updatedPhoto.url = await this.storageProvider.getFileUrl(
          updatedPhoto.url,
        );

        updatedPhotos.push(updatedPhoto);
      }

      return updatedPhotos;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error reordering photos: ${errorMessage}`, errorStack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao reordenar fotos');
    }
  }
}
