import { <PERSON>, Post, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { AgentResponseDto, AgentStatusDto } from './dto/agent-response.dto';

@ApiTags('agents')
@Controller('agents')
export class AgentsController {
  private readonly logger = new Logger(AgentsController.name);

  constructor(private readonly agentsService: AgentsService) {}

  @ApiOperation({
    summary: 'Executar agente de triagem',
    description:
      'Executa o agente de triagem para identificar pacientes e criar sugestões iniciais',
  })
  @ApiResponse({
    status: 201,
    description: 'Agente de triagem executado com sucesso',
    type: AgentResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao executar agente de triagem',
  })
  @Post('triage')
  async executeTriageAgent(): Promise<AgentResponseDto> {
    this.logger.log('Endpoint POST /agents/triage chamado');
    return this.agentsService.executeTriageAgent();
  }

  @ApiOperation({
    summary: 'Executar agente odontológico',
    description:
      'Executa o agente odontológico para processar sugestões existentes com IA',
  })
  @ApiResponse({
    status: 201,
    description: 'Agente odontológico executado com sucesso',
    type: AgentResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao executar agente odontológico',
  })
  @Post('odontology')
  async executeOdontologyAgent(): Promise<AgentResponseDto> {
    this.logger.log('Endpoint POST /agents/odontology chamado');
    return this.agentsService.executeOdontologyAgent();
  }

  @ApiOperation({
    summary: 'Executar agentes combinados',
    description:
      'Executa ambos os agentes (triagem + odontológico) em sequência',
  })
  @ApiResponse({
    status: 201,
    description: 'Agentes combinados executados com sucesso',
    type: AgentResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao executar agentes combinados',
  })
  @Post('combined')
  async executeCombinedAgents(): Promise<AgentResponseDto> {
    this.logger.log('Endpoint POST /agents/combined chamado');
    return this.agentsService.executeCombinedAgents();
  }

  @ApiOperation({
    summary: 'Status do agente de triagem',
    description: 'Verifica o status atual do agente de triagem',
  })
  @ApiResponse({
    status: 200,
    description: 'Status do agente de triagem retornado com sucesso',
    type: AgentStatusDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao verificar status',
  })
  @Get('status/triage')
  async getTriageAgentStatus(): Promise<AgentStatusDto> {
    this.logger.log('Endpoint GET /agents/status/triage chamado');
    return this.agentsService.getTriageAgentStatus();
  }

  @ApiOperation({
    summary: 'Status do agente odontológico',
    description: 'Verifica o status atual do agente odontológico',
  })
  @ApiResponse({
    status: 200,
    description: 'Status do agente odontológico retornado com sucesso',
    type: AgentStatusDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao verificar status',
  })
  @Get('status/odontology')
  async getOdontologyAgentStatus(): Promise<AgentStatusDto> {
    this.logger.log('Endpoint GET /agents/status/odontology chamado');
    return this.agentsService.getOdontologyAgentStatus();
  }

  @ApiOperation({
    summary: 'Status geral dos agentes',
    description: 'Verifica o status geral dos agentes de sugestões',
  })
  @ApiResponse({
    status: 200,
    description: 'Status geral dos agentes retornado com sucesso',
    type: AgentStatusDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor ao verificar status',
  })
  @Get('status')
  async getSuggestionsAgentStatus(): Promise<AgentStatusDto> {
    this.logger.log('Endpoint GET /agents/status chamado');
    return this.agentsService.getSuggestionsAgentStatus();
  }
}
