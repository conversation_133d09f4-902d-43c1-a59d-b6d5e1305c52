import { ApiProperty } from '@nestjs/swagger';

export class AgentResponseDto {
  @ApiProperty({
    description: 'Mensagem de confirmação da execução do agente',
    example: 'Agente de triagem iniciado com sucesso',
  })
  message: string;
}

export class AgentStatusDto {
  @ApiProperty({
    description: 'Status atual do agente',
    example: 'running',
    enum: ['idle', 'running', 'completed', 'error'],
  })
  status: string;
}
