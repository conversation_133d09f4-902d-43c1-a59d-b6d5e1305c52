import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AgentResponseDto, AgentStatusDto } from './dto/agent-response.dto';

@Injectable()
export class AgentsService {
  private readonly logger = new Logger(AgentsService.name);
  private readonly automationBaseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    // URL base do projeto de automação
    this.automationBaseUrl =
      this.configService.get<string>('AUTOMATION_URL') ||
      'http://automacao:8000';
    this.logger.log(`Automation service URL: ${this.automationBaseUrl}`);
  }

  /**
   * Executa o agente de triagem
   */
  async executeTriageAgent(): Promise<AgentResponseDto> {
    try {
      this.logger.log('Executando agente de triagem...');

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.automationBaseUrl}/executar/suggestions/triagem`,
        ),
      );

      this.logger.log('Agente de triagem executado com sucesso');
      return response.data;
    } catch (error) {
      this.logger.error('Erro ao executar agente de triagem:', error.message);
      throw new HttpException(
        'Erro ao executar agente de triagem',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Executa o agente odontológico
   */
  async executeOdontologyAgent(): Promise<AgentResponseDto> {
    try {
      this.logger.log('Executando agente odontológico...');

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.automationBaseUrl}/executar/suggestions/odontologico`,
        ),
      );

      this.logger.log('Agente odontológico executado com sucesso');
      return response.data;
    } catch (error) {
      this.logger.error('Erro ao executar agente odontológico:', error.message);
      throw new HttpException(
        'Erro ao executar agente odontológico',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Executa ambos os agentes (triagem + odontológico)
   */
  async executeCombinedAgents(): Promise<AgentResponseDto> {
    try {
      this.logger.log(
        'Executando agentes combinados (triagem + odontológico)...',
      );

      const response = await firstValueFrom(
        this.httpService.post(`${this.automationBaseUrl}/executar/suggestions`),
      );

      this.logger.log('Agentes combinados executados com sucesso');
      return response.data;
    } catch (error) {
      this.logger.error('Erro ao executar agentes combinados:', error.message);
      throw new HttpException(
        'Erro ao executar agentes combinados',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Verifica o status do agente de triagem
   */
  async getTriageAgentStatus(): Promise<AgentStatusDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.automationBaseUrl}/status/suggestions/triagem`,
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        'Erro ao verificar status do agente de triagem:',
        error.message,
      );
      throw new HttpException(
        'Erro ao verificar status do agente de triagem',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Verifica o status do agente odontológico
   */
  async getOdontologyAgentStatus(): Promise<AgentStatusDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.automationBaseUrl}/status/suggestions/odontologico`,
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        'Erro ao verificar status do agente odontológico:',
        error.message,
      );
      throw new HttpException(
        'Erro ao verificar status do agente odontológico',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Verifica o status geral dos agentes de sugestões
   */
  async getSuggestionsAgentStatus(): Promise<AgentStatusDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.automationBaseUrl}/status/suggestions`),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        'Erro ao verificar status dos agentes de sugestões:',
        error.message,
      );
      throw new HttpException(
        'Erro ao verificar status dos agentes de sugestões',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
