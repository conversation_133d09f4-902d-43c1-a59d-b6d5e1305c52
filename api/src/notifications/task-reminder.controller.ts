/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Controller, Post } from '@nestjs/common';
import { TaskReminderService } from './task-reminder.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('task-reminder-test')
@Controller('task-reminder-test')
export class TaskReminderTestController {
  constructor(private readonly taskReminderService: TaskReminderService) {}

  @Post('due-today')
  @ApiOperation({
    summary: 'Verificar tarefas que vencem hoje',
    description:
      'Executa manualmente a verificação de tarefas que vencem hoje e envia notificações aos funcionários responsáveis',
  })
  @ApiResponse({
    status: 200,
    description: 'Verificação executada com sucesso',
  })
  runCheckTasksDueToday() {
    return this.taskReminderService.checkTasksDueToday();
  }

  @Post('due-soon')
  @ApiOperation({
    summary: 'Verificar tarefas que vencem em breve',
    description:
      'Executa manualmente a verificação de tarefas que vencem em 3 dias e envia notificações aos funcionários responsáveis',
  })
  @ApiResponse({
    status: 200,
    description: 'Verificação executada com sucesso',
  })
  runCheckTasksDueSoon() {
    return this.taskReminderService.checkTasksDueSoon();
  }

  @Post('overdue')
  @ApiOperation({
    summary: 'Verificar tarefas atrasadas',
    description:
      'Executa manualmente a verificação de tarefas atrasadas e envia notificações aos funcionários responsáveis',
  })
  @ApiResponse({
    status: 200,
    description: 'Verificação executada com sucesso',
  })
  runCheckOverdueTasks() {
    return this.taskReminderService.checkOverdueTasks();
  }
}
