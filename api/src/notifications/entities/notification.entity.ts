import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Employee } from '../../employees/entities/employee.entity';
import { Task } from '../../tasks/entities/task.entity';
import { NotificationType } from '../enums/notification-type.enum';

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: 'ID único da notificação' })
  id: string;

  @Column()
  @ApiProperty({ description: 'Título da notificação' })
  title: string;

  @Column('text')
  @ApiProperty({ description: 'Mensagem da notificação' })
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.GENERAL,
  })
  @ApiProperty({
    description: 'Tipo da notificação',
    enum: NotificationType,
    default: NotificationType.GENERAL,
  })
  type: NotificationType;

  @Column({ default: false })
  @ApiProperty({
    description: 'Indica se a notificação foi lida',
    default: false,
  })
  read: boolean;

  @ManyToOne(() => Employee)
  @ApiProperty({ description: 'Funcionário destinatário' })
  employee: Employee;

  @ManyToOne(() => Task, {
    nullable: true,
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  @ApiProperty({ description: 'Tarefa relacionada', required: false })
  task: Task;

  @CreateDateColumn({ name: 'created_at' })
  @ApiProperty({ description: 'Data de criação da notificação' })
  createdAt: Date;
}
