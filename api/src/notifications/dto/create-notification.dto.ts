import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOption<PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../enums/notification-type.enum';

export class CreateNotificationDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Título da notificação' })
  title: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Mensagem da notificação' })
  message: string;

  @IsEnum(NotificationType)
  @ApiProperty({
    description: 'Tipo da notificação',
    enum: NotificationType,
    default: NotificationType.GENERAL,
  })
  type: NotificationType;

  @IsNotEmpty()
  @IsUUID()
  @ApiProperty({ description: 'ID do funcionário destinatário' })
  employee: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    description: 'ID da tarefa relacionada (opcional)',
    required: false,
  })
  task?: string;
}
