/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Controller, Get, Post, Body, Patch, Param } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { Notification } from './entities/notification.entity';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @ApiOperation({ summary: 'Criar uma nova notificação' })
  @ApiResponse({
    status: 201,
    description: 'Notificação criada com sucesso',
    type: Notification,
  })
  create(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationsService.create(createNotificationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas as notificações' })
  @ApiResponse({
    status: 200,
    description: 'Lista de notificações retornada com sucesso',
  })
  findAll() {
    return this.notificationsService.findAll();
  }

  @Get(':employeeId')
  @ApiOperation({ summary: 'Listar notificações de um funcionário' })
  @ApiParam({ name: 'employeeId', description: 'ID do funcionário' })
  @ApiQuery({ name: 'unreadOnly', required: false, type: Boolean })
  findByEmployee(@Param('employeeId') employeeId: string) {
    return this.notificationsService.findByEmployee(employeeId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma notificação pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da notificação' })
  findOne(@Param('id') id: string) {
    return this.notificationsService.findOne(id);
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Marcar uma notificação como lida' })
  @ApiParam({ name: 'id', description: 'ID da notificação' })
  markAsRead(@Param('id') id: string) {
    return this.notificationsService.markAsRead(id);
  }

  @Patch(':employeeId/read-all')
  @ApiOperation({
    summary: 'Marcar todas as notificações de um funcionário como lidas',
  })
  @ApiParam({ name: 'employeeId', description: 'ID do funcionário' })
  markAllAsReadUser(@Param('employeeId') employeeId: string) {
    return this.notificationsService.markAllAsReadByEmployee(employeeId);
  }

  @Patch('read-all')
  @ApiOperation({
    summary: 'Marcar todas as notificações de um funcionário como lidas',
  })
  markAllAsRead() {
    return this.notificationsService.markAllAsRead();
  }
}
