/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { Notification } from './entities/notification.entity';
import { NotificationType } from './enums/notification-type.enum';
import { Task } from '../tasks/entities/task.entity';
import { NotificationMapperImpl } from './mappers/notification.mapper-impl';
import { Employee } from '../employees/entities/employee.entity';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationsRepository: Repository<Notification>,
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    private notificationImpl: NotificationMapperImpl,
  ) {}

  async create(
    createNotificationDto: CreateNotificationDto,
  ): Promise<Notification> {
    try {
      const employee = await this.employeeRepository.findOneByOrFail({
        id: createNotificationDto.employee,
      });

      const notification = this.notificationImpl.toNotificationEntity(
        createNotificationDto,
      );

      const task = await this.taskRepository.findOneBy({
        id: createNotificationDto.task,
      });

      if (task) {
        notification.task = task;
      }
      notification.employee = employee;

      const response = await this.notificationsRepository.save(notification);
      return response;
    } catch {
      throw new Error();
    }
  }

  async findAll(): Promise<Notification[]> {
    return this.notificationsRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Notification | undefined> {
    const notification = await this.notificationsRepository.findOne({
      where: { id },
    });

    return notification || undefined;
  }

  async findByEmployee(employeeId: string): Promise<Notification[]> {
    const query = this.notificationsRepository
      .createQueryBuilder('notification')
      .where('notification.employee = :employeeId', { employeeId })
      .orderBy('notification.createdAt', 'DESC');

    return query.getMany();
  }

  async markAsRead(id: string): Promise<{ count: number }> {
    const result = await this.notificationsRepository.update(id, {
      read: true,
    });
    return { count: result.affected || 0 };
  }

  async markAllAsReadByEmployee(
    employeeId: string,
  ): Promise<{ count: number }> {
    const notifications = await this.notificationsRepository.find({
      where: { employee: { id: employeeId }, read: false },
      relations: ['employee'],
    });
    let result = 0;

    for (const notication of notifications) {
      if (notication.employee.id == employeeId) {
        await this.notificationsRepository.update(
          { id: notication.id },
          { read: true },
        );
        result++;
      }
    }
    return { count: result };
  }

  async markAllAsRead(): Promise<{ count: number }> {
    const notifications = await this.notificationsRepository.find({
      where: { read: false },
      relations: ['employee'],
    });
    let result = 0;

    for (const notication of notifications) {
      await this.notificationsRepository.update(
        { id: notication.id },
        { read: true },
      );
      result++;
    }
    return { count: result };
  }

  async createTaskAssignedNotification(task: Task): Promise<Notification> {
    return this.create({
      title: `Nova Tarefa: ${task.title}`,
      message: `Você recebeu uma nova tarefa, verifique o painel de demandas para mais detalhes.`,
      type: NotificationType.TASK_ASSIGNED,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async createTaskReminderNotification(
    task: Task,
    daysRemaining: number,
  ): Promise<Notification> {
    const message =
      'Fique atento em relação ao prazo das atividades. ' +
      (daysRemaining === 0)
        ? `A tarefa "${task.title}" vence hoje!`
        : `A tarefa "${task.title}" vence em ${daysRemaining} dias.`;

    return this.create({
      title: `Lembrete: Prazo da tarefa está chegando ao fim`,
      message,
      type: NotificationType.TASK_REMINDER,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async createTaskReassignmentNotification(task: Task): Promise<Notification> {
    const message = `Você foi removido dessa atividade. Verifique o painel para identificar suas demandas ativas!`;

    return this.create({
      title: `Tarefa Atualizada: ${task.title}`,
      message,
      type: NotificationType.TASK_UPDATED,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async createTaskCompletedNotification(task: Task) {
    return await this.create({
      title: `Tarefa Finalizada: ${task.title}`,
      message: `A tarefa foi concluída.`,
      type: NotificationType.TASK_COMPLETED,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async createTaskCancelledNotification(task: Task) {
    return await this.create({
      title: `Tarefa Cancelada: ${task.title}`,
      message: `A tarefa foi cancelada. Contate seu supervisor para novas instruções.`,
      type: NotificationType.TASK_COMPLETED,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async createOverdueTaskNotification(task: Task): Promise<Notification> {
    return await this.create({
      title: `Tarefa Atrasada: ${task.title}`,
      message: `A tarefa está com entrega atrasada e precisa de atenção. Contate seu supervisor para novas instruções.`,
      type: NotificationType.TASK_REMINDER,
      employee: task.employee.id,
      task: task.id,
    });
  }

  async updateTaskNotification(task: Task): Promise<Notification> {
    const message = `A tarefa foi atualizada. Verifique o painel de demandas para mais informações!`;

    return this.create({
      title: `Tarefa Atualizada: ${task.title}`,
      message,
      type: NotificationType.TASK_UPDATED,
      employee: task.employee.id,
      task: task.id,
    });
  }
}
