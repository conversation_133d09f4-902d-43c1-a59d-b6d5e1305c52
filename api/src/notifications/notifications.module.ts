// // Importa interceptor radical para crypto ANTES de qualquer outro código
// import * as path from 'path';
// try {
//   // eslint-disable-next-line @typescript-eslint/no-require-imports
//   require(path.join(__dirname, '../crypto-interceptor.js'));
// } catch (error: unknown) {
//   const errorMessage =
//     error instanceof Error ? error.message : 'Erro desconhecido';
//   console.warn(
//     'Crypto interceptor not found in notifications module, continuing without it:',
//     errorMessage,
//   );
// }

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { Notification } from './entities/notification.entity';
import { TaskReminderService } from './task-reminder.service';
import { Task } from '../tasks/entities/task.entity';
import { EmployeesModule } from '../employees/employees.module';
import { Employee } from '../employees/entities/employee.entity';
import { NotificationMapperImpl } from './mappers/notification.mapper-impl';
import { TaskReminderTestController } from './task-reminder.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notification, Task, Employee]),
    ScheduleModule.forRoot(),
    EmployeesModule,
  ],
  controllers: [NotificationsController, TaskReminderTestController],
  providers: [
    NotificationsService,
    TaskReminderService,
    NotificationMapperImpl,
  ],
  exports: [NotificationsService],
})
export class NotificationsModule {}
