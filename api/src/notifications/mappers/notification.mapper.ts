import { CreateNotificationDto } from '../dto/create-notification.dto';
import { NotificationResponseDto } from '../dto/notification-response.dto';
import { Notification } from '../entities/notification.entity';

export abstract class NotificationMapper {
  abstract toNotificationResponse(
    notification: Notification,
  ): NotificationResponseDto;
  abstract toNotificationEntity(alunoDto: CreateNotificationDto): Notification;
}
