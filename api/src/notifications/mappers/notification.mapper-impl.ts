import { Injectable } from '@nestjs/common';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { NotificationResponseDto } from '../dto/notification-response.dto';
import { Notification } from '../entities/notification.entity';
import { NotificationMapper } from './notification.mapper';

@Injectable()
export class NotificationMapperImpl implements NotificationMapper {
  toNotificationResponse(notification: Notification): NotificationResponseDto {
    const notificationResponse = new NotificationResponseDto();
    notificationResponse.id = notification.id;
    notificationResponse.title = notification.title;
    notificationResponse.type = notification.type;
    notificationResponse.message = notification.message;
    notificationResponse.read = notification.read;
    notificationResponse.employee = notification.employee;
    notificationResponse.task = notification.task;

    return notificationResponse;
  }

  toNotificationEntity(notificationDto: CreateNotificationDto): Notification {
    const notification = new Notification();
    notification.title = notificationDto.title;
    notification.type = notificationDto.type;
    notification.message = notificationDto.message;
    notification.read = false;

    return notification;
  }
}
