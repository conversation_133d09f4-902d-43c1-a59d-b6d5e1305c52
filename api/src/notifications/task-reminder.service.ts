/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, Between } from 'typeorm';
import { Task } from '../tasks/entities/task.entity';
import { NotificationsService } from './notifications.service';
import { addDays, startOfDay, endOfDay, format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

@Injectable()
export class TaskReminderService {
  private readonly logger = new Logger(TaskReminderService.name);
  private readonly timeZone = 'America/Sao_Paulo';

  constructor(
    @InjectRepository(Task)
    private tasksRepository: Repository<Task>,
    private notificationsService: NotificationsService,
  ) {}

  // Executa todos os dias às 7:00 (ho<PERSON><PERSON><PERSON>)
  @Cron(CronExpression.EVERY_DAY_AT_7AM, {
    timeZone: 'America/Sao_Paulo',
  })
  async checkTasksDueToday() {
    const tasks = await this.tasksRepository.find({
      where: {
        dueDate: Between(this.getStartOfToday(), this.getEndOfToday()),
        completedAt: undefined,
      },
      relations: ['employee'],
    });

    this.logger.log(`Encontradas ${tasks.length} tarefas que vencem hoje`);
    const assignedTasks = tasks.filter((task) => task.employee);

    for (const task of assignedTasks) {
      if (task.employee) {
        await this.notificationsService.createTaskReminderNotification(
          task,
          0, // 0 dias restantes (vence hoje)
        );
      }
    }
  }

  // Executa todos os dias às 7:00 (horário de Brasília)
  @Cron(CronExpression.EVERY_DAY_AT_7AM, {
    timeZone: 'America/Sao_Paulo',
  })
  async checkTasksDueSoon() {
    const tasks = await this.tasksRepository.find({
      where: {
        dueDate: Between(this.getStartOfToday(), this.getEndOfToday(3)),
        completedAt: undefined,
      },
      relations: ['employee'],
    });

    this.logger.log(`Encontradas ${tasks.length} tarefas que vencem em 3 dias`);
    const assignedTasks = tasks.filter((task) => task.employee);

    for (const task of assignedTasks) {
      if (task.employee) {
        await this.notificationsService.createTaskReminderNotification(
          task,
          3, // 3 dias restantes
        );
      }
    }
  }

  // Executa todos os dias às 8:00 (horário de Brasília) para verificar tarefas atrasadas
  @Cron(CronExpression.EVERY_DAY_AT_8AM, {
    timeZone: 'America/Sao_Paulo',
  })
  async checkOverdueTasks() {
    const endOfYesterday = this.getEndOfToday(-1);

    this.logger.log(
      `Data limite para tarefas atrasadas: ${format(endOfYesterday, 'yyyy-MM-dd HH:mm:ss')}`,
    );

    const tasks = await this.tasksRepository.find({
      where: {
        dueDate: LessThanOrEqual(endOfYesterday),
        completedAt: undefined,
      },
      relations: ['employee'],
    });

    this.logger.log(`Encontradas ${tasks.length} tarefas atrasadas`);
    const assignedTasks = tasks.filter((task) => task.employee);

    for (const task of assignedTasks) {
      if (task.employee) {
        await this.notificationsService.createOverdueTaskNotification(task);
      }
    }
  }

  private getStartOfToday(day: number = 0): Date {
    // Obter a data atual no fuso horário do Brasil e retorna o fim do dia 00:00
    const now = new Date();
    const brazilTime = addDays(toZonedTime(now, this.timeZone), day);

    return startOfDay(brazilTime);
  }

  private getEndOfToday(day: number = 0): Date {
    // Obter a data atual no fuso horário do Brasil e retorna o fim do dia 23:59
    const now = new Date();
    const brazilTime = addDays(toZonedTime(now, this.timeZone), day);
    return endOfDay(brazilTime);
  }
}
