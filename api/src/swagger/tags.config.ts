import { OpenAPIObject } from '@nestjs/swagger';

export class SwaggerTagsConfig {
  static configureTags(document: OpenAPIObject): OpenAPIObject {
    document.tags = [
      {
        name: 'agents',
        description: 'Operações relacionadas aos agentes de automação',
      },
      { name: 'anamnesis', description: 'Operações relacionadas a anamnese' },
      {
        name: 'appointments',
        description: 'Operações relacionadas a agendamentos',
      },
      {
        name: 'appointment-categories',
        description: 'Operações relacionadas a categorias de agendamento',
      },
      { name: 'budgets', description: 'Operações relacionadas a orçamentos' },
      { name: 'dentists', description: 'Operações relacionadas a dentistas' },
      {
        name: 'employees',
        description: 'Operações relacionadas a funcionários',
      },
      { name: 'holidays', description: 'Operações relacionadas a feriados' },
      {
        name: 'indications',
        description: 'Operações relacionadas a indicações',
      },
      {
        name: 'medical-records',
        description: 'Operações relacionadas a prontuários médicos',
      },
      { name: 'patients', description: 'Operações relacionadas a pacientes' },
      {
        name: 'patient-types',
        description: 'Operações relacionadas a tipos de paciente',
      },
      { name: 'photos', description: 'Operações relacionadas a fotos' },
      {
        name: 'procedures',
        description: 'Operações relacionadas a procedimentos',
      },
      { name: 'receipts', description: 'Operações relacionadas a recibos' },
      { name: 'reports', description: 'Operações relacionadas a relatórios' },
      {
        name: 'schedulings',
        description: 'Operações relacionadas a agendamentos',
      },
      {
        name: 'suggestions',
        description: 'Operações relacionadas a sugestões da IA',
      },
      {
        name: 'tasks',
        description: 'Operações relacionadas a demandas/tarefas',
      },
      {
        name: 'treatments',
        description: 'Operações relacionadas a tratamentos',
      },
      {
        name: 'treatment-plans',
        description: 'Operações relacionadas a planos de tratamento',
      },
    ].sort((a, b) => a.name.localeCompare(b.name)); // Ordenar alfabeticamente

    return document;
  }
}
