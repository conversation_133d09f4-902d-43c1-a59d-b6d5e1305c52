import { NestFactory } from '@nestjs/core';
import { SwaggerModule } from '@nestjs/swagger';
import { AppModule } from '../app.module';
import { SwaggerConfig } from './swagger.config';
import {
  writeFileSync,
  mkdirSync,
  existsSync,
  readFileSync,
  unlinkSync,
} from 'fs';
import { join } from 'path';
import { Logger } from '@nestjs/common';
import { formatErrorForLogging } from '../common/utils/error.utils';

/**
 * Gerador de documentação Swagger
 * Gera arquivos JSON e HTML da documentação
 */
class SwaggerGenerator {
  private readonly logger = new Logger(SwaggerGenerator.name);

  /**
   * Gera a documentação Swagger em formato JSON
   */
  async generateJson(outputPath?: string): Promise<void> {
    try {
      this.logger.log('📄 Gerando documentação JSON...');

      // Criar aplicação NestJS
      const app = await NestFactory.create(AppModule, {
        logger: false, // Desabilitar logs durante a geração
      });

      // Configurar Swagger
      const config = SwaggerConfig.createConfig();
      const document = SwaggerModule.createDocument(
        app,
        config,
        SwaggerConfig.getJsonConfig(),
      );

      // Definir caminho de saída
      const filePath = outputPath || join(process.cwd(), 'swagger.json');

      // Criar diretório se não existir
      const dir = join(filePath, '..');
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      // Escrever arquivo JSON
      writeFileSync(filePath, JSON.stringify(document, null, 2));

      this.logger.log(`✅ Documentação JSON gerada: ${filePath}`);

      // Fechar aplicação
      await app.close();
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `❌ Erro ao gerar documentação JSON: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  /**
   * Gera página HTML estática da documentação
   */
  async generateHtml(outputPath?: string): Promise<void> {
    try {
      this.logger.log('📄 Gerando documentação HTML...');

      // Primeiro gerar o JSON
      const jsonPath = join(process.cwd(), 'temp-swagger.json');
      await this.generateJson(jsonPath);

      // Ler o JSON gerado
      const swaggerJson = JSON.parse(readFileSync(jsonPath, 'utf8')) as Record<
        string,
        unknown
      >;

      // Definir caminho de saída
      const filePath = outputPath || join(process.cwd(), 'swagger-ui.html');

      // Gerar HTML
      const htmlContent = this.generateHtmlContent(swaggerJson);
      writeFileSync(filePath, htmlContent);

      this.logger.log(`✅ Documentação HTML gerada: ${filePath}`);

      // Limpar arquivo temporário
      if (existsSync(jsonPath)) {
        unlinkSync(jsonPath);
      }
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `❌ Erro ao gerar documentação HTML: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  /**
   * Gera o conteúdo HTML da documentação
   */
  private generateHtmlContent(swaggerJson: Record<string, unknown>): string {
    return `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Odontológico - API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            display: none;
        }
        .swagger-ui .info .title {
            color: #1976d2;
        }
        .swagger-ui .info .description {
            margin: 20px 0;
        }
        .swagger-ui .scheme-container {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                spec: ${JSON.stringify(swaggerJson, null, 2)},
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                docExpansion: "none",
                defaultModelsExpandDepth: -1,
                filter: true,
                showRequestDuration: true,
                tryItOutEnabled: true
            });
        };
    </script>
</body>
</html>`;
  }

  /**
   * Gera toda a documentação (JSON + HTML)
   */
  async generateAll(outputDir?: string): Promise<void> {
    try {
      this.logger.log('🚀 Gerando documentação completa...');

      const baseDir = outputDir || process.cwd();

      // Gerar JSON
      await this.generateJson(join(baseDir, 'swagger.json'));

      // Gerar HTML
      await this.generateHtml(join(baseDir, 'swagger-ui.html'));

      this.logger.log('✅ Documentação completa gerada com sucesso!');
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `❌ Erro ao gerar documentação completa: ${errorInfo.message}`,
        errorInfo.stack,
      );
      throw error;
    }
  }

  /**
   * Valida a documentação gerada
   */
  async validate(): Promise<boolean> {
    try {
      this.logger.log('🔍 Validando documentação...');

      // Criar aplicação NestJS
      const app = await NestFactory.create(AppModule, {
        logger: false,
      });

      // Configurar Swagger
      const config = SwaggerConfig.createConfig();
      const document = SwaggerModule.createDocument(
        app,
        config,
        SwaggerConfig.getJsonConfig(),
      );

      // Validações básicas
      const isValid = Boolean(
        document.info &&
          document.info.title &&
          document.paths &&
          Object.keys(document.paths).length > 0,
      );

      if (isValid) {
        this.logger.log('✅ Documentação válida!');
        this.logger.log(
          `📊 Endpoints encontrados: ${Object.keys(document.paths).length}`,
        );
      } else {
        this.logger.error('❌ Documentação inválida!');
      }

      // Fechar aplicação
      await app.close();

      return isValid;
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `❌ Erro ao validar documentação: ${errorInfo.message}`,
        errorInfo.stack,
      );
      return false;
    }
  }
}

/**
 * Função para executar geração se chamado diretamente
 */
async function generateSwaggerDocs(): Promise<void> {
  const generator = new SwaggerGenerator();

  const command = process.argv[2];

  switch (command) {
    case 'json':
      await generator.generateJson();
      break;
    case 'html':
      await generator.generateHtml();
      break;
    case 'validate':
      await generator.validate();
      break;
    case 'all':
    default:
      await generator.generateAll();
      break;
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  generateSwaggerDocs().catch((error) => {
    console.error('💥 Falha na geração da documentação:', error);
    process.exit(1);
  });
}

export { SwaggerGenerator, generateSwaggerDocs };
