import { NestFactory } from '@nestjs/core';
import { SwaggerModule } from '@nestjs/swagger';
import { AppModule } from '../app.module';
import { SwaggerConfig } from './swagger.config';
import { Logger } from '@nestjs/common';
import { formatErrorForLogging } from '../common/utils/error.utils';

/**
 * Servidor dedicado para documentação Swagger
 * Permite executar apenas a documentação em uma porta separada
 */
class SwaggerServer {
  private readonly logger = new Logger(SwaggerServer.name);

  constructor(private readonly port = 3002) {}

  async start(): Promise<void> {
    try {
      this.logger.log('🚀 Iniciando servidor Swagger dedicado...');

      // Criar aplicação NestJS
      const app = await NestFactory.create(AppModule, {
        logger: ['error', 'warn', 'log'],
      });

      // Configurar CORS
      app.enableCors({
        origin: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        credentials: true,
      });

      // Configurar Swagger
      const config = SwaggerConfig.createConfig();
      const document = SwaggerModule.createDocument(
        app,
        config,
        SwaggerConfig.getJsonConfig(),
      );

      // Setup Swagger UI
      SwaggerModule.setup(
        'docs',
        app,
        document,
        SwaggerConfig.getSwaggerOptions(),
      );

      // Rota adicional para acessar o JSON do Swagger
      app
        .getHttpAdapter()
        .get(
          '/swagger.json',
          (_req: unknown, res: { json: (data: unknown) => void }) => {
            res.json(document);
          },
        );

      // Rota raiz com informações
      app
        .getHttpAdapter()
        .get('/', (_req: unknown, res: { send: (html: string) => void }) => {
          res.send(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>CRM Odontológico - API Documentation</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
              .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              h1 { color: #1976d2; margin-bottom: 20px; }
              .links { margin: 30px 0; }
              .links a { display: inline-block; margin: 10px 15px 10px 0; padding: 12px 24px; background: #1976d2; color: white; text-decoration: none; border-radius: 4px; transition: background 0.3s; }
              .links a:hover { background: #1565c0; }
              .info { background: #e3f2fd; padding: 20px; border-radius: 4px; margin: 20px 0; }
              .status { color: #4caf50; font-weight: bold; }
            </style>
          </head>
          <body>
            <div class="container">
              <h1>🦷 CRM Odontológico - API Documentation</h1>
              <p class="status">✅ Servidor de documentação ativo na porta ${this.port}</p>
              
              <div class="info">
                <h3>📋 Recursos Disponíveis</h3>
                <ul>
                  <li><strong>Swagger UI:</strong> Interface interativa para testar a API</li>
                  <li><strong>JSON Schema:</strong> Especificação OpenAPI em formato JSON</li>
                  <li><strong>Documentação Completa:</strong> Todos os endpoints documentados</li>
                </ul>
              </div>

              <div class="links">
                <a href="/docs">📖 Swagger UI</a>
                <a href="/swagger.json">📄 JSON Schema</a>
              </div>

              <div class="info">
                <h3>🔗 Links Úteis</h3>
                <ul>
                  <li><strong>API Principal:</strong> http://localhost:3001</li>
                  <li><strong>API Docs:</strong> http://localhost:3001/api/docs</li>
                  <li><strong>Lint Status:</strong> http://localhost:3001/api/lint-status</li>
                </ul>
              </div>
            </div>
          </body>
          </html>
        `);
        });

      // Iniciar servidor
      await app.listen(this.port);

      this.logger.log(`✅ Servidor Swagger iniciado com sucesso!`);
      this.logger.log(`📖 Swagger UI: http://localhost:${this.port}/docs`);
      this.logger.log(
        `📄 JSON Schema: http://localhost:${this.port}/swagger.json`,
      );
      this.logger.log(`🏠 Página inicial: http://localhost:${this.port}`);
    } catch (error) {
      const errorInfo = formatErrorForLogging(error);
      this.logger.error(
        `❌ Erro ao iniciar servidor Swagger: ${errorInfo.message}`,
        errorInfo.stack,
      );
      process.exit(1);
    }
  }
}

/**
 * Função para iniciar o servidor se executado diretamente
 */
async function startSwaggerServer(): Promise<void> {
  const port = process.env.SWAGGER_PORT
    ? parseInt(process.env.SWAGGER_PORT)
    : 3002;

  const server = new SwaggerServer(port);
  await server.start();
}

// Executar se chamado diretamente
if (require.main === module) {
  startSwaggerServer().catch((error) => {
    console.error('💥 Falha ao iniciar servidor Swagger:', error);
    process.exit(1);
  });
}

export { startSwaggerServer, SwaggerServer };
