import {
  DocumentBuilder,
  OpenAPIObject,
  SwaggerCustomOptions,
} from '@nestjs/swagger';

/**
 * Configuração centralizada do Swagger para o CRM Odontológico
 */
export class SwaggerConfig {
  /**
   * Cria a configuração base do Swagger
   */
  static createConfig(): Omit<OpenAPIObject, 'paths'> {
    // Criar array de tags ordenado alfabeticamente
    const tags = [
      { name: 'anamnesis', description: 'Operações relacionadas a anamnese' },
      { name: 'budgets', description: 'Operações relacionadas a orçamentos' },
      {
        name: 'Categorias de Agendamento',
        description: 'Operações relacionadas a categorias de agendamento',
      },
      {
        name: 'colors',
        description: 'Operações relacionadas a cores do sistema',
      },
      { name: 'dentists', description: 'Operações relacionadas a dentistas' },
      { name: 'documents', description: 'Operações relacionadas a documentos' },
      {
        name: 'employees',
        description: 'Operações relacionadas a funcionários',
      },
      { name: 'exams', description: 'Operações relacionadas a exames' },
      {
        name: 'indications',
        description: 'Operações relacionadas a indicações',
      },
      { name: 'leads', description: 'Operações relacionadas a leads' },
      {
        name: 'medical-records',
        description: 'Operações relacionadas a prontuários médicos',
      },
      {
        name: 'notifications',
        description: 'Operações relacionadas a notificações',
      },
      { name: 'patients', description: 'Operações relacionadas a pacientes' },
      {
        name: 'patient-types',
        description: 'Operações relacionadas a tipos de paciente',
      },
      { name: 'photos', description: 'Operações relacionadas a fotos' },
      {
        name: 'procedures',
        description: 'Operações relacionadas a procedimentos',
      },
      { name: 'receipts', description: 'Operações relacionadas a recibos' },
      { name: 'reports', description: 'Operações relacionadas a relatórios' },
      {
        name: 'schedulings',
        description: 'Operações relacionadas a agendamentos',
      },
      {
        name: 'suggestions',
        description: 'Operações relacionadas a sugestões',
      },
      {
        name: 'tasks',
        description: 'Operações relacionadas a demandas/tarefas',
      },
      {
        name: 'treatments',
        description: 'Operações relacionadas a tratamentos',
      },
      {
        name: 'treatment-flow',
        description: 'Operações relacionadas ao fluxo de tratamento',
      },
      {
        name: 'treatment-plans',
        description: 'Operações relacionadas a planos de tratamento',
      },
    ];

    // Criar o builder
    const builder = new DocumentBuilder()
      .setTitle('CRM Odontológico API')
      .setDescription(
        `
        ## 🦷 Sistema de Gestão Odontológica

        API completa para gerenciamento de clínicas odontológicas, incluindo:
        
        ### 📋 Funcionalidades Principais
        - **Pacientes**: Cadastro e gestão completa
        - **Agendamentos**: Sistema de marcação de consultas
        - **Tratamentos**: Controle de procedimentos
        - **Prontuários**: Registros médicos detalhados
        - **Relatórios**: Análises e estatísticas
        - **Tarefas**: Gestão de demandas internas
        
        ### 🔧 Recursos Técnicos
        - Validação automática de dados
        - Filtros de exceção personalizados
        - Paginação e ordenação
        - Upload de arquivos
        - Autenticação JWT
        
        ### 📊 Versão
        Versão atual da API com todas as funcionalidades implementadas.
      `,
      )
      .setVersion('1.0')
      .addServer('http://localhost:3001', 'Desenvolvimento')
      .addServer('https://api.crm-odonto.com', 'Produção');

    // Adicionar tags em ordem alfabética
    tags.forEach((tag) => {
      builder.addTag(tag.name, tag.description);
    });

    // Adicionar autenticação
    builder.addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Token JWT para autenticação',
        in: 'header',
      },
      'JWT-auth',
    );

    builder.addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'Chave de API para autenticação alternativa',
      },
      'API-Key',
    );

    return builder.build();
  }

  /**
   * Opções de configuração do Swagger UI
   */
  static getSwaggerOptions(): SwaggerCustomOptions {
    return {
      swaggerOptions: {
        docExpansion: 'none', // Inicia com todos os accordions fechados
        defaultModelsExpandDepth: -1, // Oculta a seção de modelos por padrão
        filter: true, // Adiciona campo de busca
        showRequestDuration: true, // Mostra duração das requisições
        tryItOutEnabled: true, // Habilita o botão "Try it out"
        tagsSorter: 'alpha', // Ordena as tags em ordem alfabética
        operationsSorter: 'alpha', // Ordena as operações em ordem alfabética
        requestInterceptor: (
          req: Record<string, unknown>,
        ): Record<string, unknown> => {
          // Interceptor para adicionar headers customizados
          (req.headers as Record<string, string>)['X-Requested-With'] =
            'SwaggerUI';
          return req;
        },
        responseInterceptor: (
          res: Record<string, unknown>,
        ): Record<string, unknown> => {
          // Interceptor para processar respostas
          return res;
        },
      },
      customSiteTitle: 'CRM Odontológico - API Documentation',
      customfavIcon: '/favicon.ico',
      customJs: [
        'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
      ],
      customCssUrl: [
        'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
      ],
      customCss: `
        .swagger-ui .topbar { display: none; }
        .swagger-ui .info .title { color: #1976d2; }
        .swagger-ui .info .description { margin: 20px 0; }
        .swagger-ui .scheme-container { background: #f5f5f5; padding: 10px; border-radius: 4px; }
      `,
    };
  }

  /**
   * Configuração para geração de JSON
   */
  static getJsonConfig(): {
    operationIdFactory: (controllerKey: string, methodKey: string) => string;
    ignoreGlobalPrefix: boolean;
    deepScanRoutes: boolean;
  } {
    return {
      operationIdFactory: (_controllerKey: string, methodKey: string) =>
        methodKey,
      ignoreGlobalPrefix: false,
      deepScanRoutes: true,
    };
  }
}
