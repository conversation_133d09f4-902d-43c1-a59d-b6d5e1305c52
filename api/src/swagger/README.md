# 📖 Swagger Documentation - CRM Odontológico

## 🎯 Visão Geral

Sistema completo de documentação API usando Swagger/OpenAPI 3.0, implementado 100% em TypeScript para o CRM Odontológico.

## 🚀 Como Usar

### **Desenvolvimento Normal**
```bash
# Iniciar aplicação principal com Swagger
npm run start:dev

# Iniciar apenas servidor de documentação (porta 3002)
npm run swagger:server

# Iniciar aplicação dedicada ao Swagger (porta 3001)
npm run swagger:dev
```

### **Geração de Documentação**
```bash
# Gerar toda a documentação (JSON + HTML)
npm run swagger:generate

# Gerar apenas JSON
npm run swagger:generate:json

# Gerar apenas HTML
npm run swagger:generate:html

# Validar documentação
npm run swagger:validate

# Gerar e iniciar servidor
npm run swagger:docs
```

## 🔗 URLs de Acesso

### **Aplica<PERSON> Principal (Porta 3001)**
- **API**: http://localhost:3001
- **Swagger**: http://localhost:3001/api/docs

### **Servidor Dedicado (Porta 3002)**
Acesse:
- http://localhost:3002 - Página inicial
- http://localhost:3002/docs - Swagger UI
- http://localhost:3002/swagger.json - JSON da documentação

## 📁 Estrutura dos Arquivos

```
src/swagger/
├── swagger.config.ts      # Configuração centralizada
├── swagger-server.ts      # Servidor dedicado
├── swagger-generator.ts   # Geração de documentação
└── README.md             # Esta documentação
```

## ⚙️ Configuração

### **SwaggerConfig**
Configuração centralizada com:
- ✅ **Título e descrição** da API
- ✅ **Versões e servidores** (dev/prod)
- ✅ **Tags organizadas** por módulo
- ✅ **Autenticação** JWT e API Key
- ✅ **Opções do Swagger UI** customizadas

### **SwaggerServer**
Servidor dedicado que oferece:
- ✅ **Interface Swagger UI** completa
- ✅ **JSON Schema** da API
- ✅ **Página inicial** informativa
- ✅ **CORS configurado**
- ✅ **Porta customizável**

### **SwaggerGenerator**
Gerador de documentação com:
- ✅ **Geração de JSON** da especificação
- ✅ **Geração de HTML** estático
- ✅ **Validação** da documentação
- ✅ **Logs detalhados**

## 🎨 Funcionalidades

### **Interface Swagger UI**
- ✅ **Busca** por endpoints
- ✅ **Try it out** habilitado
- ✅ **Modelos** organizados
- ✅ **Duração** das requisições
- ✅ **CSS customizado**

### **Documentação Completa**
- ✅ **Todos os endpoints** documentados
- ✅ **Modelos de dados** detalhados
- ✅ **Códigos de resposta** explicados
- ✅ **Exemplos** de uso
- ✅ **Autenticação** configurada

### **Geração Automática**
- ✅ **JSON Schema** OpenAPI 3.0
- ✅ **HTML estático** para deploy
- ✅ **Validação** automática
- ✅ **Versionamento** controlado

## 🔧 Variáveis de Ambiente

```env
# Porta da API principal
API_PORT=3001

# Porta do servidor Swagger dedicado
SWAGGER_PORT=3002
```

## 📊 Scripts Disponíveis

| Script | Descrição | Porta |
|--------|-----------|-------|
| `npm run start:dev` | API principal com Swagger | 3001 |
| `npm run swagger:server` | Servidor dedicado | 3002 |
| `npm run swagger:dev` | App dedicada ao Swagger | 3001 |
| `npm run swagger:generate` | Gerar documentação | - |
| `npm run swagger:validate` | Validar documentação | - |

## 🎯 Endpoints Documentados

### **Módulos Principais**
- 🦷 **Patients** - Gestão de pacientes
- 📅 **Schedulings** - Agendamentos
- 🔬 **Treatments** - Tratamentos
- 📋 **Medical Records** - Prontuários
- 📊 **Reports** - Relatórios
- ✅ **Tasks** - Tarefas

### **Módulos Auxiliares**
- 👨‍⚕️ **Dentists** - Dentistas
- 👥 **Employees** - Funcionários
- 🔔 **Notifications** - Notificações
- 💰 **Budgets** - Orçamentos
- 🔧 **Procedures** - Procedimentos
- 🧾 **Receipts** - Recibos

### **Módulos Avançados**
- 📸 **Photos** - Fotos
- 📄 **Documents** - Documentos
- 🔬 **Exams** - Exames
- 📝 **Anamnesis** - Anamnese
- 🎯 **Leads** - Leads
- 💡 **Suggestions** - Sugestões

## 🔐 Autenticação

### **JWT Bearer Token**
```bash
Authorization: Bearer <token>
```

### **API Key**
```bash
X-API-Key: <api-key>
```

## 📈 Benefícios

### **Para Desenvolvedores**
- ✅ **Documentação sempre atualizada**
- ✅ **Testes diretos** na interface
- ✅ **Modelos de dados** claros
- ✅ **Exemplos** de requisições

### **Para Integração**
- ✅ **Especificação OpenAPI** padrão
- ✅ **Geração de clientes** automática
- ✅ **Validação** de contratos
- ✅ **Versionamento** controlado

### **Para Produção**
- ✅ **HTML estático** para deploy
- ✅ **JSON Schema** para ferramentas
- ✅ **Validação** automática
- ✅ **Performance** otimizada

## 🔗 Links Úteis

### **Desenvolvimento**
- **Aplicação Principal**: http://localhost:3001/api/docs
- **Servidor Swagger**: http://localhost:3002/docs
- **JSON da API**: http://localhost:3002/swagger.json

### **Recursos**
- [OpenAPI Specification](https://swagger.io/specification/)
- [Swagger UI](https://swagger.io/tools/swagger-ui/)
- [NestJS Swagger](https://docs.nestjs.com/openapi/introduction)

## 🎉 Status

- ✅ **Configuração** 100% TypeScript
- ✅ **Servidor dedicado** funcionando
- ✅ **Geração automática** implementada
- ✅ **Validação** funcionando
- ✅ **Interface** customizada
- ✅ **Documentação** completa

**Sistema Swagger totalmente funcional e profissional! 🚀**
