#!/bin/sh
set -e

echo "Verificando variáveis de ambiente..."
env | grep DB_

echo "Aguardando o MySQL inicializar..."

# Função para verificar se o MySQL está pronto
wait_for_mysql() {
  # <PERSON>iro, verificar se o host está acessível
  until nc -z -v -w5 $DB_HOST $DB_PORT; do
    echo "Esperando o servidor MySQL ($DB_HOST:$DB_PORT) ficar disponível..."
    sleep 2
  done

  echo "Servidor MySQL está acessível. Verificando conexão..."

  # Depois, verificar se podemos nos conectar com as credenciais
  max_attempts=30
  attempt=0

  while [ $attempt -lt $max_attempts ]; do
    if MYSQL_PWD=$DB_PASSWORD mysql -h $DB_HOST -P $DB_PORT -u $DB_USERNAME -e "SELECT 1" > /dev/null 2>&1; then
      echo "Conexão com MySQL estabelecida com sucesso!"
      return 0
    fi

    attempt=$((attempt+1))
    echo "Tentativa $attempt/$max_attempts: Ainda não foi possível conectar ao MySQL. Aguardando..."
    sleep 3
  done

  echo "Falha ao conectar ao MySQL após $max_attempts tentativas."
  return 1
}

# Aguardar o MySQL
wait_for_mysql

# Criar arquivo .env com as variáveis de ambiente
echo "Criando arquivo .env..."
cat > .env << EOL
DB_HOST=\${DB_HOST}
DB_PORT=\${DB_PORT}
DB_USERNAME=\${DB_USERNAME}
DB_PASSWORD=\${DB_PASSWORD}
DB_DATABASE=\${DB_DATABASE}
API_PORT=\${API_PORT}
JWT_SECRET=\${JWT_SECRET}
NODE_ENV=\${NODE_ENV}
EOL

# Iniciar a aplicação
echo "Iniciando a API..."
exec node dist/main.js
