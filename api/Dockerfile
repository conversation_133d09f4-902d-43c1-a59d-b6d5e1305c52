# =============================================================================
# CRM ODONTO API - UNIFIED DOCKERFILE
# =============================================================================
# Multi-stage Dockerfile que funciona tanto para desenvolvimento quanto produção
# Controlado pela variável NODE_ENV:
# - NODE_ENV=development: Hot reload, ferramentas de desenvolvimento
# - NODE_ENV=production: Otimizado, apenas dependências de produção
# =============================================================================

# =============================================================================
# BASE STAGE - Configuração comum para todos os ambientes
# =============================================================================
FROM node:20-slim AS base

# Instala dependências do sistema necessárias
RUN apt-get update && \
    apt-get install -y \
    default-mysql-client \
    netcat-openbsd \
    openssl \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Configurações do Node.js
ENV NODE_OPTIONS="--max-old-space-size=1024"

WORKDIR /app

# Copia arquivos de dependências
COPY package*.json ./

# =============================================================================
# DEVELOPMENT STAGE - Para desenvolvimento com hot reload
# =============================================================================
FROM base AS development

# Instala todas as dependências (incluindo devDependencies)
RUN npm ci

# Copia o código fonte
COPY . .

# Expõe a porta da aplicação
EXPOSE 3000

# Comando para desenvolvimento com hot reload
CMD ["npm", "run", "start:dev"]

# =============================================================================
# PRODUCTION STAGE - Para produção otimizada
# =============================================================================
FROM base AS production

# Define ambiente de produção
ENV NODE_ENV=production

# Instala o CLI do NestJS globalmente
RUN npm install -g @nestjs/cli

# Instala todas as dependências primeiro (incluindo devDependencies para build)
RUN npm ci --include=dev

# Copia o código fonte
COPY . .

# Faz o build da aplicação
RUN npm run build

# Remove devDependencies após o build para otimizar a imagem
RUN npm prune --production && npm cache clean --force

# Expõe a porta da aplicação
EXPOSE 3000

# Comando para produção
CMD ["node", "dist/main.js"]
