# CRM Odonto API

API backend para o sistema CRM Odontológico.

## Descrição

Esta API fornece endpoints para gerenciar pacientes, agendamentos e tratamentos para um sistema de CRM odontológico.

## Tecnologias Utilizadas

- Node.js
- NestJS
- TypeORM
- MySQL
- TypeScript

## Pré-requisitos

- Node.js (v14 ou superior)
- MySQL (v5.7 ou superior)

## Instalação

1. Clone o repositório:

```bash
git clone <url-do-repositorio>
cd CRM-Odonto-api
```

2. Instale as dependências:

```bash
npm install
```

3. Configure o banco de dados:

- Crie um banco de dados MySQL chamado `crm_odonto`
- Execute o script SQL para criar as tabelas e inserir dados de exemplo:

```bash
mysql -u root -p < init-db.sql
```

4. Configure as variáveis de ambiente:

- Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=sua_senha
DB_DATABASE=crm_odonto
JWT_SECRET=seu_segredo_jwt
```

## Executando a aplicação

```bash
# Desenvolvimento
npm run start

# Modo watch
npm run start:dev

# Modo produção
npm run start:prod
```

## Estrutura do Projeto

```
src/
├── appointments/       # Módulo de agendamentos
├── patients/           # Módulo de pacientes
├── treatments/         # Módulo de tratamentos
├── app.controller.ts   # Controlador principal
├── app.module.ts       # Módulo principal
├── app.service.ts      # Serviço principal
└── main.ts             # Ponto de entrada da aplicação
```

## API Endpoints

### Pacientes

- `GET /api/patients` - Listar todos os pacientes
- `GET /api/patients/:id` - Obter um paciente específico
- `POST /api/patients` - Criar um novo paciente
- `PATCH /api/patients/:id` - Atualizar um paciente
- `DELETE /api/patients/:id` - Excluir um paciente

### Agendamentos

- `GET /api/appointments` - Listar todos os agendamentos
- `GET /api/appointments?patientId=1` - Listar agendamentos de um paciente
- `GET /api/appointments/:id` - Obter um agendamento específico
- `POST /api/appointments` - Criar um novo agendamento
- `PATCH /api/appointments/:id` - Atualizar um agendamento
- `DELETE /api/appointments/:id` - Excluir um agendamento

### Tratamentos

- `GET /api/treatments` - Listar todos os tratamentos
- `GET /api/treatments?patientId=1` - Listar tratamentos de um paciente
- `GET /api/treatments/:id` - Obter um tratamento específico
- `POST /api/treatments` - Criar um novo tratamento
- `PATCH /api/treatments/:id` - Atualizar um tratamento
- `DELETE /api/treatments/:id` - Excluir um tratamento

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para mais detalhes.
